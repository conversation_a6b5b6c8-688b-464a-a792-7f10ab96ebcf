import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { augmentSession, githubSession, linearSession, addSessionCookie } from '$lib/server/session';

/**
 * GET /api/auth/session
 * Get current session information (without exposing tokens)
 */
export const GET: RequestHandler = async ({ request }) => {
	try {
		const sessions = {
			augment: augmentSession.get(request),
			github: githubSession.get(request),
			linear: linearSession.get(request)
		};

		// Return session info without exposing actual tokens
		const sessionInfo = {
			augment: sessions.augment ? {
				isAuthenticated: true,
				tenantUrl: sessions.augment.tenantUrl,
				scopes: sessions.augment.scopes,
				expiresAt: sessions.augment.expiresAt
			} : null,
			github: sessions.github ? {
				isAuthenticated: true,
				scopes: sessions.github.scopes,
				expiresAt: sessions.github.expiresAt
			} : null,
			linear: sessions.linear ? {
				isAuthenticated: true,
				expiresAt: sessions.linear.expiresAt
			} : null
		};

		return json(sessionInfo);
	} catch (error) {
		console.error('Session retrieval error:', error);
		return json({ error: 'Failed to retrieve session' }, { status: 500 });
	}
};

/**
 * DELETE /api/auth/session
 * Clear all sessions (logout)
 */
export const DELETE: RequestHandler = async () => {
	try {
		const headers = new Headers();

		// Clear all session cookies
		addSessionCookie(headers, augmentSession.clear());
		addSessionCookie(headers, githubSession.clear());
		addSessionCookie(headers, linearSession.clear());

		return json({ success: true }, { headers });
	} catch (error) {
		console.error('Session clearing error:', error);
		return json({ error: 'Failed to clear session' }, { status: 500 });
	}
};
