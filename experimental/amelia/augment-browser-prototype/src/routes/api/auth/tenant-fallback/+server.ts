import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { PUBLIC_TENANT_URL } from '$env/static/public';

export const GET: RequestHandler = async () => {
	if (!PUBLIC_TENANT_URL) {
		return json(
			{
				error: 'No fallback tenant URL configured. Please set PUBLIC_TENANT_URL in .env.local'
			},
			{ status: 500 }
		);
	}

	return json({
		tenantUrl: PUBLIC_TENANT_URL
	});
};
