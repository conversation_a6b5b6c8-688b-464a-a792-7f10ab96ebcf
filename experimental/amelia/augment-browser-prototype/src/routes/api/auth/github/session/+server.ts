import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { githubSession, addSessionCookie, type GitHubSession } from '$lib/server/session';

/**
 * GET /api/auth/github/session
 * Get current GitHub session
 */
export const GET: RequestHandler = async ({ request }) => {
	try {
		const session = githubSession.get(request);
		return json({ session });
	} catch (error) {
		console.error('GitHub session retrieval error:', error);
		return json({ error: 'Failed to get GitHub session' }, { status: 500 });
	}
};

/**
 * POST /api/auth/github/session
 * Store GitHub session securely
 */
export const POST: RequestHandler = async ({ request }) => {
	try {
		const sessionData: GitHubSession = await request.json();

		if (!sessionData.accessToken) {
			return json({ error: 'Access token is required' }, { status: 400 });
		}

		// Set secure session cookie
		const headers = new Headers();
		const sessionCookie = githubSession.set(sessionData);
		addSessionCookie(headers, sessionCookie);

		return json({ success: true }, { headers });
	} catch (error) {
		console.error('GitHub session storage error:', error);
		return json({ error: 'Failed to store GitHub session' }, { status: 500 });
	}
};

/**
 * DELETE /api/auth/github/session
 * Clear GitHub session
 */
export const DELETE: RequestHandler = async () => {
	try {
		const headers = new Headers();
		const clearCookie = githubSession.clear();
		addSessionCookie(headers, clearCookie);

		return json({ success: true }, { headers });
	} catch (error) {
		console.error('GitHub session clearing error:', error);
		return json({ error: 'Failed to clear GitHub session' }, { status: 500 });
	}
};
