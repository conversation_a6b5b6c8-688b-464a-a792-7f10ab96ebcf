import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { GITHUB_CLIENT_ID } from '$env/static/private';

export const GET: RequestHandler = async () => {
	if (!GITHUB_CLIENT_ID || GITHUB_CLIENT_ID.trim() === '') {
		return json(
			{
				error:
					'GitHub OAuth not configured. Please set up your GitHub OAuth App and update GITHUB_CLIENT_ID in .env.local'
			},
			{ status: 500 }
		);
	}

	return json({
		clientId: GITHUB_CLIENT_ID,
		redirectUri: '/auth/github/callback',
		scopes: ['repo', 'user:email']
	});
};
