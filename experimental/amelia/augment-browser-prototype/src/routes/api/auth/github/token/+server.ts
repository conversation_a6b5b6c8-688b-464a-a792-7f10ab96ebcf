import { json } from '@sveltejs/kit';
import type { <PERSON>questHand<PERSON> } from './$types';

// GitHub OAuth App credentials - these need to be configured
import { GITHUB_CLIENT_ID, GITHUB_CLIENT_SECRET } from '$env/static/private';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const { code, codeVerifier, redirectUri } = await request.json();

		if (!code) {
			return json({ error: 'Missing authorization code' }, { status: 400 });
		}

		if (
			!GITHUB_CLIENT_ID ||
			!GITHUB_CLIENT_SECRET ||
			GITHUB_CLIENT_ID.trim() === '' ||
			GITHUB_CLIENT_SECRET.trim() === ''
		) {
			console.error('GitHub OAuth not configured properly:', {
				hasClientId: !!GITHUB_CLIENT_ID,
				hasClientSecret: !!GITHUB_CLIENT_SECRET,
				clientIdEmpty: !GITHUB_CLIENT_ID || GITHUB_CLIENT_ID.trim() === '',
				clientSecretEmpty: !GITHUB_CLIENT_SECRET || GITHUB_CLIENT_SECRET.trim() === ''
			});
			return json(
				{
					error:
						'GitHub OAuth not configured. Please set up your GitHub OAuth App credentials in .env.local'
				},
				{ status: 500 }
			);
		}

		// Exchange authorization code for access token
		const tokenResponse = await fetch('https://github.com/login/oauth/access_token', {
			method: 'POST',
			headers: {
				Accept: 'application/json',
				'Content-Type': 'application/json',
				'User-Agent': 'Augment-Browser/1.0'
			},
			body: JSON.stringify({
				client_id: GITHUB_CLIENT_ID,
				client_secret: GITHUB_CLIENT_SECRET,
				code,
				redirect_uri: redirectUri
			})
		});

		if (!tokenResponse.ok) {
			const errorText = await tokenResponse.text();
			console.error('GitHub token exchange failed:', errorText);
			return json({ error: 'Failed to exchange code for token' }, { status: 400 });
		}

		const tokenData = await tokenResponse.json();

		if (tokenData.error) {
			console.error('GitHub OAuth error:', tokenData);
			return json({ error: tokenData.error_description || tokenData.error }, { status: 400 });
		}

		// Return the access token
		return json({
			access_token: tokenData.access_token,
			token_type: tokenData.token_type,
			scope: tokenData.scope
		});
	} catch (error) {
		console.error('GitHub OAuth token exchange error:', error);
		return json(
			{ error: error instanceof Error ? error.message : 'Unknown error' },
			{ status: 500 }
		);
	}
};
