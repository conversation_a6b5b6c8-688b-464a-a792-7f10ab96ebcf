import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSessionData } from '$lib/utils/session-utils';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const body = await request.json();

		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Validate tenant URL
		try {
			const url = new URL(tenantUrl);
			if (!url.hostname.endsWith('.augmentcode.com')) {
				return json({ error: 'Invalid tenant URL' }, { status: 400 });
			}
		} catch {
			return json({ error: 'Invalid tenant URL format' }, { status: 400 });
		}

		// Use the tenantUrl directly from session data
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const apiUrl = `${cleanTenantUrl}/remote-agents/create`;

		const response = await fetch(apiUrl, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${accessToken}`,
				'Content-Type': 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			},
			body: JSON.stringify(body),
			signal: AbortSignal.timeout(60000) // 60 second timeout for creation
		});

		const responseData = await response
			.json()
			.catch(() => ({ error: 'Invalid response from API' }));

		if (!response.ok) {
			console.error('Remote agent creation failed:', response.status, responseData);
			console.log(
				`Info about the request`,
				JSON.stringify({
					url: apiUrl,
					method: 'POST',
					headers: {
						Authorization: `Bearer ${accessToken}`,
						'Content-Type': 'application/json',
						'User-Agent': 'Augment-Web-Client/1.0'
					},
					body: JSON.stringify(body, null, 2)
				})
			);
			return json(responseData, { status: response.status });
		}

		return json(responseData);
	} catch (error) {
		console.error('Remote agent creation error:', error);

		if (error instanceof Error && error.name === 'AbortError') {
			return json(
				{ error: 'Request timeout - agent creation is taking too long' },
				{ status: 504 }
			);
		}

		return json(
			{
				error: error instanceof Error ? error.message : 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
