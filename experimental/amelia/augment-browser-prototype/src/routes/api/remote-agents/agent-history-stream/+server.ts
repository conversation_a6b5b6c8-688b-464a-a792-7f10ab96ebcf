import type { <PERSON><PERSON><PERSON><PERSON><PERSON> } from './$types';
import { getSessionData } from '$lib/utils/session-utils';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const body = await request.json();

		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return new Response(JSON.stringify({ error: 'Missing session data - please log in again' }), {
				status: 401,
				headers: { 'Content-Type': 'application/json' }
			});
		}

		const { tenantUrl, accessToken } = sessionData;

		// Validate tenant URL
		try {
			const url = new URL(tenantUrl);
			if (!url.hostname.endsWith('.augmentcode.com')) {
				return new Response(JSON.stringify({ error: 'Invalid tenant URL' }), {
					status: 400,
					headers: { 'Content-Type': 'application/json' }
				});
			}
		} catch {
			return new Response(JSON.stringify({ error: 'Invalid tenant URL format' }), {
				status: 400,
				headers: { 'Content-Type': 'application/json' }
			});
		}

		// Use the tenantUrl directly from session data
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const apiUrl = `${cleanTenantUrl}/remote-agents/agent-history-stream`;

		console.log('Streaming agent history via:', apiUrl, JSON.stringify(body));

		const response = await fetch(apiUrl, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${accessToken}`,
				'Content-Type': 'application/json',
				Accept: 'text/event-stream',
				'User-Agent': 'Augment-Web-Client/1.0',
				'Cache-Control': 'no-cache'
			},
			body: JSON.stringify(body),
			signal: AbortSignal.timeout(60000) // 60 second timeout for streaming
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Agent history stream failed:', response.status, errorText);
			return new Response(JSON.stringify({ error: errorText || 'Stream failed' }), {
				status: response.status,
				headers: { 'Content-Type': 'application/json' }
			});
		}

		// For streaming endpoints, we need to handle the response differently
		console.log('Starting agent history stream...');

		// Set up Server-Sent Events headers
		const headers = new Headers({
			'Content-Type': 'text/event-stream',
			'Cache-Control': 'no-cache',
			Connection: 'keep-alive',
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Headers': 'Content-Type'
		});

		// Create a readable stream to forward the response
		const stream = new ReadableStream({
			async start(controller) {
				try {
					const reader = response.body?.getReader();
					if (!reader) {
						controller.close();
						return;
					}

					const decoder = new TextDecoder();
					let buffer = '';

					while (true) {
						const { done, value } = await reader.read();
						if (done) break;

						buffer += decoder.decode(value, { stream: true });
						const lines = buffer.split('\n');
						buffer = lines.pop() || ''; // Keep incomplete line in buffer

						for (const line of lines) {
							if (line.trim()) {
								// Forward each line as a Server-Sent Event
								controller.enqueue(new TextEncoder().encode(`data: ${line}\n\n`));
							}
						}
					}

					// Handle any remaining buffer content
					if (buffer.trim()) {
						controller.enqueue(new TextEncoder().encode(`data: ${buffer}\n\n`));
					}

					controller.close();
				} catch (error) {
					console.error('Stream error:', error);

					// Send a final error message before closing
					const errorMessage = error instanceof Error ? error.message : 'Stream error';
					try {
						controller.enqueue(new TextEncoder().encode(`data: {"error": "${errorMessage}"}\n\n`));
					} catch (enqueueError) {
						console.error('Failed to send error message:', enqueueError);
					}

					controller.error(error);
				}
			}
		});

		return new Response(stream, { headers });
	} catch (error) {
		console.error('Agent history stream error:', error);

		if (error instanceof Error && error.name === 'AbortError') {
			return new Response(
				JSON.stringify({ error: 'Request timeout - stream is taking too long' }),
				{
					status: 504,
					headers: { 'Content-Type': 'application/json' }
				}
			);
		}

		return new Response(
			JSON.stringify({
				error: error instanceof Error ? error.message : 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			}),
			{
				status: 500,
				headers: { 'Content-Type': 'application/json' }
			}
		);
	}
};
