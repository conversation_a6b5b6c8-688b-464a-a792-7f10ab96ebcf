import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSessionData } from '$lib/utils/session-utils';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const body = await request.json();

		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Validate tenant URL
		try {
			const url = new URL(tenantUrl);
			if (!url.hostname.endsWith('.augmentcode.com')) {
				return json({ error: 'Invalid tenant URL' }, { status: 400 });
			}
		} catch {
			return json({ error: 'Invalid tenant URL format' }, { status: 400 });
		}

		// Use the tenantUrl directly from session data
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const apiUrl = `${cleanTenantUrl}/remote-agents/list-stream`;

		console.log('Starting agent list stream via:', apiUrl);
		console.log('Request body:', JSON.stringify(body));
		console.log('Access token length:', accessToken.length);

		console.log('Making request to backend...');
		const startTime = Date.now();

		const response = await fetch(apiUrl, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${accessToken}`,
				'Content-Type': 'application/json',
				Accept: 'text/plain', // For streaming
				'User-Agent': 'Augment-Web-Client/1.0',
				Connection: 'keep-alive',
				'Cache-Control': 'no-cache'
			},
			body: JSON.stringify(body),
			signal: AbortSignal.timeout(120000) // 2 minute timeout for streaming
		});

		const responseTime = Date.now() - startTime;
		console.log(`Backend response received in ${responseTime}ms, status: ${response.status}`);

		if (!response.ok) {
			const errorData = await response.json().catch(() => ({}));
			console.error(
				'Remote agent list stream failed:',
				response.status,
				response.statusText,
				errorData
			);
			console.error('Response headers:', Object.fromEntries(response.headers.entries()));
			return json(errorData, { status: response.status });
		}

		console.log('Backend stream connection established, setting up proxy...');

		// Set up consistent streaming headers
		const headers = new Headers({
			'Content-Type': 'text/plain',
			'Cache-Control': 'no-cache',
			Connection: 'keep-alive',
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Headers': 'Content-Type'
		});

		// Create a readable stream to handle connection issues gracefully
		const stream = new ReadableStream({
			async start(controller) {
				let bytesReceived = 0;
				let linesProcessed = 0;
				const streamStartTime = Date.now();

				try {
					const reader = response.body?.getReader();
					if (!reader) {
						console.error('No reader available from backend response');
						controller.close();
						return;
					}

					console.log('Starting to read from backend stream...');
					const decoder = new TextDecoder();
					let buffer = '';

					while (true) {
						const { done, value } = await reader.read();
						if (done) {
							const streamDuration = Date.now() - streamStartTime;
							console.log(
								`Backend stream completed after ${streamDuration}ms, ${bytesReceived} bytes, ${linesProcessed} lines`
							);
							break;
						}

						if (value) {
							bytesReceived += value.length;
						}

						buffer += decoder.decode(value, { stream: true });
						const lines = buffer.split('\n');
						buffer = lines.pop() || ''; // Keep incomplete line in buffer

						for (const line of lines) {
							if (line.trim()) {
								linesProcessed++;
								// Forward each line directly (no SSE wrapping for list stream)
								controller.enqueue(new TextEncoder().encode(`${line}\n`));
							}
						}
					}

					// Send any remaining buffer content
					if (buffer.trim()) {
						controller.enqueue(new TextEncoder().encode(`${buffer}\n`));
					}

					controller.close();
				} catch (error) {
					const streamDuration = Date.now() - streamStartTime;
					console.error(
						`Agent list stream processing error after ${streamDuration}ms, ${bytesReceived} bytes, ${linesProcessed} lines:`,
						error
					);

					// Log specific error details
					if (error instanceof Error) {
						console.error('Error name:', error.name);
						console.error('Error message:', error.message);
						if (error.stack) {
							console.error('Error stack:', error.stack);
						}
					}

					try {
						// Send error message to client
						controller.enqueue(
							new TextEncoder().encode(
								JSON.stringify({
									error: 'Stream connection lost',
									details: error instanceof Error ? error.message : 'Unknown error'
								}) + '\n'
							)
						);
					} catch (enqueueError) {
						console.error('Failed to send error message:', enqueueError);
					}
					controller.error(error);
				}
			}
		});

		return new Response(stream, { headers });
	} catch (error) {
		console.error('Remote agent list stream error:', error);

		// Log detailed error information
		if (error instanceof Error) {
			console.error('Error name:', error.name);
			console.error('Error message:', error.message);
			console.error('Error cause:', error.cause);
			if (error.stack) {
				console.error('Error stack:', error.stack);
			}
		}

		if (error instanceof Error && error.name === 'AbortError') {
			console.error('Request timed out after 120 seconds');
			return json(
				{ error: 'Request timeout - agent list streaming is taking too long' },
				{ status: 504 }
			);
		}

		// Check for specific network errors
		if (
			error instanceof Error &&
			(error.message.includes('ECONNRESET') ||
				error.message.includes('ENOTFOUND') ||
				error.message.includes('ECONNREFUSED') ||
				error.message.includes('fetch'))
		) {
			console.error('Network error connecting to backend:', error.message);
			return json({ error: 'Backend connection failed', details: error.message }, { status: 502 });
		}

		return json(
			{
				error: error instanceof Error ? error.message : 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
