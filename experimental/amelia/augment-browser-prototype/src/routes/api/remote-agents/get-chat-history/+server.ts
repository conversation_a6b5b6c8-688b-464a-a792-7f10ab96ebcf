import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSessionData } from '$lib/utils/session-utils';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const body = await request.json();

		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Validate tenant URL
		try {
			const url = new URL(tenantUrl);
			if (!url.hostname.endsWith('.augmentcode.com')) {
				return json({ error: 'Invalid tenant URL' }, { status: 400 });
			}
		} catch {
			return json({ error: 'Invalid tenant URL format' }, { status: 400 });
		}

		const tenantBaseUrl = tenantUrl.replace(/\/$/, '');

		const apiUrl = `${tenantBaseUrl}/remote-agents/get-chat-history`;

		console.log('Getting chat history via:', apiUrl, JSON.stringify(body));

		const response = await fetch(apiUrl, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${accessToken}`,
				'Content-Type': 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0',
				Accept: 'application/json'
			},
			body: JSON.stringify(body),
			signal: AbortSignal.timeout(30000) // 30 second timeout
		});

		const responseData = await response.json();

		if (!response.ok) {
			console.error('Get chat history failed:', response.status, responseData);
			return json(responseData, { status: response.status });
		}

		console.log(`Retrieved ${responseData.chat_history?.length || 0} chat history entries`);
		return json(responseData);
	} catch (error) {
		console.error('Get chat history error:', error);

		if (error instanceof Error && error.name === 'AbortError') {
			return json(
				{ error: 'Request timeout - getting chat history is taking too long' },
				{ status: 504 }
			);
		}

		return json(
			{
				error: error instanceof Error ? error.message : 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
