import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSessionData } from '$lib/utils/session-utils';
import { parseGithubRef } from '$lib/utils/github-setup-scripts';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const body = await request.json();

		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Validate tenant URL
		try {
			const url = new URL(tenantUrl);
			if (!url.hostname.endsWith('.augmentcode.com')) {
				return json({ error: 'Invalid tenant URL' }, { status: 400 });
			}
		} catch {
			return json({ error: 'Invalid tenant URL format' }, { status: 400 });
		}

		// Convert githubRef string to the expected GithubRef struct format
		// Note: The unified client converts camelCase to snake_case, so githubRef becomes github_ref
		const githubRefField = body.github_ref || body.githubRef;
		if (githubRefField && typeof githubRefField === 'string') {
			const parsed = parseGithubRef(githubRefField);
			if (!parsed.isValid) {
				return json({
					scripts: [],
					errorMessage: 'Invalid GitHub reference format'
				});
			}

			// Convert to the expected backend format (using snake_case field name)
			// Based on API docs: GithubRef expects 'url' and 'ref' fields
			body.github_ref = {
				url: `https://github.com/${parsed.owner}/${parsed.repo}`,
				ref: parsed.ref
			};
			// Remove the camelCase version if it exists
			delete body.githubRef;
		}

		const tenantBaseUrl = tenantUrl.replace(/\/$/, '');
		const apiUrl = `${tenantBaseUrl}/remote-agents/list-github-setup-scripts`;

		console.log('Listing GitHub setup scripts via:', apiUrl);
		console.log('Request body:', JSON.stringify(body, null, 2));
		console.log('Headers:', {
			Authorization: `Bearer ${accessToken.substring(0, 10)}...`,
			'Content-Type': 'application/json',
			'User-Agent': 'Augment-Web-Client/1.0',
			Accept: 'application/json'
		});

		const response = await fetch(apiUrl, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${accessToken}`,
				'Content-Type': 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0',
				Accept: 'application/json'
			},
			body: JSON.stringify(body),
			signal: AbortSignal.timeout(30000) // 30 second timeout
		});

		let responseData;
		const responseText = await response.text();

		try {
			if (responseText.trim()) {
				responseData = JSON.parse(responseText);
			} else {
				responseData = {};
			}
		} catch (parseError) {
			console.error('Failed to parse response as JSON:', parseError);
			console.error('Response status:', response.status);
			console.error('Response headers:', Object.fromEntries(response.headers.entries()));
			console.error('Response text:', responseText.substring(0, 500));
			return json(
				{
					error: 'Invalid response format from backend',
					details: `Backend returned: ${responseText.substring(0, 200)}...`
				},
				{ status: 502 }
			);
		}

		if (!response.ok) {
			console.error('List GitHub setup scripts failed:', response.status, responseData);

			// If it's a 404, the endpoint probably doesn't exist yet
			if (response.status === 404) {
				return json({
					scripts: [],
					errorMessage: 'GitHub setup scripts feature is not yet available on this backend'
				});
			}

			// If it's a 400 with "Unidentified internal error", the feature might not be implemented
			if (response.status === 400 && responseData?.error === 'Unidentified internal error') {
				return json({
					scripts: [],
					errorMessage: 'GitHub setup scripts feature is not yet available on this backend'
				});
			}

			return json(responseData, { status: response.status });
		}

		console.log(`Retrieved ${responseData.scripts?.length || 0} GitHub setup scripts`);
		return json(responseData);
	} catch (error) {
		console.error('List GitHub setup scripts error:', error);

		if (error instanceof Error && error.name === 'AbortError') {
			return json(
				{ error: 'Request timeout - listing GitHub setup scripts is taking too long' },
				{ status: 504 }
			);
		}

		return json(
			{
				error: error instanceof Error ? error.message : 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
