import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSessionData } from '$lib/utils/session-utils';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		const body = await request.json();

		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Validate tenant URL
		try {
			const url = new URL(tenantUrl);
			if (!url.hostname.endsWith('.augmentcode.com')) {
				return json({ error: 'Invalid tenant URL' }, { status: 400 });
			}
		} catch {
			return json({ error: 'Invalid tenant URL format' }, { status: 400 });
		}

		// Use the tenantUrl directly from session data
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const apiUrl = `${cleanTenantUrl}/remote-agents/chat`;

		console.log('Sending chat message via:', apiUrl);

		// Send the chat message and return a streaming response
		// We don't need to wait for the backend chat response, just initiate the message
		const response = await fetch(apiUrl, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${accessToken}`,
				'Content-Type': 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			},
			body: JSON.stringify(body),
			signal: AbortSignal.timeout(30000) // Shorter timeout since we don't need the response content
		});

		// We don't actually need to parse the response content for chat messages
		// The backend just needs to receive the message, the response comes via streaming
		if (!response.ok) {
			const errorText = await response.text();
			console.error('Chat message failed:', response.status, errorText);
			return json({ error: errorText || 'Chat request failed' }, { status: response.status });
		}

		console.log('Chat message sent successfully');

		// Return a simple streaming response that immediately closes
		// This satisfies the client expectation of a streaming response
		const stream = new ReadableStream({
			start(controller) {
				// Send a simple success message and close
				const encoder = new TextEncoder();
				controller.enqueue(
					encoder.encode('data: {"type":"success","message":"Chat message sent"}\n\n')
				);
				controller.close();
			}
		});

		return new Response(stream, {
			headers: {
				'Content-Type': 'text/event-stream',
				'Cache-Control': 'no-cache',
				Connection: 'keep-alive'
			}
		});
	} catch (error) {
		console.error('Chat endpoint error:', error);
		return json(
			{
				error: error instanceof Error ? error.message : 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
