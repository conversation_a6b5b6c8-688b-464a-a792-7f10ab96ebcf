import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getSessionData } from '$lib/utils/session-utils';
import { safeJsonParse } from '$lib/utils/api-response-utils';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		// Handle empty request body gracefully
		let body = {};
		try {
			const requestText = await request.text();
			if (requestText.trim()) {
				body = JSON.parse(requestText);
			}
		} catch {
			// Use empty object if parsing fails
			body = {};
		}

		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Validate tenant URL
		try {
			const url = new URL(tenantUrl);
			if (!url.hostname.endsWith('.augmentcode.com')) {
				return json({ error: 'Invalid tenant URL' }, { status: 400 });
			}
		} catch {
			return json({ error: 'Invalid tenant URL format' }, { status: 400 });
		}

		// Use the tenantUrl directly from session data instead of constructing it
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const apiUrl = `${cleanTenantUrl}/remote-agents/list`;

		console.log('Making request to tenant API:', {
			apiUrl,
			method: 'POST',
			body,
			hasAccessToken: !!accessToken,
			accessTokenLength: accessToken?.length
		});

		const response = await fetch(apiUrl, {
			method: 'POST',
			headers: {
				Authorization: `Bearer ${accessToken}`,
				'Content-Type': 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			},
			body: JSON.stringify(body),
			signal: AbortSignal.timeout(30000) // 30 second timeout
		});

		// Handle non-JSON responses gracefully
		const responseData = await safeJsonParse(response);

		// Log response details for debugging
		console.log('API response:', {
			status: response.status,
			statusText: response.statusText,
			contentType: response.headers.get('content-type'),
			responseData,
			hasRemoteAgents: responseData?.remote_agents?.length || 0
		});

		if (!response.ok) {
			console.error('Remote agent list failed:', response.status, responseData);
			return json(responseData, { status: response.status });
		}

		return json(responseData);
	} catch (error) {
		console.error('Remote agent list error:', error);

		if (error instanceof Error && error.name === 'AbortError') {
			return json(
				{ error: 'Request timeout - listing agents is taking too long' },
				{ status: 504 }
			);
		}

		return json(
			{
				error: error instanceof Error ? error.message : 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
