import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSessionData } from '$lib/utils/session-utils';

interface ProxyRequest {
	method: 'GET' | 'POST' | 'PUT' | 'DELETE';
	endpoint: string;
	body?: any;
}

// This is the legacy proxy endpoint - new endpoints should use specific routes

export const POST: RequestHandler = async ({ request, cookies, ...rest }) => {
	try {
		const { method, endpoint, body }: ProxyRequest = await request.json();

		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Validate tenant URL
		try {
			const url = new URL(tenantUrl);
			if (!url.hostname.endsWith('.augmentcode.com')) {
				return json({ error: 'Invalid tenant URL' }, { status: 400 });
			}
		} catch {
			return json({ error: 'Invalid tenant URL format' }, { status: 400 });
		}

		// Check if this is a streaming endpoint
		const isStreamingEndpoint =
			endpoint.includes('/agent-history-stream') || endpoint.includes('/chat');

		// Try different API path variations
		// First try the tenant URL as the base (this should be the correct approach)
		const tenantBaseUrl = tenantUrl.replace(/\/$/, '');

		// Extract shard namespace from JWT token to construct correct API URLs
		let shardNamespace = 'dev-igor'; // fallback
		try {
			const tokenParts = accessToken.split('.');
			if (tokenParts.length === 3) {
				const payload = JSON.parse(atob(tokenParts[1]));
				if (payload.shard_namespace) {
					shardNamespace = payload.shard_namespace;
					console.log('Extracted shard namespace from token:', shardNamespace);
				}
			}
		} catch {
			console.log('Could not extract shard namespace from token, using fallback:', shardNamespace);
		}

		// Try different API server variations based on the shard namespace
		const devApiUrl = `https://${shardNamespace}.us-central.api.augmentcode.com`;
		const devApiUrl2 = `https://api.${shardNamespace}.us-central1.dev.augmentcode.com`;
		const devApiUrl3 = `https://${shardNamespace}.us-central1.dev.augmentcode.com`;

		// For certain endpoints like list-remote-tools, try tenant URL first
		const isAuthEndpoint =
			endpoint.includes('/agents/list-remote-tools') ||
			endpoint.includes('/github/') ||
			endpoint.includes('/linear/');

		console.log(`Proxying ${method} request to endpoint: ${endpoint}`);
		console.log('Tenant URL:', tenantUrl);
		console.log('Access token length:', accessToken.length);
		console.log('Is streaming endpoint:', isStreamingEndpoint);

		// Validate token format (should be a JWT or similar)
		if (!accessToken || accessToken.length < 10) {
			return json({ error: 'Invalid access token format' }, { status: 400 });
		}

		// Prepare request options - no timeout for streaming endpoints
		const requestOptions: RequestInit = {
			method,
			headers: {
				Authorization: `Bearer ${accessToken}`,
				'Content-Type': 'application/json',
				Accept: isStreamingEndpoint ? 'text/plain' : 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			}
		};

		// Add timeout - different timeouts for different streaming endpoints
		if (endpoint.includes('/chat')) {
			// 5 minute timeout for chat endpoints (agents need time to think and respond)
			requestOptions.signal = AbortSignal.timeout(300000);
		} else if (endpoint.includes('/agent-history-stream')) {
			// 60 second timeout for history stream endpoints (longer than client timeout)
			requestOptions.signal = AbortSignal.timeout(60000);
		} else {
			// 30 second timeout for regular endpoints
			requestOptions.signal = AbortSignal.timeout(30000);
		}

		// Add body for POST/PUT requests
		if (body && (method === 'POST' || method === 'PUT')) {
			requestOptions.body = JSON.stringify(body);
			console.log('Request body:', JSON.stringify(body, null, 2));
		}

		console.log('Request headers:', requestOptions.headers);

		// Try each API variation until one works
		let lastError: Error | null = null;
		let lastResponse: Response | null = null;

		const apiUrl = `${tenantBaseUrl}${endpoint}`;

		try {
			const response = await fetch(apiUrl, requestOptions);
			lastResponse = response;

			console.log(`Response status: ${response.status} for ${apiUrl}`);

			// Handle streaming endpoints differently
			if (isStreamingEndpoint && response.ok) {
				console.log('Handling streaming response for agent-history-stream');
				console.log('Response headers:', Object.fromEntries(response.headers.entries()));
				console.log('Response content-type:', response.headers.get('content-type'));

				// Set a reasonable timeout for streaming responses (90 seconds)
				const streamTimeout = setTimeout(() => {
					console.log('Streaming response timeout after 90 seconds');
				}, 90000);

				try {
					// For streaming endpoints, we need to collect all the data first
					// The stream should contain newline-delimited JSON or complete JSON response
					console.log('Starting to read streaming response...');
					const responseText = (await Promise.race([
						response.text(),
						new Promise<string>((_, reject) =>
							setTimeout(() => reject(new Error('Response text timeout')), 45000)
						)
					])) as string;
					clearTimeout(streamTimeout);
					console.log('Successfully read streaming response');

					console.log('Streaming response text length:', responseText.length);
					console.log('Streaming response text (first 500 chars):', responseText.substring(0, 500));
					console.log('Streaming response text (full):', responseText);

					// If response is empty, return empty chat history
					if (!responseText || responseText.trim() === '') {
						console.log('Empty streaming response, returning empty chat history');
						return json(
							{
								chat_history: []
							},
							{
								status: response.status,
								headers: {
									'Content-Type': 'application/json'
								}
							}
						);
					}

					// Try to parse as JSON (the endpoint might return complete JSON instead of streaming)
					try {
						const responseData = JSON.parse(responseText);
						console.log('Successfully parsed streaming response as JSON:', responseData);
						return json(responseData, {
							status: response.status,
							headers: {
								'Content-Type': 'application/json'
							}
						});
					} catch (parseError) {
						console.log('Response is not JSON, treating as text. Parse error:', parseError);
						console.log('Raw response text that failed to parse:', JSON.stringify(responseText));
						// If it's not JSON, return empty chat history
						return json(
							{
								chat_history: [],
								raw_response: responseText
							},
							{
								status: response.status,
								headers: {
									'Content-Type': 'application/json'
								}
							}
						);
					}
				} catch (streamError) {
					clearTimeout(streamTimeout);
					console.error('Error reading streaming response:', streamError);

					// Check if it's a timeout error
					const isTimeout = streamError instanceof Error && streamError.message.includes('timeout');
					const errorMessage = isTimeout
						? 'Streaming response timeout'
						: 'Failed to read streaming response';

					return json(
						{
							chat_history: [],
							error: errorMessage,
							timeout: isTimeout
						},
						{
							status: isTimeout ? 408 : 500,
							headers: {
								'Content-Type': 'application/json'
							}
						}
					);
				}
			}

			// Handle non-streaming endpoints as before
			let responseData;
			const contentType = response.headers.get('content-type');

			if (contentType && contentType.includes('application/json')) {
				responseData = await response.json();
			} else {
				responseData = await response.text();
			}

			// console.log(`API response (${response.status}) from ${apiUrl}:`, responseData);

			// Log more details for auth errors
			if (response.status === 401) {
				console.log('401 Unauthorized - Token might be invalid or expired');
				console.log('Response headers:', Object.fromEntries(response.headers.entries()));
			}

			// If we get a successful response (2xx) or a client error that's not 401/404/405, return it
			if (
				response.ok ||
				(response.status >= 400 &&
					response.status < 500 &&
					response.status !== 401 &&
					response.status !== 404 &&
					response.status !== 405)
			) {
				return json(responseData, {
					status: response.status,
					headers: {
						'Content-Type': 'application/json'
					}
				});
			}

			// If it's a 401, 404 or 405
			if (response.status === 401 || response.status === 404 || response.status === 405) {
				return json(responseData, {
					status: response.status,
					headers: {
						'Content-Type': 'application/json'
					}
				});
			}

			// For other server errors (5xx), also try next variation
			console.log(`Got ${response.status} from ${apiUrl}, trying next variation...`);
		} catch (error) {
			console.error(`Error with ${apiUrl}:`, error);
			lastError = error;
		}

		// If we get here, all variations failed
		if (lastResponse) {
			// Return the last response we got
			let responseData;
			try {
				const contentType = lastResponse.headers.get('content-type');
				if (contentType && contentType.includes('application/json')) {
					responseData = await lastResponse.json();
				} else {
					responseData = await lastResponse.text();
				}
			} catch {
				responseData = { error: 'Failed to parse response' };
			}

			return json(responseData, {
				status: lastResponse.status,
				headers: {
					'Content-Type': 'application/json'
				}
			});
		}

		// If no response was received, return the last error
		throw lastError || new Error('All API variations failed');
	} catch (error) {
		console.error('API proxy error:', error);

		// Handle timeout errors specifically
		if (error instanceof Error && error.name === 'AbortError') {
			return json(
				{
					error:
						'Request timeout - the remote agent API is taking too long to respond. Please try again.'
				},
				{ status: 504 }
			);
		}

		return json(
			{
				error: error instanceof Error ? error.message : 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
