import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { augmentSession } from '$lib/server/session';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const body = await request.json();
		const { requestId, rating, note } = body;

		if (!requestId || rating === undefined) {
			return json({ error: 'requestId and rating are required' }, { status: 400 });
		}

		// Get session data from secure cookies
		const sessionData = augmentSession.get(request);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Validate tenant URL
		try {
			const url = new URL(tenantUrl);
			if (!url.hostname.endsWith('.augmentcode.com')) {
				return json({ error: 'Invalid tenant URL' }, { status: 400 });
			}
		} catch {
			return json({ error: 'Invalid tenant URL format' }, { status: 400 });
		}

		// Make request to tenant backend
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const backendEndpoint = `${cleanTenantUrl}/chat-feedback`;

		console.log('Submitting feedback:', { requestId, rating, hasNote: !!note });

		const response = await fetch(backendEndpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
				Accept: 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			},
			body: JSON.stringify({
				request_id: requestId,
				rating,
				note: note || '',
				mode: 'CHAT'
			}),
			signal: AbortSignal.timeout(30000) // 30 second timeout
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Feedback submission failed:', {
				status: response.status,
				statusText: response.statusText,
				error: errorText
			});

			return json(
				{
					error: 'Failed to submit feedback',
					details: `Backend returned ${response.status}: ${response.statusText}`
				},
				{ status: response.status }
			);
		}

		const result = await response.json();
		console.log('Feedback submitted successfully:', result);

		return json(result);
	} catch (error) {
		console.error('Feedback endpoint error:', error);

		if (error instanceof Error && error.name === 'TimeoutError') {
			return json({ error: 'Request timeout - please try again' }, { status: 408 });
		}

		return json(
			{
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
