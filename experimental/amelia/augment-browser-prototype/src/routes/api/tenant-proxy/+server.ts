/**
 * Unified Tenant Proxy Server
 *
 * Single endpoint for all tenant API operations with:
 * - Centralized session management
 * - Smart URL construction
 * - Consistent timeout handling
 * - Proper error responses
 */

import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { augmentSession } from '$lib/server/session';

// ============================================================================
// TIMEOUT CONFIGURATION
// ============================================================================

function getTimeoutForEndpoint(endpoint: string): number {
	// Chat operations need longer timeouts
	if (endpoint.includes('/chat') || endpoint.includes('/remote-agents/chat')) {
		return 300000; // 5 minutes
	}

	// Streaming operations
	if (endpoint.includes('/stream')) {
		return 60000; // 1 minute
	}

	// Agent creation
	if (endpoint.includes('/create')) {
		return 60000; // 1 minute
	}

	// Default timeout
	return 30000; // 30 seconds
}

// ============================================================================
// URL CONSTRUCTION
// ============================================================================

function constructAPIUrl(tenantUrl: string, endpoint: string): string {
	// Normalize tenant URL
	const baseUrl = tenantUrl.replace(/\/$/, '');

	// Handle different endpoint patterns
	if (endpoint.startsWith('/remote-agents/')) {
		return `${baseUrl}${endpoint}`;
	}

	if (endpoint.startsWith('/remote-agent-actions/')) {
		return `${baseUrl}${endpoint}`;
	}

	// Default: assume it's a complete endpoint
	return `${baseUrl}${endpoint}`;
}

// ============================================================================
// REQUEST HANDLER
// ============================================================================

export const POST: RequestHandler = async ({ request }) => {
	try {
		// Parse request body
		const requestData = await request.json();
		const { endpoint, method = 'GET', body } = requestData;

		if (!endpoint) {
			return json({ error: 'Endpoint is required' }, { status: 400 });
		}

		// Get session data from secure cookies
		const sessionData = augmentSession.get(request);

		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json(
				{
					error: 'Authentication required',
					details: 'No valid session found'
				},
				{ status: 401 }
			);
		}

		// Construct the full API URL
		const apiUrl = constructAPIUrl(sessionData.tenantUrl, endpoint);

		// Determine timeout based on endpoint
		const timeout = getTimeoutForEndpoint(endpoint);

		// Prepare headers
		const headers: Record<string, string> = {
			Authorization: `Bearer ${sessionData.accessToken}`,
			'Content-Type': 'application/json'
		};

		// Make the request to the tenant API
		const controller = new AbortController();
		const timeoutId = setTimeout(() => controller.abort(), timeout);

		try {
			const response = await fetch(apiUrl, {
				method,
				headers,
				body: body ? JSON.stringify(body) : undefined,
				signal: controller.signal
			});

			clearTimeout(timeoutId);

			// Handle non-JSON responses
			const contentType = response.headers.get('content-type');
			const isJson = contentType?.includes('application/json');

			if (!response.ok) {
				let errorData: any = {};

				if (isJson) {
					try {
						errorData = await response.json();
					} catch {
						// Ignore JSON parse errors for error responses
					}
				} else {
					errorData.error = await response.text();
				}

				return json(
					{
						error: errorData.error || `HTTP ${response.status}: ${response.statusText}`,
						details: errorData.details || errorData.message,
						status: response.status
					},
					{ status: response.status }
				);
			}

			// Parse successful response
			if (isJson) {
				const data = await response.json();
				return json(data);
			} else {
				// Handle non-JSON responses (like text/plain)
				const text = await response.text();
				return json({ data: text });
			}
		} catch (fetchError) {
			clearTimeout(timeoutId);

			if (fetchError instanceof Error) {
				if (fetchError.name === 'AbortError') {
					return json(
						{
							error: 'Request timeout',
							details: `Request to ${endpoint} timed out after ${timeout}ms`
						},
						{ status: 408 }
					);
				}

				return json(
					{
						error: 'Network error',
						details: fetchError.message
					},
					{ status: 500 }
				);
			}

			throw fetchError;
		}
	} catch (error) {
		console.error('Tenant proxy error:', error);

		return json(
			{
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};

// ============================================================================
// HEALTH CHECK
// ============================================================================

export const GET: RequestHandler = async ({ request }) => {
	try {
		const sessionData = augmentSession.get(request);

		return json({
			status: 'ok',
			authenticated: !!(sessionData?.tenantUrl && sessionData?.accessToken),
			tenantUrl: sessionData?.tenantUrl || null
		});
	} catch (error) {
		return json(
			{
				status: 'error',
				authenticated: false,
				error: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};

// ============================================================================
// OPTIONS HANDLER (CORS)
// ============================================================================

export const OPTIONS: RequestHandler = async () => {
	return new Response(null, {
		status: 200,
		headers: {
			'Access-Control-Allow-Origin': '*',
			'Access-Control-Allow-Methods': 'GET, POST, OPTIONS',
			'Access-Control-Allow-Headers': 'Content-Type, Authorization'
		}
	});
};
