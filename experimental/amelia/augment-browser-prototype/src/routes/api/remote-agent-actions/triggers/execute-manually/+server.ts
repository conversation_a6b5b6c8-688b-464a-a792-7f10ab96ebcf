import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getSessionData } from '$lib/utils/session-utils';
import { toSnakeCase } from '$lib/api/unified-client';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		// Get trigger execution data from request body
		const executionData = await request.json();

		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Make direct API call to tenant backend (consistent with other remote-agent-actions endpoints)
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const backendEndpoint = `${cleanTenantUrl}/remote-agent-actions/triggers/execute-manually`;

		// Convert camelCase to snake_case for backend compatibility
		const snakeCaseBody = toSnakeCase(executionData);

		console.log('Executing trigger manually via:', backendEndpoint);
		console.log('Request body:', JSON.stringify(snakeCaseBody, null, 2));

		const response = await fetch(backendEndpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
				Accept: 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			},
			body: JSON.stringify(snakeCaseBody)
		});

		console.log('Execute trigger response status:', response.status);

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Backend error executing trigger manually:', {
				status: response.status,
				statusText: response.statusText,
				body: errorText
			});
			return json(
				{ error: `Backend error: ${response.statusText}`, details: errorText },
				{ status: response.status }
			);
		}

		const data = await response.json();
		return json(data);
	} catch (error) {
		console.error('Error executing trigger manually:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
