import { json } from '@sveltejs/kit';
import type { RequestH<PERSON><PERSON> } from './$types';
import { getSessionData } from '$lib/utils/session-utils';

export const POST: RequestHandler = async ({ cookies }) => {
	try {
		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Hit the tenant URL directly
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const endpoint = `${cleanTenantUrl}/remote-agent-actions/triggers/list`;

		const response = await fetch(endpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
				Accept: 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			},
			body: JSON.stringify({})
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Backend error listing triggers:', {
				status: response.status,
				statusText: response.statusText,
				body: errorText
			});
			return json(
				{ error: `Backend error: ${response.statusText}`, details: errorText },
				{ status: response.status }
			);
		}

		const data = await response.json();
		return json(data);
	} catch (error) {
		console.error('Error listing triggers:', error);
		// Return empty array for now if there's an error
		return json([]);
	}
};
