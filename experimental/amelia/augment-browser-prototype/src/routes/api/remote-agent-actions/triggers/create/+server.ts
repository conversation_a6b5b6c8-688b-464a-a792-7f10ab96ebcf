import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { toSnakeCase } from '$lib/api/unified-client';
import { getSessionData } from '$lib/utils/session-utils';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		// Get trigger configuration from request body
		const cleanTriggerConfig = await request.json();

		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData.tenantUrl || !sessionData.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Make direct API call to tenant backend
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const backendEndpoint = `${cleanTenantUrl}/remote-agent-actions/triggers/create`;

		// Convert camelCase to snake_case for backend compatibility
		const snakeCaseBody = toSnakeCase(cleanTriggerConfig);

		const response = await fetch(backendEndpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
				Accept: 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			},
			body: JSON.stringify(snakeCaseBody)
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Backend error creating trigger:', {
				status: response.status,
				statusText: response.statusText,
				body: errorText
			});
			console.log('request info', {
				url: backendEndpoint,
				method: 'POST',
				headers: {
					'Content-Type': 'application/json',
					Authorization: `Bearer ${accessToken}`,
					Accept: 'application/json',
					'User-Agent': 'Augment-Web-Client/1.0'
				},
				body: JSON.stringify(snakeCaseBody, null, 2)
			});

			return json(
				{ error: `Backend error: ${response.statusText}`, details: errorText },
				{ status: response.status }
			);
		}

		const result = await response.json();
		return json(result);
	} catch (error) {
		console.error('Error creating trigger:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
