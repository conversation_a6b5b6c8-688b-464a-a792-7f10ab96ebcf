import { json } from '@sveltejs/kit';
import type { RequestHandler } from './$types';
import { getSessionData } from '$lib/utils/session-utils';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Make direct API call to tenant backend
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const backendEndpoint = `${cleanTenantUrl}/remote-agent-actions/triggers/dismiss-entity`;

		const apiBody = await request.json();

		console.log('Making direct request to triggers dismiss-entity:', backendEndpoint);
		console.log('Request body:', JSON.stringify(apiBody, null, 2));

		const response = await fetch(backendEndpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
				Accept: 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			},
			body: JSON.stringify(apiBody)
		});

		// log out request to share details
		console.log('request info', {
			url: backendEndpoint,
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
				Accept: 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			},
			body: JSON.stringify(apiBody, null, 2)
		});

		console.log('Dismiss entity response status:', response.status);

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Backend error dismissing entity:', {
				status: response.status,
				statusText: response.statusText,
				body: errorText
			});
			return json(
				{ error: `Backend error: ${response.statusText}`, details: errorText },
				{ status: response.status }
			);
		}

		// For dismiss endpoint, we typically just need to confirm success
		// The response might be empty or contain a simple success message
		let data;
		try {
			data = await response.json();
		} catch {
			// If response is empty or not JSON, that's fine for a dismiss operation
			data = { success: true };
		}

		console.log('Dismiss entity response data:', data);

		// Return success response
		return json(data);
	} catch (error) {
		console.error('Error dismissing entity:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
