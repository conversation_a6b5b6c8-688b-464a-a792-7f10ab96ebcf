import { json } from '@sveltejs/kit';
import type { <PERSON>quest<PERSON><PERSON><PERSON> } from './$types';
import { createLinearApiClient } from '$lib/server/api-client';

/**
 * Secure Linear GraphQL proxy endpoint
 *
 * This endpoint provides secure access to Linear's GraphQL API without exposing
 * access tokens to client-side JavaScript. All authentication is handled server-side
 * using httpOnly cookies.
 */

export const POST: RequestHandler = async ({ request }) => {
	try {
		// Create authenticated Linear API client using secure session
		const linearClient = createLinearApiClient(request);

		// Parse the GraphQL request
		const { query, variables } = await request.json();

		if (!query) {
			return json({ error: 'GraphQL query is required' }, { status: 400 });
		}

		// Make the authenticated request to Linear's GraphQL API
		const response = await linearClient.fetch('/graphql', {
			method: 'POST',
			body: JSON.stringify({
				query,
				variables: variables || {}
			})
		});

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Linear GraphQL API error:', {
				status: response.status,
				statusText: response.statusText,
				body: errorText
			});
			return json(
				{
					error: `Linear API error: ${response.statusText}`,
					details: errorText
				},
				{ status: response.status }
			);
		}

		const data = await response.json();

		// Check for GraphQL errors
		if (data.errors) {
			console.error('Linear GraphQL errors:', data.errors);
			return json(
				{
					error: 'Linear GraphQL error',
					details: data.errors.map((e: any) => e.message).join(', '),
					graphqlErrors: data.errors
				},
				{ status: 400 }
			);
		}

		return json(data);
	} catch (error) {
		console.error('Linear proxy error:', error);

		if (error instanceof Error && error.message.includes('No valid Linear session')) {
			return json(
				{
					error: 'Linear authentication required',
					details: 'Please connect your Linear account in settings'
				},
				{ status: 401 }
			);
		}

		return json(
			{
				error: 'Internal server error',
				details: error instanceof Error ? error.message : 'Unknown error'
			},
			{ status: 500 }
		);
	}
};
