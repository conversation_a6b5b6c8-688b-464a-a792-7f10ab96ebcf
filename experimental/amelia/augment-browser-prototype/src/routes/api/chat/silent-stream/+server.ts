import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { augmentSession } from '$lib/server/session';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const body = await request.json();
		const { message, selectedCode, path, lang, prefix, suffix, model, mode } = body;

		if (!message || typeof message !== 'string') {
			return json({ error: 'Message is required and must be a string' }, { status: 400 });
		}

		// Get session data from secure cookies
		const sessionData = augmentSession.get(request);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Generate required headers
		const requestId = crypto.randomUUID();
		const sessionId = crypto.randomUUID();

		// Transform request to Augment API format
		const augmentRequest = {
			message: message,
			silent: false, // Changed to false to avoid validation issues
			chat_history: [],
			blobs: {
				checkpoint_id: null,
				added_blobs: [],
				deleted_blobs: []
			},
			sequence_id: 1,
			disable_auto_external_sources: false,
			user_guided_blobs: [],
			external_source_ids: [],
			model: 'gemini2-5-flash-200k-v7-c4-p2-agent'
		};

		// Add optional fields if provided
		if (selectedCode) augmentRequest.selected_code = selectedCode;
		if (path) augmentRequest.path = path;
		if (lang) augmentRequest.lang = lang;
		if (prefix) augmentRequest.prefix = prefix;
		if (suffix) augmentRequest.suffix = suffix;
		if (model) augmentRequest.model = model;
		if (mode !== undefined) augmentRequest.mode = mode;
		else augmentRequest.mode = 'CHAT'; // Default to CHAT mode

		// Prepare headers for Augment API
		const headers = {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${accessToken}`,
			'User-Agent': 'BrowserPrototype/1.0',
			'x-request-id': requestId,
			'x-request-session-id': sessionId,
			'x-api-version': '2'
		};

		// Create streaming response
		const encoder = new TextEncoder();
		const stream = new ReadableStream({
			async start(controller) {
				const apiUrl = `${tenantUrl.replace(/\/$/, '')}/chat-stream`;
				const abortController = new AbortController();
				const timeoutId = setTimeout(() => abortController.abort(), 120000); // 2 minutes timeout

				try {
					console.log('Sending silent exchange stream request to:', apiUrl);
					console.log('Request body:', JSON.stringify(augmentRequest, null, 2));
					console.log('Headers:', headers);
					const response = await fetch(apiUrl, {
						method: 'POST',
						headers,
						body: JSON.stringify(augmentRequest),
						signal: abortController.signal
					});
					console.log('Response status:', response.status);

					clearTimeout(timeoutId);

					if (!response.ok) {
						let errorData: any = {};
						const contentType = response.headers.get('content-type');
						const isJson = contentType?.includes('application/json');

						if (isJson) {
							try {
								errorData = await response.json();
							} catch {
								// Ignore JSON parse errors for error responses
							}
						} else {
							errorData.error = await response.text();
						}

						const errorChunk = {
							error: errorData.error || `HTTP ${response.status}: ${response.statusText}`,
							details: errorData.details || errorData.message
						};

						controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
						controller.close();
						return;
					}

					// Stream the response
					const reader = response.body?.getReader();
					const decoder = new TextDecoder();

					if (!reader) {
						const errorChunk = { error: 'No response body from Augment API' };
						controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
						controller.close();
						return;
					}

					let buffer = '';

					while (true) {
						const { done, value } = await reader.read();

						if (done) {
							// Process any remaining content in buffer before closing
							if (buffer.trim()) {
								try {
									const parsed = JSON.parse(buffer);
									if (parsed.text) {
										const chunk = {
											type: 'content_delta',
											text: parsed.text
										};
										controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunk)}\n\n`));
									}
								} catch (e) {
									console.error('Error parsing final buffer content:', e);
								}
							}
							controller.enqueue(encoder.encode('data: {"type": "done"}\n\n'));
							controller.close();
							break;
						}

						buffer += decoder.decode(value, { stream: true });
						const lines = buffer.split('\n');
						buffer = lines.pop() || ''; // Keep incomplete line in buffer

						for (const line of lines) {
							if (line.trim()) {
								try {
									const parsed = JSON.parse(line);
									if (parsed.text) {
										// Transform to expected streaming format
										const chunk = {
											type: 'content_delta',
											text: parsed.text
										};
										controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunk)}\n\n`));
									}
								} catch (e) {
									console.error('Error parsing streaming data:', e, 'Line:', line);
								}
							}
						}
					}
				} catch (fetchError) {
					clearTimeout(timeoutId);

					let errorMessage = 'Unknown error';
					if (fetchError instanceof Error) {
						if (fetchError.name === 'AbortError') {
							errorMessage = 'Request timed out after 2 minutes';
						} else {
							errorMessage = fetchError.message;
						}
					}

					const errorChunk = { error: errorMessage };
					controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
					controller.close();
				}
			}
		});

		return new Response(stream, {
			headers: {
				'Content-Type': 'text/event-stream',
				'Cache-Control': 'no-cache',
				Connection: 'keep-alive'
			}
		});
	} catch (error) {
		console.error('Silent exchange stream error:', error);
		return json(
			{
				error:
					error instanceof Error
						? error.message
						: 'Failed to process silent exchange stream request'
			},
			{ status: 500 }
		);
	}
};
