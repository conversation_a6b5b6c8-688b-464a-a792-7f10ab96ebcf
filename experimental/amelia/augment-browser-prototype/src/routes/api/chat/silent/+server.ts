import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { augmentSession } from '$lib/server/session';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const body = await request.json();
		const { message, selectedCode, path, lang, prefix, suffix, model, mode } = body;

		if (!message || typeof message !== 'string') {
			return json({ error: 'Message is required and must be a string' }, { status: 400 });
		}

		// Get session data from secure cookies
		const sessionData = augmentSession.get(request);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Generate required headers
		const requestId = crypto.randomUUID();
		const sessionId = crypto.randomUUID();

		// Transform request to Augment API format
		const augmentRequest = {
			message,
			silent: true, // Keep it silent with the correct pattern
			chat_history: [],
			blobs: {
				checkpoint_id: null,
				added_blobs: [],
				deleted_blobs: []
			},
			sequence_id: 1,
			disable_auto_external_sources: false,
			user_guided_blobs: [],
			external_source_ids: [],
			model: 'gemini2-5-flash-200k-v7-c4-p2-agent'
		};

		// Add optional fields if provided
		if (selectedCode) augmentRequest.selected_code = selectedCode;
		if (path) augmentRequest.path = path;
		if (lang) augmentRequest.lang = lang;
		if (prefix) augmentRequest.prefix = prefix;
		if (suffix) augmentRequest.suffix = suffix;
		if (model) augmentRequest.model = model;
		if (mode !== undefined) augmentRequest.mode = mode;
		else augmentRequest.mode = 'CHAT'; // Default to CHAT mode

		// Add additional fields that might be required
		augmentRequest.nodes = [];
		augmentRequest.tool_definitions = [];

		// Prepare headers for Augment API
		const headers = {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${accessToken}`,
			'User-Agent': 'BrowserPrototype/1.0',
			'x-request-id': requestId,
			'x-request-session-id': sessionId,
			'x-api-version': '2'
		};

		// Make request to Augment API using streaming endpoint (more reliable)
		const apiUrl = `${tenantUrl.replace(/\/$/, '')}/chat-stream`;
		const controller = new AbortController();
		const timeoutId = setTimeout(() => controller.abort(), 300000); // 5 minutes timeout

		try {
			const response = await fetch(apiUrl, {
				method: 'POST',
				headers,
				body: JSON.stringify(augmentRequest),
				signal: controller.signal
			});

			clearTimeout(timeoutId);

			if (!response.ok) {
				let errorData: any = {};
				const contentType = response.headers.get('content-type');
				const isJson = contentType?.includes('application/json');

				if (isJson) {
					try {
						errorData = await response.json();
					} catch {
						// Ignore JSON parse errors for error responses
					}
				} else {
					errorData.error = await response.text();
				}

				return json(
					{
						error: errorData.error || `HTTP ${response.status}: ${response.statusText}`,
						details: errorData.details || errorData.message,
						status: response.status
					},
					{ status: response.status }
				);
			}

			// Handle streaming response and collect all chunks
			const reader = response.body?.getReader();
			const decoder = new TextDecoder();
			let fullText = '';
			let buffer = '';
			let lastCompleteResponse: any = null;

			if (!reader) {
				return json({ error: 'No response body from Augment API' }, { status: 500 });
			}

			while (true) {
				const { done, value } = await reader.read();

				if (done) {
					break;
				}

				buffer += decoder.decode(value, { stream: true });
				const lines = buffer.split('\n');
				buffer = lines.pop() || ''; // Keep incomplete line in buffer

				for (const line of lines) {
					if (line.trim()) {
						try {
							const parsed = JSON.parse(line);
							if (parsed.text) {
								fullText += parsed.text;
							}
							// Keep track of the last complete response for final parsing
							if (parsed.nodes && parsed.nodes.length > 0) {
								lastCompleteResponse = parsed;
							}
						} catch (e) {
							console.error('Error parsing streaming data:', e, 'Line:', line);
						}
					}
				}
			}

			// Try to get the complete response from nodes first, then fallback to fullText
			let responseText = fullText;
			if (lastCompleteResponse?.nodes) {
				// Combine all node content
				responseText = lastCompleteResponse.nodes.map((node: any) => node.content || '').join('');
			}

			console.log('Final response text:', responseText);

			// Extract the enhanced prompt from the response
			const tagPattern = /<augment-enhanced-prompt>([\s\S]*?)<\/augment-enhanced-prompt>/;
			const match = responseText.match(tagPattern);

			if (match && match[1]) {
				const enhancedPrompt = match[1].trim();
				return json({ text: enhancedPrompt });
			} else {
				// Return the full response for debugging
				return json({ text: responseText || fullText || message });
			}
		} catch (fetchError) {
			clearTimeout(timeoutId);

			if (fetchError instanceof Error) {
				if (fetchError.name === 'AbortError') {
					return json(
						{
							error: 'Request timeout',
							details: 'Request timed out after 5 minutes'
						},
						{ status: 408 }
					);
				}

				return json(
					{
						error: 'Network error',
						details: fetchError.message
					},
					{ status: 500 }
				);
			}

			throw fetchError;
		}
	} catch (error) {
		console.error('Silent exchange error:', error);
		return json(
			{
				error: error instanceof Error ? error.message : 'Failed to process silent exchange request'
			},
			{ status: 500 }
		);
	}
};
