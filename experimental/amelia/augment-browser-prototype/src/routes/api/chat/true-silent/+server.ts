import { json } from '@sveltejs/kit';
import type { <PERSON>questH<PERSON><PERSON> } from './$types';
import { augmentSession } from '$lib/server/session';

// This endpoint uses the approved prompt enhancement pattern for true silent exchanges
export const POST: RequestHandler = async ({ request }) => {
	try {
		const body = await request.json();
		const { message } = body;

		if (!message || typeof message !== 'string') {
			return json({ error: 'Message is required and must be a string' }, { status: 400 });
		}

		// Get session data from secure cookies
		const sessionData = augmentSession.get(request);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Generate required headers
		const requestId = crypto.randomUUID();
		const sessionId = crypto.randomUUID();

		// Use the approved prompt enhancement pattern for silent exchanges
		const enhancementPrompt =
			"Here is an instruction that I'd like to give you, but it needs to be improved. " +
			"Rewrite and enhance this instruction to make it clearer, more specific, " +
			"less ambiguous, and correct any mistakes. " +
			"Do not use any tools: reply immediately with your answer, even if you're not sure. " +
			"Consider the context of our conversation history when enhancing the prompt. " +
			"If there is code in triple backticks (```) consider whether it is a code sample and should remain unchanged." +
			"Reply with the following format:\n\n" +
			"### BEGIN RESPONSE ###\n" +
			"Here is an enhanced version of the original instruction that is more specific and clear:\n" +
			"<augment-enhanced-prompt>enhanced prompt goes here</augment-enhanced-prompt>\n\n" +
			"### END RESPONSE ###\n\n" +
			"Here is my original instruction:\n\n" +
			message;

		// Transform request to Augment API format using approved pattern
		const augmentRequest = {
			message: enhancementPrompt,
			silent: true, // This should work with the approved pattern
			chat_history: [],
			blobs: {
				checkpoint_id: null,
				added_blobs: [],
				deleted_blobs: []
			},
			sequence_id: 1,
			disable_auto_external_sources: false,
			user_guided_blobs: [],
			external_source_ids: [],
			mode: "CHAT"
		};

		// Prepare headers for Augment API
		const headers = {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${accessToken}`,
			'User-Agent': 'BrowserPrototype/1.0',
			'x-request-id': requestId,
			'x-request-session-id': sessionId,
			'x-api-version': '2'
		};

		// Create streaming response
		const encoder = new TextEncoder();
		const stream = new ReadableStream({
			async start(controller) {
				const apiUrl = `${tenantUrl.replace(/\/$/, '')}/chat-stream`;
				const abortController = new AbortController();
				const timeoutId = setTimeout(() => abortController.abort(), 120000); // 2 minutes timeout

				try {
					console.log('Sending true silent exchange request to:', apiUrl);
					const response = await fetch(apiUrl, {
						method: 'POST',
						headers,
						body: JSON.stringify(augmentRequest),
						signal: abortController.signal
					});

					clearTimeout(timeoutId);

					if (!response.ok) {
						let errorData: any = {};
						const contentType = response.headers.get('content-type');
						const isJson = contentType?.includes('application/json');

						if (isJson) {
							try {
								errorData = await response.json();
							} catch {
								// Ignore JSON parse errors for error responses
							}
						} else {
							errorData.error = await response.text();
						}

						const errorChunk = {
							error: errorData.error || `HTTP ${response.status}: ${response.statusText}`,
							details: errorData.details || errorData.message
						};

						controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
						controller.close();
						return;
					}

					// Stream the response
					const reader = response.body?.getReader();
					const decoder = new TextDecoder();

					if (!reader) {
						const errorChunk = { error: 'No response body from Augment API' };
						controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
						controller.close();
						return;
					}

					let buffer = '';
					let fullResponse = '';

					while (true) {
						const { done, value } = await reader.read();

						if (done) {
							// Extract the enhanced prompt from the full response
							const tagPattern = /<augment-enhanced-prompt>([\s\S]*?)<\/augment-enhanced-prompt>/;
							const match = fullResponse.match(tagPattern);

							if (match && match[1]) {
								const enhancedPrompt = match[1].trim();
								const finalChunk = { text: enhancedPrompt };
								controller.enqueue(encoder.encode(`data: ${JSON.stringify(finalChunk)}\n\n`));
							} else {
								// Fallback to original message if parsing fails
								const fallbackChunk = { text: message };
								controller.enqueue(encoder.encode(`data: ${JSON.stringify(fallbackChunk)}\n\n`));
							}

							controller.enqueue(encoder.encode('data: [DONE]\n\n'));
							controller.close();
							break;
						}

						buffer += decoder.decode(value, { stream: true });
						const lines = buffer.split('\n');
						buffer = lines.pop() || ''; // Keep incomplete line in buffer

						for (const line of lines) {
							if (line.startsWith('data: ')) {
								const data = line.slice(6);

								if (data === '[DONE]') {
									continue; // Handle at the end
								}

								try {
									const parsed = JSON.parse(data);
									if (parsed.text) {
										fullResponse += parsed.text;
									}
								} catch (e) {
									console.error('Error parsing Augment API SSE data:', e);
								}
							}
						}
					}
				} catch (fetchError) {
					clearTimeout(timeoutId);

					let errorMessage = 'Unknown error';
					if (fetchError instanceof Error) {
						if (fetchError.name === 'AbortError') {
							errorMessage = 'Request timed out after 2 minutes';
						} else {
							errorMessage = fetchError.message;
						}
					}

					const errorChunk = { error: errorMessage };
					controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
					controller.close();
				}
			}
		});

		return new Response(stream, {
			headers: {
				'Content-Type': 'text/event-stream',
				'Cache-Control': 'no-cache',
				Connection: 'keep-alive'
			}
		});
	} catch (error) {
		console.error('True silent exchange error:', error);
		return json(
			{
				error:
					error instanceof Error
						? error.message
						: 'Failed to process true silent exchange request'
			},
			{ status: 500 }
		);
	}
};
