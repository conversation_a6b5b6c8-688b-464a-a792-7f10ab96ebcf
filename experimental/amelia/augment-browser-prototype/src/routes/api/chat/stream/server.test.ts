import { describe, it, expect, vi, beforeEach, afterEach } from 'vitest';
import { POST } from './+server';
import { augmentSession } from '$lib/server/session';

// Mock the session module
vi.mock('$lib/server/session', () => ({
	augmentSession: {
		get: vi.fn()
	}
}));

// Mock fetch globally
const mockFetch = vi.fn();
global.fetch = mockFetch;

// Mock crypto.randomUUID
Object.defineProperty(global, 'crypto', {
	value: {
		randomUUID: vi.fn(() => 'mock-uuid-123')
	}
});

describe('Chat Stream API Endpoint', () => {
	beforeEach(() => {
		vi.clearAllMocks();
	});

	afterEach(() => {
		vi.restoreAllMocks();
	});

	describe('Request Validation', () => {
		it('should return 400 if messages array is missing', async () => {
			const request = new Request('http://localhost/api/chat/stream', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({})
			});

			const response = await POST({ request } as any);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toBe('Messages array is required');
		});

		it('should return 400 if messages is not an array', async () => {
			const request = new Request('http://localhost/api/chat/stream', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ messages: 'not an array' })
			});

			const response = await POST({ request } as any);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toBe('Messages array is required');
		});

		it('should return 401 if session data is missing', async () => {
			vi.mocked(augmentSession.get).mockReturnValue(null);

			const request = new Request('http://localhost/api/chat/stream', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ messages: [{ role: 'user', content: 'test' }] })
			});

			const response = await POST({ request } as any);
			const data = await response.json();

			expect(response.status).toBe(401);
			expect(data.error).toBe('Missing session data - please log in again');
		});

		it('should return 400 if no user message is found', async () => {
			vi.mocked(augmentSession.get).mockReturnValue({
				tenantUrl: 'https://test.augmentcode.com',
				accessToken: 'test-token'
			});

			const request = new Request('http://localhost/api/chat/stream', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({ messages: [{ role: 'assistant', content: 'test' }] })
			});

			const response = await POST({ request } as any);
			const data = await response.json();

			expect(response.status).toBe(400);
			expect(data.error).toBe('No user message found');
		});
	});

	describe('Request Structure Generation', () => {
		it('should generate correct augmentRequest structure without undefined values', async () => {
			vi.mocked(augmentSession.get).mockReturnValue({
				tenantUrl: 'https://test.augmentcode.com',
				accessToken: 'test-token'
			});

			// Mock a successful streaming response
			const mockResponse = {
				ok: true,
				body: {
					getReader: () => ({
						read: vi.fn()
							.mockResolvedValueOnce({
								done: false,
								value: new TextEncoder().encode('{"text": "Hello"}')
							})
							.mockResolvedValueOnce({
								done: true,
								value: undefined
							})
					})
				}
			};
			mockFetch.mockResolvedValue(mockResponse);

			const request = new Request('http://localhost/api/chat/stream', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					messages: [{ role: 'user', content: 'Help me set up a trigger for monitoring PRs' }]
				})
			});

			// Call the endpoint
			await POST({ request } as any);

			// Verify fetch was called with correct structure
			expect(mockFetch).toHaveBeenCalledWith(
				'https://test.augmentcode.com/chat-stream',
				expect.objectContaining({
					method: 'POST',
					headers: expect.objectContaining({
						'Content-Type': 'application/json',
						'Authorization': 'Bearer test-token',
						'User-Agent': 'BrowserPrototype/1.0',
						'x-request-id': 'mock-uuid-123',
						'x-request-session-id': 'mock-uuid-123',
						'x-api-version': '2'
					}),
					body: expect.any(String)
				})
			);

			// Parse the request body to verify structure
			const requestBody = JSON.parse(mockFetch.mock.calls[0][1].body);

			// Verify the structure matches our fixed format
			expect(requestBody).toEqual({
				model: null,
				path: null,
				prefix: null,
				selected_code: null,
				suffix: null,
				message: 'Help me set up a trigger for monitoring PRs',
				chat_history: [],
				lang: null,
				blobs: {
					checkpoint_id: null,
					added_blobs: [],
					deleted_blobs: []
				},
				user_guided_blobs: [],
				context_code_exchange_request_id: null,
				vcs_change: {
					working_directory_changes: []
				},
				recency_info_recent_changes: [],
				external_source_ids: [],
				disable_auto_external_sources: false,
				user_guidelines: null,
				workspace_guidelines: null,
				feature_detection_flags: {
					support_raw_output: true
				},
				tool_definitions: [],
				nodes: [],
				mode: "CHAT",
				agent_memories: null,
				persona_type: null,
				rules: [],
				silent: true,
				sequence_id: 1
			});
		});

		it('should not contain any undefined values in the request', async () => {
			vi.mocked(augmentSession.get).mockReturnValue({
				tenantUrl: 'https://test.augmentcode.com',
				accessToken: 'test-token'
			});

			const mockResponse = {
				ok: true,
				body: {
					getReader: () => ({
						read: vi.fn().mockResolvedValue({ done: true })
					})
				}
			};
			mockFetch.mockResolvedValue(mockResponse);

			const request = new Request('http://localhost/api/chat/stream', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					messages: [{ role: 'user', content: 'test message' }]
				})
			});

			await POST({ request } as any);

			const requestBody = JSON.parse(mockFetch.mock.calls[0][1].body);
			const requestString = JSON.stringify(requestBody);

			// Verify no undefined values are present in the serialized JSON
			expect(requestString).not.toContain('undefined');

			// Verify all null values are properly serialized
			expect(requestBody.model).toBe(null);
			expect(requestBody.path).toBe(null);
			expect(requestBody.blobs.checkpoint_id).toBe(null);
			expect(requestBody.disable_auto_external_sources).toBe(false);
			expect(requestBody.sequence_id).toBe(1);
		});
	});

	describe('Error Handling', () => {
		it('should handle fetch errors gracefully', async () => {
			vi.mocked(augmentSession.get).mockReturnValue({
				tenantUrl: 'https://test.augmentcode.com',
				accessToken: 'test-token'
			});

			mockFetch.mockRejectedValue(new Error('Network error'));

			const request = new Request('http://localhost/api/chat/stream', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					messages: [{ role: 'user', content: 'test' }]
				})
			});

			const response = await POST({ request } as any);

			expect(response.status).toBe(200); // Streaming response
			expect(response.headers.get('Content-Type')).toBe('text/event-stream');
		});

		it('should handle JSON parsing errors in request body', async () => {
			const request = new Request('http://localhost/api/chat/stream', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: 'invalid json'
			});

			const response = await POST({ request } as any);
			const data = await response.json();

			expect(response.status).toBe(500);
			expect(data.error).toContain('Unexpected token');
		});
	});

	describe('Regression Test for JSON Deserialization Error', () => {
		it('should not cause "invalid type: integer 0, expected a borrowed string" error', async () => {
			vi.mocked(augmentSession.get).mockReturnValue({
				tenantUrl: 'https://test.augmentcode.com',
				accessToken: 'test-token'
			});

			const mockResponse = {
				ok: true,
				body: {
					getReader: () => ({
						read: vi.fn().mockResolvedValue({ done: true })
					})
				}
			};
			mockFetch.mockResolvedValue(mockResponse);

			const request = new Request('http://localhost/api/chat/stream', {
				method: 'POST',
				headers: { 'Content-Type': 'application/json' },
				body: JSON.stringify({
					messages: [{ role: 'user', content: 'Help me set up a trigger for...' }]
				})
			});

			// This should not throw or cause deserialization errors
			const response = await POST({ request } as any);

			expect(response.status).toBe(200);
			expect(mockFetch).toHaveBeenCalled();

			// Verify the request body structure is correct
			const requestBody = JSON.parse(mockFetch.mock.calls[0][1].body);

			// All string fields should be null or valid strings, never undefined
			const stringFields = ['model', 'path', 'prefix', 'selected_code', 'suffix', 'lang',
								 'context_code_exchange_request_id', 'user_guidelines', 'workspace_guidelines',
								 'agent_memories', 'persona_type'];

			stringFields.forEach(field => {
				expect(requestBody[field]).toBeNull();
			});

			// Boolean fields should be proper booleans
			expect(typeof requestBody.disable_auto_external_sources).toBe('boolean');
			expect(typeof requestBody.silent).toBe('boolean');
			expect(typeof requestBody.feature_detection_flags.support_raw_output).toBe('boolean');

			// Numeric fields should be proper numbers
			expect(typeof requestBody.sequence_id).toBe('number');
			expect(requestBody.sequence_id).toBe(1);
		});
	});
});
