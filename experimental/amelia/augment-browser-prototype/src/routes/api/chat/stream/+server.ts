import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { augmentSession } from '$lib/server/session';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const body = await request.json();
		const { messages } = body;

		if (!messages || !Array.isArray(messages)) {
			return json({ error: 'Messages array is required' }, { status: 400 });
		}

		// Get session data from secure cookies
		const sessionData = augmentSession.get(request);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Convert messages to a single prompt for silent exchange
		// Take the last user message as the main prompt
		const lastUserMessage = messages.filter((m) => m.role === 'user').pop();
		if (!lastUserMessage) {
			return json({ error: 'No user message found' }, { status: 400 });
		}

		// Generate required headers
		const requestId = crypto.randomUUID();
		const sessionId = crypto.randomUUID();

		// Transform request to Augment API format - following the exact structure from agent_loop.ts
		// but fixing undefined values that cause JSON deserialization errors
		const augmentRequest = {
			model: null,
			path: null,
			prefix: null,
			selected_code: null,
			suffix: null,
			message: lastUserMessage.content,
			chat_history: [],
			lang: null,
			blobs: {
				checkpoint_id: null,
				added_blobs: [],
				deleted_blobs: []
			},
			user_guided_blobs: [],
			context_code_exchange_request_id: null,
			vcs_change: {
				working_directory_changes: []
			},
			recency_info_recent_changes: [],
			external_source_ids: [],
			disable_auto_external_sources: false,
			user_guidelines: null,
			workspace_guidelines: null,
			feature_detection_flags: {
				support_raw_output: true
			},
			tool_definitions: [],
			nodes: [],
			mode: 'CHAT',
			agent_memories: null,
			persona_type: null,
			rules: [],
			silent: true,
			sequence_id: 1 // Add missing sequence_id
		};

		// Prepare headers for Augment API
		const headers = {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${accessToken}`,
			'User-Agent': 'BrowserPrototype/1.0',
			'x-request-id': requestId,
			'x-request-session-id': sessionId,
			'x-api-version': '2'
		};

		// Create streaming response
		const encoder = new TextEncoder();
		const stream = new ReadableStream({
			async start(controller) {
				const apiUrl = `${tenantUrl.replace(/\/$/, '')}/chat-stream`;
				const abortController = new AbortController();
				const timeoutId = setTimeout(() => abortController.abort(), 120000); // 2 minutes timeout

				try {
					const response = await fetch(apiUrl, {
						method: 'POST',
						headers,
						body: JSON.stringify(augmentRequest),
						signal: abortController.signal
					});

					clearTimeout(timeoutId);

					if (!response.ok) {
						let errorData: { error?: string; details?: string; message?: string } = {};
						const contentType = response.headers.get('content-type');
						const isJson = contentType?.includes('application/json');

						if (isJson) {
							try {
								errorData = await response.json();
							} catch {
								// Ignore JSON parse errors for error responses
							}
						} else {
							errorData.error = await response.text();
						}

						const errorChunk = {
							choices: [
								{
									delta: {
										content: `Error: ${errorData.error || `HTTP ${response.status}: ${response.statusText}`}`
									}
								}
							]
						};

						controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
						controller.enqueue(encoder.encode('data: [DONE]\n\n'));
						controller.close();
						return;
					}

					// Stream the response
					const reader = response.body?.getReader();
					const decoder = new TextDecoder();

					if (!reader) {
						const errorChunk = {
							choices: [
								{
									delta: {
										content: 'Error: No response body from Augment API'
									}
								}
							]
						};
						controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
						controller.enqueue(encoder.encode('data: [DONE]\n\n'));
						controller.close();
						return;
					}

					let buffer = '';

					while (true) {
						const { done, value } = await reader.read();

						if (done) {
							// Process any remaining content in buffer before closing
							if (buffer.trim()) {
								try {
									const parsed = JSON.parse(buffer);
									if (parsed.text) {
										const chunk = {
											choices: [
												{
													delta: {
														content: parsed.text
													}
												}
											]
										};
										controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunk)}\n\n`));
									}
								} catch (e) {
									console.error('Error parsing final buffer content:', e);
								}
							}
							controller.enqueue(encoder.encode('data: [DONE]\n\n'));
							controller.close();
							break;
						}

						buffer += decoder.decode(value, { stream: true });
						const lines = buffer.split('\n');
						buffer = lines.pop() || ''; // Keep incomplete line in buffer

						for (const line of lines) {
							if (!line.trim()) continue;

							// Handle both SSE format and raw JSON format (Augment API returns raw JSON)
							let jsonData = line;
							if (line.startsWith('data: ')) {
								jsonData = line.slice(6);

								if (jsonData === '[DONE]') {
									controller.enqueue(encoder.encode('data: [DONE]\n\n'));
									controller.close();
									return;
								}
							}

							try {
								const parsed = JSON.parse(jsonData);
								if (parsed.text) {
									// Transform to expected streaming format
									const chunk = {
										choices: [
											{
												delta: {
													content: parsed.text
												}
											}
										]
									};
									controller.enqueue(encoder.encode(`data: ${JSON.stringify(chunk)}\n\n`));
								}

								// Check for stop_reason to end stream
								if (parsed.stop_reason !== null && parsed.stop_reason !== undefined) {
									controller.enqueue(encoder.encode('data: [DONE]\n\n'));
									controller.close();
									return;
								}
							} catch (e) {
								console.error('Error parsing Augment API JSON:', e);
							}
						}
					}
				} catch (fetchError) {
					clearTimeout(timeoutId);

					let errorMessage = 'Unknown error';
					if (fetchError instanceof Error) {
						if (fetchError.name === 'AbortError') {
							errorMessage = 'Request timed out after 2 minutes';
						} else {
							errorMessage = fetchError.message;
						}
					}

					const errorChunk = {
						choices: [
							{
								delta: {
									content: `Error: ${errorMessage}`
								}
							}
						]
					};
					controller.enqueue(encoder.encode(`data: ${JSON.stringify(errorChunk)}\n\n`));
					controller.enqueue(encoder.encode('data: [DONE]\n\n'));
					controller.close();
				}
			}
		});

		return new Response(stream, {
			headers: {
				'Content-Type': 'text/event-stream',
				'Cache-Control': 'no-cache',
				Connection: 'keep-alive'
			}
		});
	} catch (error) {
		console.error('Chat stream error:', error);
		return json(
			{ error: error instanceof Error ? error.message : 'Failed to process chat request' },
			{ status: 500 }
		);
	}
};
