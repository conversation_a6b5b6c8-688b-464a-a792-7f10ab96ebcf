import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { augmentSession } from '$lib/server/session';

export const POST: RequestHandler = async ({ request }) => {
	try {
		const body = await request.json();
		const { messages } = body;

		if (!messages || !Array.isArray(messages)) {
			return json({ error: 'Messages array is required' }, { status: 400 });
		}

		// Get session data from secure cookies
		const sessionData = augmentSession.get(request);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Convert messages to a single prompt for silent exchange
		// Take the last user message as the main prompt
		const lastUserMessage = messages.filter((m) => m.role === 'user').pop();
		if (!lastUserMessage) {
			return json({ error: 'No user message found' }, { status: 400 });
		}

		// Generate required headers
		const requestId = crypto.randomUUID();
		const sessionId = crypto.randomUUID();

		// Transform request to Augment API format for silent exchange
		const augmentRequest = {
			message: lastUserMessage.content,
			silent: true,
			chat_history: [],
			blobs: {
				checkpoint_id: null,
				added_blobs: [],
				deleted_blobs: []
			},
			sequence_id: 1,
			disable_auto_external_sources: false,
			user_guided_blobs: [],
			external_source_ids: [],
			mode: 0 // CHAT mode
		};

		// Prepare headers for Augment API
		const headers = {
			'Content-Type': 'application/json',
			Authorization: `Bearer ${accessToken}`,
			'User-Agent': 'BrowserPrototype/1.0',
			'x-request-id': requestId,
			'x-request-session-id': sessionId,
			'x-api-version': '2'
		};

		try {
			const apiUrl = `${tenantUrl.replace(/\/$/, '')}/chat`;
			const response = await fetch(apiUrl, {
				method: 'POST',
				headers,
				body: JSON.stringify(augmentRequest),
				signal: AbortSignal.timeout(300000) // 5 minutes timeout
			});

			if (!response.ok) {
				let errorData: { error?: string; details?: string; message?: string } = {};
				const contentType = response.headers.get('content-type');
				const isJson = contentType?.includes('application/json');

				if (isJson) {
					try {
						errorData = await response.json();
					} catch {
						// Ignore JSON parse errors for error responses
					}
				} else {
					errorData.error = await response.text();
				}

				return json(
					{
						error: errorData.error || `HTTP ${response.status}: ${response.statusText}`,
						details: errorData.details || errorData.message
					},
					{ status: response.status }
				);
			}

			const result = await response.json();

			// Transform Augment API response to OpenAI-compatible format
			const openAIResponse = {
				id: 'augment-' + Date.now(),
				object: 'chat.completion',
				created: Math.floor(Date.now() / 1000),
				model: 'augment-api',
				choices: [
					{
						index: 0,
						message: {
							role: 'assistant',
							content: result.text || 'No response text received'
						},
						finish_reason: result.stop_reason || 'stop'
					}
				],
				usage: {
					prompt_tokens: 0, // Augment API doesn't provide token counts
					completion_tokens: 0,
					total_tokens: 0
				}
			};

			return json(openAIResponse);
		} catch (fetchError) {
			let errorMessage = 'Unknown error';
			if (fetchError instanceof Error) {
				if (fetchError.name === 'TimeoutError') {
					errorMessage = 'Request timed out after 5 minutes';
				} else {
					errorMessage = fetchError.message;
				}
			}

			return json({ error: errorMessage }, { status: 500 });
		}
	} catch (error) {
		console.error('Chat completion error:', error);
		return json(
			{ error: error instanceof Error ? error.message : 'Failed to process chat request' },
			{ status: 500 }
		);
	}
};
