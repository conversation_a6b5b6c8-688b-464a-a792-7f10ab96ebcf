import { json } from '@sveltejs/kit';
import type { RequestHand<PERSON> } from './$types';
import { getSessionData } from '$lib/utils/session-utils';

export const POST: RequestHandler = async ({ request, cookies }) => {
	try {
		// Get session data from secure cookies
		const sessionData = await getSessionData(cookies);
		if (!sessionData?.tenantUrl || !sessionData?.accessToken) {
			return json({ error: 'Missing session data - please log in again' }, { status: 401 });
		}

		const { tenantUrl, accessToken } = sessionData;

		// Make direct API call to tenant backend
		const cleanTenantUrl = tenantUrl.endsWith('/') ? tenantUrl.slice(0, -1) : tenantUrl;
		const backendEndpoint = `${cleanTenantUrl}/github/list-repos`;

		const apiBody = await request.json();

		console.log('Making direct request to GitHub list-repos:', backendEndpoint);
		console.log('Request body:', JSON.stringify(apiBody, null, 2));

		const response = await fetch(backendEndpoint, {
			method: 'POST',
			headers: {
				'Content-Type': 'application/json',
				Authorization: `Bearer ${accessToken}`,
				Accept: 'application/json',
				'User-Agent': 'Augment-Web-Client/1.0'
			},
			body: JSON.stringify(apiBody)
		});

		console.log('GitHub list-repos response status:', response.status);

		if (!response.ok) {
			const errorText = await response.text();
			console.error('Backend error fetching GitHub repos:', {
				status: response.status,
				statusText: response.statusText,
				body: errorText
			});
			return json(
				{ error: `Backend error: ${response.statusText}`, details: errorText },
				{ status: response.status }
			);
		}

		const data = await response.json();

		return json(data);
	} catch (error) {
		console.error('Error fetching GitHub repositories:', error);
		return json({ error: 'Internal server error' }, { status: 500 });
	}
};
