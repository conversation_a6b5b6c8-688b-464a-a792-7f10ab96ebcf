<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import {
		markSiteAuthenticated,
		checkSiteAuthentication,
		siteAuthStore
	} from '$lib/stores/global-state.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import Logo from '$lib/components/ui/visualization/Logo.svelte';

	let password = $state('');
	let error = $state('');
	let isLoading = $state(false);

	const redirectTo = $derived(page.url.searchParams.get('redirectTo') || '/');

	const onMount = async () => {
		// check the authentication status on mount
		await checkSiteAuthentication();
		// if already authenticated, redirect
		if ($siteAuthStore.isAuthenticated) {
			await goto(redirectTo);
		}
	};
	onMount();

	async function handleSubmit(e: Event) {
		e.preventDefault();
		error = '';
		isLoading = true;

		try {
			const response = await fetch('/api/site-auth/login', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				},
				body: JSON.stringify({ password }),
				credentials: 'include'
			});

			const data = await response.json();

			if (response.ok && data.success) {
				markSiteAuthenticated();
				await goto(redirectTo);
			} else {
				error = data.error || 'Invalid password';
			}
		} catch (err) {
			error = 'An error occurred. Please try again.';
		} finally {
			isLoading = false;
		}
	}
</script>

<div class="flex min-h-screen items-center justify-center bg-slate-50 dark:bg-slate-900">
	<div class="w-full max-w-md">
		<div class="rounded-lg bg-white p-8 shadow-lg dark:bg-slate-800">
			<div class="mb-8 flex justify-center">
				<Logo size="lg" />
			</div>

			<h1 class="mb-8 text-center text-2xl font-bold text-slate-900 dark:text-slate-100">
				Site Authentication
			</h1>

			<form onsubmit={handleSubmit} class="space-y-6">
				<div>
					<label
						for="password"
						class="mb-2 block text-sm font-medium text-slate-700 dark:text-slate-300"
					>
						Password
					</label>
					<input
						id="password"
						type="password"
						bind:value={password}
						required
						disabled={isLoading}
						class="w-full rounded-md border border-slate-300 bg-white px-3 py-2 text-slate-900
							   shadow-sm focus:border-blue-500 focus:ring-2 focus:ring-blue-500
							   focus:outline-none disabled:cursor-not-allowed disabled:opacity-50 dark:border-slate-600
							   dark:bg-slate-700 dark:text-slate-100"
						placeholder="Enter site password"
					/>
				</div>

				{#if error}
					<div class="text-center text-sm text-red-600 dark:text-red-400">
						{error}
					</div>
				{/if}

				<Button
					type="submit"
					variant="primary"
					disabled={!password}
					loading={isLoading}
					class="w-full"
				>
					Authenticate
				</Button>
			</form>

			<div class="mt-6 text-center text-sm text-slate-600 dark:text-slate-400">
				This site requires authentication to access.
			</div>
		</div>
	</div>
</div>
