<script lang="ts">
	import '../app.css';
	import { theme } from '$lib/stores/theme';
	import { onMount } from 'svelte';

	import ToastContainer from '$lib/components/ui/feedback/ToastContainer.svelte';
	import ModalManager from '$lib/components/ui/overlays/ModalManager.svelte';
	import PasswordGuard from '$lib/components/auth/PasswordGuard.svelte';
	import AuthGuard from '$lib/components/auth/AuthGuard.svelte';
	import { initializeProviderAuth } from '$lib/stores/provider-auth';
	import { session } from '$lib/stores/auth';
	import { preloadHighlightThemes } from '$lib/utils/highlight-theme';
	import DashboardLayout from '$lib/components/ui/layout/DashboardLayout.svelte';
	import { initializeAgentLimits } from '$lib/stores/agent-limits.svelte';
	import {
		stopAgentListStream,
		stopStreamingAgent,
		activeStreams,
		debugStreamingState,
		forceCleanupAllStreams,
		initializeSession
	} from '$lib/stores/data-operations.svelte';
	import { hasAgentsWithChanges } from '$lib/stores/global-state.svelte';
	import { updateFavicon } from '$lib/utils/favicon-manager';

	// Import Vercel Analytics
	import { dev } from '$app/environment';
	import { inject } from '@vercel/analytics';
	import { track } from '@vercel/analytics';

	let { children } = $props();

	// Initialize theme and provider auth on mount
	onMount(() => {
		// Initialize Vercel Analytics
		inject({ mode: dev ? 'development' : 'production' });

		// Theme store will automatically handle the initialization
		// This just ensures the store is loaded
		$theme; // Access theme to ensure it's initialized

		// Preload highlight.js themes for better performance
		preloadHighlightThemes();

		// Initialize agent limits system
		initializeAgentLimits();

		// Add debug functions to global window for easy access in console
		if (typeof window !== 'undefined') {
			(window as any).debugStreaming = debugStreamingState;
			(window as any).forceCleanupStreams = forceCleanupAllStreams;
			console.log('Debug functions available: debugStreaming(), forceCleanupStreams()');
		}

		// Add cleanup for streaming connections when page unloads
		const handleBeforeUnload = () => {
			console.log('Page unloading - cleaning up streams');
			stopAgentListStream();

			// Stop all individual agent streams
			for (const agentId of activeStreams.keys()) {
				stopStreamingAgent(agentId);
			}
		};

		window.addEventListener('beforeunload', handleBeforeUnload);
		window.addEventListener('pagehide', handleBeforeUnload); // For mobile Safari

		// Cleanup on component destroy as well
		return () => {
			window.removeEventListener('beforeunload', handleBeforeUnload);
			window.removeEventListener('pagehide', handleBeforeUnload);
			handleBeforeUnload();
		};
	});

	// Initialize provider auth and global data when session is available
	$effect(() => {
		if ($session) {
			initializeProviderAuth();

			// Initialize global session data and start agent list streaming
			// This ensures agent list streaming is active on all pages with the sidebar
			initializeSession();

			// Track user login event with user context (optional)
			// This helps identify user patterns without exposing personal data
			track('User Session Started', {
				tenantUrl: $session.tenantUrl,
				scopes: $session.scopes?.length || 0,
				hasExpiry: !!$session.expiresAt
			});
		}
	});

	// Update favicon based on agent changes
	$effect(() => {
		// Only update favicon when we're in the browser
		if (typeof window !== 'undefined') {
			updateFavicon($hasAgentsWithChanges);
		}
	});
</script>

<div class="h-full bg-white dark:bg-slate-900" style="color-scheme: {$theme};">
	<PasswordGuard>
		<AuthGuard>
			<DashboardLayout>
				{@render children()}
			</DashboardLayout>
		</AuthGuard>
	</PasswordGuard>
</div>

<!-- Global toast container -->
<ToastContainer />

<!-- Global modal manager -->
<ModalManager />
