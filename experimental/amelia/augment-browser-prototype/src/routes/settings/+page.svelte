<script lang="ts">
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import ProviderAuthButton from '$lib/components/ui/navigation/ProviderAuthButton.svelte';
	import Switch from '$lib/components/ui/forms/Switch.svelte';
	import { session } from '$lib/stores/auth';
	import { checkAllProviderAuth, PROVIDER_CONFIGS } from '$lib/stores/provider-auth';
	import { debugSettings } from '$lib/stores/debug-settings';
	import { theme, toggleTheme } from '$lib/stores/theme';
	import { goto } from '$app/navigation';
	import { Icon, Sun, Moon, ArrowRightOnRectangle } from 'svelte-hero-icons';
	import { onMount } from 'svelte';

	let isLoading = $state(false);
	let error = $state<string | null>(null);
	let isDeletingTriggers = $state(false);

	// Handle logout
	async function handleLogout() {
		try {
			await goto('/logout');
		} catch (err) {
			console.error('Logout error:', err);
		}
	}

	async function handleRefreshProviders() {
		isLoading = true;
		error = null;
		try {
			await checkAllProviderAuth();
		} catch (err) {
			error = err instanceof Error ? err.message : 'Failed to refresh provider status';
		} finally {
			isLoading = false;
		}
	}

	async function handleDeleteAllTriggers() {
		if (!confirm('Are you sure you want to delete ALL triggers? This action cannot be undone.')) {
			return;
		}

		isDeletingTriggers = true;
		try {
			// First, get all triggers
			const listResponse = await fetch('/api/remote-agent-actions/triggers/list', {
				method: 'POST',
				headers: {
					'Content-Type': 'application/json'
				}
			});

			if (!listResponse.ok) {
				throw new Error(
					`Failed to fetch triggers: ${listResponse.status} ${listResponse.statusText}`
				);
			}

			const response = await listResponse.json();

			// The API returns { triggers: [...] } not a direct array
			const triggers = response.triggers || response;

			if (!Array.isArray(triggers) || triggers.length === 0) {
				alert(`No triggers found to delete. Found ${triggers?.length || 0} triggers.`);
				return;
			}

			// Delete each trigger
			let deletedCount = 0;
			let failedCount = 0;

			for (const trigger of triggers) {
				try {
					// Try different possible ID field names
					const triggerId = trigger.id || trigger.trigger_id || trigger.triggerId;
					if (!triggerId) {
						failedCount++;
						continue;
					}

					const deleteResponse = await fetch('/api/remote-agent-actions/triggers/delete', {
						method: 'POST',
						headers: {
							'Content-Type': 'application/json'
						},
						body: JSON.stringify({ trigger_id: triggerId })
					});

					if (deleteResponse.ok) {
						deletedCount++;
					} else {
						failedCount++;
						console.error(`Failed to delete trigger ${trigger.id}:`, await deleteResponse.text());
					}
				} catch (err) {
					failedCount++;
					console.error(`Error deleting trigger ${trigger.id}:`, err);
				}
			}

			if (failedCount === 0) {
				alert(`Successfully deleted all ${deletedCount} triggers.`);
			} else {
				alert(
					`Deleted ${deletedCount} triggers. Failed to delete ${failedCount} triggers. Check console for details.`
				);
			}
		} catch (err) {
			console.error('Error deleting triggers:', err);
			alert(err instanceof Error ? err.message : 'Failed to delete triggers');
		} finally {
			isDeletingTriggers = false;
		}
	}

	onMount(() => {
		// Check provider auth on mount if we have a session
		if ($session) {
			handleRefreshProviders();
		}
	});
</script>

<svelte:head>
	<title>Settings - Augment Code</title>
</svelte:head>

<!-- Content -->
<div class="mx-auto max-w-4xl px-4 py-12 pt-16 sm:px-6 md:pt-12 lg:px-8">
	{#if !$session}
		<div
			class="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-800 dark:bg-yellow-900/20"
		>
			<p class="text-yellow-800 dark:text-yellow-200">
				You need to be logged in to access settings.
			</p>
		</div>
	{:else}
		<div class="space-y-8">
			<!-- Provider Integrations Section -->
			<div class="rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
				<div class="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
					<div class="flex items-center justify-between">
						<div>
							<h2 class="text-lg font-semibold text-gray-900 dark:text-white">
								Provider Integrations
							</h2>
							<p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
								Connect your external services to enable triggers and automation
							</p>
						</div>
						<Button
							variant="outline"
							size="sm"
							onclick={handleRefreshProviders}
							disabled={isLoading}
						>
							{#if isLoading}
								Refreshing...
							{:else}
								Refresh all
							{/if}
						</Button>
					</div>
					{#if error}
						<div
							class="mt-3 rounded border border-red-200 bg-red-50 p-3 text-sm text-red-800 dark:border-red-800 dark:bg-red-900/20 dark:text-red-200"
						>
							{error}
						</div>
					{/if}
				</div>

				<div class="p-6">
					<div class="space-y-4">
						{#each Object.keys(PROVIDER_CONFIGS) as providerId}
							<ProviderAuthButton {providerId} variant="default" />
						{/each}
					</div>
				</div>
			</div>

			<!-- Account Information Section -->
			<!-- <div class="rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
				<div class="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
					<h2 class="text-lg font-semibold text-gray-900 dark:text-white">Account Information</h2>
					<p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Your Augment account details</p>
				</div>

				<div class="p-6">
					<div class="space-y-4">
						<div>
							<label class="mb-1 block text-sm font-medium text-gray-700 dark:text-gray-300">
								User
							</label>
							<div
								class="rounded border bg-gray-50 px-3 py-2 text-sm text-gray-900 dark:bg-gray-700 dark:text-gray-100"
							>
								Augment User
							</div>
						</div>
					</div>
				</div>
			</div> -->

			<!-- Debug Settings Section -->
			<div class="rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
				<div class="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
					<h2 class="text-lg font-semibold text-gray-900 dark:text-white">Debug Settings</h2>
					<p class="mt-1 text-sm text-gray-600 dark:text-gray-400">
						Development and debugging options
					</p>
				</div>

				<div class="p-6">
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<div class="flex-1">
								<label class="block text-sm font-medium text-gray-900 dark:text-white">
									Exploratory Mode
								</label>
								<p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
									Show Inbox and Explore tabs in navigation
								</p>
							</div>
							<Switch
								checked={$debugSettings.exploratoryMode}
								onchange={debugSettings.setExploratoryMode}
							/>
						</div>

						<!-- Delete All Triggers -->
						<div class="flex items-center justify-between">
							<div class="flex-1">
								<label class="block text-sm font-medium text-gray-900 dark:text-white">
									Delete All Triggers
								</label>
								<p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
									Remove all triggers (useful for cleaning up duplicates)
								</p>
							</div>
							<Button
								variant="destructive"
								size="sm"
								onclick={handleDeleteAllTriggers}
								disabled={isDeletingTriggers}
							>
								{isDeletingTriggers ? 'Deleting...' : 'Delete All'}
							</Button>
						</div>
					</div>
				</div>
			</div>

			<!-- Appearance Settings Section -->
			<div class="rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
				<div class="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
					<h2 class="text-lg font-semibold text-gray-900 dark:text-white">Appearance</h2>
					<p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Customize the look and feel</p>
				</div>

				<div class="p-6">
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<div class="flex-1">
								<label class="block text-sm font-medium text-gray-900 dark:text-white">
									Theme
								</label>
								<p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
									Choose between light and dark mode
								</p>
							</div>
							<button
								onclick={toggleTheme}
								class="flex items-center gap-2 rounded-lg px-3 py-2 text-sm font-medium text-gray-600 transition-all hover:bg-gray-100 hover:text-gray-900 dark:text-gray-400 dark:hover:bg-gray-700 dark:hover:text-gray-200"
							>
								<div class="relative h-5 w-5">
									<!-- Sun icon (visible in dark mode) -->
									<div
										class="absolute inset-0 flex items-center justify-center transition-all duration-300 {$theme ===
										'dark'
											? 'scale-100 rotate-0 opacity-100'
											: 'scale-75 rotate-90 opacity-0'}"
									>
										<Icon src={Sun} class="h-5 w-5" />
									</div>
									<!-- Moon icon (visible in light mode) -->
									<div
										class="absolute inset-0 flex items-center justify-center transition-all duration-300 {$theme ===
										'light'
											? 'scale-100 rotate-0 opacity-100'
											: 'scale-75 -rotate-90 opacity-0'}"
									>
										<Icon src={Moon} class="h-5 w-5" />
									</div>
								</div>
								{$theme === 'dark' ? 'Light Mode' : 'Dark Mode'}
							</button>
						</div>
					</div>
				</div>
			</div>

			<!-- Account Actions Section -->
			<div class="rounded-lg border border-gray-200 bg-white dark:border-gray-700 dark:bg-gray-800">
				<div class="border-b border-gray-200 px-6 py-4 dark:border-gray-700">
					<h2 class="text-lg font-semibold text-gray-900 dark:text-white">Account Actions</h2>
					<p class="mt-1 text-sm text-gray-600 dark:text-gray-400">Manage your account</p>
				</div>

				<div class="p-6">
					<div class="space-y-4">
						<div class="flex items-center justify-between">
							<div class="flex-1">
								<label class="block text-sm font-medium text-gray-900 dark:text-white">
									Sign Out
								</label>
								<p class="mt-1 text-xs text-gray-500 dark:text-gray-400">
									Sign out of your Augment account
								</p>
							</div>
							<Button
								variant="outline"
								size="sm"
								onclick={handleLogout}
								icon={ArrowRightOnRectangle}
							>
								Sign Out
							</Button>
						</div>
					</div>
				</div>
			</div>
		</div>
	{/if}
</div>
