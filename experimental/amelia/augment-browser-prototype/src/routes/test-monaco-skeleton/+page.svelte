<script lang="ts">
	import { onMount } from 'svelte';

	let showSkeleton = true;
	let showLoaded = false;

	onMount(() => {
		// Show skeleton for 3 seconds, then show loaded state
		setTimeout(() => {
			showSkeleton = false;
			showLoaded = true;
		}, 3000);
	});
</script>

<div class="p-8">
	<h1 class="mb-4 text-2xl font-bold">Monaco Editor Skeleton Test</h1>
	<p class="mb-6 text-gray-600">This page demonstrates the new Monaco Editor skeleton loader.</p>

	<div class="max-w-4xl space-y-8">
		<div>
			<h2 class="mb-2 text-lg font-semibold">Monaco Editor Skeleton (Always Visible)</h2>
			<div class="overflow-hidden rounded-lg border">
				<!-- Force skeleton to show by creating a mock loading state -->
				<div class="monaco-editor-skeleton">
					<!-- Editor header bar -->
					<div class="editor-header">
						<div class="header-tabs">
							<div class="tab active">
								<div class="tab-icon"></div>
								<div class="tab-text"></div>
							</div>
							<div class="tab">
								<div class="tab-icon"></div>
								<div class="tab-text"></div>
							</div>
						</div>
					</div>

					<!-- Editor content area -->
					<div class="editor-content">
						<!-- Line numbers column -->
						<div class="line-numbers">
							{#each Array(12) as _, i}
								<div class="line-number">{i + 1}</div>
							{/each}
						</div>

						<!-- Code content -->
						<div class="code-content">
							{#each Array(12) as _, i}
								{@const lineWidth =
									Math.random() > 0.3 ? (Math.random() > 0.5 ? 'w-4/5' : 'w-3/4') : 'w-1/2'}
								<div class="code-line">
									<div
										class="relative {lineWidth} h-4 overflow-hidden rounded bg-slate-200 dark:bg-slate-700"
									>
										<div
											class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
											style="animation-delay: {i * 0.1}s;"
										></div>
									</div>
								</div>
							{/each}
						</div>
					</div>
				</div>
			</div>
		</div>

		<div>
			<h2 class="mb-2 text-lg font-semibold">Timed Demo (Skeleton → Loaded)</h2>
			<div class="overflow-hidden rounded-lg border">
				{#if showSkeleton}
					<div class="monaco-editor-skeleton">
						<!-- Same skeleton structure -->
						<div class="editor-header">
							<div class="header-tabs">
								<div class="tab active">
									<div class="tab-icon"></div>
									<div class="tab-text"></div>
								</div>
								<div class="tab">
									<div class="tab-icon"></div>
									<div class="tab-text"></div>
								</div>
							</div>
						</div>
						<div class="editor-content">
							<div class="line-numbers">
								{#each Array(8) as _, i}
									<div class="line-number">{i + 1}</div>
								{/each}
							</div>
							<div class="code-content">
								{#each Array(8) as _, i}
									{@const lineWidth =
										Math.random() > 0.3 ? (Math.random() > 0.5 ? 'w-4/5' : 'w-3/4') : 'w-1/2'}
									<div class="code-line">
										<div
											class="relative {lineWidth} h-4 overflow-hidden rounded bg-slate-200 dark:bg-slate-700"
										>
											<div
												class="absolute inset-0 -translate-x-full animate-[shimmer_2s_infinite] bg-gradient-to-r from-transparent via-white/20 to-transparent"
												style="animation-delay: {i * 0.1}s;"
											></div>
										</div>
									</div>
								{/each}
							</div>
						</div>
					</div>
				{:else if showLoaded}
					<div class="border border-green-200 bg-green-50 p-4 text-green-800">
						✅ Monaco Editor has loaded successfully!
					</div>
				{/if}
			</div>
		</div>
	</div>
</div>

<style>
	/* Include the skeleton styles directly for this demo */
	.monaco-editor-skeleton {
		border: 1px solid #e5e7eb;
		border-radius: 0.375rem;
		overflow: hidden;
		background: #ffffff;
		min-height: 300px;
	}

	.editor-header {
		background: #f8fafc;
		border-bottom: 1px solid #e5e7eb;
		padding: 0.5rem;
	}

	.header-tabs {
		display: flex;
		gap: 0.25rem;
	}

	.tab {
		display: flex;
		align-items: center;
		gap: 0.5rem;
		padding: 0.375rem 0.75rem;
		border-radius: 0.25rem;
		background: #e5e7eb;
	}

	.tab.active {
		background: #ffffff;
		border: 1px solid #d1d5db;
	}

	.tab-icon {
		width: 12px;
		height: 12px;
		background: #9ca3af;
		border-radius: 2px;
	}

	.tab-text {
		width: 60px;
		height: 12px;
		background: #d1d5db;
		border-radius: 2px;
	}

	.editor-content {
		display: flex;
		min-height: 250px;
	}

	.line-numbers {
		background: #f8fafc;
		border-right: 1px solid #e5e7eb;
		padding: 0.75rem 0.5rem;
		min-width: 3rem;
		text-align: right;
		font-family: 'Monaco', 'Menlo', 'Ubuntu Mono', monospace;
		font-size: 0.75rem;
		line-height: 1.5;
	}

	.line-number {
		color: #9ca3af;
		height: 1.5rem;
		display: flex;
		align-items: center;
		justify-content: flex-end;
	}

	.code-content {
		flex: 1;
		padding: 0.75rem;
	}

	.code-line {
		height: 1.5rem;
		display: flex;
		align-items: center;
		margin-bottom: 0.125rem;
	}

	@keyframes shimmer {
		100% {
			transform: translateX(100%);
		}
	}

	/* Dark mode support */
	:global(.dark) .monaco-editor-skeleton {
		background: #1f2937;
		border-color: #374151;
	}

	:global(.dark) .editor-header {
		background: #111827;
		border-bottom-color: #374151;
	}

	:global(.dark) .tab {
		background: #374151;
	}

	:global(.dark) .tab.active {
		background: #1f2937;
		border-color: #4b5563;
	}

	:global(.dark) .tab-icon {
		background: #6b7280;
	}

	:global(.dark) .tab-text {
		background: #4b5563;
	}

	:global(.dark) .line-numbers {
		background: #111827;
		border-right-color: #374151;
	}

	:global(.dark) .line-number {
		color: #6b7280;
	}
</style>
