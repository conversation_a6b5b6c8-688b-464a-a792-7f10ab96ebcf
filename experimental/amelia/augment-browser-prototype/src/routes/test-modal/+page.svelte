<script lang="ts">
	import AgentDeletionModal from '$lib/components/ui/overlays/AgentDeletionModal.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import type { CleanRemoteAgent } from '$lib/api/unified-client';

	let showModal = $state(false);

	// Mock agent data for testing
	const mockAgent: CleanRemoteAgent = {
		id: 'test-agent-123',
		title: 'Test Agent for Modal',
		startedAt: new Date().toISOString(),
		githubUrl: 'https://github.com/test/repo',
		sessionSummary:
			'This is a test agent created to verify the deletion modal functionality works correctly.',
		workspaceStatus: 'REMOTE_AGENT_WORKSPACE_STATUS_IDLE' as any,
		lastActivity: new Date().toISOString(),
		status: 'idle' as any
	};

	function openModal() {
		showModal = true;
	}

	function closeModal() {
		showModal = false;
	}

	function handleAgentDeleted() {
		console.log('Agent deleted callback');
	}
</script>

<div class="p-8">
	<h1 class="mb-4 text-2xl font-bold">Agent Deletion Modal Test</h1>

	<Button onclick={openModal} variant="destructive">Test Delete Modal</Button>

	<AgentDeletionModal
		isOpen={showModal}
		agent={mockAgent}
		onClose={closeModal}
		onAgentDeleted={handleAgentDeleted}
	/>
</div>
