<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';
	import { auth } from '$lib/stores/auth';
	import { addToast } from '$lib/stores/toast';
	import LoadingIndicator from '$lib/components/ui/feedback/LoadingIndicator.svelte';
	import Spinner from '$lib/components/ui/feedback/Spinner.svelte';

	let error: string | null = null;
	let isProcessing = true;

	onMount(async () => {
		try {
			// Check for OAuth error in URL params
			const urlParams = new URLSearchParams(window.location.search);
			const oauthError = urlParams.get('error');

			if (oauthError) {
				const errorDescription = urlParams.get('error_description') || 'Unknown OAuth error';
				throw new Error(`OAuth error: ${oauthError} - ${errorDescription}`);
			}

			// Handle successful OAuth callback
			// Note: Site authentication check removed because if user reached OAuth,
			// they must have already passed through PasswordGuard
			const newSession = await auth.handleCallback();

			addToast({
				type: 'success',
				message: `Successfully authenticated! Welcome to Augment Actions.`,
				duration: 5000
			});

			// Wait for session to be fully set in the store
			await new Promise((resolve) => setTimeout(resolve, 200));

			// Verify session is available before redirecting
			console.log('Session after OAuth callback:', newSession);

			// Set site authentication cookie for OAuth-authenticated users
			try {
				await fetch('/api/auth/set-site-auth', { method: 'POST' });
				console.log('Site authentication cookie set successfully');
			} catch (error) {
				console.error('Failed to set site authentication cookie:', error);
			}

			// Redirect to the return URL or dashboard
			const returnTo = sessionStorage.getItem('auth_return_to');
			const targetUrl = returnTo || '/agents';

			console.log('About to redirect to:', targetUrl);
			if (returnTo) {
				sessionStorage.removeItem('auth_return_to');
				console.log('Redirecting to return URL:', targetUrl);
			} else {
				console.log('Redirecting to /agents');
			}

			await goto(targetUrl, { replaceState: true });
		} catch (err) {
			console.error('OAuth callback error:', err);
			error = err instanceof Error ? err.message : 'Authentication failed';

			addToast({
				type: 'error',
				message: error,
				duration: 10000
			});

			// Redirect to login page after a delay
			setTimeout(() => {
				goto('/login');
			}, 3000);
		} finally {
			isProcessing = false;
		}
	});

	function handleRetryLogin() {
		goto('/login');
	}
</script>

<svelte:head>
	<title>Authentication - Augment Code</title>
</svelte:head>

<div class="flex min-h-screen items-center justify-center bg-gray-50 dark:bg-gray-950">
	<div class="mx-auto w-full max-w-md">
		<div class="rounded-lg bg-white p-8 shadow-lg dark:bg-gray-900">
			{#if isProcessing}
				<div class="flex flex-col items-center space-y-6 text-center">
					<Spinner size="lg" />
					<h2 class="mb-2 text-xl font-semibold text-gray-900 dark:text-white">
						Completing Authentication
					</h2>
					<p class="text-gray-600 dark:text-gray-400">
						Please wait while we complete your authentication...
					</p>
				</div>
			{:else if error}
				<div class="text-center">
					<div class="mx-auto mb-4 h-12 w-12 text-red-500">
						<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z"
							/>
						</svg>
					</div>
					<h2 class="mb-2 text-xl font-semibold text-gray-900 dark:text-white">
						Authentication Failed
					</h2>
					<p class="mb-6 text-gray-600 dark:text-gray-400">
						{error}
					</p>
					<button
						on:click={handleRetryLogin}
						class="w-full rounded-md bg-blue-600 px-4 py-2 font-medium text-white transition-colors hover:bg-blue-700"
					>
						Try Again
					</button>
				</div>
			{:else}
				<div class="text-center">
					<div class="mx-auto mb-4 h-12 w-12 text-green-500">
						<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
							<path
								stroke-linecap="round"
								stroke-linejoin="round"
								stroke-width="2"
								d="M5 13l4 4L19 7"
							/>
						</svg>
					</div>
					<h2 class="mb-2 text-xl font-semibold text-gray-900 dark:text-white">
						Authentication Successful
					</h2>
					<p class="text-gray-600 dark:text-gray-400">Redirecting to dashboard...</p>
				</div>
			{/if}
		</div>
	</div>
</div>
