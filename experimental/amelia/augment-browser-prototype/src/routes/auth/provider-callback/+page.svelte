<script lang="ts">
	import { onMount } from 'svelte';
	import { page } from '$app/state';
	import { goto } from '$app/navigation';
	import { Icon, CheckCircle, ExclamationTriangle, ArrowPath } from 'svelte-hero-icons';
	import LoadingIndicator from '$lib/components/ui/feedback/LoadingIndicator.svelte';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { handleProviderOAuthCallback } from '$lib/stores/provider-auth';
	import { session } from '$lib/stores/auth';

	let status = $state<'loading' | 'success' | 'error'>('loading');
	let message = $state('Processing OAuth callback...');
	let providerId = $state<string | null>(null);
	let error = $state<string | null>(null);

	async function processCallback() {
		try {
			const code = page.url.searchParams.get('code');
			const state = page.url.searchParams.get('state');

			if (!code) {
				throw new Error('No authorization code received');
			}

			// Get provider ID from URL parameter (set by provider-specific callback) or session storage
			const urlProviderId = page.url.searchParams.get('provider');
			const storedProviderId = sessionStorage.getItem('oauth_provider_id');

			// Prefer URL parameter over session storage
			const targetProviderId = urlProviderId || storedProviderId;

			if (!targetProviderId) {
				throw new Error('No provider ID found. Please try the authentication process again.');
			}

			providerId = targetProviderId;

			// Store the provider ID in session storage if it came from URL
			if (urlProviderId && !storedProviderId) {
				sessionStorage.setItem('oauth_provider_id', urlProviderId);
			}
			message = `Connecting to ${providerId}...`;

			// Handle the OAuth callback using our provider auth store
			const success = await handleProviderOAuthCallback(code, targetProviderId);

			if (success) {
				status = 'success';
				message = `Successfully connected to ${providerId}!`;

				// Redirect back to the previous page after a short delay
				setTimeout(() => {
					// Try to go back to where the user came from
					const returnUrl = sessionStorage.getItem('oauth_return_url') || '/';
					sessionStorage.removeItem('oauth_return_url');
					goto(returnUrl);
				}, 2000);
			} else {
				throw new Error('OAuth callback failed');
			}

		} catch (err) {
			console.error('OAuth callback error:', err);
			status = 'error';
			error = err instanceof Error ? err.message : 'Unknown error occurred';
			message = 'Failed to complete authentication';
		}
	}

	function handleRetry() {
		status = 'loading';
		message = 'Retrying...';
		error = null;
		processCallback();
	}

	function handleGoBack() {
		const returnUrl = sessionStorage.getItem('oauth_return_url') || '/';
		sessionStorage.removeItem('oauth_return_url');
		goto(returnUrl);
	}

	onMount(() => {
		// Store the return URL if it's in the query params
		const returnUrl = page.url.searchParams.get('return_url');
		if (returnUrl) {
			sessionStorage.setItem('oauth_return_url', returnUrl);
		}

		// Process the OAuth callback
		processCallback();
	});
</script>

<svelte:head>
	<title>OAuth Callback - Augment Code</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
	<div class="max-w-md w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
		<div class="text-center">
			{#if status === 'loading'}
				<div class="w-16 h-16 mx-auto mb-4 flex items-center justify-center">
					<LoadingIndicator size="lg" />
				</div>
				<h1 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
					Connecting...
				</h1>
				<p class="text-gray-600 dark:text-gray-400">
					{message}
				</p>
			{:else if status === 'success'}
				<div class="w-16 h-16 mx-auto mb-4 bg-green-100 dark:bg-green-900 rounded-full flex items-center justify-center">
					<Icon src={CheckCircle} class="w-8 h-8 text-green-600 dark:text-green-400" />
				</div>
				<h1 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
					Success!
				</h1>
				<p class="text-gray-600 dark:text-gray-400 mb-4">
					{message}
				</p>
				<p class="text-sm text-gray-500 dark:text-gray-400">
					Redirecting you back...
				</p>
			{:else if status === 'error'}
				<div class="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
					<Icon src={ExclamationTriangle} class="w-8 h-8 text-red-600 dark:text-red-400" />
				</div>
				<h1 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
					Authentication Failed
				</h1>
				<p class="text-gray-600 dark:text-gray-400 mb-4">
					{message}
				</p>
				{#if error}
					<div class="bg-red-50 dark:bg-red-900/20 border border-red-200 dark:border-red-800 rounded-lg p-3 mb-4">
						<p class="text-sm text-red-800 dark:text-red-200">
							{error}
						</p>
					</div>
				{/if}
				<div class="flex gap-3 justify-center">
					<Button variant="outline" onclick={handleGoBack}>
						Go Back
					</Button>
					<Button variant="primary" onclick={handleRetry}>
						<Icon src={ArrowPath} class="w-4 h-4" />
						Try Again
					</Button>
				</div>
			{/if}
		</div>

		{#if providerId}
			<div class="mt-6 pt-6 border-t border-gray-200 dark:border-gray-700">
				<div class="text-center">
					<p class="text-xs text-gray-500 dark:text-gray-400">
						Provider: <span class="font-mono">{providerId}</span>
					</p>
					{#if $session?.tenantUrl}
						<p class="text-xs text-gray-500 dark:text-gray-400 mt-1">
							Tenant: <span class="font-mono">{$session.tenantUrl}</span>
						</p>
					{/if}
				</div>
			</div>
		{/if}
	</div>
</div>
