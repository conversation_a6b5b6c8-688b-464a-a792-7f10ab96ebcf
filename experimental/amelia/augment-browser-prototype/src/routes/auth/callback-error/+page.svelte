<script lang="ts">
	import { goto } from '$app/navigation';
	import { page } from '$app/state';
	import Button from '$lib/components/ui/navigation/Button.svelte';
	import { ArrowLeft, ArrowPath, ExclamationTriangle, Icon } from 'svelte-hero-icons';

	const error = $derived(page.url.searchParams.get('error') || 'unknown_error');
	const errorDescription = $derived(page.url.searchParams.get('error_description') || 'An unknown error occurred during authentication');

	// Map common OAuth errors to user-friendly messages
	const errorMessages: Record<string, { title: string; description: string; suggestion: string }> = {
		access_denied: {
			title: 'Access Denied',
			description: 'You denied access to the application.',
			suggestion: 'To use this feature, you need to grant the necessary permissions. Please try again and approve the access request.'
		},
		invalid_request: {
			title: 'Invalid Request',
			description: 'The authentication request was invalid.',
			suggestion: 'This might be a temporary issue. Please try starting the authentication process again.'
		},
		unauthorized_client: {
			title: 'Unauthorized Client',
			description: 'The application is not authorized to request access.',
			suggestion: 'This appears to be a configuration issue. Please contact support if this problem persists.'
		},
		unsupported_response_type: {
			title: 'Unsupported Response Type',
			description: 'The authorization server does not support this response type.',
			suggestion: 'This appears to be a configuration issue. Please contact support if this problem persists.'
		},
		invalid_scope: {
			title: 'Invalid Scope',
			description: 'The requested scope is invalid or unknown.',
			suggestion: 'This appears to be a configuration issue. Please contact support if this problem persists.'
		},
		server_error: {
			title: 'Server Error',
			description: 'The authorization server encountered an unexpected condition.',
			suggestion: 'This is a temporary issue with the provider. Please try again in a few minutes.'
		},
		temporarily_unavailable: {
			title: 'Service Temporarily Unavailable',
			description: 'The authorization server is temporarily overloaded or under maintenance.',
			suggestion: 'Please try again in a few minutes.'
		},
		no_code: {
			title: 'No Authorization Code',
			description: 'No authorization code was received from the provider.',
			suggestion: 'This might be a temporary issue. Please try starting the authentication process again.'
		}
	};

	const errorInfo = $derived(errorMessages[error] || {
		title: 'Authentication Error',
		description: errorDescription,
		suggestion: 'Please try the authentication process again. If the problem persists, contact support.'
	});

	function handleGoBack() {
		const returnUrl = sessionStorage.getItem('oauth_return_url') || '/';
		sessionStorage.removeItem('oauth_return_url');
		sessionStorage.removeItem('oauth_provider_id');
		goto(returnUrl);
	}

	function handleTryAgain() {
		// Clear any stored OAuth state and go back to try again
		sessionStorage.removeItem('oauth_provider_id');
		const returnUrl = sessionStorage.getItem('oauth_return_url') || '/';
		sessionStorage.removeItem('oauth_return_url');
		goto(returnUrl);
	}
</script>

<svelte:head>
	<title>Authentication Error - Augment Code</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center p-4">
	<div class="max-w-lg w-full bg-white dark:bg-gray-800 rounded-lg shadow-lg p-8">
		<div class="text-center">
			<div class="w-16 h-16 mx-auto mb-4 bg-red-100 dark:bg-red-900 rounded-full flex items-center justify-center">
				<Icon src={ExclamationTriangle} class="w-8 h-8 text-red-600 dark:text-red-400" />
			</div>

			<h1 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
				{errorInfo.title}
			</h1>

			<p class="text-gray-600 dark:text-gray-400 mb-4">
				{errorInfo.description}
			</p>

			<div class="bg-blue-50 dark:bg-blue-900/20 border border-blue-200 dark:border-blue-800 rounded-lg p-4 mb-6">
				<p class="text-sm text-blue-800 dark:text-blue-200">
					<strong>What to do next:</strong><br>
					{errorInfo.suggestion}
				</p>
			</div>

			<div class="flex gap-3 justify-center">
				<Button variant="outline" onclick={handleGoBack}>
					<Icon src={ArrowLeft} class="w-4 h-4" />
					Go Back
				</Button>
				<Button variant="primary" onclick={handleTryAgain}>
					<Icon src={ArrowPath} class="w-4 h-4" />
					Try Again
				</Button>
			</div>
		</div>

		<!-- Technical Details (collapsible) -->
		<details class="mt-8 pt-6 border-t border-gray-200 dark:border-gray-700">
			<summary class="text-sm text-gray-500 dark:text-gray-400 cursor-pointer hover:text-gray-700 dark:hover:text-gray-300">
				Technical Details
			</summary>
			<div class="mt-3 space-y-2">
				<div class="bg-gray-50 dark:bg-gray-700 rounded p-3">
					<p class="text-xs text-gray-600 dark:text-gray-400">
						<strong>Error Code:</strong> <span class="font-mono">{error}</span>
					</p>
					{#if errorDescription && errorDescription !== errorInfo.description}
						<p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
							<strong>Description:</strong> {errorDescription}
						</p>
					{/if}
					<p class="text-xs text-gray-600 dark:text-gray-400 mt-1">
						<strong>Timestamp:</strong> {new Date().toISOString()}
					</p>
				</div>
			</div>
		</details>
	</div>
</div>
