<script lang="ts">
	import { onMount } from 'svelte';
	import { goto } from '$app/navigation';

	let status = 'Checking for authentication result...';
	let error: string | null = null;

	onMount(async () => {
		// This page will be opened in a popup window by the OAuth flow
		// It will capture the redirect and send the data back to the parent window

		const urlParams = new URLSearchParams(window.location.search);
		const code = urlParams.get('code');
		const state = urlParams.get('state');
		let tenantUrl = urlParams.get('tenant_url');
		const oauthError = urlParams.get('error');

		if (oauthError) {
			const errorDescription = urlParams.get('error_description') || 'Unknown OAuth error';
			error = `OAuth error: ${oauthError} - ${errorDescription}`;
			status = 'Authentication failed';

			// Send error to parent window
			if (window.opener) {
				window.opener.postMessage({
					type: 'oauth_error',
					error: error
				}, window.location.origin);
				window.close();
			}
			return;
		}

		if (code) {
			// If no tenant URL, try to get fallback
			if (!tenantUrl) {
				console.warn('No tenant URL in OAuth redirect, attempting to fetch fallback...');
				try {
					const response = await fetch('/api/auth/tenant-fallback', {
						credentials: 'include'
					});
					if (response.ok) {
						const data = await response.json();
						tenantUrl = data.tenantUrl;
					}
				} catch (err) {
					console.error('Failed to fetch fallback tenant URL:', err);
				}
			}

			if (tenantUrl) {
				status = 'Authentication successful! Redirecting...';

				// Send success data to parent window
				if (window.opener) {
					window.opener.postMessage({
						type: 'oauth_success',
						code,
						state,
						tenantUrl
					}, window.location.origin);
					window.close();
				} else {
					// If not in popup, redirect to manual callback
					const callbackUrl = `http://127.0.0.1/api/augment/auth/result?code=${code}&state=${state || ''}&tenant_url=${encodeURIComponent(tenantUrl)}`;
					goto(`/login/manual-callback?url=${encodeURIComponent(callbackUrl)}`);
				}
			} else {
				error = 'No tenant URL available and no fallback configured';
				status = 'Authentication failed';
			}
		} else {
			error = 'No authorization code received';
			status = 'Authentication failed';
		}
	});
</script>

<svelte:head>
	<title>OAuth Redirect - Augment Code</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 dark:bg-gray-950 flex items-center justify-center">
	<div class="max-w-md w-full mx-auto">
		<div class="bg-white dark:bg-gray-900 shadow-lg rounded-lg p-8 text-center">
			{#if error}
				<div class="w-12 h-12 mx-auto mb-4 text-red-500">
					<svg fill="none" stroke="currentColor" viewBox="0 0 24 24">
						<path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 9v2m0 4h.01m-6.938 4h13.856c1.54 0 2.502-1.667 1.732-2.5L13.732 4c-.77-.833-1.732-.833-2.5 0L4.268 18.5c-.77.833.192 2.5 1.732 2.5z" />
					</svg>
				</div>
				<h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
					{status}
				</h2>
				<p class="text-gray-600 dark:text-gray-400 mb-4">
					{error}
				</p>
				<button
					onclick={() => window.close()}
					class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
				>
					Close
				</button>
			{:else}
				<div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
				<h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
					{status}
				</h2>
				<p class="text-gray-600 dark:text-gray-400">
					Please wait...
				</p>
			{/if}
		</div>
	</div>
</div>
