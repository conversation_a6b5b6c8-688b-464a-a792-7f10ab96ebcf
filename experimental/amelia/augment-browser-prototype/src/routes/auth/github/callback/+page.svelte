<script lang="ts">
  import { onMount } from 'svelte';
  import { goto } from '$app/navigation';
  import { createGitHubOAuth } from '$lib/auth/github-oauth';
  import { connectGitHubWithOAuth } from '$lib/stores/integrations';

  let status = $state('processing');
  let error = $state('');

  onMount(async () => {
    try {
      // Get GitHub OAuth config from server
      const configResponse = await fetch('/api/auth/github/config', {
        credentials: 'include'
      });
      if (!configResponse.ok) {
        throw new Error('Failed to get GitHub OAuth configuration');
      }
      const config = await configResponse.json();

      // Create GitHub OAuth client
      const githubOAuth = createGitHubOAuth({
        clientId: config.clientId,
        redirectUri: `${window.location.origin}${config.redirectUri}`,
        scopes: config.scopes
      });

      // Handle the OAuth callback
      const session = await githubOAuth.handleCallback();

      // Connect to our integrations store with the token
      const success = await connectGitHubWithOAuth(session.accessToken);

      if (success) {
        status = 'success';
        // Redirect back to the app after a short delay
        setTimeout(() => {
          goto('/');
        }, 2000);
      } else {
        throw new Error('Failed to connect GitHub account');
      }
    } catch (err) {
      console.error('GitHub OAuth callback error:', err);
      status = 'error';
      error = err instanceof Error ? err.message : 'Unknown error occurred';
    }
  });
</script>

<svelte:head>
  <title>GitHub Authentication - Augment Code</title>
</svelte:head>

<div class="min-h-screen bg-gray-50 dark:bg-gray-950 flex items-center justify-center">
  <div class="max-w-md w-full mx-4">
    <div class="bg-white dark:bg-gray-900 rounded-lg shadow-lg p-8 text-center">
      {#if status === 'processing'}
        <div class="animate-spin rounded-full h-12 w-12 border-b-2 border-blue-600 mx-auto mb-4"></div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Connecting GitHub Account
        </h2>
        <p class="text-gray-600 dark:text-gray-400">
          Please wait while we complete the authentication...
        </p>
      {:else if status === 'success'}
        <div class="w-12 h-12 bg-green-100 dark:bg-green-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-6 h-6 text-green-600 dark:text-green-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M5 13l4 4L19 7"></path>
          </svg>
        </div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          GitHub Connected Successfully!
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          Your GitHub account has been connected. You can now access your repositories and issues.
        </p>
        <p class="text-sm text-gray-500 dark:text-gray-400">
          Redirecting you back to the app...
        </p>
      {:else if status === 'error'}
        <div class="w-12 h-12 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center mx-auto mb-4">
          <svg class="w-6 h-6 text-red-600 dark:text-red-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M6 18L18 6M6 6l12 12"></path>
          </svg>
        </div>
        <h2 class="text-xl font-semibold text-gray-900 dark:text-white mb-2">
          Authentication Failed
        </h2>
        <p class="text-gray-600 dark:text-gray-400 mb-4">
          {error}
        </p>
        <button
          onclick={() => goto('/')}
          class="bg-blue-600 hover:bg-blue-700 text-white font-medium py-2 px-4 rounded-md transition-colors"
        >
          Return to App
        </button>
      {/if}
    </div>
  </div>
</div>
