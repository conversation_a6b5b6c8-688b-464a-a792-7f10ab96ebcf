<script lang="ts">
	import { onMount } from 'svelte';
	import { debugStreamingState, getStreamingStatus } from '$lib/stores/data-operations.svelte.ts';

	let streamingStatus = $state({});
	let refreshInterval: number;

	function refreshStatus() {
		streamingStatus = getStreamingStatus();
	}

	function debugStreaming() {
		debugStreamingState();
	}

	onMount(() => {
		refreshStatus();
		refreshInterval = setInterval(refreshStatus, 1000);

		return () => {
			if (refreshInterval) {
				clearInterval(refreshInterval);
			}
		};
	});
</script>

<div class="container mx-auto max-w-4xl p-6">
	<h1 class="mb-6 text-2xl font-bold">Streaming Debug Dashboard</h1>

	<div class="mb-6 rounded-lg border border-slate-200 bg-white p-4 dark:border-slate-700 dark:bg-slate-800">
		<h2 class="mb-4 text-lg font-semibold">Active Streams</h2>

		<div class="mb-4 flex gap-2">
			<button
				onclick={refreshStatus}
				class="rounded bg-blue-500 px-3 py-1 text-sm text-white hover:bg-blue-600"
			>
				Refresh Status
			</button>
			<button
				onclick={debugStreaming}
				class="rounded bg-purple-500 px-3 py-1 text-sm text-white hover:bg-purple-600"
			>
				Debug to Console
			</button>
		</div>

		{#if Object.keys(streamingStatus).length === 0}
			<p class="text-slate-600 dark:text-slate-400">No active streams</p>
		{:else}
			<div class="space-y-2">
				{#each Object.entries(streamingStatus) as [agentId, status]}
					<div class="rounded border border-slate-200 p-3 dark:border-slate-600">
						<div class="flex items-center justify-between">
							<span class="font-mono text-sm">{agentId}</span>
							<span class="rounded bg-green-100 px-2 py-1 text-xs text-green-800 dark:bg-green-900 dark:text-green-200">
								Active
							</span>
						</div>
						<div class="mt-1 text-xs text-slate-600 dark:text-slate-400">
							Last Sequence ID: {status.lastSequenceId}
						</div>
					</div>
				{/each}
			</div>
		{/if}
	</div>

	<div class="rounded-lg border border-yellow-200 bg-yellow-50 p-4 dark:border-yellow-700 dark:bg-yellow-900/20">
		<h3 class="mb-2 font-semibold text-yellow-800 dark:text-yellow-200">
			Duplicate Stream Detection
		</h3>
		<p class="mb-3 text-sm text-yellow-700 dark:text-yellow-300">
			If you're experiencing duplicate messages, check the browser console for streaming debug logs.
			Look for messages like "Stream already active" or "Stream creation already pending".
		</p>
		<p class="text-sm text-yellow-700 dark:text-yellow-300">
			Recent improvements include:
		</p>
		<ul class="mt-2 list-disc pl-5 text-sm text-yellow-700 dark:text-yellow-300">
			<li>Added race condition protection with pending streams tracking</li>
			<li>Explicit stream cleanup before starting new streams</li>
			<li>Enhanced logging for duplicate detection</li>
			<li>Atomic stream creation process</li>
		</ul>
	</div>
</div>
