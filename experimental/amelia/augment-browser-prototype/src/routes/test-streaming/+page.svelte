<script lang="ts">
	import StreamingText from '$lib/components/ui/content/StreamingText.svelte';

	let content = $state('');
	let isStreaming = $state(false);
	let intervalId: ReturnType<typeof setInterval> | null = null;
	let showError = $state(false);
	let errorMessage = $state('');

	// Test incremental content behavior
	let incrementalContent = $state('Hello, this is the initial content.');
	let incrementalStreaming = $state(false);

	const sampleTexts = [
		'Hello! This is a demonstration of the streaming text component.',
		'It can handle **markdown** formatting including *italics* and `code blocks`.',
		'The component automatically adjusts its speed when new content arrives faster than the animation.',
		'This ensures that the user always sees the most up-to-date content without lag.',
		'Here\'s some code:\n\n```javascript\nfunction streamText() {\n  console.log("Streaming!");\n}\n```',
		"And here's a list:\n\n- Item 1\n- Item 2\n- Item 3\n\nPretty cool, right?"
	];

	function startStreaming() {
		content = '';
		isStreaming = true;

		let textIndex = 0;
		let charIndex = 0;

		intervalId = setInterval(() => {
			if (textIndex >= sampleTexts.length) {
				stopStreaming();
				return;
			}

			const currentText = sampleTexts[textIndex];

			if (charIndex < currentText.length) {
				// Add characters one by one, with occasional bursts
				const burstSize = Math.random() > 0.8 ? Math.floor(Math.random() * 5) + 1 : 1;
				const endIndex = Math.min(charIndex + burstSize, currentText.length);
				content =
					sampleTexts.slice(0, textIndex).join('\n\n') +
					(textIndex > 0 ? '\n\n' : '') +
					currentText.substring(0, endIndex);
				charIndex = endIndex;
			} else {
				// Move to next text
				textIndex++;
				charIndex = 0;
				if (textIndex < sampleTexts.length) {
					content += '\n\n';
				}
			}
		}, 50); // 50ms intervals for demo
	}

	function stopStreaming() {
		isStreaming = false;
		if (intervalId) {
			clearInterval(intervalId);
			intervalId = null;
		}
	}

	function reset() {
		stopStreaming();
		content = '';
	}

	function addBurst() {
		if (isStreaming) {
			content +=
				'\n\n**BURST!** This is a sudden burst of content that should trigger catch-up mode in the streaming component.';
		}
	}

	function testError(type: string) {
		showError = true;
		switch (type) {
			case 'chunked':
				errorMessage = 'ERR_INCOMPLETE_CHUNKED_ENCODING';
				break;
			case 'network':
				errorMessage = 'TypeError: network error';
				break;
			case 'timeout':
				errorMessage = 'Request timeout';
				break;
			case 'auth':
				errorMessage = 'Authentication failed';
				break;
			default:
				errorMessage = 'Unknown error';
		}

		// Clear error after 3 seconds
		setTimeout(() => {
			showError = false;
			errorMessage = '';
		}, 3000);
	}

	// Incremental content test functions
	function startIncrementalTest() {
		incrementalContent = 'Hello, this is the initial content.';
		incrementalStreaming = false;

		// After a short delay, start "streaming" additional content
		setTimeout(() => {
			incrementalStreaming = true;
			incrementalContent = "Hello, this is the initial content. Now I'm adding more text";

			setTimeout(() => {
				incrementalContent =
					"Hello, this is the initial content. Now I'm adding more text that continues to grow";

				setTimeout(() => {
					incrementalContent =
						"Hello, this is the initial content. Now I'm adding more text that continues to grow with even more content!";
					incrementalStreaming = false;
				}, 1000);
			}, 1000);
		}, 1000);
	}

	function resetIncrementalTest() {
		incrementalContent = 'Hello, this is the initial content.';
		incrementalStreaming = false;
	}
</script>

<div class="container mx-auto max-w-4xl p-6">
	<h1 class="mb-6 text-3xl font-bold">Streaming Text Component Demo</h1>

	<div class="mb-6 flex gap-4">
		<button
			onclick={startStreaming}
			disabled={isStreaming}
			class="rounded bg-blue-500 px-4 py-2 text-white hover:bg-blue-600 disabled:opacity-50"
		>
			Start Streaming
		</button>

		<button
			onclick={stopStreaming}
			disabled={!isStreaming}
			class="rounded bg-gray-500 px-4 py-2 text-white hover:bg-gray-600 disabled:opacity-50"
		>
			Stop Streaming
		</button>

		<button onclick={reset} class="rounded border border-gray-300 px-4 py-2 hover:bg-gray-50">
			Reset
		</button>

		<button
			onclick={addBurst}
			disabled={!isStreaming}
			class="rounded border border-gray-300 px-4 py-2 hover:bg-gray-50 disabled:opacity-50"
		>
			Add Burst
		</button>
	</div>

	<div class="mb-6 flex gap-2">
		<span class="text-sm font-medium text-slate-700 dark:text-slate-300">Test Errors:</span>
		<button
			onclick={() => testError('chunked')}
			class="rounded bg-red-500 px-3 py-1 text-xs text-white hover:bg-red-600"
		>
			Chunked Encoding
		</button>
		<button
			onclick={() => testError('network')}
			class="rounded bg-red-500 px-3 py-1 text-xs text-white hover:bg-red-600"
		>
			Network Error
		</button>
		<button
			onclick={() => testError('timeout')}
			class="rounded bg-red-500 px-3 py-1 text-xs text-white hover:bg-red-600"
		>
			Timeout
		</button>
		<button
			onclick={() => testError('auth')}
			class="rounded bg-red-500 px-3 py-1 text-xs text-white hover:bg-red-600"
		>
			Auth Error
		</button>
	</div>

	<div class="mb-4 text-sm text-slate-600 dark:text-slate-400">
		Status: {isStreaming ? 'Streaming' : 'Stopped'} | Content length: {content.length} characters
	</div>

	<!-- Incremental Content Test -->
	<div
		class="mb-6 rounded-lg border border-blue-200 bg-blue-50 p-4 dark:border-blue-700 dark:bg-blue-900/20"
	>
		<h2 class="mb-3 text-lg font-semibold text-blue-800 dark:text-blue-200">
			Incremental Content Test
		</h2>
		<p class="mb-3 text-sm text-blue-700 dark:text-blue-300">
			This test demonstrates the new behavior: initial content shows immediately, then only new
			additions are animated.
		</p>
		<div class="mb-4 flex gap-2">
			<button
				onclick={startIncrementalTest}
				class="rounded bg-blue-500 px-3 py-1 text-sm text-white hover:bg-blue-600"
			>
				Start Incremental Test
			</button>
			<button
				onclick={resetIncrementalTest}
				class="rounded bg-gray-500 px-3 py-1 text-sm text-white hover:bg-gray-600"
			>
				Reset
			</button>
		</div>
		<div class="rounded bg-white p-3 dark:bg-slate-800">
			<StreamingText content={incrementalContent} isStreaming={incrementalStreaming} speed={3} />
		</div>
	</div>

	<div class="grid grid-cols-1 gap-6 lg:grid-cols-2">
		<!-- Streaming Version -->
		<div class="rounded-lg border border-slate-200 p-4 dark:border-slate-700">
			<h2 class="mb-4 text-lg font-semibold">Streaming Text Component</h2>
			<div class="min-h-[300px] rounded bg-slate-50 p-4 dark:bg-slate-800">
				<StreamingText
					{content}
					{isStreaming}
					speed={2}
					catchUpSpeed={8}
					catchUpThreshold={20}
					error={showError ? errorMessage : undefined}
				/>
			</div>
		</div>

		<!-- Static Version for Comparison -->
		<div class="rounded-lg border border-slate-200 p-4 dark:border-slate-700">
			<h2 class="mb-4 text-lg font-semibold">Static Version (for comparison)</h2>
			<div class="min-h-[300px] rounded bg-slate-50 p-4 dark:bg-slate-800">
				<StreamingText {content} isStreaming={false} disabled={true} />
			</div>
		</div>
	</div>

	<div class="mt-8 rounded-lg bg-blue-50 p-4 dark:bg-blue-900/20">
		<h3 class="mb-2 font-semibold text-blue-900 dark:text-blue-100">How it works:</h3>
		<ul class="space-y-1 text-sm text-blue-800 dark:text-blue-200">
			<li>• Characters appear one by one at a natural typing speed</li>
			<li>• When new content arrives faster than animation, it switches to "catch-up" mode</li>
			<li>• Handles markdown formatting seamlessly</li>
			<li>• Automatically stops animation when streaming ends</li>
			<li>• Robust handling of content changes and edge cases</li>
		</ul>
	</div>
</div>
