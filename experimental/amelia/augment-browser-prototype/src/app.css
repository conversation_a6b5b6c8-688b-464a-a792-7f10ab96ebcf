@import 'tailwindcss';
@plugin '@tailwindcss/typography';

@variant dark (.dark &);

@theme {
  /* Custom breakpoints - skinnier widths */
  --breakpoint-sm: 30rem; /* 480px - was 640px */
  --breakpoint-md: 36rem; /* 576px - was 768px */
  --breakpoint-lg: 52rem; /* 832px - was 1024px */
  --breakpoint-xl: 68rem; /* 1088px - was 1280px */
  --breakpoint-2xl: 84rem; /* 1344px - was 1536px */

  --color-gray-950: #030712;
  --color-gray-900: #111827;
  --color-gray-800: #1f2937;
  --color-gray-700: #374151;
  --color-gray-600: #4b5563;
  --color-gray-500: #6b7280;
  --color-gray-400: #9ca3af;
  --color-gray-300: #d1d5db;
  --color-gray-200: #e5e7eb;
  --color-gray-100: #f3f4f6;
  --color-gray-50: #f9fafb;

  /* Text and foreground colors */
  --color-foreground: #111827;
  --color-text: oklch(69.71% 0.178 284.67);
}

body {
  --panel-padding-x: 2.5rem;
}

/* Dark mode color overrides */
.dark {
  --color-foreground: #f3f4f6;
  --color-text: oklch(69.71% 0.178 284.67);
}

/* stable transitions */
    .stable-transition-container {
        display: grid;
    }

    .stable-transition-item {
        grid-column-start: 1;
        grid-column-end: 2;
        grid-row-start: 1;
        grid-row-end: 2;
    }

/* Safe area utilities for mobile devices */
.safe-area-pb {
    padding-bottom: env(safe-area-inset-bottom);
}

/* Mobile dropdown positioning utilities */
@media (max-width: 767px) {
    /* Ensure dropdowns are positioned relative to visual viewport on mobile */
    [data-combobox-dropdown] {
        /* Use transform3d to enable hardware acceleration for smoother positioning */
        transform: translate3d(0, 0, 0);
        /* Ensure dropdown stays above virtual keyboard */
        position: fixed !important;
        /* Prevent text selection issues on mobile */
        -webkit-user-select: none;
        user-select: none;
        /* Improve touch scrolling */
        -webkit-overflow-scrolling: touch;
        /* Prevent zoom on focus for iOS */
        touch-action: manipulation;
    }

    /* Prevent body scroll when dropdown is open on mobile */
    body.dropdown-open {
        overflow: hidden;
        position: fixed;
        width: 100%;
    }

    /* Improve mobile touch targets */
    [data-combobox-dropdown] button {
        min-height: 44px; /* iOS recommended touch target size */
        touch-action: manipulation;
    }
}
