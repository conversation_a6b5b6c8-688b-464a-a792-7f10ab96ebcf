<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>SVG Path Extractor for AuggieAvatar</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
            background: #f8fafc;
        }
        .container {
            background: white;
            border-radius: 12px;
            padding: 24px;
            box-shadow: 0 1px 3px rgba(0,0,0,0.1);
        }
        h1 {
            color: #1e293b;
            margin-bottom: 24px;
        }
        .input-section {
            margin-bottom: 24px;
        }
        label {
            display: block;
            margin-bottom: 8px;
            font-weight: 600;
            color: #374151;
        }
        textarea {
            width: 100%;
            height: 200px;
            padding: 12px;
            border: 2px solid #e5e7eb;
            border-radius: 8px;
            font-family: 'Monaco', '<PERSON><PERSON>', monospace;
            font-size: 14px;
            resize: vertical;
        }
        textarea:focus {
            outline: none;
            border-color: #3b82f6;
        }
        .controls {
            display: flex;
            gap: 16px;
            align-items: center;
            margin-bottom: 24px;
        }
        select {
            padding: 8px 12px;
            border: 2px solid #e5e7eb;
            border-radius: 6px;
            font-size: 14px;
        }
        button {
            background: #3b82f6;
            color: white;
            border: none;
            padding: 10px 20px;
            border-radius: 6px;
            cursor: pointer;
            font-weight: 600;
        }
        button:hover {
            background: #2563eb;
        }
        button:disabled {
            background: #9ca3af;
            cursor: not-allowed;
        }
        .results {
            margin-top: 24px;
        }
        .extracted-paths {
            background: #f1f5f9;
            border: 2px solid #e2e8f0;
            border-radius: 8px;
            padding: 16px;
            margin-bottom: 16px;
        }
        .path-item {
            background: white;
            border: 1px solid #e5e7eb;
            border-radius: 6px;
            padding: 12px;
            margin-bottom: 8px;
            font-family: 'Monaco', 'Menlo', monospace;
            font-size: 12px;
            word-break: break-all;
        }
        .final-output {
            background: #ecfdf5;
            border: 2px solid #10b981;
            border-radius: 8px;
            padding: 16px;
        }
        .copy-button {
            background: #10b981;
            margin-top: 12px;
        }
        .copy-button:hover {
            background: #059669;
        }
        .preview {
            margin-top: 16px;
            padding: 16px;
            background: #f8fafc;
            border-radius: 8px;
            border: 1px solid #e5e7eb;
        }
        .preview svg {
            max-width: 100%;
            height: auto;
        }
        .stats {
            color: #6b7280;
            font-size: 14px;
            margin-bottom: 16px;
        }
    </style>
</head>
<body>
    <div class="container">
        <h1>SVG Path Extractor for AuggieAvatar</h1>
        <p>Paste your SVG code below and select which array to add the extracted paths to. This tool will find all elements with <code>fill="#111111"</code> and format them for the AuggieAvatar component.</p>

        <div class="input-section">
            <label for="svgInput">SVG Code:</label>
            <textarea id="svgInput" placeholder="Paste your SVG code here..."></textarea>
        </div>

        <div class="controls">
            <label for="arraySelect">Target Array:</label>
            <select id="arraySelect">
                <option value="hats">hats</option>
                <option value="eyes">eyes</option>
                <option value="mouths">mouths</option>
                <option value="extras">extras</option>
            </select>
            <button onclick="extractPaths()">Extract Paths</button>
        </div>

        <div class="results" id="results" style="display: none;">
            <div class="stats" id="stats"></div>

            <div class="extracted-paths">
                <h3>Extracted Elements:</h3>
                <div id="extractedPaths"></div>
            </div>

            <div class="final-output">
                <h3>Formatted for AuggieAvatar:</h3>
                <div id="finalOutput"></div>
                <button class="copy-button" onclick="copyToClipboard()">Copy to Clipboard</button>
            </div>

            <div class="preview">
                <h3>Preview:</h3>
                <div id="preview"></div>
            </div>
        </div>
    </div>

    <script>
        let extractedContent = '';

        function extractPaths() {
            const svgInput = document.getElementById('svgInput').value.trim();
            const arraySelect = document.getElementById('arraySelect').value;

            if (!svgInput) {
                alert('Please paste some SVG code first!');
                return;
            }

            // Create a temporary DOM element to parse the SVG
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = svgInput;

            // Find all elements with fill="#111111"
            const elementsWithFill = tempDiv.querySelectorAll('[fill="#111111"]');

            if (elementsWithFill.length === 0) {
                alert('No elements found with fill="#111111"');
                return;
            }

            // Extract the elements
            const extractedElements = Array.from(elementsWithFill).map(el => {
                return el.outerHTML;
            });

            // Display results
            displayResults(extractedElements, arraySelect);
        }

        function displayResults(elements, targetArray) {
            const resultsDiv = document.getElementById('results');
            const statsDiv = document.getElementById('stats');
            const extractedPathsDiv = document.getElementById('extractedPaths');
            const finalOutputDiv = document.getElementById('finalOutput');
            const previewDiv = document.getElementById('preview');

            // Show results section
            resultsDiv.style.display = 'block';

            // Update stats
            statsDiv.textContent = `Found ${elements.length} element(s) with fill="#111111"`;

            // Display extracted elements
            extractedPathsDiv.innerHTML = elements.map((el, index) =>
                `<div class="path-item">${escapeHtml(el)}</div>`
            ).join('');

            // Format for AuggieAvatar
            const formattedContent = formatForAuggieAvatar(elements, targetArray);
            extractedContent = formattedContent;

            finalOutputDiv.innerHTML = `<pre style="white-space: pre-wrap; font-family: Monaco, monospace; font-size: 12px;">${escapeHtml(formattedContent)}</pre>`;

            // Create preview
            const previewSvg = createPreview(elements);
            previewDiv.innerHTML = previewSvg;
        }

        function formatForAuggieAvatar(elements, targetArray) {
            const joinedElements = elements.join('\n\t\t\t');

            return `// Add this to the ${targetArray} array in AuggieAvatar.svelte
\`
\t\t\t${joinedElements}
\`,`;
        }

        function createPreview(elements) {
            const joinedElements = elements.join('\n');
            return `<svg width="200" height="200" viewBox="0 0 553 553" style="border: 1px solid #e5e7eb;">
                ${joinedElements}
            </svg>`;
        }

        function escapeHtml(text) {
            const div = document.createElement('div');
            div.textContent = text;
            return div.innerHTML;
        }

        function copyToClipboard() {
            navigator.clipboard.writeText(extractedContent).then(() => {
                const button = document.querySelector('.copy-button');
                const originalText = button.textContent;
                button.textContent = 'Copied!';
                button.style.background = '#059669';

                setTimeout(() => {
                    button.textContent = originalText;
                    button.style.background = '#10b981';
                }, 2000);
            }).catch(err => {
                console.error('Failed to copy: ', err);
                alert('Failed to copy to clipboard. Please select and copy manually.');
            });
        }

        // Auto-extract when SVG is pasted
        document.getElementById('svgInput').addEventListener('paste', () => {
            setTimeout(() => {
                const svgInput = document.getElementById('svgInput').value.trim();
                if (svgInput && svgInput.includes('fill="#111111"')) {
                    extractPaths();
                }
            }, 100);
        });
    </script>
</body>
</html>
