# Augment Browser

A Svelte-based browser application for task management and remote agent automation.

## Getting Started

### Prerequisites

- Node.js (v18 or higher)
- pnpm (recommended) or npm

### Installation

1. Clone the repository and navigate to the project directory
2. Install dependencies:

```bash
pnpm install
# or
npm install
```

### Development

Start the development server:

```bash
pnpm run dev
# or
npm run dev

# To open the app in a new browser tab automatically
pnpm run dev -- --open
```

The application will be available at `http://localhost:5173`

### Environment Variables

The application uses environment variables for configuration. Copy `.env.example` to `.env.local` and fill in your values:

```bash
cp .env.example .env.local
```

**Required variables:**
- `DATABASE_URL` - SQLite database path (default: `"file:./dev.db"`)
- `SECURITY_PASSWORD` - Site access password

**Tenant URL Configuration:**
- `PUBLIC_DEV_TENANT_URL` - Development environment tenant URL
- `PUBLIC_STAGING_TENANT_URL` - Staging environment tenant URL
- `PUBLIC_TENANT_URL` - Legacy fallback tenant URL (optional)

**Optional variables (for experimental features):**
- `ANTHROPIC_APIKEY` - Only needed for chat/AI routes (get from [Anthropic Console](https://console.anthropic.com/settings/keys))
- `GITHUB_CLIENT_ID` & `GITHUB_CLIENT_SECRET` - Only needed for GitHub OAuth (create at [GitHub Developer Settings](https://github.com/settings/developers))

The `.env` file serves as a backup with default values. Your `.env.local` file will override these values and should contain your actual credentials.

### Available Scripts

- `pnpm run dev` - Start development server
- `pnpm run build` - Build for production
- `pnpm run preview` - Preview production build
- `pnpm run check` - Run type checking
- `pnpm run format` - Format code with Prettier
- `pnpm run lint` - Lint code with ESLint
- `pnpm run db:seed` - Seed the database with sample data

## Building

To create a production version of your app:

```bash
pnpm run build
# or
npm run build
```

You can preview the production build with:

```bash
pnpm run preview
# or
npm run preview
```

## Technology Stack

- **Frontend**: Svelte 5 + SvelteKit
- **Styling**: Tailwind CSS
- **Data**: In-memory stores and API integrations
- **Icons**: Heroicons (via svelte-hero-icons)
- **UI Components**: Radix Svelte
- **Code Highlighting**: highlight.js
- **Build Tool**: Vite

## Project Structure

- `/src` - Application source code

- `/static` - Static assets
- `/docs` - Project documentation
