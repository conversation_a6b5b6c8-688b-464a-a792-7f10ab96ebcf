{"name": "augment-browser", "private": true, "version": "0.0.1", "type": "module", "scripts": {"dev": "vite dev --host", "build": "vite build", "preview": "vite preview", "prepare": "svelte-kit sync || echo ''", "check": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json", "check:watch": "svelte-kit sync && svelte-check --tsconfig ./tsconfig.json --watch", "format": "prettier --write .", "lint": "prettier --check . && eslint .", "security:check": "node scripts/security-check.js", "pre-deploy": "npm run security:check && npm run lint && npm run check"}, "devDependencies": {"@eslint/compat": "^1.2.5", "@eslint/js": "^9.18.0", "@sveltejs/adapter-auto": "^6.0.0", "@sveltejs/kit": "^2.16.0", "@sveltejs/vite-plugin-svelte": "^5.0.0", "@tailwindcss/typography": "^0.5.15", "@tailwindcss/vite": "^4.0.0", "@types/d3": "^7.4.3", "@types/node": "^22.0.0", "eslint": "^9.18.0", "eslint-config-prettier": "^10.0.1", "eslint-plugin-svelte": "^3.0.0", "globals": "^16.0.0", "prettier": "^3.4.2", "prettier-plugin-svelte": "^3.3.3", "prettier-plugin-tailwindcss": "^0.6.11", "sharp": "^0.34.3", "svelte": "^5.0.0", "svelte-check": "^4.0.0", "tailwindcss": "^4.0.0", "tsx": "^4.19.4", "typescript": "^5.0.0", "typescript-eslint": "^8.20.0", "vite": "^6.2.6"}, "pnpm": {"onlyBuiltDependencies": ["esbuild"]}, "dependencies": {"@anthropic-ai/sdk": "^0.52.0", "@floating-ui/dom": "^1.7.2", "@types/marked": "^5.0.2", "@vercel/analytics": "^1.5.0", "cronstrue": "^2.61.0", "crypto-js": "^4.2.0", "d3": "^7.9.0", "diff": "^8.0.2", "fuzzysort": "^3.1.0", "highlight.js": "^11.11.1", "marked": "^15.0.12", "monaco-editor": "^0.52.2", "partial-json": "^0.1.7", "svelte-fa": "^4.0.4", "svelte-hero-icons": "^5.2.0"}}