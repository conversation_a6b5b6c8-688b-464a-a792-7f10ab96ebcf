#!/usr/bin/env node

const fs = require('fs');
const path = require('path');

// Configuration
const AUGGIE_AVATAR_PATH = './src/lib/components/ui/AuggieAvatar.svelte';
const TARGET_FILL = '#111111';

// Array markers for finding insertion points
const ARRAY_MARKERS = {
    hats: 'const hats = [',
    eyes: 'const eyes = [',
    mouths: 'const mouths = [',
    extras: 'const extras = ['
};

function extractElementsWithFill(svgContent, targetFill = TARGET_FILL) {
    // Simple regex to find elements with the target fill
    const fillRegex = new RegExp(`<([^>]+fill="${targetFill.replace('#', '\\#')}"[^>]*)>`, 'gi');
    const matches = [];
    let match;

    while ((match = fillRegex.exec(svgContent)) !== null) {
        // Extract the full element
        const elementStart = match.index;
        const tagName = match[1].split(' ')[0].replace('<', '');

        // Find the closing tag or self-closing
        if (match[0].endsWith('/>')) {
            // Self-closing tag
            matches.push(match[0]);
        } else {
            // Find closing tag
            const closingTagRegex = new RegExp(`</${tagName}>`, 'i');
            const closingMatch = closingTagRegex.exec(svgContent.substring(elementStart));
            if (closingMatch) {
                const fullElement = svgContent.substring(elementStart, elementStart + closingMatch.index + closingMatch[0].length);
                matches.push(fullElement);
            } else {
                // Fallback to just the opening tag if no closing found
                matches.push(match[0]);
            }
        }
    }

    return matches;
}

function formatForAuggieAvatar(elements) {
    if (elements.length === 0) return '';

    const indentedElements = elements.map(el => `\t\t\t${el}`).join('\n');
    return `\t\t\`\n${indentedElements}\n\t\t\`,`;
}

function addToAuggieAvatar(targetArray, formattedContent) {
    if (!fs.existsSync(AUGGIE_AVATAR_PATH)) {
        console.error(`Error: AuggieAvatar.svelte not found at ${AUGGIE_AVATAR_PATH}`);
        return false;
    }

    let content = fs.readFileSync(AUGGIE_AVATAR_PATH, 'utf8');
    const arrayMarker = ARRAY_MARKERS[targetArray];

    if (!arrayMarker) {
        console.error(`Error: Unknown array "${targetArray}". Valid options: ${Object.keys(ARRAY_MARKERS).join(', ')}`);
        return false;
    }

    const arrayStartIndex = content.indexOf(arrayMarker);
    if (arrayStartIndex === -1) {
        console.error(`Error: Could not find ${targetArray} array in AuggieAvatar.svelte`);
        return false;
    }

    // Find the closing bracket of the array
    let bracketCount = 0;
    let insertIndex = -1;

    for (let i = arrayStartIndex; i < content.length; i++) {
        if (content[i] === '[') {
            bracketCount++;
        } else if (content[i] === ']') {
            bracketCount--;
            if (bracketCount === 0) {
                insertIndex = i;
                break;
            }
        }
    }

    if (insertIndex === -1) {
        console.error(`Error: Could not find closing bracket for ${targetArray} array`);
        return false;
    }

    // Insert the new content before the closing bracket
    const beforeClosing = content.substring(0, insertIndex);
    const afterClosing = content.substring(insertIndex);

    // Add comma if there are existing items
    const needsComma = beforeClosing.trim().endsWith(',') || beforeClosing.trim().endsWith('`') ? '' : ',';

    const newContent = beforeClosing + needsComma + '\n' + formattedContent + '\n\t\t' + afterClosing;

    // Write back to file
    fs.writeFileSync(AUGGIE_AVATAR_PATH, newContent, 'utf8');
    return true;
}

function main() {
    const args = process.argv.slice(2);

    if (args.length < 2) {
        console.log(`
Usage: node svg-extractor.cjs <target-array> <svg-content-or-file>

target-array: hats, eyes, mouths, or extras
svg-content-or-file: Either SVG content as string or path to SVG file

Examples:
  node svg-extractor.cjs hats '<svg><path fill="#111111" d="..."/></svg>'
  node svg-extractor.cjs eyes ./my-svg-file.svg

The script will extract all elements with fill="#111111" and add them to the specified array in AuggieAvatar.svelte

Piped input examples:
  echo '<svg>...</svg>' | node svg-extractor.cjs hats
  pbpaste | node svg-extractor.cjs eyes
        `);
        process.exit(1);
    }

    const targetArray = args[0];
    const svgInput = args[1];

    // Check if input is a file path or SVG content
    let svgContent;
    if (fs.existsSync(svgInput)) {
        svgContent = fs.readFileSync(svgInput, 'utf8');
        console.log(`Reading SVG from file: ${svgInput}`);
    } else {
        svgContent = svgInput;
        console.log('Using SVG content from command line');
    }

    // Extract elements
    const elements = extractElementsWithFill(svgContent);

    if (elements.length === 0) {
        console.log(`No elements found with fill="${TARGET_FILL}"`);
        return;
    }

    console.log(`Found ${elements.length} element(s) with fill="${TARGET_FILL}":`);
    elements.forEach((el, i) => {
        console.log(`  ${i + 1}. ${el.substring(0, 80)}${el.length > 80 ? '...' : ''}`);
    });

    // Format for AuggieAvatar
    const formattedContent = formatForAuggieAvatar(elements);

    // Add to AuggieAvatar
    if (addToAuggieAvatar(targetArray, formattedContent)) {
        console.log(`\n✅ Successfully added ${elements.length} element(s) to the ${targetArray} array in AuggieAvatar.svelte`);
    } else {
        console.log('\n❌ Failed to update AuggieAvatar.svelte');
        console.log('\nFormatted content that would have been added:');
        console.log(formattedContent);
    }
}

// Handle piped input
if (!process.stdin.isTTY) {
    let stdinData = '';
    process.stdin.on('data', chunk => {
        stdinData += chunk;
    });
    process.stdin.on('end', () => {
        if (process.argv.length >= 3) {
            const targetArray = process.argv[2];
            const elements = extractElementsWithFill(stdinData);

            if (elements.length === 0) {
                console.log(`No elements found with fill="${TARGET_FILL}"`);
                return;
            }

            console.log(`Found ${elements.length} element(s) with fill="${TARGET_FILL}"`);
            const formattedContent = formatForAuggieAvatar(elements);

            if (addToAuggieAvatar(targetArray, formattedContent)) {
                console.log(`✅ Successfully added to ${targetArray} array`);
            } else {
                console.log('❌ Failed to update AuggieAvatar.svelte');
            }
        } else {
            console.log('Usage: echo "<svg>...</svg>" | node svg-extractor.cjs <target-array>');
        }
    });
} else {
    main();
}
