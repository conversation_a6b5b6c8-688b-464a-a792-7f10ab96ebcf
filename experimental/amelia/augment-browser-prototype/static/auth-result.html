<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Authentication Result - Augment</title>
    <style>
        body {
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            display: flex;
            justify-content: center;
            align-items: center;
            min-height: 100dvh;
            margin: 0;
            background: #f9fafb;
        }
        .container {
            text-align: center;
            padding: 2rem;
            background: white;
            border-radius: 8px;
            box-shadow: 0 1px 3px rgba(0, 0, 0, 0.1);
            max-width: 400px;
        }
        .spinner {
            width: 40px;
            height: 40px;
            border: 4px solid #e5e7eb;
            border-top: 4px solid #3b82f6;
            border-radius: 50%;
            animation: spin 1s linear infinite;
            margin: 0 auto 1rem;
        }
        @keyframes spin {
            0% { transform: rotate(0deg); }
            100% { transform: rotate(360deg); }
        }
        .error {
            color: #dc2626;
            margin-top: 1rem;
        }
    </style>
</head>
<body>
    <div class="container">
        <div class="spinner"></div>
        <h2>Processing Authentication...</h2>
        <p>Please wait while we complete your authentication.</p>
        <div id="error" class="error" style="display: none;"></div>
    </div>

    <script>
        function showError(message) {
            document.getElementById('error').textContent = message;
            document.getElementById('error').style.display = 'block';
            document.querySelector('.spinner').style.display = 'none';
        }

        function processAuthResult() {
            try {
                // Get URL parameters
                const urlParams = new URLSearchParams(window.location.search);
                const code = urlParams.get('code');
                const state = urlParams.get('state');
                const tenantUrl = urlParams.get('tenant_url');
                const error = urlParams.get('error');

                console.log('Auth result params:', { code, state, tenantUrl, error });

                if (error) {
                    const errorDescription = urlParams.get('error_description') || 'Unknown error';
                    showError(`Authentication failed: ${error} - ${errorDescription}`);
                    return;
                }

                if (!code) {
                    showError('No authorization code received');
                    return;
                }

                if (!tenantUrl) {
                    showError('No tenant URL received');
                    return;
                }

                // Construct callback URL for our app
                const appCallbackUrl = new URL('/login/callback', window.location.origin);
                appCallbackUrl.searchParams.set('code', code);
                appCallbackUrl.searchParams.set('state', state);
                appCallbackUrl.searchParams.set('tenant_url', tenantUrl);

                console.log('Redirecting to app callback:', appCallbackUrl.toString());

                // Redirect to our app's callback handler
                window.location.href = appCallbackUrl.toString();

            } catch (err) {
                console.error('Error processing auth result:', err);
                showError('Failed to process authentication result');
            }
        }

        // Process the authentication result when page loads
        window.addEventListener('load', processAuthResult);
    </script>
</body>
</html>
