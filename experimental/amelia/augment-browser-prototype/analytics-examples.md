# Vercel Analytics Usage Examples

## Basic Usage

```typescript
import { trackUserAction, trackFeatureUsage, trackPageView } from '$lib/utils/analytics';

// Track button clicks
trackUserAction('Button Click', {
  location: 'header',
  feature: 'navigation',
  metadata: { buttonType: 'primary' }
});

// Track feature usage
trackFeatureUsage('Agent Chat', 'message_sent', {
  userType: 'external',
  metadata: { messageLength: 150 }
});

// Track page views
trackPageView('Dashboard', {
  userType: 'internal',
  metadata: { hasActiveAgents: true }
});
```

## User Pattern Analysis Questions You Can Answer:

### 1. **Feature Adoption Patterns**
```typescript
// Track which features are used most
trackFeatureUsage('Code Editor', 'opened');
trackFeatureUsage('Agent Chat', 'started');
trackFeatureUsage('File Explorer', 'navigated');
```

**Dashboard Questions:**
- Which features are most popular?
- How often do users engage with different tools?
- What's the adoption rate of new features?

### 2. **User Journey Analysis**
```typescript
// Track workflow steps
trackWorkflow('Agent Setup', 'step_1_auth');
trackWorkflow('Agent Setup', 'step_2_config');
trackWorkflow('Agent Setup', 'step_3_complete');
```

**Dashboard Questions:**
- Where do users drop off in workflows?
- What's the completion rate for key processes?
- Which steps take the longest?

### 3. **Error Pattern Analysis**
```typescript
// Track errors for pattern identification
trackError('API_ERROR', 'Failed to connect to agent', {
  feature: 'agent_chat',
  metadata: { errorCode: 500 }
});
```

**Dashboard Questions:**
- What are the most common error types?
- Which features have the highest error rates?
- Are errors correlated with specific user types?

### 4. **Performance Pattern Analysis**
```typescript
// Track performance metrics
trackPerformance('page_load_time', 1250, {
  metadata: { page: 'dashboard' }
});
```

**Dashboard Questions:**
- Which pages are slowest to load?
- How does performance vary by user type?
- Are there performance bottlenecks?

## Limitations & Privacy

### ✅ What You CAN Track:
- Aggregate behavior patterns
- Feature usage statistics
- Error and performance patterns
- Geographic and device trends
- Custom event data

### ❌ What You CANNOT Track:
- Individual user identification
- Personal data or PII
- Cross-device user tracking
- Detailed individual user journeys

## Dashboard Access

1. Go to [Vercel Dashboard](https://vercel.com/dashboard)
2. Select your project
3. Click "Analytics" tab
4. View "Events" panel for custom events
5. Filter and analyze patterns

## Pro/Enterprise Features Required

- Custom events tracking requires Pro or Enterprise plan
- Advanced filtering and analysis features
- Higher event limits and data retention
