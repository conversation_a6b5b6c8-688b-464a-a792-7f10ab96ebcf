{"cells": [{"cell_type": "markdown", "id": "196e9b52", "metadata": {}, "source": ["# Finetune 2B model with long context\n", "\n", "We finetune a 2B model on longer context compared to the original pretraining and then evaluate the finetuned model with different sequence lengths.\n", "\n", "**Note:** The long-context finetuning in these experiments requires FlashAttention.\n", "\n", "## Finetuning\n", "\n", "We finetune the baseline 2B model originally pretrained with context length 2048 on the-stack-dedup dataset with repository and path-locality ordering with context lengths 16384 and 32768. We finetune on 6.5B tokens.\n", "\n", "Since we are using path locality ordering, consecutive files may be relevant, and so we are giving the model a chance to utilize the very long context.\n", "\n", "| Model | Context Length | Experiment | wandb |\n", "|-------|----------------|------------|-------|\n", "| codegen-2B-multi                | 2048  | baseline | N/A |\n", "| codegen-2B-repos_sort2-ctx16384 | 16384 | [Experiment](https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/1110/) | [wandb](https://wandb.ai/augment/codegen/runs/xp033dhv/overview) |\n", "| codegen-2B-repos_sort2-ctx32768 | 32768 | [Experiment](https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/1111/) | [wandb](https://wandb.ai/augment/codegen/runs/3uwrrbs9/overview) |\n", "\n", "During the finetuning, we observe that the loss starts high around 5.0 per token, but quickly drops down to ~0.6. The original model gets confused by long context lengths, but finetuning rapidly mitigates the confusion.\n", "\n", "## Evaluation\n", "\n", "We evaluate the models on Polycoder with varying context length."]}, {"cell_type": "markdown", "id": "b9297ab7", "metadata": {}, "source": ["### Requirements and Functions"]}, {"cell_type": "code", "execution_count": null, "id": "1d285d4d", "metadata": {"scrolled": true}, "outputs": [], "source": ["!git clone https://www.github.com/igor0/lm-plot\n", "!pip install -e lm-plot\n", "!pip install numexpr==2.7.3"]}, {"cell_type": "code", "execution_count": 1, "id": "4dd9e37d", "metadata": {"tags": ["hide-cell", "hide-input"]}, "outputs": [], "source": ["import lm_plot\n", "\n", "def get_results(json_files):\n", "    df = lm_plot.collect(json_files, lm_plot.files.ConfigExtractor()).raw()\n", "    df = df[(df[\"metric\"]==\"byte_perplexity\")].sort_values(\"model\")\n", "\n", "    # Compute mean byte perplexity across all repositories\n", "    df_means = df.groupby(\"model\")[\"value\"].agg(\"mean\").reset_index()\n", "    df_means = df_means.rename(columns={\"value\": \"byte perplexity\"})\n", "\n", "    return df_means"]}, {"cell_type": "markdown", "id": "17d8e53b", "metadata": {}, "source": ["### Evaluate PolyCoder with Different Context Lengths"]}, {"cell_type": "code", "execution_count": 2, "id": "51dc710a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th>context len</th>\n", "      <th>1024</th>\n", "      <th>2048</th>\n", "      <th>4096</th>\n", "      <th>8192</th>\n", "      <th>16384</th>\n", "      <th>32768</th>\n", "      <th>65536</th>\n", "    </tr>\n", "    <tr>\n", "      <th>model</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>codegen-2B-multi</th>\n", "      <td>1.337328</td>\n", "      <td>1.312637</td>\n", "      <td>1.789320</td>\n", "      <td>2.128836</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>codegen-2B-repos_sort2-ctx16384</th>\n", "      <td>1.329306</td>\n", "      <td>1.304617</td>\n", "      <td>1.295552</td>\n", "      <td>1.292938</td>\n", "      <td>1.292090</td>\n", "      <td>1.292289</td>\n", "      <td>1.292289</td>\n", "    </tr>\n", "    <tr>\n", "      <th>codegen-2B-repos_sort2-ctx32768</th>\n", "      <td>1.334640</td>\n", "      <td>1.309619</td>\n", "      <td>1.300500</td>\n", "      <td>1.297820</td>\n", "      <td>1.296794</td>\n", "      <td>1.296775</td>\n", "      <td>1.296775</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["context len                         1024      2048      4096      8192   \\\n", "model                                                                     \n", "codegen-2B-multi                 1.337328  1.312637  1.789320  2.128836   \n", "codegen-2B-repos_sort2-ctx16384  1.329306  1.304617  1.295552  1.292938   \n", "codegen-2B-repos_sort2-ctx32768  1.334640  1.309619  1.300500  1.297820   \n", "\n", "context len                         16384     32768     65536  \n", "model                                                          \n", "codegen-2B-multi                      NaN       NaN       NaN  \n", "codegen-2B-repos_sort2-ctx16384  1.292090  1.292289  1.292289  \n", "codegen-2B-repos_sort2-ctx32768  1.296794  1.296775  1.296775  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["import pandas as pd\n", "\n", "dfs = []\n", "\n", "# Accumulate the results across all context lengths\n", "for context_len in [1024, 2048, 4096, 8192, 16384, 32768, 65536]:\n", "    df = get_results(f\"eval/seq{context_len}/*/*.json\")\n", "    df[\"context len\"] = context_len\n", "    dfs.append(df)\n", "\n", "pd.concat(dfs).pivot(index='model', columns='context len', values='byte perplexity')"]}, {"cell_type": "markdown", "id": "21f7c980", "metadata": {}, "source": ["## Observations\n", "\n", "**Finetuned models are better than CodeGen-2B, even on short context lengths.**\n", "Additional training is expected to make the model better, although in this case we are seeing a more significant improvement than what we might expect from training on just 6.5B tokens, given that the baseline model was trained on 500B tokens. This suggests that our training is different in some way from the original pretraining of the CodeGen models.\n", "\n", "**CodeGen-2B explodes on context lengths >2048.**\n", "This is expected given that CodeGen-2B was trained with context length of 2048.\n", "\n", "**Fine-tuned models benefit from the longer context.**\n", "Generally, models continue improving with longer context, until they reach the context length beyond what they saw during training. Unfortunately, our PolyCoder eval doesn't push long context very much (see next point), so we don't really see a spike \n", "\n", "**Our PolyCoder eval is not designed for long context.**\n", "Since our PolyCoder eval only puts one file at a time into the context, our ability to measure the benefit of very long context is somewhat limited. There seem to be no files beyond 32,768 tokens and very few beyond 16,384 tokens. We can see that the columns 16384/32768/65536 are nearly identical.\n", "\n", "Also, since we ordered files by path locality during training, the model could have perhaps learned to attend across files. But our current PolyCoder eval doesn't measure this because we evaluate one file at a time.\n", "\n", "So, the PolyCoder eval shows us that finetuning on long context is doing something helpful, but it isn't sufficient to fully evaluate its effectiveness.\n", "\n", "## Conclusion\n", "\n", "Finetuning a model on context up to 65536 tokens gives the model at least some ability to utilize longer context. We don't currently have appropriate long-context evals to measure the effects.\n", "\n", "One possible follow-up experiment is to fine-tune our retrieval-capable model with a long context window for retrieved chunks."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 5}