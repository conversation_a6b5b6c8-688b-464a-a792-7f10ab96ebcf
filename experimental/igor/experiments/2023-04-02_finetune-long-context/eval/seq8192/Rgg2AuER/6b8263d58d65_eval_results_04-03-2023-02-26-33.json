{"results": {"gitrepo_poly_C_small": {"word_perplexity": 503261.99679202086, "byte_perplexity": 4.369491365179463, "bits_per_byte": 2.127465351203863, "token_perplexity": 35.13524232790419}, "gitrepo_poly_C++_small": {"word_perplexity": 255.6624865450918, "byte_perplexity": 1.7499425897529919, "bits_per_byte": 0.8073075924363099, "token_perplexity": 4.462429258202762}, "gitrepo_poly_Go_small": {"word_perplexity": 33.95248453664363, "byte_perplexity": 1.5185119135962952, "bits_per_byte": 0.6026582273192485, "token_perplexity": 3.303912804429112}, "gitrepo_poly_Java_small": {"word_perplexity": 1686.6012161949843, "byte_perplexity": 1.91603092546882, "bits_per_byte": 0.938120846909397, "token_perplexity": 7.455023339900855}, "gitrepo_poly_JavaScript_small": {"word_perplexity": 34.09102007442023, "byte_perplexity": 1.4024250703928467, "bits_per_byte": 0.4879236917178932, "token_perplexity": 3.0837384090047903}, "gitrepo_poly_Python_small": {"word_perplexity": 1455.1570200690894, "byte_perplexity": 1.8166165532542382, "bits_per_byte": 0.86125393135072, "token_perplexity": 6.767160406252739}}, "versions": {"gitrepo_poly_C_small": 3.0, "gitrepo_poly_C++_small": 3.0, "gitrepo_poly_Go_small": 3.0, "gitrepo_poly_Java_small": 3.0, "gitrepo_poly_JavaScript_small": 3.0, "gitrepo_poly_Python_small": 3.0}, "config": {"model": "neox", "model_args": {"total_model_params": 2779683841, "embedding_model_params": 262195200, "memory_config": {}, "memory_size": 4096, "memorize_mode": "host", "memory_invalid_query_mode": "ignore_eod", "num_memory_write_heads": null, "memory_save": "/mnt/efs/augment/mem/scratch/6b8263d58d65", "memory_load": "/mnt/efs/augment/mem/scratch/6b8263d58d65", "memory_train_on_gpu": false, "memory_partition_count": 24, "memorize_files": null, "memorize_filelist": null, "memorize_report": null, "distributed_backend": "nccl", "local_rank": 0, "rank": 0, "lazy_mpu_init": false, "short_seq_prob": 0.1, "eod_mask_loss": false, "adlr_autoresume": false, "adlr_autoresume_interval": 1000, "seed": 1234, "onnx_safe": false, "deepscale": false, "deepscale_config": null, "deepspeed_mpi": false, "user_script": "evaluate.py", "iteration": 0, "do_train": null, "do_valid": null, "do_test": null, "global_num_gpus": 1, "text_gen_type": "unconditional", "temperature": 0.0, "top_p": 0.0, "top_k": 0, "maximum_tokens": 64, "sample_input_file": null, "sample_output_file": "samples.txt", "num_samples": 1, "recompute": false, "eval_results_prefix": "/mnt/efs/augment/eval/jobs/Rgg2AuER/6b8263d58d65", "eval_tasks": ["gitrepo_poly_C_small", "gitrepo_poly_C++_small", "gitrepo_poly_Go_small", "gitrepo_poly_Java_small", "gitrepo_poly_JavaScript_small", "gitrepo_poly_Python_small"], "memory_object_names": null, "memory_object_names_file": null, "eval_batch_size": 1, "eval_with_memories": false, "eval_tags": {}, "use_wandb": true, "wandb_group": "eHbFD9QJumM5hG4bajCFsa_2gdd9bgb", "wandb_team": null, "wandb_project": "codegen", "wandb_host": "https://api.wandb.ai", "wandb_name": null, "wandb_init_all_ranks": false, "git_hash": "763226a", "log_dir": null, "tensorboard_dir": null, "log_interval": 100, "log_grad_pct_zeros": false, "log_param_norm": false, "log_grad_norm": false, "log_optimizer_states": false, "log_gradient_noise_scale": false, "gradient_noise_scale_n_batches": 5, "gradient_noise_scale_cpu_offload": false, "pipe_parallel_size": 1, "model_parallel_size": 1, "pipe_partition_method": "type:transformer|mlp", "world_size": 1, "is_pipe_parallel": true, "data_path": "/mnt/efs/augment/data/github/github_small_text_document", "train_data_paths": null, "test_data_paths": null, "valid_data_paths": null, "train_data_weights": null, "valid_data_weights": null, "test_data_weights": null, "weight_by_num_documents": false, "weighted_sampler_alpha": 0.3, "data_impl": "mmap", "mmap_warmup": false, "save": null, "config_files": {"config.yml": "attention-dropout: 0.0\ncheckpoint-activations: true\ncheckpoint-num-layers: 1\ndata-impl: mmap\ndata-path: /mnt/efs/augment/data/github/github_small_text_document\ndistributed-backend: nccl\nfinal_linear_bias: true\nfinetune: true\nfp16:\n  enabled: true\n  hysteresis: 2\n  loss_scale: 0\n  loss_scale_window: 1000\n  min_loss_scale: 1\ngpt_j_residual: true\ngradient_accumulation_steps: 24\ngradient_clipping: 1.0\nhidden-dropout: 0.0\nhidden-size: 2560\nkeep-last-n-checkpoints: 1\nload: /mnt/efs/augment/checkpoints/codegen-2B-multi\nlog-interval: 100\nlr-decay-style: cosine\nmake_vocab_size_divisible_by: 51200\nmax-position-embeddings: 2048\nmem_friendly_batch: true\nmemorize_mode: train\nmemory_load: /mnt/efs/augment/mem\nmemory_save: /mnt/efs/augment/mem\nno_weight_tying: true\nnorm: layernorm\nnum-attention-heads: 32\nnum-layers: 32\npartition-activations: true\npipe-parallel-size: 1\npos-emb: rotary\nrotary_interleave: true\nrotary_pct: 0.8\nscaled-upper-triang-masked-softmax-fusion: false\nseq-length: 1024\nsteps_per_print: 10\nsynchronize-each-layer: true\ntokenizer_type: CodeGenTokenizer\ntrain_micro_batch_size_per_gpu: 12\nuse_post_attn_norm: false\nvocab_file: none\nwall_clock_breakdown: true\nwandb_project: codegen\nwarmup: 0.01\nweight-decay: 0.1\nzero_optimization:\n  allgather_bucket_size: 500000000\n  allgather_partitions: true\n  contiguous_gradients: true\n  cpu_offload: false\n  overlap_comm: true\n  reduce_bucket_size: 500000000\n  reduce_scatter: true\n  stage: 1\n", "eval_config.yml": "eval_batch_size: 1\neval_tags: {}\neval_with_memories: false\nmax-position-embeddings: 16384\nseq-length: 8192\ntemperature: 0\ntop_k: 0\ntop_p: 0\n"}, "load": "/mnt/efs/augment/checkpoints/codegen-2B-multi", "checkpoint_validation_with_forward_pass": false, "save_interval": null, "no_save_optim": false, "no_save_rng": false, "no_load_optim": true, "no_load_rng": true, "finetune": false, "batch_size": 12, "train_iters": null, "eval_iters": 100, "keep_last_n_checkpoints": 1, "eval_interval": 1000, "early_stopping": false, "early_stopping_metric": "lm_loss", "early_stopping_threshold": 0.01, "split": "969, 30, 1", "vocab_file": "none", "merge_file": null, "num_workers": 2, "exit_interval": null, "attention_dropout": 0.0, "hidden_dropout": 0.0, "weight_decay": 0.1, "checkpoint_activations": false, "checkpoint_num_layers": 1, "deepspeed_activation_checkpointing": true, "contiguous_checkpointing": false, "checkpoint_in_cpu": false, "synchronize_each_layer": true, "profile_backward": false, "partition_activations": false, "gas": 24, "clip_grad": 1.0, "hysteresis": 2, "dynamic_loss_scale": true, "loss_scale": null, "loss_scale_window": 1000.0, "min_scale": 1.0, "char_level_ppl": false, "mem_friendly_batch": true, "train_only": null, "tokenizer_type": "CodeGenTokenizer", "padded_vocab_size": 51200, "optimizer_type": "<PERSON>", "use_bnb_optimizer": false, "zero_stage": 0, "zero_reduce_scatter": true, "zero_contiguous_gradients": false, "zero_reduce_bucket_size": 500000000, "zero_allgather_bucket_size": 500000000, "lr": 0.001, "lr_decay_style": "cosine", "lr_decay_iters": null, "min_lr": 0.0, "warmup": 0.01, "override_lr_scheduler": false, "use_checkpoint_lr_scheduler": false, "precision": "fp16", "num_layers": 32, "hidden_size": 2560, "num_attention_heads": 32, "seq_length": 8192, "max_position_embeddings": 16384, "norm": "layernorm", "layernorm_epsilon": 1e-05, "rms_norm_epsilon": 1e-08, "scalenorm_epsilon": 1e-08, "pos_emb": "rotary", "rotary_interleave": true, "rpe_num_buckets": 32, "rpe_max_distance": 128, "no_weight_tying": true, "attention_config": ["global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global"], "sparsity_config": {}, "num_unique_layers": null, "param_sharing_style": "grouped", "make_vocab_size_divisible_by": 51200, "activation": "gelu", "scaled_upper_triang_masked_softmax_fusion": false, "scaled_masked_softmax_fusion": false, "bias_gelu_fusion": false, "bias_dropout_fusion": false, "fp16_lm_cross_entropy": false, "init_method_std": 0.02, "apply_query_key_layer_scaling": false, "use_cpu_initialization": false, "attention_softmax_in_fp32": false, "rotary_pct": 0.8, "rotary_emb_base": 10000, "init_method": "normal", "output_layer_init_method": "scaled_normal", "gmlp_attn_dim": 64, "gpt_j_residual": true, "soft_prompt_tuning": null, "output_layer_parallelism": "row", "use_post_attn_norm": false, "final_linear_bias": true, "deepspeed": true, "train_batch_size": 288, "train_micro_batch_size_per_gpu": 12, "gradient_accumulation_steps": 24, "optimizer": null, "scheduler": null, "fp32_allreduce": false, "prescale_gradients": false, "gradient_predivide_factor": 1.0, "sparse_gradients": false, "fp16": {"enabled": true, "hysteresis": 2, "loss_scale": 0, "loss_scale_window": 1000, "min_loss_scale": 1}, "amp": null, "gradient_clipping": 1.0, "zero_optimization": {"stage": 0, "allgather_partitions": true, "allgather_bucket_size": 500000000, "overlap_comm": false, "reduce_scatter": true, "reduce_bucket_size": 500000000, "contiguous_gradients": false, "cpu_offload": false}, "steps_per_print": 10, "wall_clock_breakdown": true, "dump_state": false, "flops_profiler": null, "zero_allow_untested_optimizer": false, "hostfile": null, "include": null, "exclude": null, "num_nodes": -1, "num_gpus": null, "master_port": 29500, "master_addr": null, "launcher": "pdsh", "detect_nvlink_pairs": false}, "num_fewshot": 0, "batch_size": 1, "device": "cuda:0", "no_cache": false, "limit": null, "bootstrap_iters": 10000, "description_dict": null, "duration": 92.2965738773346, "eval_with_memories": false, "eval_tags": {}}}