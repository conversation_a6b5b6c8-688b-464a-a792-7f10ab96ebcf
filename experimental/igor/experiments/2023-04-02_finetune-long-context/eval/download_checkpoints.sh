#!/bin/bash

SCRATCH=/mnt/efs/augment/user/igor/scratch

function download_ckpt {
    uuid=$1
    name=$2

    s3_path=s3://dev-training-dai/$uuid
    tgt_path=$SCRATCH/$name

    s3cmd get --recursive --exclude=*zero_pp* --exclude=*code* s3://dev-training-dai/$uuid $SCRATCH

    mv $SCRATCH/$uuid $tgt_path
    python ~/augment/research/gpt-neox/jobs/inf_config.py $tgt_path -m /mnt/efs/augment/configs/codegen-H/inference/codegen.yml
}

# download checkpoints from S3
download_ckpt 65d61818-86e2-48ac-92fc-086f1d4acb87 codegen-2B-repos_sort2-ctx16384
download_ckpt b68bc3ce-f065-4246-99ea-897c459b2722 codegen-2B-repos_sort2-ctx32768
download_ckpt 515d799c-8b94-4c7a-bc60-ee94d3c38bac codegen-2B-repos_sort2-ctx65536

# symlink the baseline checkpoint (eval job expects checkpoints to be in the same directory)
ln -s /mnt/efs/augment/checkpoints/codegen-2B-multi $SCRATCH
