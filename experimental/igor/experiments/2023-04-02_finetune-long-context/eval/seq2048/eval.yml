
model_path: "/mnt/efs/augment/user/igor/scratch"

eval_tags: {}

neox_args: {
        "temperature": 0,
        "top_p": 0,
        "top_k": 0,
        "eval_with_memories": False,
        "seq-length": 2048,
        "max-position-embeddings": 4096,
}

models: [
 {"name": "codegen-2B-multi"},
 {"name": "codegen-2B-repos_sort2-ctx16384"},
 {"name": "codegen-2B-repos_sort2-ctx32768"},
 {"name": "codegen-2B-repos_sort2-ctx65536"},
]

tasks: [
"gitrepo_poly_C_small",
"gitrepo_poly_C++_small",
"gitrepo_poly_Go_small",
"gitrepo_poly_Java_small",
"gitrepo_poly_JavaScript_small",
"gitrepo_poly_Python_small",
]
