{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import numpy as np\n", "import pandas as pd\n", "\n", "from research.models.core import (\n", "    CodeGen_2B_FIM,\n", "    CodeGen_16B_Indiana,\n", "    StarCoder,\n", ")\n", "\n", "import logging\n", "logging.basicConfig(level=logging.WARNING)\n", "\n", "checkpoints_root = \"/mnt/efs/augment/checkpoints\"\n", "retrieval_top_k = 3\n", "\n", "print(\"Loading the model...\")\n", "model_fim = CodeGen_2B_FIM(checkpoints_root)\n", "model_fim.load()\n", "\n", "model_indiana = CodeGen_16B_Indiana(checkpoints_root)\n", "model_indiana.load()\n", "\n", "model_starcoder = StarCoder(checkpoints_root)\n", "model_starcoder.load()"]}, {"cell_type": "code", "execution_count": 6, "metadata": {}, "outputs": [], "source": ["import json\n", "if True:\n", "    with open(\"all.json\") as fh:\n", "        all = json.load(fh)\n", "\n", "if False:\n", "    with open(\"all.json\", \"w\") as fh:\n", "        json.dump(all, fh)"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["all = {\n", "    \"indiana\": {\n", "        \"<|startofsequence|><|pref-repo-small|>\": [],\n", "        \"<|startofsequence|><|pref-repo-large|>\": [],\n", "        \"<|startofsequence|>\": [],\n", "        \"<|pref-repo-small|>\": [],\n", "        \"<|pref-repo-large|>\": [],\n", "    },\n", "    \"starcoder\": {\n", "        \"<|endoftext|>\": [],\n", "    },\n", "    \"codegen-2b-fim\": {\n", "        \"<|endoftext|>\": [],\n", "    }\n", "}\n", "\n", "for i in range(100):\n", "    for model in all.keys():\n", "        for prompt in all[model].keys():\n", "            if model == \"indiana\":\n", "                completion = model_indiana._generate(\n", "                    prompt=prompt,\n", "                    temperature=1,\n", "                    max_generated_tokens=100000  # Bigger number than seq_length\n", "                )\n", "            elif model == \"starcoder\":\n", "                completion = model_starcoder.generate(\n", "                    prefix=prompt,\n", "                    temperature=1,\n", "                    max_generated_tokens=8000\n", "                )\n", "            elif model == \"codegen-2b-fim\":\n", "                completion = model_fim._generate(\n", "                    prompt=prompt,\n", "                    temperature=1,\n", "                    max_generated_tokens=100000  # Bigger number than seq_length\n", "                )\n", "\n", "            all[model][prompt].append(completion)\n", "            \n", "            print(f\"=======================================================================\")\n", "            print(\"PROMPT\", prompt, \"MODEL\", model)\n", "            print(completion)"]}, {"cell_type": "code", "execution_count": 10, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>samples</th>\n", "      <th>mean # tokens</th>\n", "      <th>has &lt;|fim-sep|&gt;</th>\n", "      <th>has &lt;|fim-eos|&gt;</th>\n", "      <th>has &lt;|ret-endofdoc|&gt;</th>\n", "      <th>fim-eos (25-percentile)</th>\n", "      <th>fim-eos (median)</th>\n", "      <th>fim-eos (75-percentile)</th>\n", "    </tr>\n", "    <tr>\n", "      <th>model</th>\n", "      <th>prompt</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>codegen-2b-fim</th>\n", "      <th>&lt;|endoftext|&gt;</th>\n", "      <td>100</td>\n", "      <td>1138.38</td>\n", "      <td>19</td>\n", "      <td>19</td>\n", "      <td>0</td>\n", "      <td>177.00</td>\n", "      <td>928.0</td>\n", "      <td>1024.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th rowspan=\"5\" valign=\"top\">indiana</th>\n", "      <th>&lt;|pref-repo-large|&gt;</th>\n", "      <td>100</td>\n", "      <td>1712.63</td>\n", "      <td>30</td>\n", "      <td>20</td>\n", "      <td>2</td>\n", "      <td>1517.25</td>\n", "      <td>2046.0</td>\n", "      <td>2047.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>&lt;|pref-repo-small|&gt;</th>\n", "      <td>100</td>\n", "      <td>1503.47</td>\n", "      <td>25</td>\n", "      <td>20</td>\n", "      <td>74</td>\n", "      <td>2044.75</td>\n", "      <td>2045.5</td>\n", "      <td>2047.00</td>\n", "    </tr>\n", "    <tr>\n", "      <th>&lt;|startofsequence|&gt;</th>\n", "      <td>100</td>\n", "      <td>1646.69</td>\n", "      <td>16</td>\n", "      <td>4</td>\n", "      <td>32</td>\n", "      <td>660.25</td>\n", "      <td>1055.0</td>\n", "      <td>1258.25</td>\n", "    </tr>\n", "    <tr>\n", "      <th>&lt;|startofsequence|&gt;&lt;|pref-repo-large|&gt;</th>\n", "      <td>100</td>\n", "      <td>1809.44</td>\n", "      <td>28</td>\n", "      <td>8</td>\n", "      <td>0</td>\n", "      <td>453.00</td>\n", "      <td>1183.5</td>\n", "      <td>1653.75</td>\n", "    </tr>\n", "    <tr>\n", "      <th>&lt;|startofsequence|&gt;&lt;|pref-repo-small|&gt;</th>\n", "      <td>100</td>\n", "      <td>1589.80</td>\n", "      <td>19</td>\n", "      <td>6</td>\n", "      <td>48</td>\n", "      <td>982.75</td>\n", "      <td>1072.0</td>\n", "      <td>1539.25</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                       samples  mean # tokens   \n", "model          prompt                                                           \n", "codegen-2b-fim <|endoftext|>                               100        1138.38  \\\n", "indiana        <|pref-repo-large|>                         100        1712.63   \n", "               <|pref-repo-small|>                         100        1503.47   \n", "               <|startofsequence|>                         100        1646.69   \n", "               <|startofsequence|><|pref-repo-large|>      100        1809.44   \n", "               <|startofsequence|><|pref-repo-small|>      100        1589.80   \n", "\n", "                                                       has <|fim-sep|>   \n", "model          prompt                                                    \n", "codegen-2b-fim <|endoftext|>                                        19  \\\n", "indiana        <|pref-repo-large|>                                  30   \n", "               <|pref-repo-small|>                                  25   \n", "               <|startofsequence|>                                  16   \n", "               <|startofsequence|><|pref-repo-large|>               28   \n", "               <|startofsequence|><|pref-repo-small|>               19   \n", "\n", "                                                       has <|fim-eos|>   \n", "model          prompt                                                    \n", "codegen-2b-fim <|endoftext|>                                        19  \\\n", "indiana        <|pref-repo-large|>                                  20   \n", "               <|pref-repo-small|>                                  20   \n", "               <|startofsequence|>                                   4   \n", "               <|startofsequence|><|pref-repo-large|>                8   \n", "               <|startofsequence|><|pref-repo-small|>                6   \n", "\n", "                                                       has <|ret-endofdoc|>   \n", "model          prompt                                                         \n", "codegen-2b-fim <|endoftext|>                                              0  \\\n", "indiana        <|pref-repo-large|>                                        2   \n", "               <|pref-repo-small|>                                       74   \n", "               <|startofsequence|>                                       32   \n", "               <|startofsequence|><|pref-repo-large|>                     0   \n", "               <|startofsequence|><|pref-repo-small|>                    48   \n", "\n", "                                                       fim-eos (25-percentile)   \n", "model          prompt                                                            \n", "codegen-2b-fim <|endoftext|>                                            177.00  \\\n", "indiana        <|pref-repo-large|>                                     1517.25   \n", "               <|pref-repo-small|>                                     2044.75   \n", "               <|startofsequence|>                                      660.25   \n", "               <|startofsequence|><|pref-repo-large|>                   453.00   \n", "               <|startofsequence|><|pref-repo-small|>                   982.75   \n", "\n", "                                                       fim-eos (median)   \n", "model          prompt                                                     \n", "codegen-2b-fim <|endoftext|>                                      928.0  \\\n", "indiana        <|pref-repo-large|>                               2046.0   \n", "               <|pref-repo-small|>                               2045.5   \n", "               <|startofsequence|>                               1055.0   \n", "               <|startofsequence|><|pref-repo-large|>            1183.5   \n", "               <|startofsequence|><|pref-repo-small|>            1072.0   \n", "\n", "                                                       fim-eos (75-percentile)  \n", "model          prompt                                                           \n", "codegen-2b-fim <|endoftext|>                                           1024.00  \n", "indiana        <|pref-repo-large|>                                     2047.00  \n", "               <|pref-repo-small|>                                     2047.00  \n", "               <|startofsequence|>                                     1258.25  \n", "               <|startofsequence|><|pref-repo-large|>                  1653.75  \n", "               <|startofsequence|><|pref-repo-small|>                  1539.25  "]}, "execution_count": 10, "metadata": {}, "output_type": "execute_result"}], "source": ["all_stat = []\n", "\n", "for model in [\"indiana\", \"codegen-2b-fim\"]:\n", "    for prompt in all[model].keys():\n", "        for completion in all[model][prompt]:\n", "            text = prompt + completion\n", "\n", "            if \"<|fim-eos|>\" in text:\n", "                fim_eos_idx = [len(model_indiana.tokenizer.tokenize(text[:text.find(\"<|fim-eos|>\")]))]\n", "            else:\n", "                fim_eos_idx = []\n", "\n", "            all_stat.append({\n", "                \"model\": model,\n", "                \"prompt\": prompt,\n", "                \"samples\": 1,\n", "                \"mean # tokens\": len(model_indiana.tokenizer.tokenize(text)),\n", "                \"has <|fim-sep|>\": 1 if \"<|fim-sep|>\" in text else 0,\n", "                \"has <|fim-eos|>\": 1 if \"<|fim-eos|>\" in text else 0,\n", "                \"fim-eos idx\": fim_eos_idx,\n", "                \"has <|ret-endofdoc|>\": 1 if \"<|ret-endofdoc|>\" in text else 0,\n", "            })\n", "df = pd.DataFrame(all_stat).groupby([\"model\", \"prompt\"]).agg(\n", "    {\n", "        \"samples\": \"sum\",\n", "        \"mean # tokens\": \"mean\",\n", "        \"has <|fim-sep|>\": \"sum\",\n", "        \"has <|fim-eos|>\": \"sum\",\n", "        \"has <|ret-endofdoc|>\": \"sum\",\n", "        \"fim-eos idx\": lambda x: sum(x, []),\n", "    }\n", ")\n", "df[\"fim-eos (25-percentile)\"] = df[\"fim-eos idx\"].map(lambda x: np.percentile(x, 25))\n", "df[\"fim-eos (median)\"] = df[\"fim-eos idx\"].map(lambda x: np.median(x))\n", "df[\"fim-eos (75-percentile)\"] = df[\"fim-eos idx\"].map(lambda x: np.percentile(x, 75))\n", "del df[\"fim-eos idx\"]\n", "df"]}, {"cell_type": "code", "execution_count": 11, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th></th>\n", "      <th>samples</th>\n", "      <th>has &lt;fim_prefix&gt;</th>\n", "      <th>has &lt;fim_middle&gt;</th>\n", "      <th>has &lt;fim_suffix&gt;</th>\n", "    </tr>\n", "    <tr>\n", "      <th>model</th>\n", "      <th>prompt</th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "      <th></th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>starcoder</th>\n", "      <th>&lt;|endoftext|&gt;</th>\n", "      <td>100</td>\n", "      <td>9</td>\n", "      <td>69</td>\n", "      <td>72</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                         samples  has <fim_prefix>  has <fim_middle>   \n", "model     prompt                                                       \n", "starcoder <|endoftext|>      100                 9                69  \\\n", "\n", "                         has <fim_suffix>  \n", "model     prompt                           \n", "starcoder <|endoftext|>                72  "]}, "execution_count": 11, "metadata": {}, "output_type": "execute_result"}], "source": ["sc_stat = []\n", "\n", "for model in [\"starcoder\"]:\n", "    for prompt in all[model].keys():\n", "        for completion in all[model][prompt]:\n", "            text = prompt + completion\n", "\n", "            sc_stat.append({\n", "                \"model\": model,\n", "                \"prompt\": prompt,\n", "                \"samples\": 1,\n", "                \"has <fim_prefix>\": 1 if \"<fim_prefix>\" in text else 0,\n", "                \"has <fim_middle>\": 1 if \"<fim_middle>\" in text else 0,\n", "                \"has <fim_suffix>\": 1 if \"<fim_suffix>\" in text else 0,\n", "            })\n", "df_sc = pd.DataFrame(sc_stat).groupby([\"model\", \"prompt\"]).agg(\n", "    {\n", "        \"samples\": \"sum\",\n", "        \"has <fim_prefix>\": \"sum\",\n", "        \"has <fim_middle>\": \"sum\",\n", "        \"has <fim_suffix>\": \"sum\",\n", "    }\n", ")\n", "\n", "df_sc\n"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}