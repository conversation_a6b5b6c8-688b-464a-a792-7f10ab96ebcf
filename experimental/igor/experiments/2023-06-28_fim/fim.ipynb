{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["%load_ext autoreload\n", "%autoreload 2\n", "\n", "import os\n", "import sys\n", "import numpy as np\n", "from termcolor import colored\n", "from textwrap import dedent\n", "\n", "from research.models.core import (\n", "    CodeGen_2B_FIM,\n", "    CodeGen_16B_Indiana\n", ")\n", "from research.retrieval.types import Document, Chunk\n", "from research.retrieval.legacy_retrieval_implementations.bm25  import Bm25DocumentIndex\n", "\n", "import logging\n", "logging.basicConfig(level=logging.WARNING)\n", "\n", "checkpoints_root = \"/mnt/efs/augment/checkpoints\"\n", "retrieval_top_k = 3\n", "RESEARCH_STARCODER_URL = \"http://starcoder-a40.tenant-augment-eng.coreweave.cloud:5000\"\n", "AUGMENT_PROD_URL = \"https://ab7796028231e4077b4c0877e6451186-1787811428.api.augmentcode.com\"\n", "OPENAI_API_KEY = \"\"\n", "\n", "print(\"Loading the model...\")\n", "model_fim = CodeGen_2B_FIM(checkpoints_root, retrieval_top_k)\n", "model_fim.load()\n", "\n", "model_indiana = CodeGen_16B_Indiana(checkpoints_root, retrieval_top_k)\n", "model_indiana.load()"]}, {"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["def fim_tests(model):\n", "    def fim_test(name, prompt):\n", "        fim_marker = \"<FILL-HERE>\"\n", "        fim_marker_idx = prompt.find(fim_marker)\n", "        prefix = prompt[0:fim_marker_idx]\n", "        suffix = prompt[fim_marker_idx+len(fim_marker):]\n", "\n", "        assert prefix + fim_marker + suffix == prompt\n", "\n", "        generated = model.generate(\n", "            prefix=prefix,\n", "            suffix=suffix,\n", "            temperature=0,\n", "            max_generated_tokens=250,\n", "        )\n", "\n", "        eos_idx = generated.find(\"<|fim-eos|>\")\n", "        generated_clipped = generated if eos_idx < 0 else generated[:eos_idx]\n", "\n", "        print(\"========================================================================================\")\n", "        print(f\"EXAMPLE: {name}\")\n", "        print(\"----------------------------------------------------------------------------------------\")\n", "        print(prompt)\n", "        print(\"----------------------------------------------------------------------------------------\")\n", "        print(\n", "            colored(prefix, \"blue\") +\n", "            colored(generated_clipped, \"white\", \"on_black\") +\n", "            colored(suffix, \"blue\")\n", "        )\n", "        print(\"========================================================================================\")\n", "        print()\n", "        print()\n", "\n", "    fim_test(\n", "        name=\"Trailing Return\",\n", "        prompt=dedent(\"\"\"\n", "            def get_odd_ints(lower, upper):\n", "            <FILL-HERE>\n", "                return results\n", "            \"\"\")\n", "    )\n", "\n", "    fim_test(\n", "        name=\"Var Def + Trailing Return\",\n", "        prompt=dedent(\"\"\"\n", "            def get_odd_ints(lower, upper):\n", "                results = []<FILL-HERE>\n", "                return results\n", "            \"\"\"),\n", "    )\n", "\n", "    fim_test(\n", "        name=\"Fill-in Docstring\",\n", "        prompt=dedent(\"\"\"\n", "            def get_odd_ints(lower, upper):\n", "                \\\"\\\"\\\"<FILL-HERE>\\\"\\\"\\\"\n", "                return [i for i in range(lower, upper) if i % 2 == 1]\n", "            \"\"\"),\n", "    )\n", "\n", "    fim_test(\n", "        name=\"Just EOS\",\n", "        prompt=dedent(\"\"\"\n", "            def hello_world():<FILL-HERE>\n", "                print(\"Hello World!\")\n", "            \"\"\"),\n", "    )\n", "\n", "    fim_test(\n", "        name=\"Missing Space\",\n", "        prompt=dedent(\"\"\"\n", "            def hello_world():,\n", "            <FILL-HERE>print(\"Hello World!\")\n", "        \"\"\")\n", "    )\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## CodeGen_2B_FIM"]}, {"cell_type": "code", "execution_count": 14, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: Trailing Return\n", "----------------------------------------------------------------------------------------\n", "\n", "def get_odd_ints(lower, upper):\n", "<FILL-HERE>\n", "    return results\n", "\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_odd_ints(lower, upper):\n", "\u001b[0m\u001b[100m\u001b[97m    results = []\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: Var Def + Trailing Return\n", "----------------------------------------------------------------------------------------\n", "\n", "def get_odd_ints(lower, upper):\n", "    results = []<FILL-HERE>\n", "    return results\n", "\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_odd_ints(lower, upper):\n", "    results = []\u001b[0m\u001b[100m\u001b[97m\n", "    for i in range(lower, upper + 1):\n", "        if i % 2 == 1:\n", "            results.append(i)\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: Fill-in Docstring\n", "----------------------------------------------------------------------------------------\n", "\n", "def get_odd_ints(lower, upper):\n", "    \"\"\"<FILL-HERE>\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_odd_ints(lower, upper):\n", "    \"\"\"\u001b[0m\u001b[100m\u001b[97m\n", "    Return a list of odd integers in the range [lower, upper).\n", "    \u001b[0m\u001b[34m\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: Just EOS\n", "----------------------------------------------------------------------------------------\n", "\n", "def hello_world():<FILL-HERE>\n", "    print(\"Hello World!\")\n", "\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def hello_world():\u001b[0m\u001b[100m\u001b[97m\u001b[0m\u001b[34m\n", "    print(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: Missing Space\n", "----------------------------------------------------------------------------------------\n", "\n", "def hello_world():,\n", "<FILL-HERE>print(\"Hello World!\")\n", "\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def hello_world():,\n", "\u001b[0m\u001b[100m\u001b[97m    \u001b[0m\u001b[34mprint(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["fim_tests(model_fim)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## CodeGen_16B_Indiana"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["========================================================================================\n", "EXAMPLE: Trailing Return\n", "----------------------------------------------------------------------------------------\n", "\n", "def get_odd_ints(lower, upper):\n", "<FILL-HERE>\n", "    return results\n", "\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_odd_ints(lower, upper):\n", "\u001b[0m\u001b[100m\u001b[97m    results = []\n", "    for i in range(lower, upper):\n", "        if i % 2 == 1:\n", "            results.append(i)\n", "    return results\n", "\n", "def get_even_ints(lower, upper):\n", "    results = []\n", "    for i in range(lower, upper):\n", "        if i % 2 == 0:\n", "            results.append(i)\n", "    return results\n", "\n", "def get_odd_floats(lower, upper):\n", "    results = []\n", "    for i in range(lower, upper):\n", "        if i % 2 == 1:\n", "            results.append(i)\n", "    return results\n", "\n", "def get_even_floats(lower, upper):\n", "    results = []\n", "    for i in range(lower, upper):\n", "        if i % 2 == 0:\n", "            results.append(i)\n", "    return results\n", "\n", "def get_odd_strings(lower, upper):\n", "    results = []\n", "    for i in range(lower, upper):\n", "        if i % 2 == 1:\n", "            results.append(i)\n", "    return results\n", "\n", "def get_even_strings(lower, upper\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: Var Def + Trailing Return\n", "----------------------------------------------------------------------------------------\n", "\n", "def get_odd_ints(lower, upper):\n", "    results = []<FILL-HERE>\n", "    return results\n", "\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_odd_ints(lower, upper):\n", "    results = []\u001b[0m\u001b[100m\u001b[97m\n", "    for i in range(lower, upper):\n", "        if i % 2 == 1:\n", "            results.append(i)\n", "    return results\n", "\n", "def get_even_ints(lower, upper):\n", "    results = []\n", "    for i in range(lower, upper):\n", "        if i % 2 == 0:\n", "            results.append(i)\n", "    return results\n", "\n", "def get_odd_floats(lower, upper):\n", "    results = []\n", "    for i in range(lower, upper):\n", "        if i % 2 == 1:\n", "            results.append(i)\n", "    return results\n", "\n", "def get_even_floats(lower, upper):\n", "    results = []\n", "    for i in range(lower, upper):\n", "        if i % 2 == 0:\n", "            results.append(i)\n", "    return results\n", "\n", "def get_odd_strings(lower, upper):\n", "    results = []\n", "    for i in range(lower, upper):\n", "        if i % 2 == 1:\n", "            results.append(i)\n", "    return results\n", "\n", "def get_even_strings(lower, upper):\n", "    results\u001b[0m\u001b[34m\n", "    return results\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: Fill-in Docstring\n", "----------------------------------------------------------------------------------------\n", "\n", "def get_odd_ints(lower, upper):\n", "    \"\"\"<FILL-HERE>\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def get_odd_ints(lower, upper):\n", "    \"\"\"\u001b[0m\u001b[100m\u001b[97mReturn a list of odd integers in the range [lower, upper]\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\n", "def get_even_ints(lower, upper):\n", "    \"\"\"Return a list of even integers in the range [lower, upper]\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 0]\n", "\n", "def get_odd_ints_in_range(lower, upper):\n", "    \"\"\"Return a list of odd integers in the range [lower, upper]\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\n", "def get_even_ints_in_range(lower, upper):\n", "    \"\"\"Return a list of even integers in the range [lower, upper]\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 0]\n", "\n", "def get_odd_ints_in_range(lower, upper):\n", "    \"\"\"Return a list of odd integers in the range [lower, upper]\"\"\"\n", "    return [i for i in range(lower, upper) if\u001b[0m\u001b[34m\"\"\"\n", "    return [i for i in range(lower, upper) if i % 2 == 1]\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: Just EOS\n", "----------------------------------------------------------------------------------------\n", "\n", "def hello_world():<FILL-HERE>\n", "    print(\"Hello World!\")\n", "\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def hello_world():\u001b[0m\u001b[100m\u001b[97m\n", "    \"\"\"\n", "    Hello World!\n", "    \"\"\"\n", "\u001b[0m\u001b[34m\n", "    print(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n", "========================================================================================\n", "EXAMPLE: Missing Space\n", "----------------------------------------------------------------------------------------\n", "\n", "def hello_world():,\n", "<FILL-HERE>print(\"Hello World!\")\n", "\n", "----------------------------------------------------------------------------------------\n", "\u001b[34m\n", "def hello_world():,\n", "\u001b[0m\u001b[100m\u001b[97m    print(\"Hello World!\")\n", "\n", "hello_world()\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello World!\")\n", "\n", "print(\"Hello\u001b[0m\u001b[34mprint(\"Hello World!\")\n", "\u001b[0m\n", "========================================================================================\n", "\n", "\n"]}], "source": ["fim_tests(model_indiana)"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}