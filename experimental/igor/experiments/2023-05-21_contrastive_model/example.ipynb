{"cells": [{"cell_type": "code", "execution_count": null, "metadata": {}, "outputs": [], "source": ["import gzip\n", "import jsonlines\n", "from megatron.inference.inference_model import InferenceModel\n", "from megatron.inference.process_wrap import ProcessWrappedObject\n", "\n", "yaml_files=[\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml\",\n", "    \"/mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml\",\n", "]\n", "overwrite_values={\n", "    \"load\": \"/mnt/efs/augment/checkpoints/5df89921-5f51-4af1-abf9-dccb0996aa09\",\n", "}\n", "\n", "def json_to_pairs(jsonl_gz_path, query_key, doc_key):\n", "    pairs = []\n", "    with gzip.open(jsonl_gz_path, 'rb') as fp:\n", "        for obj in jsonlines.Reader(fp):\n", "            pairs.append((obj[query_key], obj[doc_key]))\n", "    return pairs\n", "\n", "try:\n", "    model = ProcessWrappedObject(\n", "        InferenceModel,\n", "        yaml_files=yaml_files,\n", "        overwrite_values=overwrite_values,\n", "        use_cache=False\n", "    )\n", "\n", "    results = {}\n", "\n", "    for lang in [\"go\", \"java\", \"javascript\", \"php\", \"python\", \"ruby\"]:\n", "        codesearch_root = f\"/mnt/efs/augment/data/eval/retrieval/CodeSearchNet\"\n", "        jsonl_gz_path = f\"{codesearch_root}/{lang}/final/jsonl/test/{lang}_test_0.jsonl.gz\"\n", "        pairs = json_to_pairs(jsonl_gz_path, \"docstring\", \"code\")\n", "        \n", "        # Evaluate one batch -- comment out next line to evaluate all\n", "        pairs = pairs[:1000]\n", "\n", "        results[lang] = model.contrastive_eval(pairs, 1000, 10)\n", "\n", "        # Evaluate one language -- comment out next line to evaluate all\n", "        break\n", "finally:\n", "    del model\n", "\n", "results"]}], "metadata": {"kernelspec": {"display_name": "augment_env", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}