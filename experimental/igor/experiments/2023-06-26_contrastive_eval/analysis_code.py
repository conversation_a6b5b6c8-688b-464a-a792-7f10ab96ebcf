"""Analysis code."""

import json
from pathlib import Path

import pandas as pd
import seaborn as sns


def load_results():
    results = []

    for path in Path(".").glob("*_eval_results_*.json"):
        with path.open() as fh:
            j = json.load(fh)

            tags = j["config"]["eval_tags"]

            for benchmark in j["results"].keys():
                results.append(
                    {"benchmark": benchmark, **tags, **j["results"][benchmark]}
                )

    return pd.DataFrame(results)


def plot_results(df):
    sns.set_theme(style="whitegrid")

    g = sns.catplot(
        data=df.sort_values(by=["benchmark", "name"]),
        kind="bar",
        x="mrr",
        y="benchmark",
        hue="name",
        orient="h",
        aspect=2,
    )
    g.legend.set_title("")
    g.set_axis_labels("Mean Reciprocal Rank (MRR)", "")
