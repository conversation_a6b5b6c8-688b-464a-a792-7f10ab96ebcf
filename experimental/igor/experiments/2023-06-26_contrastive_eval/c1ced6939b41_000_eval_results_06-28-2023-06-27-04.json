{"results": {"codesearchnet_go_1000": {"mrr": 0.45499732957354616, "mrr_stderr": 0.03918321481520876, "mean_rank": 60.65886041096279, "mean_rank_stderr": 28.265136455209326, "exact_match_accuracy": 0.3385000142401883, "exact_match_accuracy_stderr": 0.029812741047073498, "truncations": 542}, "codesearchnet_java_1000": {"mrr": 0.3969983538756004, "mrr_stderr": 0.012332233353059227, "mean_rank": 69.8652730354896, "mean_rank_stderr": 5.81127304458173, "exact_match_accuracy": 0.27473078352900654, "exact_match_accuracy_stderr": 0.009772419839646874, "truncations": 1279}, "codesearchnet_javascript_1000": {"mrr": 0.44139595329761505, "mrr_stderr": 0.015981818135267212, "mean_rank": 53.66633605957031, "mean_rank_stderr": 4.146258057198738, "exact_match_accuracy": 0.33000000814596814, "exact_match_accuracy_stderr": 0.014973311071410735, "truncations": 413}, "codesearchnet_php_1000": {"mrr": 0.3765011823603085, "mrr_stderr": 0.010246474328620057, "mean_rank": 73.30757413591657, "mean_rank_stderr": 6.341053509768431, "exact_match_accuracy": 0.2610357299979244, "exact_match_accuracy_stderr": 0.009014023016164042, "truncations": 1150}, "codesearchnet_python_1000": {"mrr": 0.8054589818824421, "mrr_stderr": 0.006888955980997683, "mean_rank": 6.015409339557994, "mean_rank_stderr": 0.47684663930272747, "exact_match_accuracy": 0.723818223584782, "exact_match_accuracy_stderr": 0.00785712095727271, "truncations": 2382}, "codesearchnet_ruby_1000": {"mrr": 0.46033336222171783, "mrr_stderr": 0.007435038685798644, "mean_rank": 42.58900260925293, "mean_rank_stderr": 4.448999404907227, "exact_match_accuracy": 0.3305000215768814, "exact_match_accuracy_stderr": 0.010499998927116392, "truncations": 76}, "codesearchnet_go_10000": {"mrr": 0.3614718019962311, "mrr_stderr": NaN, "mean_rank": 241.22000122070312, "mean_rank_stderr": NaN, "exact_match_accuracy": 0.26269999146461487, "exact_match_accuracy_stderr": NaN, "truncations": 422}, "codesearchnet_java_10000": {"mrr": 0.3090992122888565, "mrr_stderr": 0.004622861742973327, "mean_rank": 555.1988830566406, "mean_rank_stderr": 19.586517333984375, "exact_match_accuracy": 0.20684999227523804, "exact_match_accuracy_stderr": 0.003849998116493225, "truncations": 1009}, "codesearchnet_php_10000": {"mrr": 0.2731928825378418, "mrr_stderr": 0.006656050682067871, "mean_rank": 667.7662048339844, "mean_rank_stderr": 73.94369506835938, "exact_match_accuracy": 0.18104999512434006, "exact_match_accuracy_stderr": 0.006049998104572296, "truncations": 787}, "codesearchnet_python_10000": {"mrr": 0.7678098678588867, "mrr_stderr": 0.001307666301727295, "mean_rank": 38.187299728393555, "mean_rank_stderr": 0.06690025329589842, "exact_match_accuracy": 0.6873999834060669, "exact_match_accuracy_stderr": 0.0023999810218811035, "truncations": 2282}}, "versions": {"codesearchnet_go_1000": 1, "codesearchnet_java_1000": 1, "codesearchnet_javascript_1000": 1, "codesearchnet_php_1000": 1, "codesearchnet_python_1000": 1, "codesearchnet_ruby_1000": 1, "codesearchnet_go_10000": 1, "codesearchnet_java_10000": 1, "codesearchnet_php_10000": 1, "codesearchnet_python_10000": 1}, "config": {"model": "neox", "model_args": {"contrastive": true, "contastive_init_log_scale": -4, "contrastive_similarity": "inner_product", "total_model_params": 304314370, "embedding_model_params": 0, "memory_config": {}, "memory_size": 4096, "memorize_mode": "host", "memory_invalid_query_mode": "ignore_eod", "num_memory_write_heads": null, "memory_save": "/mnt/efs/augment/mem/scratch/exp-7281-trial-7302-0-7281.5d473b7a-f50e-40a4-adc0-f4537f246ead-334", "memory_load": "/mnt/efs/augment/mem/scratch/exp-7281-trial-7302-0-7281.5d473b7a-f50e-40a4-adc0-f4537f246ead-334", "memory_train_on_gpu": false, "memory_partition_count": null, "memorize_files": null, "memorize_filelist": null, "memory_knn_top_k": 32, "memorize_report": null, "distributed_backend": "nccl", "local_rank": 0, "rank": 0, "lazy_mpu_init": false, "short_seq_prob": 0.1, "attn_mask_mode": "causal", "loss_mask_mode": "none", "adlr_autoresume": false, "adlr_autoresume_interval": 1000, "seed": 1234, "onnx_safe": false, "deepscale": false, "deepscale_config": null, "deepspeed_mpi": false, "user_script": "evaluate.py", "iteration": 22000, "do_train": null, "do_valid": null, "do_test": null, "global_num_gpus": 1, "text_gen_type": "unconditional", "temperature": 0.0, "top_p": 0.0, "top_k": 0, "maximum_tokens": 64, "sample_input_file": null, "sample_output_file": "samples.txt", "num_samples": 1, "recompute": false, "eval_results_prefix": "/mnt/efs/augment/eval/jobs/c  j  h  y  V  p  U  f  /c1ced6939b41_000", "eval_tasks": ["codesearchnet_go_1000", "codesearchnet_java_1000", "codesearchnet_javascript_1000", "codesearchnet_php_1000", "codesearchnet_python_1000", "codesearchnet_ruby_1000", "codesearchnet_go_10000", "codesearchnet_java_10000", "codesearchnet_php_10000", "codesearchnet_python_10000"], "memory_object_names": null, "memory_object_names_file": null, "eval_batch_size": 1, "eval_with_memories": false, "eval_tags": {"experiment": "https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/3493/overview", "name": "commit-contrast-350M-batch1024"}, "retrieval_index": null, "similarity_cutoff": 0.5, "ngram_size": 20, "path_delimiter": "\n", "document_delimiter": "<|ret-endofdoc|>", "use_wandb": true, "wandb_group": "Hk6rqPwMkS8sARxR9Xzjvq", "wandb_team": null, "wandb_project": "codegen", "wandb_host": "https://api.wandb.ai", "wandb_name": null, "wandb_init_all_ranks": false, "git_hash": null, "log_dir": null, "tensorboard_dir": null, "log_interval": 100, "log_grad_pct_zeros": false, "log_param_norm": false, "log_grad_norm": false, "log_optimizer_states": false, "log_gradient_noise_scale": false, "gradient_noise_scale_n_batches": 5, "gradient_noise_scale_cpu_offload": false, "pipe_parallel_size": 1, "model_parallel_size": 1, "pipe_partition_method": "type:transformer|mlp", "world_size": 1, "is_pipe_parallel": true, "data_path": "/mnt/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document", "train_data_paths": null, "test_data_paths": null, "valid_data_paths": null, "dataset_type": "gpt2", "train_data_weights": null, "valid_data_weights": null, "test_data_weights": null, "weight_by_num_documents": false, "weighted_sampler_alpha": 0.3, "data_impl": "mmap", "mmap_warmup": false, "save": null, "config_files": {"config.yml": "attention-dropout: 0.0\nbias-gelu-fusion: true\ncheckpoint-activations: true\ncheckpoint-num-layers: 1\ncontastive_init_log_scale: -4\ncontrastive: true\ndata-impl: mmap\ndata-path: /mnt/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document\ndistributed-backend: nccl\neval-interval: 40\neval-iters: 32\nfinal_linear_bias: true\nfinetune: true\nfp16:\n  enabled: true\n  hysteresis: 2\n  initial_scale_power: 12\n  loss_scale: 0\n  loss_scale_window: 1000\n  min_loss_scale: 1\ngpt_j_residual: true\ngradient_accumulation_steps: 1\ngradient_clipping: 1.0\nhidden-dropout: 0.0\nhidden-size: 1024\ninit_method: small_init\nkeep-last-n-checkpoints: 1\nload: /run/determined/checkpoint/ba1de34a-3703-4bc9-bf8f-1674430550f9\nlog-interval: 100\nlr-decay-iters: 15000\nlr-decay-style: cosine\nmake_vocab_size_divisible_by: 51200\nmax-position-embeddings: 2048\nmemorize_mode: train\nmemory_load: /mnt/efs/augment/mem\nmemory_save: /mnt/efs/augment/mem\nmin_lr: 4.0e-06\nno_weight_tying: true\nnorm: layernorm\nnum-attention-heads: 16\nnum-layers: 20\noptimizer:\n  params:\n    betas:\n    - 0.9\n    - 0.95\n    eps: 1.0e-08\n    lr: 4.0e-05\n  type: Adam\noutput_layer_init_method: wang_init\npartition-activations: true\npipe-parallel-size: 1\npos-emb: rotary\nrotary_interleave: true\nrotary_pct: 0.5\nsave-interval: 500\nscaled-upper-triang-masked-softmax-fusion: true\nseq-length: 2048\nsteps_per_print: 10\nsynchronize-each-layer: true\ntokenizer_type: CodeGenTokenizer\ntrain-iters: 15000\ntrain_batch_size: 12\ntrain_micro_batch_size_per_gpu: 12\nuse_post_attn_norm: false\nvocab_file: none\nwall_clock_breakdown: true\nwandb_project: codegen\nwarmup: 0.2\nweight-decay: 0.1\nzero_optimization:\n  allgather_bucket_size: 500000000\n  allgather_partitions: true\n  contiguous_gradients: true\n  cpu_offload: false\n  overlap_comm: true\n  reduce_bucket_size: 500000000\n  reduce_scatter: true\n  stage: 1\n", "tmpnefpdf1x.yml": "eval_batch_size: 1\neval_tags:\n  experiment: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/3493/overview\n  name: commit-contrast-350M-batch1024\nseq_length: 640\ntemperature: 0\n", "results_prefix.yml": "eval_results_prefix: /mnt/efs/augment/eval/jobs/cjhyVpUf/c1ced6939b41_000\n"}, "load": "/run/determined/checkpoint/ba1de34a-3703-4bc9-bf8f-1674430550f9", "checkpoint_validation_with_forward_pass": false, "save_interval": 500, "no_save_optim": false, "no_save_rng": false, "no_load_optim": true, "no_load_rng": true, "finetune": false, "batch_size": 12, "train_iters": 15000, "eval_iters": 32, "keep_last_n_checkpoints": 1, "eval_interval": 40, "early_stopping": false, "early_stopping_metric": "lm_loss", "early_stopping_threshold": 0.01, "split": "969, 30, 1", "vocab_file": "none", "merge_file": null, "num_workers": 2, "exit_interval": null, "attention_dropout": 0.0, "hidden_dropout": 0.0, "weight_decay": 0.1, "checkpoint_activations": false, "checkpoint_num_layers": 1, "deepspeed_activation_checkpointing": true, "contiguous_checkpointing": false, "checkpoint_in_cpu": false, "synchronize_each_layer": true, "profile_backward": false, "partition_activations": false, "gas": 1, "clip_grad": 1.0, "hysteresis": 2, "dynamic_loss_scale": true, "loss_scale": null, "loss_scale_window": 1000.0, "min_scale": 1.0, "char_level_ppl": false, "mem_friendly_batch": false, "train_only": null, "tokenizer_type": "CodeGenTokenizer", "padded_vocab_size": 51200, "optimizer_type": "<PERSON>", "use_bnb_optimizer": false, "zero_stage": 0, "zero_reduce_scatter": true, "zero_contiguous_gradients": false, "zero_reduce_bucket_size": 500000000, "zero_allgather_bucket_size": 500000000, "lr": 4e-05, "lr_decay_style": "cosine", "lr_decay_iters": 15000, "min_lr": 4e-06, "warmup": 0.2, "override_lr_scheduler": false, "use_checkpoint_lr_scheduler": false, "precision": "fp16", "num_layers": 20, "hidden_size": 1024, "num_attention_heads": 16, "seq_length": 640, "max_position_embeddings": 2048, "norm": "layernorm", "layernorm_epsilon": 1e-05, "rms_norm_epsilon": 1e-08, "scalenorm_epsilon": 1e-08, "pos_emb": "rotary", "rotary_interleave": true, "rpe_num_buckets": 32, "rpe_max_distance": 128, "no_weight_tying": true, "attention_config": ["global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global"], "sparsity_config": {}, "num_unique_layers": null, "param_sharing_style": "grouped", "make_vocab_size_divisible_by": 51200, "activation": "gelu", "scaled_upper_triang_masked_softmax_fusion": true, "scaled_masked_softmax_fusion": false, "bias_gelu_fusion": true, "bias_dropout_fusion": false, "scaled_dot_product_attention_fusion": true, "fp16_lm_cross_entropy": false, "init_method_std": 0.02, "apply_query_key_layer_scaling": false, "use_cpu_initialization": false, "attention_softmax_in_fp32": false, "rotary_pct": 0.5, "rotary_emb_base": 10000, "init_method": "small_init", "output_layer_init_method": "wang_init", "gmlp_attn_dim": 64, "gpt_j_residual": true, "soft_prompt_tuning": null, "output_layer_parallelism": "column", "use_post_attn_norm": false, "final_linear_bias": true, "deepspeed": true, "train_batch_size": 12, "train_micro_batch_size_per_gpu": 12, "gradient_accumulation_steps": 1, "optimizer": {"params": {"betas": [0.9, 0.95], "eps": 1e-08, "lr": 4e-05}, "type": "<PERSON>"}, "scheduler": null, "fp32_allreduce": false, "prescale_gradients": false, "gradient_predivide_factor": 1.0, "sparse_gradients": false, "fp16": {"enabled": true, "hysteresis": 2, "initial_scale_power": 12, "loss_scale": 0, "loss_scale_window": 1000, "min_loss_scale": 1}, "amp": null, "gradient_clipping": 1.0, "zero_optimization": {"stage": 0, "allgather_partitions": true, "allgather_bucket_size": 500000000, "overlap_comm": false, "reduce_scatter": true, "reduce_bucket_size": 500000000, "contiguous_gradients": false, "cpu_offload": false, "elastic_checkpoint": false}, "steps_per_print": 10, "wall_clock_breakdown": true, "dump_state": false, "flops_profiler": null, "zero_allow_untested_optimizer": false, "hostfile": null, "include": null, "exclude": null, "num_nodes": -1, "num_gpus": null, "master_port": 29500, "master_addr": null, "launcher": "pdsh", "detect_nvlink_pairs": false}, "num_fewshot": 0, "batch_size": 1, "device": "cuda:0", "no_cache": false, "limit": null, "bootstrap_iters": 10000, "description_dict": null, "duration": 1946.4242079257965, "eval_with_memories": false, "eval_tags": {"experiment": "https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/3493/overview", "name": "commit-contrast-350M-batch1024"}}}