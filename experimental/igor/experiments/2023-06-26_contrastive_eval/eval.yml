checkpoints:
  - path: s3://dev-training-dai/8d1a4e10-f513-41df-9579-2d55c44e196a
    tags:
      name: contrieve-350M-batch1024
      experiment: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/2694
      wandb: https://wandb.ai/igoro/codegen/runs/212p8ix8/overview
  - path: s3://dev-training-dai/1fb59e7c-6e10-4d9d-96ac-131fda053762
    tags:
      name: contrieve-350M-batch16384
      experiment: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/2694
      wandb: https://wandb.ai/igoro/codegen/runs/22zy3i66/overview



tasks: [
    "codesearchnet_go_1000",
    "codesearchnet_java_1000",
    "codesearchnet_javascript_1000",
    "codesearchnet_php_1000",
    "codesearchnet_python_1000",
    "codesearchnet_ruby_1000",
    "codesearchnet_go_10000",
    "codesearchnet_java_10000",
    "codesearchnet_php_10000",
    "codesearchnet_python_10000",
]

podspec: gpu-small.yaml

neox_args:
  temperature: 0  # need something or else eval.py will complain

determined:
  name: Eval Test
  workspace: Dev
  project: Eval
  metaconfig: jobs/templates/batch-eval.yaml
