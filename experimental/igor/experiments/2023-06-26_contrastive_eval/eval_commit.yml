checkpoints:
  - path: s3://dev-training-dai/ba1de34a-3703-4bc9-bf8f-1674430550f9
    tags:
      name: commit-contrast-350M-batch1024
      experiment: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/3493/overview



tasks: [
    "codesearchnet_go_1000",
    "codesearchnet_java_1000",
    "codesearchnet_javascript_1000",
    "codesearchnet_php_1000",
    "codesearchnet_python_1000",
    "codesearchnet_ruby_1000",
    "codesearchnet_go_10000",
    "codesearchnet_java_10000",
    "codesearchnet_php_10000",
    "codesearchnet_python_10000",
]

podspec: gpu-small.yaml

neox_args:
  temperature: 0  # need something or else eval.py will complain
  seq_length: 640

determined:
  name: Eval Test
  workspace: Dev
  project: Eval
  metaconfig: jobs/templates/batch-eval.yaml
