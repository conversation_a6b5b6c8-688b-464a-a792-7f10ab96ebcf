{"cells": [{"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["# CodeSearchNet evaluation of contrieve and commit-contrast models\n", "\n"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["We compare the CodeSearchNet scores for commit-contrast-350M-batch1024, contrieve-350M-batch1024 and contrieve-350M-batch16384 models."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## CodeSearchNet Evaluation"]}, {"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"data": {"image/png": "iVBORw0KGgoAAAANSUhEUgAABKkAAAHkCAYAAAD1vXyrAAAAOXRFWHRTb2Z0d2FyZQBNYXRwbG90bGliIHZlcnNpb24zLjcuMSwgaHR0cHM6Ly9tYXRwbG90bGliLm9yZy/bCgiHAAAACXBIWXMAAA9hAAAPYQGoP6dpAAEAAElEQVR4nOzdeVhW1f7//ycgkwOoIM4amJHIIJoD4pSEcznkScxjKqhkqRmSSllp0XEsUVBDRHLM0mNZOZX6zVk6JWkePWYO5YQ4ggYow/37wx/74+2Ns3mbvh7XxZX33muv/d5rb0nerPXeNiaTyYSIiIiIiIiIiIgV2Vo7ABERERERERERESWpRERERERERETE6pSkEhERERERERERq1OSSkRERERERERErE5JKhERERERERERsTolqURERERERERExOqUpBIREREREREREatTkkpERERERERERKxOSSoRua59+/axb98+a4chIiIiIiIijwAlqUTkui5fvszFixe5dOmStUMRK7t06RI//fSTnoVHnJ4DKaJnQYroWRARkXtJSSoRuamCggJrhyBWVvQM6Fl4tOk5kCJ6FqSIngUREbmXlKQSERERERERERGrU5JKRG7KxsbG2iGIldnY2ODs7Kxn4RGn50BERERE/kolrB2AiDzYHBwccHZ2tnYYYmXOzs74+PhYOwyxsgfxOSgsNGFrq6SZiIiIyMNASSoRuanpn27hWEamtcMQETFT1cOVV3sGWzsMEREREblHlKQSkZs6lpHJ4WPnrB2GiIiIiIiIPMRUk0pERERERERERKxOSSoREREREREREbE6JalERERERERERMTqlKQSERERERERERGrU5JKRERERERERESsTkkqERERERERERGxOiWpRERERERERETE6kpYOwARefAFeFehSgUXa4chImKmQvnS1g5BRERERO4hJanuk71799KlSxfmzZtH48aNrR3OPXX06FFCQkKYOnUq7dq1uy/n3Lt3L2vXrqV///44Ozvfl3PerpUrV7Jq1Sp27tzJyZMnGTFiBBERERbtLly4wLhx41i7di15eXk0b96c0aNH4+HhYdZux44dTJgwgb179+Lm5kbPnj0ZMGAANjY2RhuTyURSUhKLFi3i7Nmz1KlTh5iYGOrVq3dX19Kj3d0dLyIit8ZUWIiNrSa6i4iIyKNJSSr5W9q7dy8JCQn06tXrgU1SrV69miNHjtCqVSs+++yz67YbNmwYv/32G2PGjMHR0ZG4uDgGDBjAv//9b0qUuPJX9PfffyciIoLg4GCGDRvGvn37mDx5MnZ2dmaJr6SkJKZNm0Z0dDTe3t4sXLiQ8PBwli9fTvXq1e/4Wg59k0TOmRN3fLyIiNycs1tlPDsNsHYYIiIiIlajJJVcl8lkIi8vDwcHB2uH8rcUFxeH7f//2/DrJanS0tLYvHkzycnJNGvWDABPT086dOjAt99+S4cOHQBITk6mXLlyfPTRRzg4OBAUFMTZs2f5+OOP6d27Nw4ODly6dInExETCw8Pp27cvAA0aNKBdu3YkJyczZsyYO76WnDMnyDn5xx0fLyIiIiIiInIzmk9ejLS0NMLDw6lfvz6BgYH84x//YMuWLQCcP3+emJgYGjdujL+/P2FhYfznP/+x6GPGjBkEBwcTGBjI4MGDOXPmjEUbk8lEcnIybdu2xdfXl5CQED755BOzNunp6bz22ms0bdoUPz8/Wrduzb/+9S+zNgcOHGDQoEE0aNCAevXqMXDgQP74wzyhMGfOHJ5//nkaNGhAUFAQkZGRHDp0yKzNqFGj6NSpExs2bOC5557Dz8+P9evX33RMily6dIn33nuPhg0b0qxZMyZMmEB+fr6xPz4+nsDAQPbt20fPnj0JCAigU6dObNq0yWJsli1bxrPPPoufnx/NmzdnypQpFBQUGPtiYmIACAoKwtvbm9atW1v0URyTyURCQoJxb4YOHcrWrVvx9vYmNTXV7FrGjRtHs2bN8PPzo3Pnznz33Xe3dI4itrewXGPjxo24uLgQHBxsbPPy8qJOnTps3LjRrF1ISIhZwrBDhw5kZWWRlpYGXFkOePHiRdq3b2+0cXBwIDQ01KwvERERERERkQeRZlJd46effqJPnz7Uq1eP2NhYXFxc2L17N8ePH6egoIABAwZw5MgRoqOjcXd3Z/78+fTr14/Fixfj6+sLwIIFC5g6dSrh4eE0bdqUrVu38tZbb1mc64MPPmDJkiW8/PLLBAQEsGPHDiZPnoyjoyM9e/YEYMSIEWRkZDB69Gjc3Nw4ceIEu3fvNvo4cuQIYWFh1K5dm/Hjx2NjY8PHH39M3759Wb16tZHUSE9P55///CdVqlTh4sWLLF68mLCwMNasWUPZsmWN/jIyMoiNjWXQoEFUrlyZKlWq3HBMrhYXF0dISAhxcXGkpaURHx9PjRo1jGsByMvLIzo6mpdeeolXXnmFpKQkhg4dyvr16ylXrhwAKSkpTJo0iT59+jBq1CgOHDhgJKmio6Np1aoVgwYNYubMmcyePZsyZcrc8myv+fPnk5CQQP/+/WnSpAnbt29n9OjRFu2io6PZtGkTw4YNw8vLi+XLlzNkyBCmT59OSEjILZ3rVhw8eBBPT0+zulJwJVF18OBBALKzszlx4gReXl4WbWxsbDh48CCNGzc22l/brlatWsydO5fc3FycnJzuWewiIiIiIiIi95KSVNeYNGkSNWvWZO7cudjZ2QEYy7DWrVvHrl27mD17Ns2bNzf2tWnThsTEROLj4ykoKCAxMZHOnTszcuRIAJo3b86ZM2dYvny5cZ4//viDBQsWMHbsWHr06AFA06ZNyc3NZfr06fTo0QNbW1t++eUXoqKijGVfAF26dDH+nJCQgKurKykpKTg6OgJQv359QkJCWLJkCb169QLgzTffNI4pKCggODiYoKAg1qxZY5wfIDMzk6SkJAICAoxtI0eOvO6YXM3f399I+AQHB5OamsqaNWuKTVK1bNkSuLK0LSQkhI0bN9K5c2cuXrzItGnT6N+/P1FRUUZf9vb2jB8/noiICMqXL0+NGjUAqFu3LuXLl7/hPb36umfNmkW3bt2Ijo42ruPcuXMsXbrUaPe///2Pb7/9lrFjxxIWFgZAixYtOHbs2D1PUmVlZVGmTBmL7a6urkYy8sKFCwC4uJi/Xc/BwQFnZ2cyMzONvhwcHIznoIiLiwsmk4nMzEwlqUREREREROSBpeV+V8nJyWHnzp106dLFSMZc7ccff6R06dJGggrA3t6e0NBQfvrpJ+DKjKWMjAxCQ0PNjm3btq3Z561btwLQpk0b8vPzja+mTZty6tQpTpy4UqTax8eHOXPmsGjRIn7//XeLmLZs2ULr1q2xs7Mz+nBxccHHx8dsxtXPP/9Mv379aNy4MT4+PgQEBJCdnc3hw4fN+itbtqxZgupmY3K1axNXtWrVIj093Wybra0tQUFBxudq1arh5OTEyZMngSvLCrOzs2nXrp3FuOTm5rJ///4bxnAj6enpnDp1ymJp4LVJp6J7ee2bCtu3b8+ePXvIzs6+4xhEREREREREpHiaSXWVrKwsCgsL8fDwuO5+Nzc3i+3u7u7GbJZTp04BWMzucXd3N/t87tw5TCYTTZo0KfZcJ06coGrVqkyZMoUpU6YQFxfH2LFj8fT0JCoqijZt2hj9zJ07l7lz51r0YW9vD8Dx48cJDw/H19eXsWPH4uHhgb29PZGRkVy6dOmGcd5sTK527Ywge3t7Ll++bLbNycnJYmmevb29Ece5c+cA6Nq1a7HnKEre3Ynr3Ztr72lmZib29vZmyyDhytiYTCYuXLhAyZIl7ziOq7m4uFgk8opicHV1Bf5vXItmVBW5fPkyOTk5RjsXFxcuX77MpUuXzGZTZWVlYWNjY7QTEREREREReRApSXWVMmXKYGtrS0ZGRrH7XV1diy2Afvr0aSMBUKFCBQDOnj1r0ebavmxsbFi0aJGRTLqap6cnAB4eHowbN47CwkJ2797NzJkzef3111m9ejXVq1fH1dWVli1b8uKLL1r0UapUKQA2bdpEdnY2CQkJxpKx/Px8I7F2tWtrI91sTO61onFMSEigUqVKFvurVat2x31f795ce09dXV3Jy8szSxTBlXtoY2NT7PK8O+Xl5cW2bdswmUxmY3/o0CGeeOIJAEqWLEnlypWNmlNXtzGZTEYNqqL/Hjp0iCeffNJod/DgQapUqaKlfiIiIiIiIvJA03K/q5QsWZJ69eqxfPly401yV2vQoAEXL15k8+bNxrb8/HzWrl1LgwYNAKhUqRIVKlSweBPcmjVrzD4XLXk7f/48fn5+Fl+lS5c2a29ra4u/vz/Dhg0jPz/fWPoXFBTE/v378fHxseijKGmRm5uLjY0NJUr8X05y1apVZm/eu9MxudcCAwNxdnYmPT292HEpKq5elNi7dqbWjRTdm3Xr1pltX7t2rdnnonu5evVqs+2rV6/Gx8fnns2igiu1rjIzM9m2bZux7dChQ+zZs4cWLVqYtVu3bh15eXnGtpUrV+Li4kJgYCBwpRZZ6dKlWbVqldEmLy+Pb7/91qwvERERERERkQeRZlJdY/jw4fTt25e+ffvy4osv4urqyn//+1/KlStH165d8ff354033mD48OHG2/0yMjKYNm0aAHZ2dgwcOJAPPvgANzc3goOD2bJlC6mpqWbn8fT0pFevXowYMYKIiAgCAgLIy8vj8OHDpKamMmPGDC5cuEBERASdO3fG09OTvLw85s+fb9ScAhg6dCjdu3cnIiKCF154AXd3d06fPs0PP/zAU089RadOnYwlhTExMYSFhbF//35SUlIsCnHfyZh07979Ho7+lSVrQ4cOZdKkSaSnp9OoUSPs7Ow4cuQI69atIz4+HmdnZ2rVqgXAwoULeeaZZ3BycsLb2/uGfRfdm3/961+4u7vTuHFjUlNTjQSRre2VnO2TTz5JmzZtGD9+PLm5uXh6evLVV1+RlpbGjBkzbvlafvvtN3777Tfj86+//srq1atxdnY2CscHBgbSrFkz3nzzTUaOHImjoyNTpkzB29vbWNIJEBERwddff83w4cPp2bMnv/76K8nJybz++uvG8klHR0ciIyOJj4+nfPnyPPHEE3z66aecP3+eiIiIW467OK6evjiVt5zZJiIi946jq/vNG4mIiIg8xJSkusZTTz3FvHnziIuLIyYmBltbW2rXrs2wYcOws7Nj1qxZTJw4kUmTJpGdnU3dunWZM2cOvr6+Rh+9e/cmKyuLRYsW8emnnxIUFERsbCz9+/c3O9fo0aPx9PTks88+Y/r06ZQqVQpPT0+jYLejoyNPPPEE8+fP58SJEzg5OeHr60tycrJRV6lmzZosWbLEqFmVnZ1NhQoVaNiwoZG08fb2Zty4cSQkJBAZGUmdOnWYOnUqw4YNu+sx+SuEh4dTsWJFUlJSWLBgASVKlKBGjRq0atXKmEHl4+PDkCFDWLJkCbNnz6Zy5cqsX7/+pn1ffW/mz59PUFAQb7zxBq+//rrZMr5Jkybx0UcfkZSUxPnz5/Hy8mLatGkWRddvZNWqVSQkJBifv/zyS7788kuqVq1qFmtcXBzjxo3jnXfeIT8/n2bNmjF69GizmW81a9YkOTmZ8ePHM3DgQMqXL8/QoUMJDw83O+eAAQMwmUzMmTOHs2fPUqdOHZKTk6levfotx12cqi263dXxIiLyYCosLDR+SSMiIiJibTYmk8lk7SBErCkuLo6UlBRSU1NVt+kav/zyCwBbz6ZxPPOklaMREZF7qYprRSJbvnRHx2ZnZ7N3717q1KlzT5fBy9+PngUREbmXNJNKHikHDhzgq6++IjAwEHt7e3744QeSk5Pp2bOnElQ3cDzzJL+fOWrtMEREREREROQhpiSVPDQKCgq40cTAEiVK4OTkRFpaGp9++il//vknFStWJCIigiFDhtzWuW5UdN7GxgY7O7vb6k9ERERERETkUacklTw0QkNDOXbs2HX379u3j6pVqzJv3ry7Os/Ro0cJCQm57v5GjRoxf/78uzqHiIiIiIiIyKNGSSp5aMycOZPLly//5efx8PBg6dKl191fqlSpvzwGERERERERkYeNklTy0Ch6m+FfzcHBAT8/v/tyLhEREREREZFHhd45LCIiIiIiIiIiVqcklYiIiIiIiIiIWJ2SVCIiIiIiIiIiYnWqSSUiN1XFtaK1QxARkXtM39tFRETkQaMklYjcVGTLl6wdgoiI/AUKCwuxtdXEehEREXkw6F8lInJDly9fJicnx9phiJXl5OSwZ88ePQuPOD0HDx8lqERERORBon+ZiMhNmUwma4cgVmYymcjJydGz8IjTcyAiIiIifyUlqURERERERERExOqUpBIREREREREREatTkkpERERERERERKxOSSoREREREREREbE6JalE5KZsbGysHYJYmY2NDc7OznoWRERERETkL1PC2gGIyIPNwcEBZ2dna4chVubs7IyPj4+1w5CbKCw0YWurRKKIiIiI/D0pSSUiNzX90y0cy8i0dhgicgNVPVx5tWewtcMQEREREbljSlKJyE0dy8jk8LFz1g5DREREREREHmKqSSUiIiIiIiIiIlanJJWIiIiIiIiIiFidklQiIiIiIiIiImJ1SlKJiIiIiIiIiIjVKUklIiIiIiIiIiJWpySViIiIiIiIiIhYnZJUIiIiIiIiIiJidSWsHYCIPPgCvKtQpYKLtcMQkRuoUL70X34OGxsbnJ2dsbGx+cvPJSIiIiKPHiWp7tLevXvp0qUL8+bNo3HjxtYO5546evQoISEhTJ06lXbt2t2Xc+7du5e1a9fSv39/nJ2d78s5b9fKlStZtWoVO3fu5OTJk4wYMYKIiAiLdhcuXGDcuHGsXbuWvLw8mjdvzujRo/Hw8DBrt2PHDiZMmMDevXtxc3OjZ8+eDBgwwOyHQJPJRFJSEosWLeLs2bPUqVOHmJgY6tWrZ9bXyZMniY2NZfPmzdjb2xMaGkpMTAylS9/dD6892tW7aRsRefg5Ozvj4+Nz385nKizExlaTvkVEREQeFUpSyQNl7969JCQk0KtXrwc2SbV69WqOHDlCq1at+Oyzz67bbtiwYfz222+MGTMGR0dH4uLiGDBgAP/+978pUeLKX73ff/+diIgIgoODGTZsGPv27WPy5MnY2dmZJb6SkpKYNm0a0dHReHt7s3DhQsLDw1m+fDnVq1cHIC8vj/79+wPw4Ycfkpuby4QJExg+fDiJiYl3dc2Hvkki58yJu+pDROR2OLtVxrPTAGuHISIiIiL3kZJUjyCTyUReXh4ODg7WDuVvKS4uDtv//zf710tSpaWlsXnzZpKTk2nWrBkAnp6edOjQgW+//ZYOHToAkJycTLly5fjoo49wcHAgKCiIs2fP8vHHH9O7d28cHBy4dOkSiYmJhIeH07dvXwAaNGhAu3btSE5OZsyYMQCsWbOG/fv3s3LlSry8vABwcXEhIiKCXbt24e/vf8fXnHPmBDkn/7jj40VERERERERu5pGaQ5+WlkZ4eDj169cnMDCQf/zjH2zZsgWA8+fPExMTQ+PGjfH39ycsLIz//Oc/Fn3MmDGD4OBgAgMDGTx4MGfOnLFoYzKZSE5Opm3btvj6+hISEsInn3xi1iY9PZ3XXnuNpk2b4ufnR+vWrfnXv/5l1ubAgQMMGjSIBg0aUK9ePQYOHMgff5gnCubMmcPzzz9PgwYNCAoKIjIykkOHDpm1GTVqFJ06dWLDhg0899xz+Pn5sX79+puOSZFLly7x3nvv0bBhQ5o1a8aECRPIz8839sfHxxMYGMi+ffvo2bMnAQEBdOrUiU2bNlmMzbJly3j22Wfx8/OjefPmTJkyhYKCAmNfTEwMAEFBQXh7e9O6dWuLPopjMplISEgw7s3QoUPZunUr3t7epKamml3LuHHjaNasGX5+fnTu3Jnvvvvuls5RxPYWlp5s3LgRFxcXgoODjW1eXl7UqVOHjRs3mrULCQkxSxh26NCBrKws0tLSgCvLAS9evEj79u2NNg4ODoSGhlr05e3tbSSoAIKDgylbtiwbNmy4rWsUERERERERud8emZlUP/30E3369KFevXrExsbi4uLC7t27OX78OAUFBQwYMIAjR44QHR2Nu7s78+fPp1+/fixevBhfX18AFixYwNSpUwkPD6dp06Zs3bqVt956y+JcH3zwAUuWLOHll18mICCAHTt2MHnyZBwdHenZsycAI0aMICMjg9GjR+Pm5saJEyfYvXu30ceRI0cICwujdu3ajB8/HhsbGz7++GP69u3L6tWrjaRGeno6//znP6lSpQoXL15k8eLFhIWFsWbNGsqWLWv0l5GRQWxsLIMGDaJy5cpUqVLlhmNytbi4OEJCQoiLiyMtLY34+Hhq1KhhXAtcWWoWHR3NSy+9xCuvvEJSUhJDhw5l/fr1lCtXDoCUlBQmTZpEnz59GDVqFAcOHDCSVNHR0bRq1YpBgwYxc+ZMZs+eTZkyZW55ttf8+fNJSEigf//+NGnShO3btzN69GiLdtHR0WzatIlhw4bh5eXF8uXLGTJkCNOnTyckJOSWznUrDh48iKenp0VxYS8vLw4ePAhAdnY2J06cMEsqFbWxsbHh4MGDNG7c2Gh/bbtatWoxd+5ccnNzcXJy4uDBgxZtbGxs8PT0NPoQEREREREReVA9MkmqSZMmUbNmTebOnYudnR2AsQxr3bp17Nq1i9mzZ9O8eXNjX5s2bUhMTCQ+Pp6CggISExPp3LkzI0eOBKB58+acOXOG5cuXG+f5448/WLBgAWPHjqVHjx4ANG3alNzcXKZPn06PHj2wtbXll19+ISoqylj2BdClSxfjzwkJCbi6upKSkoKjoyMA9evXJyQkhCVLltCrVy8A3nzzTeOYgoICgoODCQoKYs2aNcb5ATIzM0lKSiIgIMDYNnLkyOuOydX8/f2NhE9wcDCpqamsWbOm2CRVy5YtgStL20JCQti4cSOdO3fm4sWLTJs2jf79+xMVFWX0ZW9vz/jx44mIiKB8+fLUqFEDgLp161K+fPkb3tOrr3vWrFl069aN6Oho4zrOnTvH0qVLjXb/+9//+Pbbbxk7dixhYWEAtGjRgmPHjt3zJFVWVhZlypSx2O7q6mokIy9cuABcWZJ3NQcHB5ydncnMzDT6cnBwMJ6DIi4uLphMJjIzM3FycrrhOYv6EhEREREREXlQPRLL/XJycti5cyddunQxkjFX+/HHHyldurSRoAKMN6P99NNPwJUZSxkZGYSGhpod27ZtW7PPW7duBaBNmzbk5+cbX02bNuXUqVOcOHGl+LSPjw9z5sxh0aJF/P777xYxbdmyhdatW2NnZ2f04eLigo+Pj9mMq59//pl+/frRuHFjfHx8CAgIIDs7m8OHD5v1V7ZsWbME1c3G5GrXJq5q1apFenq62TZbW1uCgoKMz9WqVcPJyYmTJ08CV5YVZmdn065dO4txyc3NZf/+/TeM4UbS09M5deqUxdLAa5NORffy2jcVtm/fnj179pCdnX3HMYiIiIiIiIjI3XkkZlJlZWVRWFiIh4fHdfe7ublZbHd3dzdmoJw6dQrAYnaPu7u72edz585hMplo0qRJsec6ceIEVatWZcqUKUyZMoW4uDjGjh2Lp6cnUVFRtGnTxuhn7ty5zJ0716IPe3t7AI4fP054eDi+vr6MHTsWDw8P7O3tiYyM5NKlSzeM82ZjcrVrZ+fY29tz+fJls21OTk4WS/Ps7e2NOM6dOwdA165diz1HUfLuTlzv3lx7TzMzM7G3tzdbBglXxsZkMnHhwgVKlix5x3FczcXFxSKRVxSDq6sr8H/jWjSjqsjly5fJyckx2rm4uHD58mUuXbpkNpsqKysLGxsbs3YXL14s9pyVK1e+J9clIiIiIiIi8ld5JJJUZcqUwdbWloyMjGL3u7q6FlsA/fTp00YCoEKFCgCcPXvWos21fdnY2LBo0SIjmXQ1T09PADw8PBg3bhyFhYXs3r2bmTNn8vrrr7N69WqqV6+Oq6srLVu25MUXX7Too1SpUgBs2rSJ7OxsEhISjCVj+fn5xS7turY20s3G5F4rGseEhAQqVapksb9atWp33Pf17s2199TV1ZW8vDyzRBFcuYc2NjbFLpW7U15eXmzbtg2TyWQ29ocOHeKJJ54AoGTJklSuXNmiXtShQ4cwmUxGfami/x46dIgnn3zSaHfw4EGqVKmCk5OT0e7XX38168tkMnHo0CGzAu4iIiIiIiIiD6JHYrlfyZIlqVevHsuXLzfeJHe1Bg0acPHiRTZv3mxsy8/PZ+3atTRo0ACASpUqUaFCBYs3wa1Zs8bsc9GSt/Pnz+Pn52fxVbp0abP2tra2+Pv7M2zYMPLz842lf0FBQezfvx8fHx+LPoqSFrm5udjY2FCixP/lGletWmX25r07HZN7LTAwEGdnZ9LT04sdl6Li6kWJvWtnat1I0b1Zt26d2fa1a9eafS66l6tXrzbbvnr1anx8fO7ZLCq4UusqMzOTbdu2GdsOHTrEnj17aNGihVm7devWkZeXZ2xbuXIlLi4uBAYGAldqkZUuXZpVq1YZbfLy8vj2228t+vrf//5nttRz27ZtnD9/3qgVJiIiIiIiIvKgeiRmUgEMHz6cvn370rdvX1588UVcXV3573//S7ly5ejatSv+/v688cYbDB8+3Hi7X0ZGBtOmTQPAzs6OgQMH8sEHH+Dm5kZwcDBbtmwhNTXV7Dyenp706tWLESNGEBERQUBAAHl5eRw+fJjU1FRmzJjBhQsXiIiIoHPnznh6epKXl8f8+fONmlMAQ4cOpXv37kRERPDCCy/g7u7O6dOn+eGHH3jqqafo1KmTsaQwJiaGsLAw9u/fT0pKikUh7jsZk+7du9/D0b+yFG3o0KFMmjSJ9PR0GjVqhJ2dHUeOHGHdunXEx8fj7OxMrVq1AFi4cCHPPPMMTk5OeHt737Dvonvzr3/9C3d3dxo3bkxqaqqRILK1vZKLffLJJ2nTpg3jx48nNzcXT09PvvrqK9LS0pgxY8YtX8tvv/3Gb7/9Znz+9ddfWb16Nc7OzkYyKDAwkGbNmvHmm28ycuRIHB0dmTJlCt7e3saSToCIiAi+/vprhg8fTs+ePfn1119JTk7m9ddfN5ZPOjo6EhkZSXx8POXLl+eJJ57g008/5fz580RERBh9tW3blsTERIYMGUJUVBQ5OTlMnDiRVq1a4e/vf8vXVxxXT1+cylvOgBMR+as4urrfvJGIiIiIPFQemSTVU089xbx584iLiyMmJgZbW1tq167NsGHDsLOzY9asWUycOJFJkyaRnZ1N3bp1mTNnDr6+vkYfvXv3Jisri0WLFvHpp58SFBREbGws/fv3NzvX6NGj8fT05LPPPmP69OmUKlUKT09Po2C3o6MjTzzxBPPnz+fEiRM4OTnh6+tLcnKyUVepZs2aLFmyxKhZlZ2dTYUKFWjYsKGRtPH29mbcuHEkJCQQGRlJnTp1mDp1KsOGDbvrMfkrhIeHU7FiRVJSUliwYAElSpSgRo0atGrVyphB5ePjw5AhQ1iyZAmzZ8+mcuXKrF+//qZ9X31v5s+fT1BQEG+88Qavv/662TK+SZMm8dFHH5GUlMT58+fx8vJi2rRpFkXXb2TVqlUkJCQYn7/88ku+/PJLqlatahZrXFwc48aN45133iE/P59mzZoxevRos5lvNWvWJDk5mfHjxzNw4EDKly/P0KFDCQ8PNzvngAEDMJlMzJkzh7Nnz1KnTh2Sk5OpXr260cbe3p7Zs2cTGxtLVFQUJUqUIDQ01OwNkHeqaotud92HiMjfSWFhofFLDhERERG5P2xMJpPJ2kGI/BXi4uJISUkhNTXVqNskt+eXX34BYOvZNI5nnrRyNCIi90cV14pEtnzJ2mE80LKzs9m7dy916tS5p8vl5e9Hz4KIiNxLj8xMKnm4HThwgK+++orAwEDs7e354YcfSE5OpmfPnkpQ3QPHM0/y+5mj1g5DREREREREHmJKUskDr6CggBtN+CtRogROTk6kpaXx6aef8ueff1KxYkUiIiIYMmTIbZ3rRkXnbWxssLOzu63+REREREREROTWKEklD7zQ0FCOHTt23f379u2jatWqzJs3767Oc/ToUUJCQq67v1GjRsyfP/+uziEiIiIiIiIixVOSSh54M2fO5PLly3/5eTw8PFi6dOl195cqVeovj0FERERERETkUaUklTzwit5m+FdzcHDAz8/vvpxLRERERERERMzp3coiIiIiIiIiImJ1SlKJiIiIiIiIiIjVKUklIiIiIiIiIiJWp5pUInJTVVwrWjsEEZH7Rt/zRERERKxDSSoRuanIli9ZOwQRkfuqsLAQW1tNOBcRERG5n/SvLxG5ocuXL5OTk2PtMMTKcnJy2LNnj56FR9yj9BwoQSUiIiJy/+lfYCJyUyaTydohiJWZTCZycnL0LDzi9ByIiIiIyF9JSSoREREREREREbE6JalERERERERERMTqlKQSERERERERERGrU5JKRERERERERESsTkkqERERERERERGxOiWpROSmbGxsrB2CWJmNjQ3Ozs56FkRERERE5C9TwtoBiMiDzcHBAWdnZ2uHIVbm7OyMj4+PtcMQKyosNClJKSIiIiJ/KSWpROSmpn+6hWMZmdYOQ0SspKqHK6/2DLZ2GCIiIiLykFOSSkRu6lhGJoePnbN2GCIiIiIiIvIQU00qERERERERERGxOiWpRERERERERETE6pSkEhERERERERERq1OSSkRERERERERErE5JKhERERERERERsTolqURERERERERExOqUpBIREREREREREasrYe0A/o727t1Lly5dmDdvHo0bN7Z2OPfU0aNHCQkJYerUqbRr1+6+nHPv3r2sXbuW/v374+zsfMvH9e7dm5IlS5KYmPgXRnfnFi5cyMaNG9m5cyfnzp277piePHmS2NhYNm/ejL29PaGhocTExFC6dGmzduvXrycuLo5Dhw5RpUoVBg4cyPPPP2/W5vLly0yZMoWvvvqKP//8k8DAQN5++228vLzu6loCvKtQpYLLXfUhIn9fFcpf+X5kY2ODs7MzNjY2Vo5IRERERB5GSlKJ1e3du5eEhAR69ep1W0mqd999F1vbB3cy4PLlywFo2bIlX375ZbFt8vLy6N+/PwAffvghubm5TJgwgeHDh5sl33788UcGDx5M9+7defPNN9m+fTtvvfUWpUqVMkt8xcbGsnLlSkaNGkXFihX5+OOP6du3LytWrKBMmTJ3fC092tW742NF5OHh5OSEj4+PtcN4pJgKC7F5gP9fJyIiInIvKUn1iDCZTOTl5eHg4GDtUO6Zxx9/3Noh3NDixYuxtbXl6NGj101SrVmzhv3797Ny5UpjtpOLiwsRERHs2rULf39/AGbOnIm/vz/vvfceAE2aNOHIkSNMmzbNSFKlp6ezdOlS3n33Xbp37w6An58fTz/9NIsXL2bAgAF3fC2Hvkki58yJOz5eRERun7NbZTw73fn3bhEREZG/m4c+SZWWlkZ8fDw///wzJpOJxx9/nGHDhhEcHMz58+eZMGEC69evJycnBx8fH4YPH07Dhg3N+pgxYwYLFy4kOzub4OBgwsLCLM5jMpmYM2cOn3/+OceOHaNixYr07t2bvn37Gm3S09MZN24c//nPf7hw4QIVKlTgmWee4c033zTaHDhwgMmTJ/PDDz9QUFBAo0aNGD16NDVq1DDazJkzhxUrVnD48GEcHBzw9/dn1KhReHp6Gm1GjRrF7t27eeONN/jwww85ePAgkydPpl27djcckyKXLl3ivffe4+uvv8bR0ZFnn32W4cOHU6LElUcmPj6eOXPmsHjxYsaMGcOePXuoXr06I0eOpHnz5mZjs2zZMlJSUjh8+DBly5alW7duDB06FDs7O5YtW0ZMTAwAQUFBAFStWpX169ff9N5eu9zvwIEDJCQksGPHDs6fP0/VqlXp3r07ffv2NWZctW7dmlatWvHOO++Y9TVhwgRWrFjB999/j62tLZMnT2bDhg0cPXqU0qVL07BhQ0aNGoWHh8dN4ypyK7O8Nm7ciLe3t9lyvODgYMqWLcuGDRvw9/fn8uXLpKamEh0dbXZshw4d+Oabbzh69CjVqlVj8+bNFBYWms2sKlu2LMHBwWzcuPGuklQ5Z06Qc/KPOz5eRERERERE5GYe6iTVTz/9RJ8+fahXrx6xsbG4uLiwe/dujh8/TkFBAQMGDODIkSNER0fj7u7O/Pnz6devH4sXL8bX1xeABQsWMHXqVMLDw2natClbt27lrbfesjjXBx98wJIlS3j55ZcJCAhgx44dTJ48GUdHR3r27AnAiBEjyMjIYPTo0bi5uXHixAl2795t9HHkyBHCwsKoXbs248ePx8bGxliutXr1amMWVHp6Ov/85z+pUqUKFy9eZPHixYSFhbFmzRrKli1r9JeRkUFsbCyDBg2icuXKVKlS5YZjcrW4uDhCQkKIi4szklo1atQwrgWuLFWLjo7mpZde4pVXXiEpKYmhQ4eyfv16ypUrB0BKSgqTJk2iT58+jBo1igMHDjBlyhQKCgqIjo6mVatWDBo0iJkzZzJ79mzKlClzx7O9MjIy8PT05Nlnn6VUqVLs3buX+Ph4srOzGTx4MAAdO3Zk2bJlvPXWW9jZ2QFXEowrV66kQ4cORmLpzJkzREZG4uHhwdmzZ0lJSaF3796sWLHCSNTdCwcPHrSoF2VjY4OnpycHDx4E4I8//iAvL8+iXa1atYw+qlWrxsGDB3Fzc8PV1dWi3dKlS+9ZzCIiIiIiIiJ/hYc6STVp0iRq1qzJ3LlzjYREs2bNAFi3bh27du1i9uzZxsyfZs2a0aZNGxITE4mPj6egoIDExEQ6d+7MyJEjAWjevDlnzpwx6g3BlSTCggULGDt2LD169ACgadOm5ObmMn36dHr06IGtrS2//PILUVFRdOjQwTi2S5cuxp8TEhJwdXUlJSUFR0dHAOrXr09ISAhLliyhV69eAGYzrwoKCggODiYoKIg1a9YY5wfIzMwkKSmJgIAAY9vIkSOvOyZX8/f3Z/To0cCVmT2pqamsWbOm2CRVy5YtAfD09CQkJISNGzfSuXNnLl68yLRp0+jfvz9RUVFGX/b29owfP56IiAjKly9vzBKrW7cu5cuXv+E9vZGgoCBjNpbJZKJBgwbk5uayYMECsyTVrFmz2L59uzFz7McffyQ9PZ2OHTsafY0bN874c0FBAYGBgbRo0YLt27cXO153Kisrq9haUa6urmRmZgIY/3VxMS9cXvS5aP/1+nJxcTHaiIiIiIiIiDyoHtpKnDk5OezcuZMuXboYyZir/fjjj5QuXdpsaVrRm9V++ukn4MqMpYyMDEJDQ82Obdu2rdnnrVu3AtCmTRvy8/ONr6ZNm3Lq1ClOnLhSy8fHx4c5c+awaNEifv/9d4uYtmzZQuvWrbGzszP6cHFxwcfHx2zG1c8//0y/fv1o3LgxPj4+BAQEkJ2dzeHDh836K1u2rFmC6mZjcrVrEzG1atUiPT3dbJutra2RFAKoVq0aTk5OnDx5Eriy1DI7O5t27dpZjEtubi779++/YQy369KlS0ybNo3Q0FD8/PyoW7cuU6ZM4dSpU/z5558APPnkkzz++OOsWLHCOG7FihU89thj+Pn5Gds2bNhAWFgYDRo0wMfHhxYtWgBYjLGIiIiIiIiI3BsP7UyqrKwsCgsLr1tDKCsrCzc3N4vt7u7uxqyTU6dOAVjM7nF3dzf7fO7cOUwmE02aNCn2XCdOnKBq1apMmTKFKVOmEBcXx9ixY/H09CQqKoo2bdoY/cydO5e5c+da9GFvbw/A8ePHCQ8Px9fXl7Fjx+Lh4YG9vT2RkZFcunTphnHebEyudu2MHHt7ey5fvmy2zcnJyWJpnr29vRHHuXPnAOjatWux5yhK3t0rkyZNYsmSJbz66qv4+vpSpkwZ1q1bx8yZM7l06RKlSpUCrsymSklJYcyYMdja2lrMENu1axevvPIKISEhDBgwADc3N2xsbHjhhRcsxvhuubi4cPHiRYvtmZmZVK5cGcBYvnfhwgWzNllZWWb7r9dXVlaWxRJAERERERERkQfNQ5ukKlOmDLa2tmRkZBS739XVlTNnzlhsP336tPEDfYUKFQA4e/asRZtr+7KxsWHRokVGMulqRQXNPTw8GDduHIWFhezevZuZM2fy+uuvs3r1aqpXr46rqystW7bkxRdftOijKMGyadMmsrOzSUhIMJZ75efnF7ucy8bG5rbG5F4rGseEhAQqVapksb9atWr39HyrV6+mR48eDBw40Ni2YcMGi3YdO3Zk6tSpbNq0CQcHB86ePWu21G/t2rWULl2auLg4o0bVsWPH7mmsRby8vPj111/NtplMJg4dOmQsR6xRowb29vYcPHjQbOZfUc2qolpVXl5enD59mszMTLOkVHF1r0REREREREQeNA/tcr+SJUtSr149li9fTkFBgcX+Bg0acPHiRTZv3mxsy8/PZ+3atTRo0ACASpUqUaFCBb777juzY9esWWP2uWjJ2/nz5/Hz87P4Kl26tFl7W1tb/P39GTZsGPn5+cbSv6CgIPbv34+Pj49FH0VJhtzcXGxsbMyKd69atYr8/Py7HpN7LTAwEGdnZ9LT04sdl6Li6kWJvWtnat2uS5cumSUJCwoKzJb1FalZsyZ+fn6sWLGCFStWUKdOHaMIOVwZY3t7e7Mk39dff31XsV1PixYt+N///me2jHDbtm2cP3/eqPXl4OBA48aNLZ67lStXUqtWLSPZ16xZM2xtbfn222+NNpmZmWzevNlYrigiIiIiIiLyoHpoZ1IBDB8+nL59+9K3b19efPFFXF1d+e9//0u5cuXo2rUr/v7+vPHGGwwfPtx4u19GRgbTpk0DwM7OjoEDB/LBBx/g5uZGcHAwW7ZsITU11ew8np6e9OrVixEjRhAREUFAQAB5eXkcPnyY1NRUZsyYwYULF4iIiKBz5854enqSl5fH/PnzjZpTAEOHDqV79+5ERETwwgsv4O7uzunTp/nhhx946qmn6NSpk7GkMCYmhrCwMPbv309KSopFUe07GZPu3bvfw9G/svxs6NChTJo0ifT0dBo1aoSdnR1Hjhxh3bp1xMfH4+zsbCSIFi5cyDPPPIOTkxPe3t63fb6mTZuyZMkSHn/8ccqVK8eiRYuum/jq1KkTU6dOxc7OjpdfftlsX3BwMHPnzuX9998nNDSUtLQ0s0L5t+qXX37h2LFjxky8nTt3AleWjzZq1Ai4Ut8sMTGRIUOGEBUVRU5ODhMnTqRVq1b4+/sbfQ0aNIiXXnqJMWPG0L59e1JTU/nmm2+YMmWK0aZSpUp0796diRMnYmtrS8WKFUlMTKRMmTKEhYXddvwiIiIiIiIi99NDnaR66qmnmDdvHnFxccTExGBra0vt2rUZNmwYdnZ2zJo1i4kTJzJp0iSys7OpW7cuc+bMwdfX1+ijd+/eZGVlsWjRIj799FOCgoKIjY2lf//+ZucaPXo0np6efPbZZ0yfPp1SpUrh6elJu3btAHB0dOSJJ55g/vz5nDhxAicnJ3x9fUlOTjZqXtWsWZMlS5YYNauys7OpUKECDRs2NJI23t7ejBs3joSEBCIjI6lTpw5Tp05l2LBhdz0mf4Xw8HAqVqxISkoKCxYsoESJEtSoUYNWrVoZs558fHwYMmQIS5YsYfbs2VSuXJn169ffUv9Xz3Z6++23effdd3n//fdxdnama9euhIaGGm8pvFr79u2ZMGECJpPJbKkfQMuWLYmOjmbBggUsW7aM+vXrk5iYaFEw/2YWLlzIF198YXyeM2cOAI0aNWL+/PnAlVlks2fPJjY2lqioKEqUKEFoaKjZGxzhyn2Lj48nLi6OpUuXUqVKFWJjY2nfvr1Zu9GjR1OqVCk+/PBD/vzzT+rXr09KSkqxb/27Ha6evjiVt1yyKSIifx1HV/ebNxIRERF5iNiYTCaTtYMQuRPdunWjdu3aTJgwwdqhPLR++eUXALM3H4qIyIOnsLDQqKN4P2RnZ7N3717q1KlDyZIl79t55cGjZ0FERO6lh3omlTycTp06xfbt29m3bx/dunWzdjiPhMQN8zieedLaYYiISDGquFYksuVL1g5DRERE5K4pSSUPpIKCAq43ye+rr77i448/pkOHDve8jtatMJlMNyw8b2tre19/m30/HM88ye9njlo7DBEREREREXmIKUklD6TQ0FCOHTt23f379u27j9GY++GHH3jppev/xrpr166MHz/+PkYkIiIiIiIi8venJJU8kGbOnHndN/NZW926dVm6dOl195crV+4+RiMiIiIiIiLycFCSSh5IRW8zfBCVLl1ahcRFRERERERE7rGHq3COiIiIiIiIiIj8LSlJJSIiIiIiIiIiVqcklYiIiIiIiIiIWJ2SVCIiIiIiIiIiYnUqnC4iN1XFtaK1QxARkevQ92gRERF5WChJJSI3FdnyJWuHICIiN1BYWIitrSbIi4iIyN+b/jUjIjd0+fJlcnJyrB2GWFlOTg579uzRs/CI03Pw4FKCSkRERB4G+heNiNyUyWSydghiZSaTiZycHD0Ljzg9ByIiIiLyV1KSSkRERERERERErE5JKhERERERERERsTolqURERERERERExOqUpBIREREREREREatTkkpEbsrGxsbaIYiV2djY4OzsrGdBRERERET+MiWsHYCIPNgcHBxwdna2dhhiZc7Ozvj4+Fg7jAdWYaEJW1sl8ERERERE7oaSVCJyU9M/3cKxjExrhyHyQKrq4cqrPYOtHYaIiIiIyN+eklQiclPHMjI5fOyctcMQERERERGRh5hqUomIiIiIiIiIiNUpSSUiIiIiIiIiIlanJJWIiIiIiIiIiFidklQiIiIiIiIiImJ1SlKJiIiIiIiIiIjVKUklIiIiIiIiIiJWpySViIiIiIiIiIhYXQlrB/Ag2bt3L126dGHevHk0btzY2uHcU0ePHiUkJISpU6fSrl27+3LOvXv3snbtWvr374+zs/MtH9e7d29KlixJYmLiXxjdnVu4cCEbN25k586dnDt37rpjevLkSWJjY9m8eTP29vaEhoYSExND6dKlzdqtX7+euLg4Dh06RJUqVRg4cCDPP/+8WZvLly8zZcoUvvrqK/78808CAwN5++238fLyMmt34MABYmNjSUtLo1SpUnTu3Jlhw4bh4OBwV9cc4F2FKhVc7qoPkYdVhfKlb97oIWFjY4OzszM2NjbWDkVEREREHkJKUslfZu/evSQkJNCrV6/bSlK9++672No+uJP8li9fDkDLli358ssvi22Tl5dH//79Afjwww/Jzc1lwoQJDB8+3Cz59uOPPzJ48GC6d+/Om2++yfbt23nrrbcoVaqUWeIrNjaWlStXMmrUKCpWrMjHH39M3759WbFiBWXKlAEgMzOTPn368NhjjxEfH8/JkycZP348ubm5vPPOO3d1zT3a1bur40Xk4eDs7IyPj4+1w7gpU2EhNg/w/0dEREREpHhKUv3NmUwm8vLy7nqmzIPk8ccft3YIN7R48WJsbW05evTodZNUa9asYf/+/axcudKY7eTi4kJERAS7du3C398fgJkzZ+Lv7897770HQJMmTThy5AjTpk0zklTp6eksXbqUd999l+7duwPg5+fH008/zeLFixkwYIAR159//klCQgJly5YFoKCggLFjxxIZGUnFihXv+JoPfZNEzpkTd3y8iMj94uxWGc9OA6wdhoiIiIjcgb9tkiotLY34+Hh+/vlnTCYTjz/+OMOGDSM4OJjz588zYcIE1q9fT05ODj4+PgwfPpyGDRua9TFjxgwWLlxIdnY2wcHBhIWFWZzHZDIxZ84cPv/8c44dO0bFihXp3bs3ffv2Ndqkp6czbtw4/vOf/3DhwgUqVKjAM888w5tvvmm0OXDgAJMnT+aHH36goKCARo0aMXr0aGrUqGG0mTNnDitWrODw4cM4ODjg7+/PqFGj8PT0NNqMGjWK3bt388Ybb/Dhhx9y8OBBJk+eTLt27W44JkUuXbrEe++9x9dff42joyPPPvssw4cPp0SJK49CfHw8c+bMYfHixYwZM4Y9e/ZQvXp1Ro4cSfPmzc3GZtmyZaSkpHD48GHKli1Lt27dGDp0KHZ2dixbtoyYmBgAgoKCAKhatSrr16+/6b29drnfgQMHSEhIYMeOHZw/f56qVavSvXt3+vbta8y4at26Na1atbKYMTRhwgRWrFjB999/j62tLZMnT2bDhg0cPXqU0qVL07BhQ0aNGoWHh8dN4ypyK7O8Nm7ciLe3t9lyvODgYMqWLcuGDRvw9/fn8uXLpKamEh0dbXZshw4d+Oabbzh69CjVqlVj8+bNFBYWms2sKlu2LMHBwWzcuNFIUm3cuJGgoCAjQQXQvn173n33XbZs2UK3bt1u+RqvlXPmBDkn/7jj40VERERERERu5m+ZpPrpp5/o06cP9erVIzY2FhcXF3bv3s3x48cpKChgwIABHDlyhOjoaNzd3Zk/fz79+vVj8eLF+Pr6ArBgwQKmTp1KeHg4TZs2ZevWrbz11lsW5/rggw9YsmQJL7/8MgEBAezYsYPJkyfj6OhIz549ARgxYgQZGRmMHj0aNzc3Tpw4we7du40+jhw5QlhYGLVr12b8+PHY2NgYy7VWr15tzIJKT0/nn//8J1WqVOHixYssXryYsLAw1qxZY5Z4yMjIIDY2lkGDBlG5cmWqVKlywzG5WlxcHCEhIcTFxRlJrRo1ahjXAleWqkVHR/PSSy/xyiuvkJSUxNChQ1m/fj3lypUDICUlhUmTJtGnTx9GjRrFgQMHmDJlCgUFBURHR9OqVSsGDRrEzJkzmT17NmXKlLnj2V4ZGRl4enry7LPPUqpUKfbu3Ut8fDzZ2dkMHjwYgI4dO7Js2TLeeust7OzsgCsJxpUrV9KhQwcjsXTmzBkiIyPx8PDg7NmzpKSk0Lt3b1asWGEk6u6FgwcPWtSLsrGxwdPTk4MHDwLwxx9/kJeXZ9GuVq1aRh/VqlXj4MGDuLm54erqatFu6dKlZue8tpaVi4sLFSpUMM4pIiIiIiIi8qD6WyapJk2aRM2aNZk7d66RkGjWrBkA69atY9euXcyePduY+dOsWTPatGlDYmIi8fHxFBQUkJiYSOfOnRk5ciQAzZs358yZM0a9IbiSRFiwYAFjx46lR48eADRt2pTc3FymT59Ojx49sLW15ZdffiEqKooOHToYx3bp0sX4c0JCAq6urqSkpODo6AhA/fr1CQkJYcmSJfTq1QvAbOZVQUEBwcHBBAUFsWbNGuP8cKX2UFJSEgEBAca2kSNHXndMrubv78/o0aOBKzN7UlNTWbNmTbFJqpYtWwLg6elJSEgIGzdupHPnzly8eJFp06bRv39/oqKijL7s7e0ZP348ERERlC9f3pglVrduXcqXL3/De3ojQUFBxmwsk8lEgwYNyM3NZcGCBWZJqlmzZrF9+3Zj5tiPP/5Ieno6HTt2NPoaN26c8eeCggICAwNp0aIF27dvL3a87lRWVpZRK+pqrq6uZGZmAhj/dXExL0he9Llo//X6cnFxMdoUtbu2r2vPKSIiIiIiIvKg+ttVFc3JyWHnzp106dLFSMZc7ccff6R06dJmS9OK3qz2008/AVdmLGVkZBAaGmp2bNu2bc0+b926FYA2bdqQn59vfDVt2pRTp05x4sSVGj0+Pj7MmTOHRYsW8fvvv1vEtGXLFlq3bo2dnZ3Rh4uLCz4+PmYzrn7++Wf69etH48aN8fHxISAggOzsbA4fPmzWX9myZc0SVDcbk6tdm4ipVasW6enpZttsbW2NpBBAtWrVcHJy4uTJk8CVpZbZ2dm0a9fOYlxyc3PZv3//DWO4XZcuXWLatGmEhobi5+dH3bp1mTJlCqdOneLPP/8E4Mknn+Txxx9nxYoVxnErVqzgsccew8/Pz9i2YcMGwsLCaNCgAT4+PrRo0QLAYoxFRERERERE5P76282kysrKorCw8Lo1hLKysnBzc7PY7u7ubswmOXXqFIDF7B53d3ezz+fOncNkMtGkSZNiz3XixAmqVq3KlClTmDJlCnFxcYwdOxZPT0+ioqJo06aN0c/cuXOZO3euRR/29vYAHD9+nPDwcHx9fRk7diweHh7Y29sTGRnJpUuXbhjnzcbkatfOyLG3t+fy5ctm25ycnCyW5tnb2xtxnDt3DoCuXbsWe46i5N29MmnSJJYsWcKrr76Kr68vZcqUYd26dcycOZNLly5RqlQp4MpsqpSUFMaMGYOtra3FDLFdu3bxyiuvEBISwoABA3Bzc8PGxoYXXnjBYozvlouLCxcvXrTYnpmZSeXKlQGM5XsXLlwwa5OVlWW2/3p9ZWVlmS0BdHFxseir6JzXLhUUERERERERedD87ZJUZcqUwdbWloyMjGL3u7q6cubMGYvtp0+fNn5Qr1ChAgBnz561aHNtXzY2NixatMhIJl2tqKC5h4cH48aNo7CwkN27dzNz5kxef/11Vq9eTfXq1XF1daVly5a8+OKLFn0UJVg2bdpEdnY2CQkJxpKt/Pz8Ypdp2djY3NaY3GtF45iQkEClSpUs9lerVu2enm/16tX06NGDgQMHGts2bNhg0a5jx45MnTqVTZs24eDgwNmzZ82W+q1du5bSpUsTFxdn1Kg6duzYPY21iJeXF7/++qvZNpPJxKFDh4zliDVq1MDe3p6DBw+azfwrqh9VVKvKy8uL06dPWySbrq175eXlZVF76sKFC5w6dcqi7pWIiIiIiIjIg+Zvt9yvZMmS1KtXj+XLl1NQUGCxv0GDBly8eJHNmzcb2/Lz81m7di0NGjQAoFKlSlSoUIHvvvvO7Ng1a9aYfS5a8nb+/Hn8/PwsvkqXLm3W3tbWFn9/f4YNG0Z+fr6x9C8oKIj9+/fj4+Nj0UdR8iA3NxcbGxuz4t2rVq0iPz//rsfkXgsMDMTZ2Zn09PRix6WouHpRYu/amVq369KlS2ZJwoKCArNlfUVq1qyJn58fK1asYMWKFdSpU8coQg5Xxtje3t4syff111/fVWzX06JFC/73v/+ZLSPctm0b58+fN2p9OTg40LhxY4vnbuXKldSqVctI9jVr1gxbW1u+/fZbo01mZiabN282lisWnXPr1q3GTCy4kuCztbU1e8OjiIiIiIiIyIPobzeTCmD48OH07duXvn378uKLL+Lq6sp///tfypUrR9euXfH39+eNN95g+PDhxtv9MjIymDZtGgB2dnYMHDiQDz74ADc3N4KDg9myZQupqalm5/H09KRXr16MGDGCiIgIAgICyMvL4/Dhw6SmpjJjxgwuXLhAREQEnTt3xtPTk7y8PObPn2/UnAIYOnQo3bt3JyIighdeeAF3d3dOnz7NDz/8wFNPPUWnTp2MJYUxMTGEhYWxf/9+UlJSii2Efbtj0r1793s4+leWlQ0dOpRJkyaRnp5Oo0aNsLOz48iRI6xbt474+HicnZ2NBNHChQt55plncHJywtvb+7bP17RpU5YsWcLjjz9OuXLlWLRo0XUTX506dWLq1KnY2dnx8ssvm+0LDg5m7ty5vP/++4SGhpKWlmZWKP9W/fLLLxw7dsyYibdz507gyvLRRo0aAVfqmyUmJjJkyBCioqLIyclh4sSJtGrVCn9/f6OvQYMG8dJLLzFmzBjat29Pamoq33zzDVOmTDHaVKpUie7duzNx4kRsbW2pWLEiiYmJlClThrCwMKNdWFgY8+fP59VXXyUyMpKTJ08yceJEwsLCqFix4m1fp4iIiIiIiMj99LdMUj311FPMmzePuLg4YmJisLW1pXbt2gwbNgw7OztmzZrFxIkTmTRpEtnZ2dStW5c5c+bg6+tr9NG7d2+ysrJYtGgRn376KUFBQcTGxtK/f3+zc40ePRpPT08+++wzpk+fTqlSpfD09KRdu3YAODo68sQTTzB//nxOnDiBk5MTvr6+JCcnGzWvatasyZIlS4yaVdnZ2VSoUIGGDRsaSRtvb2/GjRtHQkICkZGR1KlTh6lTpzJs2LC7HpO/Qnh4OBUrViQlJYUFCxZQokQJatSoQatWrYxZTz4+PgwZMoQlS5Ywe/ZsKleuzPr162+p/6tnO7399tu8++67vP/++zg7O9O1a1dCQ0ONtxRerX379kyYMAGTyWS21A+gZcuWREdHs2DBApYtW0b9+vVJTEy0KJh/MwsXLuSLL74wPs+ZMweARo0aMX/+fODKLLLZs2cTGxtLVFQUJUqUIDQ01OwNjnDlvsXHxxMXF8fSpUupUqUKsbGxtG/f3qzd6NGjKVWqFB9++CF//vkn9evXJyUlxazGmKurq5GEe/XVVylVqhTdu3fn9ddfv63rK46rpy9O5S2XdoqIPGgcXd1v3khEREREHkg2JpPJZO0gRK7WrVs3ateuzYQJE6wdyiPvl19+ATB7Q6KIyN9dYWGhUZtQ7kx2djZ79+6lTp06lCxZ0trhiBXpWRARkXvpbzmTSh5Op06dYvv27ezbt49u3bpZOxy5SuKGeRzPPGntMERE7loV14pEtnzJ2mGIiIiISDGUpJL7qqCggOtN3vvqq6/4+OOP6dChwz2vo3UrTCbTDQvP29raPrK/eT+eeZLfzxy1dhgiIiIiIiLyEFOSSu6r0NBQjh07dt39+/btu4/RmPvhhx946aXr/3a9a9eujB8//j5GJCIiIiIiIvLoUJJK7quZM2de98181la3bl2WLl163f3lypW7j9GIiIiIiIiIPFqUpJL7quhthg+i0qVLq0C4iIiIiIiIiJU8mgV2RERERERERETkgaIklYiIiIiIiIiIWJ2SVCIiIiIiIiIiYnVKUomIiIiIiIiIiNWpcLqI3FQV14rWDkFE5J7Q9zMRERGRB5eSVCJyU5EtX7J2CCIi90xhYSG2tppMLiIiIvKg0b/QROSGLl++TE5OjrXDECvLyclhz549ehYecQ/Lc6AElYiIiMiDSf9KE5GbMplM1g5BrMxkMpGTk6Nn4RGn50BERERE/kpKUomIiIiIiIiIiNUpSSUiIiIiIiIiIlanJJWIiIiIiIiIiFidklQiIiIiIiIiImJ1SlKJyE3Z2NhYOwSxMhsbG5ydnfUsPOL0HIiIiIjIX6mEtQMQkQebg4MDzs7O1g5DrMzZ2RkfHx9rhyFWdr+fg8JCE7a2SoiJiIiIPCqUpBKRm5r+6RaOZWRaOwwReYRU9XDl1Z7B1g5DRERERO4jJalE5KaOZWRy+Ng5a4chIiIiIiIiDzHVpBIREREREREREatTkkpERERERERERKxOSSoREREREREREbE6JalERERERERERMTqlKQSERERERERERGrU5JKRERERERERESsTkkqERERERERERGxuhLWDkBEHnwB3lWoUsHF2mGIyCOkQvnS1g5BRERERO6zByZJtXfvXrp06cK8efNo3LixtcO5p44ePUpISAhTp06lXbt29+Wce/fuZe3atfTv3x9nZ+dbPq53796ULFmSxMTEvzA662jdujWtWrXinXfeua3jPvnkEzw9PWnZsuVtHTd9+nR+/PFHfvnlFy5cuMDSpUvx8/OzaHfgwAFiY2NJS0ujVKlSdO7cmWHDhuHg4GDWbsmSJcyePZvjx4/j6enJ66+/ztNPP23W5sKFC4wbN461a9eSl5dH8+bNGT16NB4eHrcV+7V6tKt3V8eLiMidMxUWYmOrye8iIiLy8HtgklRyb+3du5eEhAR69ep1W0mqd999F9uH9B/CCQkJuLjc/mygefPm0apVq9tOUn322WfUqFGDpk2bsmbNmmLbZGZm0qdPHx577DHi4+M5efIk48ePJzc31yyZtmLFCt5++21efvllmjRpwsqVKxk8eDALFy6kXr16Rrthw4bx22+/MWbMGBwdHYmLi2PAgAH8+9//pkSJO//rfuibJHLOnLjj40VE5M44u1XGs9MAa4chIiIicl8oSXUXTCYTeXl5FjNe/s4ef/xxa4dwz+Xm5uLk5ISPj899Pe/333+Pra0tqamp101SLV68mD///JOEhATKli0LQEFBAWPHjiUyMpKKFSsCMG3aNDp27MiwYcMAaNKkCb/++ivTp08nKSkJgLS0NDZv3kxycjLNmjUDwNPTkw4dOvDtt9/SoUOHO76WnDMnyDn5xx0fLyIiIiIiInIzdzRlJi0tjfDwcOrXr09gYCD/+Mc/2LJlCwDnz58nJiaGxo0b4+/vT1hYGP/5z38s+pgxYwbBwcEEBgYyePBgzpw5Y9HGZDKRnJxM27Zt8fX1JSQkhE8++cSsTXp6Oq+99hpNmzbFz8+P1q1b869//cuszYEDBxg0aBANGjSgXr16DBw4kD/+MP+Be86cOTz//PM0aNCAoKAgIiMjOXTokFmbUaNG0alTJzZs2MBzzz2Hn58f69evv+mYFLl06RLvvfceDRs2pFmzZkyYMIH8/Hxjf3x8PIGBgezbt4+ePXsSEBBAp06d2LRpk8XYLFu2jGeffRY/Pz+aN2/OlClTKCgoMPbFxMQAEBQUhLe3N61bt7boozi9e/cmMjLSbOxef/11WrZsSUBAAB06dGDOnDkUFhYabVq3bs17771n0deECRNo0aKF0Xby5Mk8++yzBAYG0rx5c6KiosjIyDA75qeffqJXr140aNCAwMBAnn32Wb744guzNt9//z1hYWEEBATQsGFDevfuzZ49ewBITU3F29ub77//nqFDh1K/fn1ee+21YuO8+n526tQJPz8/unXrxs8//2x2bceOHWPhwoV4e3vj7e3NsmXLbmksb2VG2saNGwkKCjISVADt27ensLDQeH6OHDnC4cOHad++vdmxHTp0YNu2bVy+fNnoy8XFheDgYKONl5cXderUYePGjbcUs4iIiIiIiIi13PZMqp9++ok+ffpQr149YmNjcXFxYffu3Rw/fpyCggIGDBjAkSNHiI6Oxt3dnfnz59OvXz8WL16Mr68vAAsWLGDq1KmEh4fTtGlTtm7dyltvvWVxrg8++IAlS5bw8ssvExAQwI4dO5g8eTKOjo707NkTgBEjRpCRkcHo0aNxc3PjxIkT7N692+jjyJEjhIWFUbt2bcaPH4+NjQ0ff/wxffv2ZfXq1cYsqPT0dP75z39SpUoVLl68yOLFiwkLC2PNmjVmCYSMjAxiY2MZNGgQlStXpkqVKjcck6vFxcUREhJCXFwcaWlpxMfHU6NGDeNaAPLy8oiOjuall17ilVdeISkpiaFDh7J+/XrKlSsHQEpKCpMmTaJPnz6MGjWKAwcOGEmq6OhoWrVqxaBBg5g5cyazZ8+mTJkydzzbKyMjA09PT5599llKlSrF3r17iY+PJzs7m8GDBwPQsWNHli1bxltvvYWdnR1wJcG4cuVKOnToYCRrzpw5Q2RkJB4eHpw9e5aUlBR69+7NihUrKFGiBBcvXiQyMpIGDRrw0Ucf4eDgwG+//UZWVpYRz8qVK4mKiiIkJIQPP/wQe3t7duzYwcmTJ81mSr399ts899xzTJ8+/YbJolOnTjF27FiGDBmCi4sLSUlJRERE8O233+Lm5kZCQgIDBw6kfv36hIeHA1CjRo07GsviHDx4kOeff95sm4uLCxUqVODgwYNGG7gyK+pqtWrVIi8vjyNHjlCrVi0OHjyIp6cnNjY2Zu28vLyMPkREREREREQeVLedpJo0aRI1a9Zk7ty5RkKiaGnRunXr2LVrF7Nnz6Z58+bGvjZt2pCYmEh8fDwFBQUkJibSuXNnRo4cCUDz5s05c+YMy5cvN87zxx9/sGDBAsaOHUuPHj0AaNq0Kbm5uUyfPp0ePXpga2vLL7/8QlRUlNlSpi5duhh/TkhIwNXVlZSUFBwdHQGoX78+ISEhLFmyhF69egHw5ptvGscUFBQQHBxMUFAQa9asMc4PV2oIJSUlERAQYGwbOXLkdcfkav7+/owePRqA4OBgYxlYcUmqovpHnp6ehISEsHHjRjp37szFixeZNm0a/fv3JyoqyujL3t6e8ePHExERQfny5Y1ESt26dSlfvvwN7+mNBAUFERQUBFxJPDVo0IDc3FwWLFhglqSaNWsW27dvN2bx/Pjjj6Snp9OxY0ejr3Hjxhl/LigoIDAwkBYtWrB9+3aaNWvGoUOHuHDhAlFRUXh7exvnL2IymZgwYQLBwcFMnz7d2F5crajWrVvzxhtv3PT6zp8/T1xcnHGeRo0a0bJlSz755BOGDx+Oj48PDg4OuLu7m9V+uleysrKKrZPl6upKZmYmgPHfa9sVfS7an5WVRZkyZYrt6+rErYiIiIiIiMiD6LaW++Xk5LBz5066dOliJGOu9uOPP1K6dGkjQQVgb29PaGgoP/30E3BlxlJGRgahoaFmx7Zt29bs89atWwFo06YN+fn5xlfTpk05deoUJ05cKeLs4+PDnDlzWLRoEb///rtFTFu2bKF169bY2dkZfbi4uODj42P2g/vPP/9Mv379aNy4MT4+PgQEBJCdnc3hw4fN+itbtqxZgupmY3K1axNXtWrVIj093Wybra2tWWKmWrVqODk5cfLkSeDKssLs7GzatWtnMS65ubns37//hjHcrkuXLjFt2jRCQ0Px8/Ojbt26TJkyhVOnTvHnn38C8OSTT/L444+zYsUK47gVK1bw2GOPmb3NbsOGDYSFhdGgQQN8fHxo0aIFgDHGNWrUoHTp0owZM4aVK1dy9uxZs1gOHjxIenq6xcyj4rRq1eqWrq9MmTJm412mTBmaNm3Kzp07b+l4EREREREREbk3bmsmVVZWFoWFhdd9nX1WVhZubm4W293d3Y3ZHqdOnQKwmN3j7u5u9vncuXOYTCaaNGlS7LlOnDhB1apVmTJlClOmTCEuLo6xY8fi6elJVFQUbdq0MfqZO3cuc+fOtejD3t4egOPHjxMeHo6vry9jx47Fw8MDe3t7IiMjuXTp0g3jvNmYXO3aWS729vZGPaEiTk5OFkvz7O3tjTjOnTsHQNeuXYs9R1Hy7l6ZNGkSS5Ys4dVXX8XX15cyZcqwbt06Zs6cyaVLlyhVqhRwZTZVSkoKY8aMwdbW1mKG2K5du3jllVcICQlhwIABuLm5YWNjwwsvvGBcW9GMt2nTpjFixAgKCgp46qmnGD16NN7e3pw/fx7glsa6uOewOMXNMnNzc+PAgQO3dPzdcnFx4cKFCxbbMzMzcXV1BTD+e+HCBSpUqGC0KVoGWbTfxcXFIul5bV8iIiIiIiIiD6rbSlKVKVMGW1tbi2LXRVxdXYstgH769Gnjh+SiH7KvnSVz+vRpi75sbGxYtGiRkUy6WlF9Hg8PD8aNG0dhYSG7d+9m5syZvP7666xevZrq1avj6upKy5YtefHFFy36KEqwbNq0iezsbBISEowlVPn5+UZi7WrX1vu52Zjca0XjmJCQQKVKlSz2V6tW7Z6eb/Xq1fTo0YOBAwca2zZs2GDRrmPHjkydOpVNmzbh4ODA2bNnzZb6rV27ltKlSxMXF2fUiDp27JhFP/7+/syePZvc3FxSU1OZMGECr776KmvXrjVqg93KWF97n67n2ucQrtTOujoZ9Fcqrl7UhQsXOHXqFF5eXkYbuDKTrOjPRZ/t7e2pXr260W7btm2YTCaz6z906BBPPPHEX30pIiIiIiIiInfltpb7lSxZknr16rF8+XLjTXJXa9CgARcvXmTz5s3Gtvz8fNauXUuDBg0AqFSpEhUqVOC7774zO3bNmjVmn4uWYJ0/fx4/Pz+Lr9KlS5tfiK0t/v7+DBs2jPz8fGPpX1BQEPv378fHx8eij6If+HNzc7GxsaFEif/L2a1atcrszXt3Oib3WmBgIM7OzqSnpxc7LkXF1YsSe9fO1Lpdly5dMksSFhQUmC3rK1KzZk38/PxYsWIFK1asoE6dOtSqVcvYn5ubi729vVny5Ouvv77ueZ2cnGjZsiU9e/bk6NGjXLp0CS8vLypVqnTLb9e7FRcuXGDbtm1mn7du3Wq2pPPqmWz3WosWLdi6datZcfjVq1dja2tr1PeqXr06jz32GKtXrzY7duXKlQQFBRkz71q0aEFmZqbZ9Rw6dIg9e/YYSytFREREREREHlS3XTh9+PDh9O3bl759+/Liiy/i6urKf//7X8qVK0fXrl3x9/fnjTfeYPjw4cbb/TIyMpg2bRoAdnZ2DBw4kA8++AA3NzeCg4PZsmULqampZufx9PSkV69ejBgxgoiICAICAsjLy+Pw4cOkpqYyY8YMLly4QEREBJ07d8bT05O8vDzmz59v1JwCGDp0KN27dyciIoIXXngBd3d3Tp8+zQ8//MBTTz1Fp06djCWFMTExhIWFsX//flJSUootaH27Y9K9e/fbHeIbcnFxYejQoUyaNIn09HQaNWqEnZ0dR44cYd26dcTHx+Ps7GwkiBYuXMgzzzyDk5OTUYz8djRt2pQlS5bw+OOPU65cORYtWnTdxFenTp2YOnUqdnZ2vPzyy2b7goODmTt3Lu+//z6hoaGkpaWZFcoH+P7771m6dCnPPPMMVapU4fTp0yxYsID69esbRe9HjhxJVFQUQ4YMoXPnzjg4OPDzzz/j5+fH008/fdvXV7ZsWd566y2GDh1KmTJlSEpKwmQy0adPH6ONl5cX27dvZ8uWLbi4uFCtWjUjGXgjP/zwA2fPnuW3334DYPv27Rw7doyqVasatbrCwsKYP38+r776KpGRkZw8eZKJEycSFhZGxYoVjb6GDBlCdHQ0NWrUoHHjxqxcuZJdu3axYMECo01gYCDNmjXjzTffZOTIkTg6OjJlyhS8vb2N5a93ytXTF6fyljP3RETkr+Xo6n7zRiIiIiIPidtOUj311FPMmzePuLg4YmJisLW1pXbt2gwbNgw7OztmzZrFxIkTmTRpEtnZ2dStW5c5c+bg6+tr9NG7d2+ysrJYtGgRn376KUFBQcTGxtK/f3+zc40ePRpPT08+++wzpk+fTqlSpfD09KRdu3YAODo68sQTTzB//nxOnDiBk5MTvr6+JCcnG7WGatasyZIlS4yaVdnZ2VSoUIGGDRsaSRtvb2/GjRtHQkICkZGR1KlTh6lTpzJs2LC7HpO/Qnh4OBUrViQlJYUFCxZQokQJatSoQatWrYxZTz4+PgwZMoQlS5Ywe/ZsKleuzPr162+p/6tnO7399tu8++67vP/++zg7O9O1a1dCQ0ONtxRerX379kyYMAGTyWS21A+uvIEvOjqaBQsWsGzZMurXr09iYqJZwfwaNWpga2tLXFwcZ86coWzZsjRr1sx4iyFAhw4dcHJy4uOPPyYqKgpHR0d8fHwsCvHfqgoVKhAdHc3EiRP5448/qF27NsnJyWa1x6KiohgzZgxDhgzhzz//ZNy4cXTr1u2mfcfHx/PDDz8YnydPngxcqSc2fvx44MryzaLk3auvvkqpUqXo3r07r7/+ullfnTp1Iicnh6SkJGbNmoWnpycJCQkEBgaatYuLi2PcuHG888475Ofn06xZM0aPHm02S/BOVG1x8+sVEZG/j8LCQmP5vYiIiMiDwsZkMpmsHYQ8OLp160bt2rWZMGGCtUP5y40aNYrdu3fzzTffWDuUB9Yvv/wCwNazaRzPPGnlaERE5F6o4lqRyJYv3VUf2dnZ7N27lzp16lCyZMl7FJn8HelZEBGRe+nuplfIQ+PUqVNs376dffv23dIsIXm0HM88ye9njlo7DBEREREREXmIKUn1CCkoKOB6E+e++uorPv74Yzp06HDP62g9bEwm0w2L5Nva2moJhYiIiIiIiMhtUpLqERIaGsqxY8euu3/fvn33MRrrK6oLdbu++OILYmJirrt/8ODBDBky5E7DEhEREREREXkkKUn1CJk5c+Z138wnt+7pp59m6dKl193v4eFxH6MREREREREReTgoSfUIKXqbodydcuXKUa5cOWuHISIiIiIiIvJQUeEcERERERERERGxOiWpRERERERERETE6pSkEhERERERERERq1NNKhG5qSquFa0dgoiI3CP6ni4iIiIPKiWpROSmIlu+ZO0QRETkHiosLMTWVhPqRUSu5u3tzYgRI4iIiABg2bJl2Nvb8+yzz1o5MkvWii0+Pp7g4GDq169/07a7du1iypQp/Prrr2RmZuLu7k7Tpk157bXXqFjx/35hEh8fT0JCgsXxY8aMoWfPnsZnk8lEUlISixYt4uzZs9SpU4eYmBjq1atntFm2bBkxMTE4ODiwdetWypQpY9bn8OHD+eabb2jUqBHz58+/YfytW7emVatWvPPOOze91pu50/uVmprKSy+9xNKlS/Hz87tuu99//53k5GR27tzJ/v378fLy4ptvvim27ZIlS5g9ezbHjx/H09OT119/naefftrYv2vXLj799FN+/PFHMjIyqFixIm3btmXQoEGULFmy2D7T09Np37492dnZbNu2jfLly9/WdV5NSSoRuaHLly+Tk5ODs7OztUMRK8rJyeHQoUN4enrqWXiE6Tl4eChBJfL3UFhowtbW5pE7t7V89tlnVKlSxfj8xRdfULJkyQcySWWt2BISEihZsuQtJamysrLw8vLiH//4B25ubhw5coQZM2bwyy+/8O9//xsHBwejrZOTE3PnzjU7vnr16mafk5KSmDZtGtHR0Xh7e7Nw4ULCw8NZvny5RdsSJUrw3Xff0a1bN2NbTk4O69evv26i5a/0V9+v/fv3s2HDBgICAigsLMRkMhXbbsWKFbz99tu8/PLLNGnShJUrVzJ48GAWLlxoJPtWrVrF77//Tv/+/Xnsscf47bffmDZtGjt37mTevHnF9jt+/HhKlixJdnb2XV+LklQiclPX+yYnjw6TyUROTo6ehUecngMRkfvL1taG6Z9u4VhG5n09b1UPV17tGXxfz/kguHpGzsOioKCAwsJC7O3t7/u5mzVrRrNmzYzPjRs3pnLlyoSHh7N7926zRJetre0Nx//SpUskJiYSHh5O3759AWjQoAHt2rUjOTmZMWPGmLUPCQlhxYoVZkmq//f//h8ODg4EBASQk5NzT67xQdG6dWueeeYZAEaNGsXu3buLbTdt2jQ6duzIsGHDAGjSpAm//vor06dPJykpCYABAwaYzYRq3LgxLi4uREdHs3v3bnx9fc363LZtG9u2bSMyMpIJEybc9bXo12giIiIiIiIPqGMZmRw+du6+ft1tUiwtLY3w8HDq169PYGAg//jHP9iyZQsA58+fJyYmhsaNG+Pv709YWBj/+c9/zI7v3bs3kZGRfPPNN7Rp04aAgABefvllMjMzOXbsGBEREQQGBtKxY0dSU1PNjm3dujXvvfcen3zyCS1btiQwMJBRo0Zx+fJl9u7dS1hYGPXq1aN79+7s27fP7Fhvb2+Sk5ONGH744Qe+//57vL298fb2Jj4+/obXnZWVxfvvv0+LFi3w9fWldevWfPjhh2ZtFi9eTNu2bY39M2bMoLCw0Ni/bNkyvL292bNnD/3796devXq0adOGL7/80mx8rhdb0dh98cUXtG3bFj8/P/73v/+RkZFBTEwMISEh+Pv706ZNGz766CMuX75sFt/SpUvp2LEj/v7+NG7cmJ49e7Jr1y5jfAAmTpxonPfa8b+ZsmXLApCXl3dbx+3YsYOLFy/Svn17Y5uDgwOhoaFs3LjRon2nTp3Ytm0bZ86cMbZ9/fXXtG3blhIlbm+uzuzZs2nevDkBAQEMGjSIjIwMs/2TJ0/m2WefJTAwkObNmxMVFWXW5mbP0vfff09YWBgBAQE0bNiQ3r17s2fPHrNzZGVlMXz4cAIDA3n66aeNhFKRW5khfeTIEQ4fPmw2hgAdOnRg27ZtxrNQ3FI9Hx8fAItrz8vL4/3332fIkCHGvb1bmkklIiIiIiIi98RPP/1Enz59qFevHrGxsbi4uLB7926OHz9OQUEBAwYM4MiRI0RHR+Pu7s78+fPp168fixcvNpuhsWfPHs6dO8eIESO4ePEisbGxvP322xw7dowuXbrQr18/EhMTGTJkCP/v//0/SpUqZRy7bt06ateuzXvvvceRI0cYP3489vb2/Pzzz/Tt2xd3d3cmT57Ma6+9xsqVK4v9Af/dd9/ljTfewMnJiZEjRwJQqVKl61735cuX6dOnD8eOHePVV1/liSeeID09nZ9++sloM3/+fGJjY+nduzetWrUiLS2NhIQELly4YJyjSHR0NC+88AL9+vXj888/Z9SoUfj5+VGrVq2bxrZ7926OHTvGa6+9houLC5UrV+bMmTOULVuWmJgYXFxcOHz4MPHx8Zw6dYpx48YB8J///Ie33nqL8PBwWrZsSW5uLrt27eLChQvAleWQPXr0oHfv3nTq1AmAxx9//KbPREFBAQUFBRw5coRJkyZRt25dGjRoYNYmNzeXJk2akJWVxWOPPUbfvn154YUXjP0HDx4EwMvLy+y4WrVqMXfuXHJzc3FycjK2+/v7U6VKFVavXk2vXr3Iyspi06ZNJCcnWywrvJHvvvuOqlWrMmbMGLKyspg8eTJDhgzhs88+M9qcOXOGyMhIPDw8OHv2LCkpKfTu3ZsVK1ZQokSJG96vlStXEhUVRUhICB9++CH29vbs2LGDkydPGokhuPI8du7cmenTp7N27VomT56Mt7c3LVq0uOVrKRpDT09PizHMy8vjyJEj1KpVq9hji57ja8d/3rx52NnZ0bNnT5YvX37LsdyIklQiIiIiIiJyT0yaNImaNWsyd+5c7OzsAIwlX+vWrWPXrl3GzJSifW3atCExMdFsdsnFixf5+OOPjVkd+/btY86cOWbFtD08PHj22WfZtm2bsdSpyIwZM4yaRz/88AOff/45SUlJxg/1hYWFvPzyy/z66688+eSTFtfx+OOPU7p0aUqWLHlLywC//PJL9uzZw+LFiwkMDDS2d+3aFbiSqJk+fTodO3Zk9OjRxrXn5eUxZ84cBg4cSLly5YzjevXqRa9evQAIDAxkw4YNrFmzhldeeeWmsWVmZrJ06VIqV65sbHN3dzdLhNWvXx9nZ2dGjRrFO++8g7OzM7t27aJs2bJm7Vq1amX8uehclStXvq2lkf/85z/ZsWMHAL6+vsyaNctsNlONGjWIjo7Gx8eHS5cu8fXXX/P2229z4cIFo4h9VlYWDg4OODo6mvXt4uKCyWQiMzPTLEkF0LFjR1asWEGvXr1Ys2YN5cuXp2HDhreVpPrzzz9JSkoyCrBXqlSJvn37smnTJuMZLkrywZX7HBgYSIsWLdi+fTvNmjW77v0ymUxMmDCB4OBgpk+fbmxv2bKlRRxt2rRhyJAhAAQFBfH999+zZs2a20pSZWZemSHp4uJitr3oc9H+a509e5b4+HhCQkJ47LHHjO0nT55k+vTpTJ8+3fi7fi9ouZ+IiIiIiIjctZycHHbu3EmXLl2K/aH1xx9/pHTp0sYP9wD29vaEhoaazTgCePLJJ82WHRX9cNy0aVOLbenp6WbHNmzY0Kwo92OPPYatrS1NmjSxOPbEiRO3dY0mk4n8/Hzjq2ip3rZt26hVq5ZZgupqBw8e5Ny5c7Rr185se4cOHcjLyzOW1BW5upZTyZIlqVKlisV1Xs8TTzxhlqAqivuTTz6hQ4cO+Pv7U7duXaKjo8nPz+fIkSPAlSVd58+fZ9SoUWzZsuWW6zYVFhaajcm1tSs/+OADPv/8cyZNmsTly5fp168fFy9eNPZ37tyZiIgIgoKCaNWqFR9++CFt27Zl5syZt70s8GodO3Zkx44dnDhxghUrVtChQ4diZ81dHXt+fr7ZvsaNG5u9ITAoKIiyZcuyc+dOY9uGDRsICwujQYMG+Pj4GImjw4cP3zC+gwcPkp6ezvPPP3/Ta7n6ebCxsaFWrVq3/Dzcjby8PKKiogAs6n5NnDiR4OBggoKC7uk5laQSkZuysXm03uwilmxsbHB2dtazICIiIteVlZVFYWEhHh4e193v5uZmsd3d3d1iFse1sz2KCn9fnTAoSkRdunTppsc6OTmZJa6K+rv22Jv54osvqFu3rvH15ptvAldqbV3vuuH/Zqlce/1Fn6+9/quvsyjea+tHXY+7u7vFtrlz5zJhwgRCQkKYMWMGS5Ys4Z133gH+bwyCgoKYOHEi+/fvJyIigiZNmjBixAjOnz9/w/O9+eabZmPyxRdfmO338vIiICCA5557jpSUFA4fPmy2XK447du358KFC/zxxx/AlXt6+fJli/uVlZWFjY0Nrq6uFn088cQT1K5dm08++YTU1FRjieK1ro69bt26ZvuKe17Lly/PqVOnANi1axevvPIKHh4eTJw4kc8++4zPP/8cuPmzVTSuN3puitzN81CkaIyKlm8WycrKMttfxGQy8eabb7Jr1y6SkpLM4kxLS2PNmjUMGjSIrKwssrKyjKTmn3/+eVeF6bXcT0RuyMHBQa+aF5ydnc3Wxcvf26P4WnEREfnrlSlTBltbW4viykVcXV3NClkXOX36dLFJhgfR008/zdKlS43PRUv0ypYta1GI/WpFRaXPnj1rtr1oPO7l9Rf3S8XVq1fTunVrhg8fbmw7cOCARbvOnTvTuXNnzp49y7p16xg3bhwlSpTgX//613XPN3jwYGNpIkC1atWu29bd3Z1KlSrx+++/3+rlAP9XC+nQoUNmyzMPHjxIlSpVLJb6FenYsSNTp06lRo0aFm+lK3L1/bxWcc/r2bNnqVChAgBr166ldOnSxMXFGbO0jh07dkvXVPRMXO/vy71WNIYHDx40qy118OBB7O3tqV69uln7CRMmsGrVKpKSkiyWxB46dIi8vDxjOevVnnnmGTp06MCUKVPuKE4lqUTkpqzx6mMR+Ws8qq8VFxGRv15RzZ3ly5cTHh5useSvQYMGJCcns3nzZmP5Un5+PmvXrrUopP0gsLe3t5gNU65cObPaUUWaNm3KypUr2blzJwEBARb7PT09KV++PKtXryY0NNTYvmrVKuzt7fH397/r2G4kNzfXmD1W5Ouvv75u+/Lly/OPf/yDjRs3GgW3r3featWq3TAxdbUTJ05w/Phxi4TItVauXImLiws1atQArtTQKl26NKtWrTISJnl5eXz77bc3rMvUqVMndu3aRUhIyHXb+Pn5XXdfamoqFy5cMGYybdu2jfPnzxv3uGhcr04MFjeuxY2bl5cXlSpVYtmyZXTo0OG6Mdwr1atX57HHHmP16tVmNdxWrlxJUFCQ2UzDWbNm8cknnzB58uRil/M1b96cefPmmW3btGkTSUlJTJ8+3ax21e1SkkpEbqro1cciIiIiIjcyfPhw+vbtS9++fXnxxRdxdXXlv//9L+XKlaNr1674+/vzxhtvMHz4cOPtfhkZGUybNs3aoVvw8vLiyy+/ZP369VSoUAEPDw8qVqxYbNvOnTuzaNEiBg4cyODBg6lduzYnT57kxx9/5P3338fOzo5XXnmF2NhYypcvT8uWLfn5559JSkqiT58+xSa+7lVscCWJNm/ePBYsWMBjjz3GV199ZTGbadq0aZw/f55GjRrh5ubGr7/+yqZNm+jbt6/ZedetW8dTTz2Fs7Mznp6elC5duthzvvPOO5QrVw4/Pz9Kly7NoUOHSElJwc3Nje7duxvtunXrRpcuXfDy8iI3N5evv/6ab7/9ljfffNNIrDk6OhIZGUl8fDzly5fniSee4NNPP+X8+fNGcfXiVKtWjRkzZtzKkBarVKlSDBgwgAEDBnDhwgUmT56Mv7+/UVctODiYuXPn8v777xMaGkpaWlqxb7m73v0aOXIkUVFRDBkyhM6dO+Pg4MDPP/+Mn58fTz/99C3HmZOTw4YNG4ArM7kuXrzI6tWrAWjUqJFR323IkCFER0dTo0YNGjduzMqVK9m1axcLFiww+vr666/58MMPee6556hWrRo///yzsa9GjRqUL1+eChUqGLPJihTNIKtfv75ZPbnbpSSViIiIiIjIA6qqx/1fBnc353zqqaeYN28ecXFxxMTEYGtrS+3atRk2bBh2dnbMmjWLiRMnMmnSJLKzs6lbty5z5sy57lIsaxowYAB//PEHI0eOJCsri8GDBxtvWLuWg4MDn3zyCVOmTCExMZHz589TqVIlOnbsaLTp3bs3JUqU4JNPPuHTTz+lQoUKDB48mJdffvkvjQ3g1Vdf5dy5c0YysG3btowePdrs3H5+fsydO5dVq1Zx8eJFKlWqREREBIMGDTLavPPOO/zrX/9iwIAB5ObmMm/ePBo3blzsOf39/fn8889ZtGgRly9fpnLlyrRo0YKXX37ZLClXo0YNPvnkE06fPo2NjQ1PPPEEkyZN4rnnnrO4ZpPJxJw5czh79ix16tQhOTn5prOy7kZoaCiVKlXi3XffJSsri6ZNmzJ27Fhjf8uWLYmOjmbBggUsW7aM+vXrk5iYSNu2bS1iL+5+dejQAScnJz7++GOioqJwdHTEx8fHbLbdrThz5gyvvfaa2baiz1ffo06dOpGTk0NSUhKzZs3C09OThIQEs4L/W7ZsAeCrr77iq6++Mutz3LhxdOvW7bZiu102pmtL74uI/P9++eUXAD5df0QzqUQeEo9VLce/XruzKeXZ2dns3buXOnXqULJkyXscmfyd6FmQInoW/lrWrCGo+oUiYg16u5+IiIiIiMgDyJpJIiWoRMQalKQSERERERERERGrU5JKRERERERERESsTkkqERERERERERGxOiWpRERERERERETE6kpYOwARefAFeFehSgUXa4chIvdAhfKl7/hYGxsbnJ2dsbFRMV0RERERufeUpLpLe/fupUuXLsybN4/GjRtbO5x76ujRo4SEhDB16lTatWt3X865d+9e1q5dS//+/XF2dr7n/Xt7ezNixAgiIiLued/XWrlyJatWrWLnzp2cPHnyuue9cOEC48aNY+3ateTl5dG8eXNGjx6Nh4eHWbsdO3YwYcIE9u7di5ubGz179mTAgAFmPyyaTCaSkpJYtGgRZ8+epU6dOsTExFCvXr27upYe7e7ueBF5ODg7O+Pj42PtMB4IpsJCbGw1IV1ERETkXlKSSh4oe/fuJSEhgV69ev0lSar7afXq1Rw5coRWrVrx2WefXbfdsGHD+O233xgzZgyOjo7ExcUxYMAA/v3vf1OixJW/or///jsREREEBwczbNgw9u3bx+TJk7GzszNLfCUlJTFt2jSio6Px9vZm4cKFhIeHs3z5cqpXr37H13LomyRyzpy44+NFRB4mzm6V8ew0wNphiIiIiDx0lKR6BJlMJvLy8nBwcLB2KA+1uLg4bP//37JfL0mVlpbG5s2bSU5OplmzZgB4enrSoUMHvv32Wzp06ABAcnIy5cqV46OPPsLBwYGgoCDOnj3Lxx9/TO/evXFwcODSpUskJiYSHh5O3759AWjQoAHt2rUjOTmZMWPG3PG15Jw5Qc7JP+74eBEREREREZGbeaTmqaelpREeHk79+vUJDAzkH//4B1u2bAHg/PnzxMTE0LhxY/z9/QkLC+M///mPRR8zZswgODiYwMBABg8ezJkzZyzamEwmkpOTadu2Lb6+voSEhPDJJ5+YtUlPT+e1116jadOm+Pn50bp1a/71r3+ZtTlw4ACDBg2iQYMG1KtXj4EDB/LHH+aJgjlz5vD888/ToEEDgoKCiIyM5NChQ2ZtRo0aRadOndiwYQPPPfccfn5+rF+//qZjUuTSpUu89957NGzYkGbNmjFhwgTy8/ON/fHx8QQGBrJv3z569uxJQEAAnTp1YtOmTRZjs2zZMp599ln8/Pxo3rw5U6ZMoaCgwNgXExMDQFBQEN7e3rRu3dqij+L07t2byMhIvvzyS5555hn8/f3p3bs3Bw8etGhbWFhIfHw8TZs2pXHjxsTExJCdnW0Wo7e3Nz///DMvvfQSAQEBtG7dmqVLl95SLEVsb2EZyMaNG3FxcSE4ONjY5uXlRZ06ddi4caNZu5CQELPEYocOHcjKyiItLQ24shzw4sWLtG/f3mjj4OBAaGioWV8iIiIiIiIiD6JHJkn1008/0bt3by5fvkxsbCzx8fGEhIRw/PhxCgoKGDBgAP/v//0/oqOjmTp1KiVLlqRfv37s3r3b6GPBggVMnTqV5557jmnTplG9enXeeusti3N98MEHTJs2jS5dujBr1iy6du3K5MmT+fTTT402I0aMYN++fYwePZrZs2czdOhQCgsLjf1HjhwhLCyMzMxMxo8fz+TJkzl79ix9+/bl8uXLRrv09HT++c9/MmPGDGJjYyksLCQsLIzz58+bxZSRkUFsbCx9+/YlKSmJOnXq3HBMrlY0IyguLo6wsDDmzJnDkiVLzNrk5eURHR1Nt27dSEhIoHz58gwdOpRz584ZbVJSUhg9ejTNmjXj448/ZsCAAcybN48pU6YA0KpVKwYNGgTA7Nmz+eyzz0hISLjVW8x///tfEhMTGT58OBMmTCAjI4P+/fubjRfAwoULOXz4MOPHj+fVV1/l66+/ZsaMGRb9RUVFERwcTEJCAo0bN+att96658megwcP4unpaVGE2MvLy0iwZWdnc+LECby8vCza2NjYGO2K/nttu1q1anH8+HFyc3PvaewiIiIiItaSlZVFfHw8v/322y0fU/SL7QfNhAkT6NixI4GBgdSvX5/nn3+eFStWWLTz9va2+Lr6l91FDhw4QL9+/ahXrx7BwcFMnDjR4mei1q1b4+3tzeTJky2OP3z4sNF/amrqDWMv+gX/2bNnb/OqLR09epT4+HhOnjx528fe6r1duHAhkZGRNGnSBG9vb1avXl1su5MnTzJkyBACAwNp1KgRb731FhcvXjT2FxQUkJSURK9evWjcuDGNGjWid+/e/Pjjjzc8/wcffIC3tzfvvffe7V3gI+SRWe43adIkatasydy5c7GzswMwlletW7eOXbt2MXv2bJo3b27sa9OmDYmJicTHx1NQUEBiYiKdO3dm5MiRADRv3pwzZ86wfPly4zx//PEHCxYsYOzYsfTo0QOApk2bkpuby/Tp0+nRowe2trb88ssvREVFGcu5ALp06WL8OSEhAVdXV1JSUnB0dASgfv36hISEsGTJEnr16gXAm2++aRxTUFBAcHAwQUFBrFmzxjg/QGZmJklJSQQEBBjbRo4ced0xuZq/vz+jR48GIDg4mNTUVNasWUPPnj2NNkVJqpYtWwJXlqyFhISwceNGOnfuzMWLF5k2bRr9+/cnKirK6Mve3p7x48cTERFB+fLlqVGjBgB169alfPnyN7yn1zpz5gwLFizgscceA8DHx4d27dqxbNkywsLCjHYVKlTgww8/BKBFixbs2bOHNWvWEB0dbdZf586djW90zZs358iRI0yfPp0WLVrcVlw3kpWVRZkyZSy2u7q6GgnSCxcuAODiYv52PQcHB5ydncnMzDT6cnBwMJ6XIi4uLphMJjIzM3FycrpnsYuIiIjIX8uaL2l40F8QkZWVRUJCArVr1+bxxx+/pWPefffdW1rtcL/9+eef/OMf/zB+Cb1mzRqioqIoLCzk2WefNWvbu3dvOnXqZHy2t7c325+ZmUmfPn147LHHjITP+PHjyc3N5Z133jFrW7JkSVauXGnxc9A333xDyZIlzVab3A/Hjh0jISGBVq1aUbFixb/kHEU/u7ds2ZIvv/yy2DZ5eXn0798fgA8//JDc3FwmTJjA8OHDSUxMBCA3N9eYkDJgwABsbW35/PPPeemll0hOTiYoKMii33379vHvf/+b0qXv/E3Lj4JHIkmVk5PDzp07iYqKMpIxV/vxxx8pXbq0kaCCK3/ZQ0ND+eabb4ArM5YyMjIIDQ01O7Zt27ZmSaqtW7cC0KZNG7MlcU2bNiUpKYkTJ05QtWpVfHx8mDNnDnZ2dgQHB1OzZk2zfrds2UKHDh2ws7Mz+nFxccHHx8dsdtfPP//M1KlT2bNnj9nsqcOHD5v1V7ZsWbME1c3G5GrXJq5q1arF9u3bzbbZ2tqa/UWsVq0aTk5ORhY8LS2N7Oxs2rVrZzEuubm57N+/n0aNGt0wjpupXbu2kaACqFmzJk8++SQ7d+40S1I1bdrU4nqK+03Ftfe6TZs2TJw4kYKCgpuOmYiIiIjI3bKxtbXKC2wethdE5Obm4uTkdMvJrPvt2lk1zZs357fffuOLL76wSFJVrlz5hm/uXrx4MX/++ScJCQmULVsWuDKZYezYsURGRpolf1q1asW3335LWloagYGBxvYVK1bwzDPP8NVXX939xT1gFi9ejK2tLUePHr1ukmrNmjXs37+flStXGqtUXFxciIiIYNeuXfj7++Pk5MTatWtxdXU1jgsODqZTp07MnTu32CTV+++/T9++fa97XrnikUhSZWVlUVhYiIeHx3X3u7m5WWx3d3c3ZqmcOnUKwGJ2j7u7u9nnc+fOYTKZaNKkSbHnKkpSTZkyhSlTphAXF8fYsWPx9PQkKiqKNm3aGP3MnTuXuXPnWvRRlC0/fvw44eHh+Pr6MnbsWDw8PLC3tycyMpJLly7dMM6bjcnVrp3pY29vbzFd1MnJyaIQu729vRFH0bK/rl27FnuOEyfu/n+8xd1DNzc3494VuXZGUnHXU1x/7u7u5OXlce7cOYvxvFMuLi6kp6dbbM/MzDS+4RWNf9GMqiKXL18mJyfHaOfi4sLly5e5dOmS2WyqrKwsbGxszL6BioiIiMjfw9/xBTZpaWnEx8fz888/YzKZePzxxxk2bBjBwcGcP3+eCRMmsH79enJycvDx8WH48OE0bNjQOL53796ULFmSrl27MmXKFDIyMvDz8yM2NpYaNWpw9OhRQkJCAHjttdeM49atWwdASEgI48aNY8eOHXz33Xd4eHjw9ddfG/0WzYaBK0vjJk+ezA8//EBBQQGNGjVi9OjRxgqP4o6BK6VgJk6cyJYtWyhTpgwmk4k5c+bw+eefc+zYMSpWrEjv3r2NFxrdrrJly/Lnn3/e9nEbN24kKCjISFABtG/fnnfffZctW7bQrVs3Y3u5cuUICgpixYoVRpJqz549HD58mJEjR95WkuqPP/5g2LBh7Ny5Ezc3N1555RW6d+9u7E9LSyMxMZHdu3dz8eJFatasSb9+/YzVRKmpqbz00ksAZsft27cPuPIzzdSpU/nuu+84e/YsHh4edOzYkeHDh5vFsXr16mKfmSK3WjfY29vbrIxKcHAwZcuWZcOGDfj7+2NnZ2fx85WdnR3e3t4WdaQBvvrqK44ePUpSUpKSVDfxSCSpypQpg62tLRkZGcXud3V1LbYA+unTp40Hr0KFCgAWa21Pnz5t0ZeNjQ2LFi2ymHoJV5bBAXh4eDBu3DgKCwvZvXs3M2fO5PXXX2f16tVUr14dV1dXWrZsyYsvvmjRR6lSpQDYtGkT2dnZJCQkGImX/Px8I7F2tWtrHt1sTO61onFMSEigUqVKFvurVat21+co7h6eOXOGJ5988o77u/o3DadPn8be3p5y5crdcYzX8vLyYtu2bZhMJrN7dOjQIZ544gngyjTcypUrWxSBP3ToECaTyfjmWfTfQ4cOmV3zwYMHqVKlipb6iYiIiMhf7qeffqJPnz7Uq1eP2NhYXFxc2L17t1kt4CNHjhAdHY27uzvz58+nX79+LF68GF9fX6OfvXv3cvbsWaKjoykoKGD8+PG88cYbfPbZZ3h4eJCQkMDgwYOJioqicePGwJWfsYp+vvnoo49o2bIlH374oVnt36sV1QGuXbs248ePx8bGho8//pi+ffuyevVqHBwc6NixI7GxsZw/f94s8fPNN9/QsmVL4xfKH3zwAUuWLOHll18mICCAHTt2MHnyZBwdHc3KpFyPyWSioKCA7Oxs1q9fz5YtW5g0aZJFu1mzZvHRRx/h7OxMs2bNGDFiBFWqVDH2Hzx4kOeff97sGBcXFypU+P/Yu/Owqqr9j+NvQBAQQVGch9AURQXHDHEgFYey65AmDpiBSlaaoZWYebMo50TBARXnobJMy5xKf2laWZY5pUY5jzgBGijT+f3Bw74eDwoqekw/r+fxuZ691177u9dZ1+TrWt/tketLpTp06MDEiRMZMWIEtra2rF69moYNG972drvw8HC6d+9O//79WbNmDW+//TalSpUySqWcOnWK+vXr06NHDxwcHPjtt98YOXIkJpOJzp07U6tWLUaNGsV7773HmDFjzBJEaWlpvPDCC5w8eZJXXnmF6tWrc+bMGX799VezGG41Z27HoUOHLOr82tjY4OnpmesY5sjIyGDXrl00aNDA7PiVK1cYP348I0aMwMnJ6bZieRQ9EkkqZ2dn6taty6pVqwgJCbHYqtWgQQPi4uLYunWrsbUtIyODb7/91phgZcqUwcPDg2+++cZsG9j69evN+spZ1peYmJivN9PZ2tri4+PDkCFD2LRpE0ePHqVixYr4+fkRHx+Pt7f3TbeWXb16FRsbGwoV+t/XuHbtWrPtdHc6JgWtXr16ODk5cebMGYttdNfLSezltrIpL/Hx8Rw9etTYOnn06FEOHDhgVpvrdnzzzTd4e3sbnzds2ECtWrUKdKyaN2/O9OnT+fHHH41tiIcPH+aPP/4w9kHntNu4cSNvvPGGMUZr1qzB1dXV+FeP+vXr4+Liwtq1a40kVXp6Ohs2bCjQOloiIiIiIjdzt7WAc1y+fJmVK1caO1lSUlKIiIjgzJkzlClThpo1awLZJT5y2/5Wo0YNPvjgg1vGmp86wG3btiUyMpINGzbw/PPPA9m1k37//XeioqKA/NclvpUff/yRF198EYBChQrxzjvv0K5dO7M2nTp1IiAggJIlS/Lnn38yY8YMevbsyapVq4xFAcnJyRY7RyB70UBuixlat27NqFGj2L59O08++SRr1qwxXmZ1O/Kq5/vMM88YbU0mE40aNeLs2bN88skndO7cGRcXF2M7ZrVq1ahTp47RfuXKlfzxxx98/PHHZtsSb9ylk9ecya9b1Q3ObQxzzJkzh7Nnz1qsnouJiaFy5cpm9ajl5h6JJBXA0KFD6du3L3379qVnz564ubmxb98+ihcvTufOnfHx8eGNN95g6NChRkY/ISGBqVOnAtlL9wYMGMAHH3xAiRIl8Pf3Z9u2bRZvO/D09KRXr168+eabhIaG4uvrS3p6OkeOHGH79u1Mnz6dy5cvExoaSseOHfH09CQ9PZ1FixYZNacABg8eTNeuXQkNDeX555+nZMmSnD9/np9//pmGDRvSoUMHY0thREQEQUFBxMfHM2/evFz/ULrdMbl+iWVBcHV1ZfDgwUyYMIEzZ87wxBNPYGdnx/Hjx9m4cSPR0dE4OTlRtWpVIPutC61bt8bR0REvL6983aNEiRK89NJLDB48GIApU6ZQunRpsyWtt2PVqlU4Ojri7e3NmjVr+OWXX5g1a1a+r//rr7/M3jby559/sm7dOpycnIwC8/Xq1aNp06aMGDGCt956i8KFCzN58mS8vLyMrZ8AoaGhfPXVVwwdOpQePXrw559/EhcXx+uvv25ssyxcuDBhYWFER0fj7u5O9erVWbZsGYmJiYSGht7RGORw86yNo3v+/2AXEXmYFXYrmC3fIiIPm4KoBZyjRo0aZqVWchIY+U04BAQE5NkmP3WAixcvTpMmTfj666+NJNWaNWtwdnbmqaeeAvJfl/j6czY2NmZj5OPjw2effcaVK1fYsmULkZGR2NnZ0a1bN6PNuHHjjN83atSIBg0a0KVLFz799FP697+zGmIuLi4EBASwevVq7O3tOX/+PG3btrUox5KVlWW2Is3W1tYs8ZZXPd+kpCSio6PZuHEjZ8+eJTMzE8BsddrN/Pjjj1StWtUsQZWbu50zd2Pbtm1ER0fz8ssvm60IjI+PZ8mSJXz66af39P4Pk0cmSdWwYUMWLlxIVFQUERER2NraUq1aNYYMGYKdnR2zZs1i/PjxTJgwgZSUFGrVqsXcuXPNJlhwcDDJycksXbqUZcuW4efnR2RkpNmKF4CRI0fi6enJJ598wrRp0yhSpAienp5GJrxw4cJUr16dRYsWcfr0aRwdHalduzZxcXHG/6kqV67M8uXLjZpVKSkpeHh40KhRIyNp4+XlxZgxY4iJiSEsLIyaNWsyZcoUhgwZctdjci+EhIRQunRp5s2bx+LFiylUqBCVKlUiICDAWB3k7e3NoEGDWL58OXPmzKFs2bJs2rQpX/3XqlWLNm3aMGHCBM6dO4evry+jR4+2qJWVX5MmTeKjjz5i2rRplChRgvfff99ILuXH2rVriYmJMT6vXLmSlStXUr58ebNnioqKYsyYMYwaNYqMjAyaNm3KyJEjzVbIVa5cmbi4OMaOHcuAAQNwd3dn8ODBhISEmN2zf//+xn74ixcvUrNmTeLi4qhYseIdjUGO8s3vLNEnIiJ5y8rKeiDfNiUicrsKohZwjtzqyAIWtXdvJrf73Cg/dYAhexXQ8OHDOXfuHB4eHnz99dcEBgYaq6/yU5fYZDIZdbQAi58JXFxcjNVDfn5+xna1Ll263HQnR40aNfD09GTfvn3GMVdXV4tatmBe8/ZGzzzzDO+88w6QvbKtWLFiFkmqadOmmf1s8+qrrzJo0CDjc171fIcPH87OnTt55ZVXePzxx3FxcWHZsmWsXbs215iul5iYmK9aync7Z67v58qVKxbHk5KSKFu2rMXxffv2MWjQIDp06MCrr75qdm7s2LG0a9eO8uXLk5ycDGT/dz89PZ3k5GRcXFz0d4AbPDJJKsheurlw4cJczxUvXpwxY8bc8nobGxteffVVi4mXU8zt+na9e/emd+/eufbj4OBAZGRknvE+9thjxhLSm+nUqZNRbC7HjUmdsWPH3vT6W41JhQoVLJ4N4O233+btt982Pg8aNMjsD6gcO3bssDj2zDPPmC31zE1uY5xfzz33nMUe7Ovl9jw5q8luVLlyZRYtWnRHccDNx+VGRYsW5cMPP+TDDz+8Zbv69evnmYG3sbEhLCzMWGpbUGI3L+RU0tkC7VNERKCcW2nCWvSxdhgiIgWiIGoBF5Qba/LeLJ686gBDdiF2BwcH1q5dS9OmTdm/fz/h4eFm/eRVl9jBwYHPPvvMOJbXP6TXqlWLBQsWcPHiRaM+cn5UqVLFom7S5cuXOXfunEWdpRwBAQFkZGSwYsUKxo8fn2ub559/3mx12o1Jo1vV87127Rrfffcdw4cPJzg42GizdOnSfD1TsWLFcv057l6pUqUKf/75p9kxk8nE4cOH8ff3Nzt+9OhR+vfvT7169XL9Gf/w4cNs3brVogj9p59+yqeffsqaNWuM3USS7ZFKUonInTmVdJajF05YOwwREREReYAVRC3g/LrTVTLXy08dYPjflrivv/6apKQk3N3djXqyOf1A3nWJr6+zlJdff/0VFxeXW760af/+/Rw+fNisvEnz5s2ZOXOmWW2qdevWYWtra5FgyVG4cGFeeukldu/ebbba63qlS5e+ZTH1W9XzTUlJISsryyyBd+XKFYvFFTf7Tps0acKaNWvYtWsXvr6+N42hoDRv3pwvv/ySI0eO8NhjjwHZWw4TExPNdtYkJCQQEhJC2bJlmTp1aq4Jyo8++sjiecLDw6lbty59+vQxK3ov2ZSkkgdeZmYmJpPppuev3xZ3v9yqOP2N+8tFRERERB4Vd1sLOL88PDxwdXXl66+/pkKFCjg4OOS7lm2O/NQBzpGzlevkyZO0a9fO7GeQ/NQlvpkDBw4wceJEY0tYSkoK3333HcuXLyc8PNy4T1xcHMeOHaNx48a4u7sTHx/PzJkzKVOmjFndqqCgIBYtWsQrr7xCWFgYZ8+eZfz48QQFBd0yyTRgwIDbGrsb3aqeb9GiRalTpw6zZ8/G3d2dQoUKMWvWLFxcXLh48aLRx2OPPYadnR2ff/45hQoVws7Ojjp16tCxY0eWLl3KgAEDePXVV6lWrRpnz55lx44dvP/++7cV5549ezh58qRx3127dgHg7u7OE088AUDbtm2JjY1l0KBBhIeHk5qayvjx4wkICMDHxwfIfolZ//79uXTpEm+//Tbx8fHGPRwcHIyEXW5F/QsXLkzp0qWNt1KKOSWp5IEXGBjIyZMnb3r+4MGDd7Ut70ZdunS5ZbH1EydO3PRfGACeeOKJAo1HRERERB5dTiUsa+A8yPcsiFrA+WFra8uYMWP46KOP6Nu3L2lpaWzcuPG2+shPHeAcLVq0oGjRopw7dy7X8iV51SW+mZIlS+Lq6sr06dM5d+4cRYsWpUqVKsTExNC6dWujnaenJxs2bGDt2rX8888/FC9enBYtWjBkyBCzWkxubm4sWLCA999/n1deeYUiRYrQtWtXXn/99dsam9uVVz3fSZMmMWrUKIYPH06xYsUIDg4mJSWFuXPnGm3c3d0ZNWoUc+bM4csvvyQjI4ODBw/i4ODA/PnzmTx5MrGxsSQmJlKmTJk8y8jkZsmSJXzxxRfG55z7X/8znL29PXPmzCEyMtJIFAYGBjJixAjjuvPnz3PgwAEAi7ch3lhvTG6PjelWS1REHgAHDx4kLS3tpudvZ9lsQUhLS7vlnugiRYrcdL/3v82ePXsA+OzwOm33ExG5ByqXqMDo/7xh7TBuW0pKCvv376dmzZo4OztbOxyxIs2Fe8uUlYWNlYoqW/PeIvLo0koqeeDd7rLde83BweG+J8ZERERE5NFjzSSRElQiYg36k0dERERERERERKxOSSoREREREREREbE6JalERERERERERMTqVJNKRPJUzu3mr6sVEZE7pz9fRURERP5HSSoRyVNYiz7WDkFE5KGVlZWFrQoUi4iIiGi7n4jcWlpaGqmpqdYOQ6wsNTWVP/74Q3PhEad5cG8oQSUiIiKSTX8rEpE8mUwma4cgVmYymUhNTdVceMRpHoiIiIjIvaQklYiIiIiIiIiIWJ2SVCIiIiIiIvKvkpycTHR0NH/99Ve+rwkODiYsLOweRnVnxo0bxzPPPEO9evWoX78+zz33HF9//bVFOy8vL4tf/v7+Fu3+/vtvXnzxRerWrYu/vz/jx48nLS3NrE3Lli3x8vJi4sSJFtcfOXLE6H/79u23jH3FihV4eXlx8eLF23xqSydOnCA6OpqzZ8/e9rX5/W6XLFlCWFgYTz75JF5eXqxbt+6mbb/77juCgoKoW7cujRo1Ijg4mDNnzhjnv/jiC55//nmeeOIJ6tSpQ9u2bZk2bZrFWKempjJp0iRatWqFr68vbdu2ZebMmWRkZNz03i+//DJeXl7ExcXl4+kfLiqcLiIiIiIi8gCy5osVHvSXOiQnJxMTE0O1atV4/PHH83XNf//73wfymf755x+6detGlSpVsLGxYf369YSHh5OVlcWzzz5r1jY4OJgOHToYn+3t7c3OJyUl8cILL/DYY48ZCZ+xY8dy9epVRo0aZdbW2dmZNWvWMGzYMLPjq1evxtnZmZSUlAJ+0ls7efIkMTExBAQEULr0vXn77apVqwBo0aIFK1euvGW7t99+m5CQEIYMGcI///zDjh07uHbtmtEmKSmJZs2aMWDAAFxcXNi9ezcxMTGcOXOG999/32j33nvvsWHDBsLDw6latSq///47U6dOJTU1lddff93i3ps3b2bXrl0F99D/MkpSiYiIiIiIPIBsbW2J3byQU0m3v7LkbpRzK/1Qvd356tWrODo65juZdb+99957Zp+bNWvGX3/9xRdffGGRpCpbtix169a9aV8ff/wx//zzDzExMRQrVgyAzMxMRo8eTVhYmFnyJyAggA0bNrBz507q1atnHP/6669p3bo1X3755d0/3APm448/xtbWlhMnTtw0SZWYmMh7773HiBEj6Nmzp3G8VatWZu369u1r9vnJJ5/kn3/+Yf78+bz77rvY2dmRlZXF2rVrCQ0NpVevXka7w4cP8/XXX1skqdLS0vjggw8IDw9nxIgRd//A/0IPXhpZRB44NjY21g5BrMzGxgYnJyfNBRERkfvsVNJZjl44cV9/3W1SbOfOnYSEhFC/fn3q1atHt27d2LZtG5CdAIiIiKBx48b4+PgQFBTEL7/8YnZ9ztatdevW0bZtW+rVq0efPn04duwYkL0tLCdh8Nprrxlb006cOMGJEyfw8vJixYoVjBw5ksaNG9OtWzezfq/3999/M3DgQBo0aEDdunUZMGCAcZ+bXQOwePFifHx8uHz5MpD9cpG4uDjatm1L7dq1adWqFfPnz7/jMSxWrBjp6em3fd2WLVvw8/MzElQA7du3Jysry/gOchQvXhw/Pz+zrYV//PEHR44c4Zlnnrmt+x47dow+ffrg6+tLy5Yt+eyzz8zO79y5k5deeommTZtSt25dOnbsaJYk2r59O336ZCdGu3btanynOZKTk3n//fdp3rw5tWvXpmXLlkyaNMkijpvNmRz5WUm3du1asrKy6Nq16+0MAZD9vWVkZJCVlQVkz4uMjAyKFi1q1q5o0aK5vogmLi4OV1dXunTpctv3flgoSSUit+Tg4ICTk5O1wxArc3Jywtvb+189F7Ky9EY6ERGRe+3XX38lODiYtLQ0IiMjiY6OplWrVpw6dYrMzEz69+/P//3f/zFs2DCmTJmCs7MzL774Inv37jXrZ//+/cTFxTFs2DDGjBnDsWPHeOONNwAoVaoUMTExAISHh/PJJ5/wySefUKpUKeP6jz76CJPJxKRJk4zrbnT8+HGCgoJISkpi7NixTJw4kYsXL9K3b1+jrtAzzzzDtm3bSExMNLt29erVtGjRwkg+fPDBB0ydOpVOnToxa9YsOnfuzMSJE1m2bFm+xi0nmZGcnMzKlSvZtm2bsfLmerNmzaJWrVo0bNiQIUOGcOrUKbPzhw4dokqVKmbHXF1d8fDw4NChQxb9dejQgXXr1hlJldWrV9OwYcPb3m4XHh6Ov78/MTExNG7cmLfffpstW7YY50+dOkX9+vX54IMPmDFjBm3atGHkyJF88cUXANSqVcvYjjhmzBjjO4Xs1UUvvPACX331FaGhocyePZtBgwZx6dIlsxhuNWdux65du/D09GTlypU89dRTeHt707FjRzZv3pxr+4yMDFJTU9mxYwcLFiygR48exjZMOzs7unTpwuLFi9m9ezf//PMPP/zwA6tWraJ3795m/Zw6dYpZs2YxcuTIR/ofhrXdT0TyNG3ZNk4mJFk7DJE7Vr6UG6/0sCwsKiIiIgVrwoQJVK5cmQULFmBnZwdA06ZNAdi4cSO7d+9mzpw5NGvWzDjXpk0bYmNjiY6ONvq5fPkyK1euxN3dHYCUlBQiIiI4c+YMZcqUoWbNmgBUrlw51+1vNWrU4IMPPrhlrDExMbi5uTFv3jwKFy4MQP369WnVqhXLly+nV69etG3blsjISDZs2MDzzz8PZNdO+v3334mKigKyVxEtXryY0aNH0717dwCaNGnC1atXmTZtGt27d89zBc+PP/7Iiy++CEChQoV45513aNeunVmbTp06ERAQQMmSJfnzzz+ZMWMGPXv2ZNWqVbi5uQHZK45cXV0t+ndzcyMpyfLv861bt2bUqFFs376dJ598kjVr1jBw4MBbxpqbjh07GivOmjVrxvHjx5k2bRrNmzcHMFuZZTKZaNSoEWfPnuWTTz6hc+fOuLi4GNsxq1WrRp06dYz2K1eu5I8//uDjjz8225bYuXNnsxjymjP5de7cOQ4fPsyUKVN444038PDwYMmSJbz88susXLmSatWqGW0zMjKoVauWWUw3btP773//y3//+19jRR9AWFiY8X3nGDNmDIGBgbfczvkoUJJKRPJ0MiGJIycv5d1QRERERB5Zqamp7Nq1i/DwcCNBdb0dO3bg4uJiJKggu/B3YGAgq1evNmtbo0YNI9kAGAmM/CYcAgIC8myzbds2nn76aezs7Iw3rbm6uuLt7W2s7CpevDhNmjTh66+/NpJUa9aswdnZmaeeegqAH374AYA2bdqYvbGtSZMmzJ49m9OnT1O+fHmzczY2NmZj5OPjw2effcaVK1fYsmULkZGR2NnZmSU2xo0bZ/y+UaNGNGjQgC5duvDpp5/Sv3//PJ83Ny4uLgQEBLB69Wrs7e05f/48bdu25fTp02btsrKyjNVWkL1t7vrEW2BgoFn7Nm3aMH78eDIzM7GzsyMpKYno6Gg2btzI2bNnyczMBDDblngzP/74I1WrVjVLUOXmbudMDpPJREpKChMnTjS2lT7xxBO0bduW2bNnM378eKNtoUKF+Oyzz7h27Rp79+5lxowZREREmH1XEydO5LvvviMyMpLHHnuM33//nWnTpuHq6kq/fv0A2Lp1K1u3br3l2wYfFUpSiYiIiIiIyF1LTk4mKyvLbNvdjedLlChhcbxkyZIWq3xuXA2Us33q+rer3Upu97nRpUuXWLBgAQsWLLA4d/1b85555hmGDx/OuXPn8PDw4OuvvyYwMNBYfXXp0iVMJhNPPvlkrvc5ffo0JpPJrPB2+fLl2bRpk/HZxcXFWD3k5+dHZmYmY8eOpUuXLrkm/CA7KePp6cm+ffuMY66urkadrOslJSUZq61u9Mwzz/DOO+8A2SvbihUrZpGkmjZtmrHFEuDVV19l0KBBxucbx7tkyZKkp6dz6dIlSpYsyfDhw9m5cyevvPIKjz/+OC4uLixbtoy1a9fmGtP1EhMTbzqnrne3c+bGfq7/Pu3t7WnUqBHx8fEW7XO+t4YNG1KhQgVeeeUVevfuTZ06dfjzzz+ZO3cuM2bMoGXLlkB2gjEjI4MpU6YQFBSEi4sLkZGR9OnTBycnJ5KTk42+r127dtPVcQ8rJalERERERETkrhUtWhRbW1sSEhJyPe/m5saFCxcsjp8/f/6mCZQ7lZ+aPm5ubrRo0cLsDW45ihQpYvy+VatWODg4sHbtWpo2bcr+/fsJDw8368fGxoalS5eaJbdyeHp64uDgYFZM3MHB4Zax1apViwULFnDx4kU8PDzyfJYcVapUsag9dfnyZc6dO2dRqypHQEAAGRkZrFixwmyV0PWef/55s9VpNyaNLly4YFbH6vz589jb21O8eHGuXbvGd999x/DhwwkODjbaLF26NF/PVKxYMQ4ePJivtgXhVm+BzCvhVbt2bSB7C2idOnX466+/AIztqTm8vb1JS0vj7NmzuLi4cPjwYWbOnMnMmTPN2k2ZMoUpU6awe/duIyn6sFOSSkRERERERO6as7MzdevWZdWqVYSEhFisAGrQoAFxcXFs3brVqFOVkZHBt99+S4MGDW7rXne6SuZ6fn5+xMfH4+3tfdPVSvC/LXFff/01SUlJuLu706RJE7N+IHvFT85qmdxcX2cpL7/++isuLi4UL178pm3279/P4cOHzd4E17x5c2bOnGm2+mbdunXY2tri7597fc7ChQvz0ksvsXv3brPVXtcrXbr0LYupf/PNN3h7exufN2zYQK1atbCzsyMlJYWsrCyzBN6VK1fMVpLBzb/TJk2asGbNGnbt2oWvr+9NYygoTz31FNHR0fz444+0bt0ayC7e/ssvv9CwYcNbXvvrr78CULFiRSB7xRzAvn37KFu2rNFu79692NjYUK5cOQAWLlxo0VefPn0ICgri6aefzjX5+bBSkkpEREREREQKxNChQ+nbty99+/alZ8+euLm5sW/fPooXL07nzp3x8fHhjTfeYOjQoZQsWZJFixaRkJDA1KlTb+s+Hh4euLq68vXXX1OhQgUcHBzw8vK6rT4GDx5M165dCQ0N5fnnn6dkyZKcP3+en3/+mYYNG9KhQwejbYcOHXj11Vc5efIk7dq1o1Ch//0o7enpSa9evXjzzTcJDQ3F19eX9PR0jhw5wvbt25k+ffpNYzhw4AATJ06kXbt2lC9fnpSUFL777juWL19OeHi4cZ+4uDiOHTtG48aNcXd3Jz4+npkzZ1KmTBmzulVBQUEsWrSIV155hbCwMM6ePcv48eMJCgq6ZZJpwIABtzV2N1q1ahWOjo54e3uzZs0afvnlF2bNmgVkr7CrU6cOs2fPxt3dnUKFCjFr1ixcXFy4ePGi0cdjjz2GnZ0dn3/+OYUKFcLOzo46derQsWNHli5dyoABA3j11VepVq0aZ8+eZceOHbz//vu3FeeePXs4efKkcd9du3YB4O7uzhNPPAFkr2Jr27Yt77zzDomJiXh4eLB06VLOnz9PaGio0VevXr0IDAykSpUq2NrasmvXLubOnUuzZs3w8fEBsldW1a5dm//+979cuHCBSpUqsXv3bmbNmsVzzz1nvDm7cePGucZbqVKlm557WClJJSIiIiIi8oAq53bzxMKDeM+GDRuycOFCoqKiiIiIwNbWlmrVqjFkyBDs7OyYNWsW48ePZ8KECaSkpFCrVi3mzp1rbJPKL1tbW8aMGcNHH31E3759SUtLY+PGjbfVR+XKlVm+fDlRUVGMHj2alJQUPDw8aNSokUXCq0WLFhQtWpRz586Zvakux8iRI/H09OSTTz5h2rRpFClSBE9PT4s39N2oZMmSuLq6Mn36dM6dO0fRokWpUqUKMTExxioeyE6EbdiwgbVr1/LPP/9QvHhxWrRowZAhQ8zqFbm5ubFgwQLef/99XnnlFYoUKULXrl15/fXXb2tsbtekSZP46KOPmDZtGiVKlOD999+nRYsWZudHjRrF8OHDKVasGMHBwaSkpDB37lyjjbu7O6NGjWLOnDl8+eWXZGRkcPDgQRwcHJg/fz6TJ08mNjaWxMREypQpk+v3kJclS5bwxRdfGJ9z7v/EE0+waNEi4/jYsWP56KOPmDRpEleuXKFWrVrMmzfPbF7Url2bTz/9lFOnTlGoUCEqVKjAoEGDzLaP2tnZMXPmTKZMmUJsbCwXLlygTJky9OvX746L3T/sbEwmk8naQYjIg2nPnj0ALNt0XG/3k3+1x8oX58PXnrZ2GP96KSkp7N+/n5o1a+Ls7GztcMSKNBckh+bCvZWVlWX2BrVH5d4i8ujSnzoiIiIiIiIPIGsmiZSgEhFr0HY/EcmTr1c5ynk8Oq89lYePh7uLtUN4KNjY2DxShTtFRERE5P5Skuom9u/fT6dOnVi4cOFDV6jsxIkTtGrViilTpuS5R7qg7N+/n2+//ZZ+/foZxeEKkpeXl1Go8F5bs2YNa9euZdeuXZw9e/am9718+TJjxozh22+/JT09nWbNmjFy5EiL17X+9ttvjBs3jv3791OiRAl69OhB//79zV6bazKZmD17NkuXLuXixYvUrFmTiIgI6tata9bX2bNniYyMZOvWrdjb2xMYGEhERAQuLnf3A3r3dnXzbCMiDz8nJydq1fImLS3d2qGIiIiIyENISSq5L/bv309MTAy9evW6J0mq+2ndunUcP36cgIAAPvnkk5u2GzJkCH/99RfvvvsuhQsXJioqiv79+xtvqwA4evQooaGh+Pv7M2TIEA4ePMjEiROxs7MzS3zNnj2bqVOnMmzYMLy8vFiyZAkhISGsWrXKeL1peno6/fr1A7ILE169epVx48YxdOhQYmNj7+qZD6+eTeqF03fVh4j8+zmVKItnh/6AklQiIiIiUvCUpHqImEwm0tPTcXBwsHYoD7WoqChjj/7NklQ7d+5k69atxMXF0bRpUyD7jRxPP/00GzZs4Omnsws4x8XFUbx4cT766CMcHBzw8/Pj4sWLzJw5k+DgYBwcHLh27RqxsbGEhITQt29fABo0aEC7du2Ii4vj3XffBWD9+vXEx8ezZs0aqlSpAoCrqyuhoaHs3r3beA3qnUi9cJrUs8fu+HoRERERERGRvDwU1fB27txJSEgI9evXp169enTr1o1t27YBkJiYSEREBI0bN8bHx4egoCB++eUXiz6mT5+Ov78/9erV49VXX+XChQsWbUwmE3FxcbRt25batWvTqlUr5s+fb9bmzJkzvPbaazRp0oQ6derQsmVLPvzwQ7M2f//9NwMHDqRBgwbUrVuXAQMGcOyYeQJg7ty5PPfcczRo0AA/Pz/CwsI4fPiwWZvhw4fToUMHNm/ezH/+8x/q1KnDpk2b8hyTHNeuXeO9996jUaNGNG3alHHjxpGRkWGcj46Opl69ehw8eJAePXrg6+tLhw4d+P777y3GZsWKFTz77LPUqVOHZs2aMXnyZDIzM41zERERAPj5+eHl5UXLli0t+shNcHAwYWFhrFy5ktatW+Pj40NwcDCHDh2yaJuVlUV0dDRNmjShcePGREREkJKSYhajl5cXv//+O3369MHX15eWLVvy2Wef5SuWHPkpIrllyxZcXV3x9/c3jlWpUoWaNWuyZcsWs3atWrUySyw+/fTTJCcns3PnTiB7O+CVK1do37690cbBwYHAwECLvry8vIwEFYC/vz/FihVj8+bNt/WMIiIiIiIiIvfbv34l1a+//soLL7xA3bp1iYyMxNXVlb1793Lq1CkyMzPp378/x48fZ9iwYZQsWZJFixbx4osv8vHHH1O7dm0AFi9ezJQpUwgJCaFJkyb88MMPvP322xb3+uCDD1i+fDkvvfQSvr6+/Pbbb0ycOJHChQvTo0cPAN58800SEhIYOXIkJUqU4PTp0+zdu9fo4/jx4wQFBVGtWjXGjh2LjY0NM2fOpG/fvqxbt85IVpw5c4bevXtTrlw5rly5wscff0xQUBDr16+nWLFiRn8JCQlERkYycOBAypYtS7ly5W45JteLioqiVatWREVFsXPnTqKjo6lUqZLxLJC9hWzYsGH06dOHl19+mdmzZzN48GA2bdpE8eLFAZg3bx4TJkzghRdeYPjw4fz9999GkmrYsGEEBAQwcOBAZsyYwZw5cyhatOhtrfbat28fx44dY+jQoUbc/fr1MxsvgCVLltCgQQPGjh3LkSNHGD9+PCVKlGDYsGFm/YWHh9O9e3f69+/PmjVrePvttylVqhTNmzfPd0x5OXToEJ6enmZ1pSA7UZWTYEtJSeH06dNmSaWcNjY2Nhw6dIjGjRsb7W9sV7VqVRYsWMDVq1dxdHTk0KFDFm1sbGzw9PTMNaknIiIiIiIi8iD51yepJkyYQOXKlVmwYAF2dnYAxvaqjRs3snv3bubMmUOzZs2Mc23atCE2Npbo6GgyMzOJjY2lY8eOvPXWWwA0a9aMCxcusGrVKuM+x44dY/HixYwePZru3bsD0KRJE65evcq0adPo3r07tra27Nmzh/DwcGM7F0CnTp2M38fExODm5sa8efMoXLgwAPXr16dVq1YsX76cXr16ATBixAjjmszMTPz9/fHz82P9+vXG/QGSkpKYPXs2vr6+xrG33nrrpmNyPR8fH0aOHAlkr7jZvn0769evzzVJ1aJFCyB7y1qrVq3YsmULHTt25MqVK0ydOpV+/foRHh5u9GVvb8/YsWMJDQ3F3d2dSpUqAVCrVi3c3d1v+Z3e6MKFCyxevJjHHnsMAG9vb9q1a8eKFSsICgoy2nl4eDBp0iQAmjdvzh9//MH69estklQdO3YkLCwMyP6ujx8/zrRp0wo0SZWcnEzRokUtjru5uRlJy8uXLwPZW/Ku5+DggJOTE0lJSUZfDg4OxnzJ4erqislkIikpCUdHx1veM6cvERERERERkQfVv3q7X2pqKrt27aJTp05GMuZ6O3bswMXFxUhQAcYbz3799Vcge8VSQkICgYGBZte2bdvW7PMPP/wAQJs2bcjIyDB+NWnShHPnznH6dHZRaW9vb+bOncvSpUs5evSoRUzbtm2jZcuW2NnZGX24urri7e1ttuLq999/58UXX6Rx48Z4e3vj6+tLSkoKR44cMeuvWLFiZgmqvMbkejcmrqpWrcqZM2fMjtna2uLn52d8rlChAo6Ojpw9exbI3laYkpJCu3btLMbl6tWrxMfH3zKG/KhWrZqRoAKoXLkyNWrUYNeuXWbtmjRpkufzABbfdZs2bdi3b5+xPVFERERERERE7r9/9Uqq5ORksrKyKFWq1E3PlyhRwuJ4yZIljZUl586dA7BY3VOyZEmzz5cuXcJkMvHkk0/meq/Tp09Tvnx5Jk+ezOTJk4mKimL06NF4enoSHh5OmzZtjH4WLFjAggULLPqwt7cH4NSpU4SEhFC7dm1Gjx5NqVKlsLe3JywsjGvXrt0yzrzG5Ho3rrqxt7cnLS3N7Jijo6PF1jx7e3sjjkuXLgHQuXPnXO+Rk7y7G7l9hyVKlDC+uxw3rkjK7Xly669kyZKkp6dz6dIli/G8U66urrkmyJKSknBzcwP+N/45K6pypKWlkZqaarRzdXUlLS2Na9euma2mSk5OxsbGxqzdlStXcr1n2bJlC+S5RERERERERO6Vf3WSqmjRotja2pKQkJDreTc3t1wLoJ8/f974wd7DwwOAixcvWrS5sS8bGxuWLl1qJJOu5+npCUCpUqUYM2YMWVlZ7N27lxkzZvD666+zbt06KlasiJubGy1atKBnz54WfRQpUgSA77//npSUFGJiYozES0ZGRq5btm6seZTXmBS0nHGMiYmhTJkyFucrVKhw1/fI7Tu8cOECNWrUuOP+SpcubXw+f/489vb2Ro2tglClShV+/PFHTCaT2Xd0+PBhqlevDoCzszNly5a1qBd1+PBhTCaTUV8q538PHz5s9syHDh2iXLlyODo6Gu3+/PNPs75MJhOHDx82K+AuIiIiIiIi8iD6V2/3c3Z2pm7duqxatSrXrVoNGjTgypUrbN261TiWkZHBt99+S4MGDQAoU6YMHh4efPPNN2bXrl+/3uxzzpa3xMRE6tSpY/HLxcXFrL2trS0+Pj4MGTKEjIwMY+ufn58f8fHxeHt7W/SRk4y4evUqNjY2FCr0vxzi2rVrzd68d6djUtDq1auHk5MTZ86cyXVcchI/OYm93FY25SU+Pt5s6+TRo0c5cOCA2TbH23Hjd71hwwZq1aqV5/bI29G8eXOSkpL48ccfjWOHDx/mjz/+MKt91bx5czZu3Eh6erpxbM2aNbi6ulKvXj0gu2aZi4sLa9euNdqkp6ezYcMGi74OHDhgtiX0xx9/JDEx0agpJiIiIiIiIvKg+levpAIYOnQoffv2pW/fvvTs2RM3Nzf27dtH8eLF6dy5Mz4+PrzxxhsMHTrUeLtfQkICU6dOBcDOzo4BAwbwwQcfUKJECfz9/dm2bRvbt283u4+npye9evXizTffJDQ0FF9fX9LT0zly5Ajbt29n+vTpXL58mdDQUDp27Iinpyfp6eksWrTIqDkFMHjwYLp27UpoaCjPP/88JUuW5Pz58/z88880bNiQDh06GFsKIyIiCAoKIj4+nnnz5llsZ7uTMenatWsBjn72FrPBgwczYcIEzpw5wxNPPIGdnR3Hjx9n48aNREdH4+TkRNWqVYHsN/C1bt0aR0dHvLy88nWPEiVK8NJLLzF48GAApkyZQunSpenSpcsdxbxq1SocHR3x9vZmzZo1/PLLL8yaNSvf1//111/89ddfxuc///yTdevW4eTkZCSD6tWrR9OmTRkxYgRvvfUWhQsXZvLkyXh5eRlbPwFCQ0P56quvGDp0KD169ODPP/8kLi6O119/3dhmWbhwYcLCwoiOjsbd3Z3q1auzbNkyEhMTCQ0NNfpq27YtsbGxDBo0iPDwcFJTUxk/fjwBAQH4+Pjc0VjlcPOsjaO75Uo5EXm0FHbL3hJ94ypeEREREZGC8K9PUjVs2JCFCxcSFRVFREQEtra2VKtWjSFDhmBnZ8esWbMYP348EyZMICUlhVq1ajF37lxq165t9BEcHExycjJLly5l2bJl+Pn5ERkZSb9+/czuNXLkSDw9Pfnkk0+YNm0aRYoUwdPTk3bt2gHZyYTq1auzaNEiTp8+jaOjI7Vr1yYuLs6oeVW5cmWWL19u1KxKSUnBw8ODRo0aGUkbLy8vxowZQ0xMDGFhYdSsWZMpU6YwZMiQux6TeyEkJITSpUszb948Fi9eTKFChahUqRIBAQHGCipvb28GDRrE8uXLmTNnDmXLlmXTpk356r9WrVq0adOGCRMmcO7cOXx9fRk9erRFraz8mjRpEh999BHTpk2jRIkSvP/++7e10mjt2rXExMQYn1euXMnKlSspX7682TNFRUUxZswYRo0aRUZGBk2bNmXkyJFmK+QqV65MXFwcY8eOZcCAAbi7uzN48GBCQkLM7tm/f39MJhNz587l4sWL1KxZk7i4OCpWrGi0sbe3Z86cOURGRhIeHk6hQoUIDAw0e1PknSrf/M4SgiLycMrZZlzQsrKysLX9Vy/yFhEREZG7YGMymUzWDkLkZoKDg3F2diY2Nvau+1qxYgURERH8+OOPFoXyJXd79uwB4IeLOzmVdNbK0YjIw6ycW2nCWvSxdhiSTykpKezfv5+aNWvi7Oxs7XDEijQXRESkIP3rV1KJyL13KuksRy+csHYYIiIiIiIi8hBTkkqsJjMzk1st5Lt+W9z9cqvi9DY2NgVaXF1ERERERERE/kdJKrGawMBATp48edPzBw8eZNGiRQV2vy5dutyy2PqJEydo1arVTc8/8cQTBRqPiIiIiIiIiPyPklRiNTNmzCAtLc3aYRhKlSrFZ599dtPzRYoUuY/RiIiIiIiIiDxalKQSq8l5m+GDwsHBgTp16lg7DBEREREREZFHkt7zLCIiIiIiIiIiVqcklYiIiIiIiIiIWJ2SVCIiIiIiIiIiYnWqSSUieSrnVtraIYjIQ05/zoiIiIiIklQikqewFn2sHYKIPAKysrKwtdUibxEREZFHlf4mKCK3lJaWRmpqqrXDECtLTU3ljz/+0Fx4xN3reaAElYiIiMijTX8bFJE8mUwma4cgVmYymUhNTdVceMRpHoiIiIjIvaQklYiIiIiIiIiIWJ2SVCIiIiIiIiIiYnVKUomIiIiIiIiIiNUpSSUiIiIiIiIiIlanJJWIiIiIiIiIiFidklQikicbGxtrhyBWZmNjg5OTk+bCI07zQHJoLkgOzQURESlINia9R1pEbmLPnj0A1KlTx8qRiIiIyL9BVpYJW1slrERE5M4UsnYAIvLgm7ZsGycTkqwdhoiIiDzAypdy45Ue/tYOQ0RE/sWUpBKRPJ1MSOLIyUvWDkNEREREREQeYqpJJSIiIiIiIiIiVqcklYiIiIiIiIiIWJ2SVCIiIiIiIiIiYnVKUomIiIiIiIiIiNUpSSUiIiIiIiIiIlanJJWIiIiIiIiIiFidklQiIiIiIiIiImJ1hawdwL20f/9+OnXqxMKFC2ncuLG1wylQJ06coFWrVkyZMoV27drdl3vu37+fb7/9ln79+uHk5HRf7nmj6Oho/P39qV+/vtlxLy8v3nzzTUJDQ60S14327NnD0qVL+f333zl8+DAtWrQgNjbWop3JZGL27NksXbqUixcvUrNmTSIiIqhbt65Zu7NnzxIZGcnWrVuxt7cnMDCQiIgIXFxczNpt2rSJqKgoDh8+TLly5RgwYADPPffcXT+Pr1c5ynm43nU/IiIi8vDycHfJu5GIiMgtPNRJKilY+/fvJyYmhl69elktSRUTE4Ozs7NFkupB89tvv7Fjxw58fHy4du3aTdvNnj2bqVOnMmzYMLy8vFiyZAkhISGsWrWKihUrApCenk6/fv0AmDRpElevXmXcuHEMHTrULPG1Y8cOXn31Vbp27cqIESP46aefePvttylSpMhdJzK7t6t7V9eLiIiI3E+mrCxsbLVpRETk30ZJqgeMyWQiPT0dBwcHa4cidyE4OJgXXnjB+H1url27RmxsLCEhIfTt2xeABg0a0K5dO+Li4nj33XcBWL9+PfHx8axZs4YqVaoA4OrqSmhoKLt378bHxweAGTNm4OPjw3vvvQfAk08+yfHjx5k6depdJ6kOr55N6oXTd9WHiIiIyP3gVKIsnh36WzsMERG5Aw9Mkmrnzp1ER0fz+++/YzKZePzxxxkyZAj+/v4kJiYybtw4Nm3aRGpqKt7e3gwdOpRGjRqZ9TF9+nSWLFlCSkoK/v7+BAUFWdzHZDIxd+5cPv30U06ePEnp0qUJDg42kgQAZ86cYcyYMfzyyy9cvnwZDw8PWrduzYgRI4w2f//9NxMnTuTnn38mMzOTJ554gpEjR1KpUiWjzdy5c/n66685cuQIDg4O+Pj4MHz4cDw9PY02w4cPZ+/evbzxxhtMmjSJQ4cOMXHiRNq1a3fLMclx7do13nvvPb766isKFy7Ms88+y9ChQylUKPurjY6OZu7cuXz88ce8++67/PHHH1SsWJG33nqLZs2amY3NihUrmDdvHkeOHKFYsWJ06dKFwYMHY2dnx4oVK4iIiADAz88PgPLly7Np06Y8v9vg4GCcnZ1p3749MTExJCQk4Ovry+jRo42ky6BBgzh37hwff/yx2bVLly5lzJgxfP/998aWzfHjxzN+/HgAs62cWVlZREdHs2zZMjIzM2nZsiXvvPMOzs7ORn8HDx5k/Pjx/Prrr9jZ2eHv78/w4cMpV66c0cbLy4thw4Zx9erVW/Z1K7b5+Je73377jStXrtC+fXvjmIODA4GBgXzzzTfGsS1btuDl5WWMFYC/vz/FihVj8+bN+Pj4kJaWxvbt2xk2bJjZPZ5++mlWr17NiRMnqFChQr5iz03qhdOknj12x9eLiIiIiIiI5OWBSFL9+uuvvPDCC9StW5fIyEhcXV3Zu3cvp06dIjMzk/79+3P8+HGGDRtGyZIlWbRoES+++CIff/wxtWvXBmDx4sVMmTKFkJAQmjRpwg8//MDbb79tca8PPviA5cuX89JLL+Hr68tvv/3GxIkTKVy4MD169ADgzTffJCEhgZEjR1KiRAlOnz7N3r17jT6OHz9OUFAQ1apVY+zYsdjY2DBz5kz69u3LunXrjFVQZ86coXfv3pQrV44rV67w8ccfExQUxPr16ylWrJjRX0JCApGRkQwcOJCyZctSrly5W47J9aKiomjVqhVRUVFGUqtSpUrGs0D2drFhw4bRp08fXn75ZWbPns3gwYPZtGkTxYsXB2DevHlMmDCBF154geHDh/P3338zefJkMjMzGTZsGAEBAQwcOJAZM2YwZ84cihYtelurvfbt28exY8cYOnSoEXe/fv2M8erWrRv9+/fn0KFDZsmYzz//nMDAQIoVK8Ynn3xC9+7dCQ4OpkOHDgA8/vjjRtslS5bQoEEDxo4dy5EjRxg/fjwlSpQwEjenT5+md+/eVKxYkQkTJnDt2jUmT55M7969+fLLL83qO+XVV0E4dOgQgNnzAlStWpUFCxZw9epVHB0dLcYEwMbGBk9PT6OPY8eOkZ6enmtfOfe6mySViIiIiIiIyL32QCSpJkyYQOXKlVmwYAF2dnYANG3aFICNGzeye/du5syZY6z8adq0KW3atCE2Npbo6GgyMzOJjY2lY8eOvPXWWwA0a9aMCxcusGrVKuM+x44dY/HixYwePZru3bsD0KRJE65evcq0adPo3r07tra27Nmzh/DwcJ5++mnj2k6dOhm/j4mJwc3NjXnz5lG4cGEA6tevT6tWrVi+fDm9evUCMFt5lZmZib+/P35+fqxfv964P0BSUhKzZ8/G19fXOPbWW2/ddEyu5+Pjw8iRI4Hs1TXbt29n/fr1uSapWrRoAYCnpyetWrViy5YtdOzYkStXrjB16lT69etHeHi40Ze9vT1jx44lNDQUd3d3Y5VYrVq1cHd3v+V3eqMLFy6wePFiHnvsMQC8vb1p164dK1asICgoiKZNm1KuXDk+//xz3njjDQD+/PNP9u7da8SUU0y8bNmyFoXFATw8PJg0aRIAzZs3548//mD9+vVGYmn+/PlkZGQwd+5cI0lYs2ZNnnnmGb744guzbXl59VUQkpOTcXBwMOZQDldXV0wmE0lJSTg6OpKcnEzRokUtrndzcyMpKQnA+F9XV1eLvq4/LyIiIiIiIvKgsno1wdTUVHbt2kWnTp2MZMz1duzYgYuLi9nWtJy3m/36669A9oqlhIQEAgMDza5t27at2ecffvgBgDZt2pCRkWH8atKkCefOneP06eyaO97e3sydO5elS5dy9OhRi5i2bdtGy5YtsbOzM/pwdXXF29vbbMXV77//zosvvkjjxo3x9vbG19eXlJQUjhw5YtZfsWLFzBJUeY3J9W5MXFWtWpUzZ86YHbO1tTW26AFUqFABR0dHzp49C2RvtUxJSaFdu3YW43L16lXi4+NvGUN+VKtWzUhQAVSuXJkaNWqwa9cuI8bnnnuOVatWkZGRAWSvoipfvrxZ7LfSpEkTs883jsWOHTto3Lix2Sq2qlWrUqNGDWMu5bcvERERERERESlYVl9JlZycTFZWFqVKlbrp+RIlSlgcL1mypLE65Ny5cwAWq3tKlixp9vnSpUuYTCaefPLJXO91+vRpypcvz+TJk5k8eTJRUVGMHj0aT09PwsPDadOmjdHPggULWLBggUUf9vb2AJw6dYqQkBBq167N6NGjKVWqFPb29oSFhVm87e3GOPMak+vduMLG3t6etLQ0s2OOjo4WW/Ps7e2NOC5dugRA586dc71HTvLubuT2HZYoUcL47gC6du3K9OnT2bx5M82bN+fLL7+kZ8+e+arvBJariG4ci+TkZGrWrJlrHDeuNMqrr4Lg6upKWloa165dM1tNlZycjI2NDW5ubka7K1euWFyflJRE2bJlAYy2ly9fNmuTnJxsdl5ERERERETkQWX1JFXRokWxtbUlISEh1/Nubm5cuHDB4vj58+eNH7w9PDwAuHjxokWbG/uysbFh6dKlRjLpejkFzUuVKsWYMWPIyspi7969zJgxg9dff51169ZRsWJF3NzcaNGiBT179rToo0iRIgB8//33pKSkEBMTYyQ8MjIyct12ZWNjc1tjUtByxjEmJoYyZcpYnC+IWka5fYcXLlygRo0axucyZcrQrFkzPv/8czIzM7l06RJdunS563vnuNlcunDhgtkqr/slp37U4cOHzcbh0KFDlCtXDkdHR6Pdn3/+aXatyWTi8OHDRhH9SpUqYW9vz6FDh8xWHd6s7pWIiIiIiIjIg8bq2/2cnZ2pW7cuq1atIjMz0+J8gwYNuHLlClu3bjWOZWRk8O2339KgQQMgO7nh4eFh9kY0gPXr15t9ztk2lpiYSJ06dSx+XV84G7K3oPn4+DBkyBAyMjKMrX9+fn7Ex8fj7e1t0UdOMuDq1avY2NgYb9kDWLt2rbGV7W7GpKDVq1cPJycnzpw5k+u45BRXz0ns3cmKovj4eLOtk0ePHuXAgQNm2xwBunXrxubNm5k7dy5+fn6UL1/e7Pz1K8BuV4MGDfjpp5/MEoWHDh3i4MGDxly6n+rXr4+Liwtr1641jqWnp7NhwwaaN29uHGvevDkHDhww2yb6448/kpiYaNQZc3BwoHHjxhZzfs2aNVStWlVF00VEREREROSBZ/WVVABDhw6lb9++9O3bl549e+Lm5sa+ffsoXrw4nTt3xsfHhzfeeIOhQ4cab/dLSEhg6tSpANjZ2TFgwAA++OADSpQogb+/P9u2bWP79u1m9/H09KRXr168+eabhIaG4uvrS3p6OkeOHGH79u1Mnz6dy5cvExoaSseOHfH09CQ9PZ1FixYZNacABg8eTNeuXQkNDeX555+nZMmSnD9/np9//pmGDRvSoUMHY0thREQEQUFBxMfHM2/ePIttZHcyJl27di3A0c/eTjZ48GAmTJjAmTNneOKJJ7Czs+P48eNs3LiR6OhonJycjDfFLVmyhNatW+Po6IiXl1e+7lGiRAleeuklBg8eDMCUKVMoXbq0xUqpgIAAihcvzs6dO/noo48s+qlSpQobN26kYcOGODk54enpaZFcvJm+ffuyYsUKQkJCGDhwINeuXSMqKoqyZcvedKvjnbp48SI///yz8ft//vmHdevWAdCiRQucnJwoXLgwYWFhREdH4+7uTvXq1Vm2bBmJiYmEhoYafbVt25bY2FgGDRpEeHg4qampjB8/noCAAHx8fIx2AwcOpE+fPrz77ru0b9+e7du3s3r1aiZPnlygzyYiIiIiIiJyLzwQSaqGDRuycOFCoqKiiIiIwNbWlmrVqjFkyBDs7OyYNWsW48ePZ8KECaSkpFCrVi3mzp1L7dq1jT6Cg4NJTk5m6dKlLFu2DD8/PyIjI+nXr5/ZvUaOHImnpyeffPIJ06ZNo0iRInh6etKuXTsAChcuTPXq1Vm0aBGnT5/G0dGR2rVrExcXZ9S8qly5MsuXLzdqVqWkpODh4UGjRo2MpI2XlxdjxowhJiaGsLAwatasyZQpUxgyZMhdj8m9EBISQunSpZk3bx6LFy+mUKFCVKpUiYCAAGMFlbe3N4MGDWL58uXMmTOHsmXLsmnTpnz1X6tWLdq0acOECRM4d+4cvr6+jB492qJWVqFChWjZsiXr1q2zKIQPMGrUKD788EP69+/P1atXWbhwIY0bN85XDGXLlmXRokWMHz+eYcOGYWtri7+/P8OHD893oiu/4uPjee2118yO5XzeuHGjsbKpf//+mEwm5s6dy8WLF6lZsyZxcXFUrFjRuM7e3p45c+YQGRlJeHg4hQoVIjAw0OztkZA9Z6Kjo4mKiuKzzz6jXLlyREZG0r59+7t+HjfP2ji6W24FFREREXnQFHYrmXcjERF5INmYTCaTtYOQh1twcDDOzs7Exsbm2TYrK4vWrVvz1FNP8c4779yH6ORW9uzZA0CdOnWsHImIiIhIwcnKysr3y3lEROT+eSBWUomkpaVx4MAB1q9fz5kzZ+jVq5e1Q5LrxG5eyKmks9YOQ0REROSulXMrTViLPtYOQ0REcqEkldyVzMxMbrUY7/rC8beSkJBAt27dcHd355133nmg30ZXUM/8b3Iq6SxHL5ywdhgiIiIiIiLyEHv4fpqW+yowMJCTJ0/e9PzBgwdZtGhRnv1UqFCBgwcPFmRo90x+nllEREREREREbo+SVHJXZsyYQVpamrXDuK8exWcWERERERERudeUpJK7kvM2w0fJo/jMIiIiIiIiIveaXmkhIiIiIiIiIiJWpySViIiIiIiIiIhYnZJUIiIiIiIiIiJidUpSiYiIiIiIiIiI1alwuojkqZxbaWuHICIiIlIg9PcaEZEHl5JUIpKnsBZ9rB2CiIiISIHJysrC1labSkREHjT6k1lEbiktLY3U1FRrhyFWlpqayh9//KG58IjTPJAcmguS4986F5SgEhF5MOlPZxHJk8lksnYIYmUmk4nU1FTNhUec5oHk0FyQHJoLIiJSkJSkEhERERERERERq1OSSkRERERERERErE5JKhERERERERERsTolqURERERERERExOqUpBKRPNnY2Fg7BLEyGxsbnJycNBcecZoHkkNzQURERO6FQtYOQEQebA4ODjg5OVk7DLEyJycnvL29rR2GWJnmgeTQXPj3ysoyYWur5KKIiDyYlKQSkTxNW7aNkwlJ1g5DRERE7kL5Um680sPf2mGIiIjclJJUIpKnkwlJHDl5ydphiIiIiIiIyENMNalERERERERERMTqlKQSERERERERERGrU5JKRERERERERESsTkkqERERERERERGxOiWpRERERERERETE6pSkEhERERERERERq1OSSkRERERERERErK6QtQMQkQefr1c5ynm4WjsMERERuQse7i4F3qeNjQ1OTk7Y2NgUeN8iIvLosTGZTCZrB3En9u/fT6dOnVi4cCGNGze2djgF6sSJE7Rq1YopU6bQrl27+3LP/fv38+2339KvXz+cnJzuyz1vFB0djb+/P/Xr1zc77uXlxZtvvkloaKhV4rrRnj17WLp0Kb///juHDx+mRYsWxMbGWrQzmUzMnj2bpUuXcvHiRWrWrElERAR169Y1a3f27FkiIyPZunUr9vb2BAYGEhERgYuL+V8kN23aRFRUFIcPH6ZcuXIMGDCA5557zqxNWloakydP5ssvv+Sff/6hXr16vPPOO1SpUuWOnxWgTp06d3S9iIiISH6YsrKwsdUmDxGRR51WUgmQnaSKiYmhV69eVktSxcTE4OzsbJGketD89ttv7NixAx8fH65du3bTdrNnz2bq1KkMGzYMLy8vlixZQkhICKtWraJixYoApKen069fPwAmTZrE1atXGTduHEOHDjVLfO3YsYNXX32Vrl27MmLECH766SfefvttihQpYpbIjIyMZM2aNQwfPpzSpUszc+ZM+vbty9dff03RokXv+JkPr55N6oXTd3y9iIiIyM04lSiLZ4f+1g5DREQeAEpS3Ucmk4n09HQcHBysHYrcheDgYF544QXj97m5du0asbGxhISE0LdvXwAaNGhAu3btiIuL49133wVg/fr1xMfHs2bNGmO1k6urK6GhoezevRsfHx8AZsyYgY+PD++99x4ATz75JMePH2fq1KlGkurMmTN89tln/Pe//6Vr165A9gqop556io8//pj+/e/8L3+pF06TevbYHV8vIiIiIiIikpf7sqZ2586dhISEUL9+ferVq0e3bt3Ytm0bAImJiURERNC4cWN8fHwICgril19+sehj+vTp+Pv7U69ePV599VUuXLhg0cZkMhEXF0fbtm2pXbs2rVq1Yv78+WZtzpw5w2uvvUaTJk2oU6cOLVu25MMPPzRr8/fffzNw4EAaNGhA3bp1GTBgAMeOmf+APnfuXJ577jkaNGiAn58fYWFhHD582KzN8OHD6dChA5s3b+Y///kPderUYdOmTXmOSY5r167x3nvv0ahRI5o2bcq4cePIyMgwzkdHR1OvXj0OHjxIjx498PX1pUOHDnz//fcWY7NixQqeffZZ6tSpQ7NmzZg8eTKZmZnGuYiICAD8/Pzw8vKiZcuWFn3kJjg4mLCwMFauXEnr1q3x8fEhODiYQ4cOGW0GDRpEUFCQxbVLly6lTp06JCYm4uXlBcD48ePx8vLCy8uL7du3G22zsrKIjo6mSZMmNG7cmIiICFJSUsz6O3jwIKGhodStW5cGDRowePBgTp06ZdbGy8uL2bNn59nXrdjmYyn6b7/9xpUrV2jfvr1xzMHBgcDAQLZs2WIc27JlC15eXmbb8fz9/SlWrBibN28Gsrfwbd++3WLr59NPP83ff//NiRMnANi6dStZWVlm7YoVK4a/v7/ZPUVEREREREQeRPc8SfXrr78SHBxMWloakZGRREdH06pVK06dOkVmZib9+/fn//7v/xg2bBhTpkzB2dmZF198kb179xp9LF68mClTpvCf//yHqVOnUrFiRd5++22Le33wwQdMnTqVTp06MWvWLDp37szEiRNZtmyZ0ebNN9/k4MGDjBw5kjlz5jB48GCysrKM88ePHycoKIikpCTGjh3LxIkTuXjxIn379iUtLc1od+bMGXr37s306dOJjIwkKyuLoKAgEhMTzWJKSEggMjKSvn37Mnv2bGrWrHnLMbleVFQUtra2REVFERQUxNy5c1m+fLlZm/T0dIYNG0aXLl2IiYnB3d2dwYMHc+nSJaPNvHnzGDlyJE2bNmXmzJn079+fhQsXMnnyZAACAgIYOHAgAHPmzOGTTz4hJiYmv18x+/btIzY2lqFDhzJu3DgSEhLo16+fMV7dunVj586dZokrgM8//5zAwECKFSvGJ598AmQnvT755BM++eQTatWqZbRdsmQJR44cYezYsbzyyit89dVXTJ8+3Th/+vRpevfuzaVLl5gwYQKjR49m37599O7dmytXrpjdN6++CkLOs95YC6pq1aqcOnWKq1evGu1ubGNjY4Onp6fRx7Fjx0hPT8+1r+vvdejQIUqUKIGbm5tFuxvHXkRERERERORBc8+3+02YMIHKlSuzYMEC7OzsAGjatCkAGzduZPfu3cyZM4dmzZoZ59q0aUNsbCzR0dFkZmYSGxtLx44deeuttwBo1qwZFy5cYNWqVcZ9jh07xuLFixk9ejTdu3cHoEmTJly9epVp06bRvXt3bG1t2bNnD+Hh4Tz99NPGtZ06dTJ+HxMTg5ubG/PmzaNw4cIA1K9fn1atWrF8+XJ69eoFwIgRI4xrMjMz8ff3x8/Pj/Xr1xv3B0hKSmL27Nn4+voax956662bjsn1fHx8GDlyJJC9umb79u2sX7+eHj16GG1yklQtWrQAwNPTk1atWrFlyxY6duzIlStXmDp1Kv369SM8PNzoy97enrFjxxIaGoq7uzuVKlUCoFatWri7u9/yO73RhQsXWLx4MY899hgA3t7etGvXjhUrVhAUFETTpk0pV64cn3/+OW+88QYAf/75J3v37jViyikmXrZsWYvC4gAeHh5MmjQJgObNm/PHH3+wfv16hg0bBsD8+fPJyMhg7ty5FCtWDICaNWvyzDPP8MUXX5hty8urr4KQnJyMg4ODMYdyuLq6YjKZSEpKwtHRkeTk5FxrRbm5uZGUlARg/K+rq6tFX9efv1lfrq6uRhsRERERERGRB9U9XUmVmprKrl276NSpk5GMud6OHTtwcXExElSA8XazX3/9FchesZSQkEBgYKDZtW3btjX7/MMPPwDQpk0bMjIyjF9NmjTh3LlznD6dXfTZ29ubuXPnsnTpUo4ePWoR07Zt22jZsiV2dnZGH66urnh7e5ut7vr999958cUXady4Md7e3vj6+pKSksKRI0fM+itWrJhZgiqvMbnejYmrqlWrcubMGbNjtra2+Pn5GZ8rVKiAo6MjZ8+eBbK3FaakpNCuXTuLcbl69Srx8fG3jCE/qlWrZiSoACpXrkyNGjXYtWuXEeNzzz3HqlWrjO2Kn3/+OeXLlzeL/VaaNGli9vnGsdixYweNGzc2ElQ5bWrUqGHMpfz2JSIiIiIiIiL33z1dSZWcnExWVhalSpW66fkSJUpYHC9ZsqSx8uPcuXMAFqt7SpYsafb50qVLmEwmnnzyyVzvdfr0acqXL8/kyZOZPHkyUVFRjB49Gk9PT8LDw2nTpo3Rz4IFC1iwYIFFH/b29gCcOnWKkJAQateuzejRoylVqhT29vaEhYVZvO3txjjzGpPr3bgqxt7e3mzLIYCjo6NFIXZ7e3sjjpxtf507d871HjnJu7uR23dYokQJ47sD6Nq1K9OnT2fz5s00b96cL7/8kp49e+arvhNYriK6cSySk5OpWbNmrnHcuIoor74KgqurK2lpaVy7ds1sNVVycjI2NjbGljxXV1eL7YiQvTqqbNmyAEbby5cvm7VJTk42O3+zvpKTky22AIqIiIiIiIg8aO5pkqpo0aLY2tqSkJCQ63k3N7dcC6CfP3/e+KHaw8MDgIsXL1q0ubEvGxsbli5daiSTrufp6QlAqVKlGDNmDFlZWezdu5cZM2bw+uuvs27dOipWrIibmxstWrSgZ8+eFn0UKVIEgO+//56UlBRiYmKMhEdGRkauW6psbGxua0wKWs44xsTEUKZMGYvzFSpUuOt75PYdXrhwgRo1ahify5QpQ7Nmzfj888/JzMzk0qVLdOnS5a7vneNmc+nChQtmq7zul5z6UYcPHzYbh0OHDlGuXDkcHR2Ndn/++afZtSaTicOHD+Pv7w9ApUqVsLe359ChQ2arDm+se1WlShXOnz9PUlKSWVIqt7pXIiIiIiIiIg+ae7rdz9nZmbp167Jq1SrjTXLXa9CgAVeuXGHr1q3GsYyMDL799lsaNGgAZCc3PDw8+Oabb8yuXb9+vdnnnG1jiYmJ1KlTx+KXi4uLWXtbW1t8fHwYMmQIGRkZxtY/Pz8/4uPj8fb2tugj5wf9q1evYmNjQ6FC/8vxrV271uzNe3c6JgWtXr16ODk5cebMmVzHpXjx4sD/VondyYqi+Ph4s62TR48e5cCBA2bbHCG7gPrmzZuZO3cufn5+lC9f3uz89SvAbleDBg346aefzBKFhw4d4uDBg8Zcup/q16+Pi4sLa9euNY6lp6ezYcMGmjdvbhxr3rw5Bw4cMNsm+uOPP5KYmGjUGXNwcKBx48YWc37NmjVUrVrVSDQ2bdoUW1tbNmzYYLRJSkpi69atZvcUEREREREReRDd88LpQ4cOpW/fvvTt25eePXvi5ubGvn37KF68OJ07d8bHx4c33niDoUOHUrJkSRYtWkRCQgJTp04FwM7OjgEDBvDBBx9QokQJ/P392bZtG9u3bze7j6enJ7169eLNN98kNDQUX19f0tPTOXLkCNu3b2f69OlcvnyZ0NBQOnbsiKenJ+np6SxatMioOQUwePBgunbtSmhoKM8//zwlS5bk/Pnz/PzzzzRs2JAOHToYWwojIiIICgoiPj6eefPmWWwju5Mx6dq1awGOfvYWsMGDBzNhwgTOnDnDE088gZ2dHcePH2fjxo1ER0fj5ORkvCluyZIltG7dGkdHR7y8vPJ1jxIlSvDSSy8xePBgAKZMmULp0qUtVkoFBARQvHhxdu7cyUcffWTRT5UqVdi4cSMNGzbEyckJT09Pi+TizfTt25cVK1YQEhLCwIEDuXbtGlFRUZQtW/amWx3v1MWLF/n555+N3//zzz+sW7cOgBYtWuDk5EThwoUJCwsjOjoad3d3qlevzrJly0hMTCQ0NNToq23btsTGxjJo0CDCw8NJTU1l/PjxBAQE4OPjY7QbOHAgffr04d1336V9+/Zs376d1atXG29ohOyEbteuXRk/fjy2traULl2a2NhYihYtSlBQ0F09s5tnbRzdLVfiiYiIiNytwm4l824kIiKPhHuepGrYsCELFy4kKiqKiIgIbG1tqVatGkOGDMHOzo5Zs2Yxfvx4JkyYQEpKCrVq1WLu3LnUrl3b6CM4OJjk5GSWLl3KsmXL8PPzIzIykn79+pnda+TIkXh6evLJJ58wbdo0ihQpgqenJ+3atQOgcOHCVK9enUWLFnH69GkcHR2pXbs2cXFxRs2rypUrs3z5cqNmVUpKCh4eHjRq1MhI2nh5eTFmzBhiYmIICwujZs2aTJkyhSFDhtz1mNwLISEhlC5dmnnz5rF48WIKFSpEpUqVCAgIMFZQeXt7M2jQIJYvX86cOXMoW7YsmzZtylf/tWrVok2bNkyYMIFz587h6+vL6NGjLWplFSpUiJYtW7Ju3TqLQvgAo0aN4sMPP6R///5cvXqVhQsX0rhx43zFULZsWRYtWsT48eMZNmwYtra2+Pv7M3z48HwnuvIrPj6e1157zexYzueNGzcaK5v69++PyWRi7ty5XLx4kZo1axIXF0fFihWN6+zt7ZkzZw6RkZGEh4dTqFAhAgMDzd4eCdlzJjo6mqioKD777DPKlStHZGQk7du3N2s3cuRIihQpwqRJk/jnn3+oX78+8+bNy/Wtf7ejfPOC25opIiIicieysrLyXc9URET+nWxMJpPJ2kHIv1dwcDDOzs7Exsbm2TYrK4vWrVvz1FNP8c4779yH6ORu7dmzB4AfLu7kVNJZK0cjIiIij6pybqUJa9HH2mGIiMg9ds9XUomkpaVx4MAB1q9fz5kzZ+jVq5e1Q5LbdCrpLEcvnLB2GCIiIiIiIvIQU5JKbiozM5NbLbS7vnD8rSQkJNCtWzfc3d155513Hug3zRXUM4uIiIiIiIjI7dFP3HJTgYGBnDx58qbnDx48yKJFi/Lsp0KFChw8eLAgQ7tn8vPMIiIiIiIiIlLwlKSSm5oxYwZpaWnWDuO+ehSfWURERERERORBoCSV3FTO2wwfJY/iM4uIiIiIiIg8CPQOVxERERERERERsTolqURERERERERExOqUpBIREREREREREatTTSoRyVM5t9LWDkFEREQeYfq7iIjIo0FJKhHJU1iLPtYOQURERB5xWVlZ2NpqI4iIyMNMf8qLyC2lpaWRmppq7TDEylJTU/njjz80Fx5xmgeSQ3NBctzPuaAElYjIw09/0otInkwmk7VDECszmUykpqZqLjziNA8kh+aC5NBcEBGRgqQklYiIiIiIiIiIWJ2SVCIiIiIiIiIiYnVKUomIiIiIiIiIiNUpSSUiIiIiIiIiIlanJJWI5MnGxsbaIYiV2djY4OTkpLnwiNM8EBEREZF7qZC1AxCRB5uDgwNOTk7WDkOszMnJCW9vb2uHIVb2b5gHWVkmbG2VRBMRERH5N1KSSkTyNG3ZNk4mJFk7DBGRWypfyo1XevhbOwwRERERuUNKUolInk4mJHHk5CVrhyEiIiIiIiIPMdWkEhERERERERERq1OSSkRERERERERErE5JKhERERERERERsTolqURERERERERExOqUpBIREREREREREatTkkpERERERERERKxOSSoREREREREREbG6QtYOQEQefL5e5Sjn4WrtMEREbsnD3cXaIYiIiIjIXVCSKp/2799Pp06dWLhwIY0bN7Z2OAXqxIkTtGrViilTptCuXbv7cs/9+/fz7bff0q9fP5ycnO7LPQGCg4NxdnYmNjb2nt4nLS2NqKgodu3axb59+0hNTeXHH3/E3d3dou1vv/3GuHHj2L9/PyVKlKBHjx70798fGxsbo43JZGL27NksXbqUixcvUrNmTSIiIqhbt65ZX2fPniUyMpKtW7dib29PYGAgERERuLjc3Q9u3dvVzbONiIhYnykrCxtbLZQXERGRfyclqcQq9u/fT0xMDL169bqvSar75erVqyxfvpw6derQoEEDtm7dmmu7o0ePEhoair+/P0OGDOHgwYNMnDgROzs7QkNDjXazZ89m6tSpDBs2DC8vL5YsWUJISAirVq2iYsWKAKSnp9OvXz8AJk2axNWrVxk3bhxDhw6966Tc4dWzSb1w+q76EBGRe8upRFk8O/S3dhgiIiIid0xJqoeYyWQiPT0dBwcHa4dyz2RmZpKVlYW9vb21QzHj6urKzz//jI2NDStWrLhpkiouLo7ixYvz0Ucf4eDggJ+fHxcvXmTmzJkEBwfj4ODAtWvXiI2NJSQkhL59+wLQoEED2rVrR1xcHO+++y4A69evJz4+njVr1lClShUjjtDQUHbv3o2Pj88dP0/qhdOknj12x9eLiIiIiIiI5OWhXA++c+dOQkJCqF+/PvXq1aNbt25s27YNgMTERCIiImjcuDE+Pj4EBQXxyy+/WPQxffp0/P39qVevHq+++ioXLlywaGMymYiLi6Nt27bUrl2bVq1aMX/+fLM2Z86c4bXXXqNJkybUqVOHli1b8uGHH5q1+fvvvxk4cCANGjSgbt26DBgwgGPHzBMCc+fO5bnnnqNBgwb4+fkRFhbG4cOHzdoMHz6cDh06sHnzZv7zn/9Qp04dNm3alOeY5Lh27RrvvfcejRo1omnTpowbN46MjAzjfHR0NPXq1ePgwYP06NEDX19fOnTowPfff28xNitWrODZZ5+lTp06NGvWjMmTJ5OZmWmci4iIAMDPzw8vLy9atmxp0UdugoODCQsL44svvqBt27bUqVOHAwcOGLHdqGHDhkRHR1scX7lyJa1bt8bHx4fg4GAOHTpknBs0aBBBQUEW1yxdupQ6deqQmJiYr1iv3653M1u2bKFVq1ZmicSnn36a5ORkdu7cCWRvB7xy5Qrt27c32jg4OBAYGMiWLVvM+vLy8jISVAD+/v4UK1aMzZs35ytmEREREREREWt56FZS/frrr7zwwgvUrVuXyMhIXF1d2bt3L6dOnSIzM5P+/ftz/Phxhg0bRsmSJVm0aBEvvvgiH3/8MbVr1wZg8eLFTJkyhZCQEJo0acIPP/zA22+/bXGvDz74gOXLl/PSSy/h6+vLb7/9xsSJEylcuDA9evQA4M033yQhIYGRI0dSokQJTp8+zd69e40+jh8/TlBQENWqVWPs2LHY2Ngwc+ZM+vbty7p164zkxZkzZ+jduzflypXjypUrfPzxxwQFBbF+/XqKFStm9JeQkEBkZCQDBw6kbNmylCtX7pZjcr2oqChatWpFVFQUO3fuJDo6mkqVKhnPAtlbyoYNG0afPn14+eWXmT17NoMHD2bTpk0UL14cgHnz5jFhwgReeOEFhg8fzt9//20kqYYNG0ZAQAADBw5kxowZzJkzh6JFi97Waq+9e/dy8uRJXnvtNVxdXSlbtmy+rwXYt28fx44dY+jQocZz9+vXzxjvbt260b9/fw4dOmSW8Pn8888JDAw0G++7kZKSwunTp83uAVClShVsbGw4dOgQjRs3NhJoN7arWrUqCxYs4OrVqzg6OlrEC9mJMk9PT7MknIiIiIiIiMiD6KFLUk2YMIHKlSuzYMEC7OzsAGjatCkAGzduZPfu3cyZM4dmzZoZ59q0aUNsbCzR0dFkZmYSGxtLx44deeuttwBo1qwZFy5cYNWqVcZ9jh07xuLFixk9ejTdu3cHoEmTJly9epVp06bRvXt3bG1t2bNnD+Hh4Tz99NPGtZ06dTJ+HxMTg5ubG/PmzaNw4cIA1K9fn1atWrF8+XJ69eoFwIgRI4xrMjMz8ff3x8/Pj/Xr1xv3B0hKSmL27Nn4+voax956662bjsn1fHx8GDlyJJC9Amf79u2sX78+1yRVixYtAPD09KRVq1Zs2bKFjh07cuXKFaZOnUq/fv0IDw83+rK3t2fs2LGEhobi7u5OpUqVAKhVq1auxcRvJSkpic8+++y2k1M5Lly4wOLFi3nssccA8Pb2pl27dqxYsYKgoCCaNm1KuXLl+Pzzz3njjTcA+PPPP9m7d6/xTAXh8uXLQPaWvOs5ODjg5OREUlISAMnJyTg4OBjzI4erqysmk4mkpCQcHR1JTk6maNGiFvdxc3Mz+hIRERERERF5UD1U2/1SU1PZtWsXnTp1MpIx19uxYwcuLi5Gggow3oD266+/AtkrlhISEggMDDS7tm3btmaff/jhBwDatGlDRkaG8atJkyacO3eO06ezi0x7e3szd+5cli5dytGjRy1i2rZtGy1btsTOzs7ow9XVFW9vb7MVV7///jsvvvgijRs3xtvbG19fX1JSUjhy5IhZf8WKFTNLUOU1Jte7MXFVtWpVzpw5Y3bM1tYWPz8/43OFChVwdHTk7NmzQPa2wpSUFNq1a2cxLlevXiU+Pv6WMeRH9erV7zhBBVCtWjUjQQVQuXJlatSowa5du4DsZ3zuuedYtWqVsd3x888/p3z58mbPLiIiIiIiIiIF56FaSZWcnExWVhalSpW66fkSJUpYHC9ZsqSx0uTcuXMAFqt7SpYsafb50qVLmEwmnnzyyVzvdfr0acqXL8/kyZOZPHkyUVFRjB49Gk9PT8LDw2nTpo3Rz4IFC1iwYIFFHznFwE+dOkVISAi1a9dm9OjRlCpVCnt7e8LCwrh27dot48xrTK534yoce3t70tLSzI45OjpabM2zt7c34rh06RIAnTt3zvUeOcm7u3HjM96u3OZAiRIljO8eoGvXrkyfPp3NmzfTvHlzvvzyS3r27IltAb7WO2e8c1ZU5UhLSyM1NRU3Nzcge8VUWloa165dM1tNlZycjI2NjVm7K1euWNwnKSnprpJ6IiIiIiIiIvfDQ5WkKlq0KLa2tiQkJOR63s3NLdcC6OfPnzd+0Pfw8ADg4sWLFm1u7MvGxoalS5fm+mY5T09PAEqVKsWYMWPIyspi7969zJgxg9dff51169ZRsWJF3NzcaNGiBT179rToo0iRIgB8//33pKSkEBMTY2wNy8jIyHUL143FuvMak4KWM44xMTGUKVPG4nyFChXu+h65FSQvXLgw6enpZsfS09NJSUmxaJvbHLhw4QI1atQwPpcpU4ZmzZrx+eefk5mZyaVLl+jSpctdx349Z2dnypYta1Ev6vDhw5hMJqO+VM7/Hj582CzGQ4cOUa5cORwdHY12f/75p1lfJpOJw4cP4+/vX6Cxi4iIiIiIiBS0h2q7n7OzM3Xr1mXVqlXGm+Su16BBA65cucLWrVuNYxkZGXz77bc0aNAAyE5OeHh48M0335hdu379erPPOdu+EhMTqVOnjsUvFxcXs/a2trb4+PgwZMgQMjIyjK1/fn5+xMfH4+3tbdFHTnLi6tWr2NjYUKjQ/3KKa9euNXvz3p2OSUGrV68eTk5OnDlzJtdxySmunpPYu3Gl1p0qXbo06enpZm9F/Omnn3J95vj4eLOtl0ePHuXAgQNm2yQBunXrxubNm5k7dy5+fn6UL1++QGK9XvPmzdm4caNZgm3NmjW4uroabyusX78+Li4urF271miTnp7Ohg0baN68uVlfBw4cMNsC+uOPP5KYmGjUEBMRERERERF5UD1UK6kAhg4dSt++fenbty89e/bEzc2Nffv2Ubx4cTp37oyPjw9vvPEGQ4cONd7ul5CQwNSpUwGws7NjwIABfPDBB5QoUQJ/f3+2bdvG9u3bze7j6elJr169ePPNNwkNDcXX15f09HSOHDnC9u3bmT59OpcvXyY0NJSOHTvi6elJeno6ixYtMmpOAQwePJiuXbsSGhrK888/T8mSJTl//jw///wzDRs2pEOHDsaWwoiICIKCgoiPj2fevHkWBbfvZEy6du1agKOfveVs8ODBTJgwgTNnzvDEE09gZ2fH8ePH2bhxI9HR0Tg5OVG1alUAlixZQuvWrXF0dMTLy+uO79u8eXOcnZ0ZOXIk/fv358yZMyxcuNCi2Dhkb+176aWXGDx4MABTpkyhdOnSFiulAgICKF68ODt37uSjjz667Zg2b95MamqqUVvs//7v/yhSpAiPP/44jz/+OAChoaF89dVXDB06lB49evDnn38SFxfH66+/bmyrLFy4MGFhYURHR+Pu7k716tVZtmwZiYmJhIaGGvdr27YtsbGxDBo0iPDwcFJTUxk/fjwBAQH4+PjcdvzXc/OsjaO75co4ERF5cBR2u7vt8CIiIiLW9tAlqRo2bMjChQuJiooiIiICW1tbqlWrxpAhQ7Czs2PWrFmMHz+eCRMmkJKSQq1atZg7dy61a9c2+ggODiY5OZmlS5eybNky/Pz8iIyMpF+/fmb3GjlyJJ6ennzyySdMmzaNIkWK4OnpSbt27YDs5EL16tVZtGgRp0+fxtHRkdq1axMXF2fUvKpcuTLLly83alalpKTg4eFBo0aNjKSNl5cXY8aMISYmhrCwMGrWrMmUKVMYMmTIXY/JvRASEkLp0qWZN28eixcvplChQlSqVImAgABjBZW3tzeDBg1i+fLlzJkzh7Jly7Jp06Y7vmfx4sWZOnUq48aN45VXXqFmzZqMHz+e4OBgi7a1atWiTZs2TJgwgXPnzuHr68vo0aMtam0VKlSIli1bsm7dOotC+vkxevRoTp48aXzOeUPjq6++yqBBg4Ds7z8uLo6xY8cyYMAA3N3dGTx4MCEhIWZ99e/fH5PJxNy5c7l48SI1a9YkLi6OihUrGm3s7e2ZM2cOkZGRhIeHU6hQIQIDA83eDHmnyjcv2K2OIiLy4MnKyirQ2osiIiIit8vGZDKZrB2EyIMoKyuL1q1b89RTT/HOO+9YOxyr2LNnDwA/XNzJqaSzVo5GRETulXJupQlr0Sff7VNSUti/fz81a9bE2dn5HkYmDzrNBRERKUgP3UoqkbuVlpbGgQMHWL9+PWfOnKFXr17WDsnqTiWd5eiFE9YOQ0RERERERB5iSlLJAyMzM5NbLey7vnD8vZSQkEC3bt1wd3fnnXfeMQrY58jKyiIrK+um19vZ2eX6BkIRERERERERuTklqeSBERgYaFbD6UYHDx68L3FUqFDhlveaNm0aMTExNz0/ZswYiyLsIiIiIiIiInJrSlLJA2PGjBmkpaVZO4w8Pf/88wQEBNz0fIUKFe5fMCIiIiIiIiIPCSWp5IGR8zbDB13p0qUpXbq0tcMQEREREREReajoPcMiIiIiIiIiImJ1SlKJiIiIiIiIiIjVKUklIiIiIiIiIiJWp5pUIpKncm6qwSUi8jDTn/MiIiLyIFCSSkTyFNaij7VDEBGReywrKwtbWy2yFxEREevR30RE5JbS0tJITU21dhhiZampqfzxxx+aC484zYOHmxJUIiIiYm3624iI5MlkMlk7BLEyk8lEamqq5sIjTvNARERERO4lG5P+pikiN/Hbb79hMpmwt7fHxsbG2uGIFZlMJtLT0zUXHnGaB5JDc0FyaC7kj4ODA15eXtYOQ0TkgaeaVCJyUzl/2dRfOsXGxgYHBwdrhyFWpnkgOTQXJIfmgoiIFCStpBIREREREREREatTTSoREREREREREbE6JalERERERERERMTqlKQSERERERERERGrU5JKRERERERERESsTkkqERERERERERGxOiWpRERERERERETE6pSkEhERERERERERq1OSSkRERERERERErE5JKhERERERERERsTolqURERERERERExOqUpBIREREREREREatTkkrkEfX333/z4osvUrduXfz9/Rk/fjxpaWl5XmcymZg1axYBAQH4+PjQvXt3fv/993sfsNwzdzIXEhISGD9+PB07dqRevXo0b96coUOHcvLkyfsUtRS0O/0z4Xrz58/Hy8uLsLCwexSl3A93MxfOnj3LW2+9xZNPPomPjw/t27fnyy+/vMcRy71yp3Ph0qVLjBo1ioCAAOrWrUuHDh1YtmzZfYhYRET+7QpZOwARuf+SkpJ44YUXeOyxx4iOjubs2bOMHTuWq1evMmrUqFteO3v2bKZOncqwYcPw8vJiyZIlhISEsGrVKipWrHifnkAKyp3OhX379vHNN9/w3HPP4evry6VLl5gxYwbdunVj9erVuLu738enkLt1N38m5Dh37hzTpk2jRIkS9zhauZfuZi4kJCTQvXt3PD09ef/993FxcSE+Pv62k53yYLibufDaa69x6NAhwsPDKVu2LFu2bOHdd9/Fzs6O559//j49gYiI/BspSSXyCPr444/5559/iImJoVixYgBkZmYyevRowsLCKF26dK7XXbt2jdjYWEJCQujbty8ADRo0oF27dsTFxfHuu+/enweQAnOnc6FBgwasXbuWQoX+95+R+vXrExAQwMqVKwkJCbkf4UsBudN5cL0JEybQsmVLTp06dY+jlXvpbubChAkTKFOmDHPmzMHOzg4APz+/+xG23AN3OhfOnTvH9u3bGTNmDF26dAGy58GePXv4+uuvlaQSEZFb0nY/kUfQli1b8PPzM/7SCdC+fXuysrLYtm3bTa/77bffuHLlCu3btzeOOTg4EBgYyJYtW+5lyHKP3OlccHV1NUtQAZQpUwZ3d3cSEhLuVbhyj9zpPMixY8cOvv32W4YOHXoPo5T74U7nwpUrV1i7di09e/Y0ElTy73ancyEjIwOAokWLmh13cXHBZDLdk1hFROThoSSVyCPo0KFDVKlSxeyYq6srHh4eHDp06JbXARbXVq1alVOnTnH16tWCD1buqTudC7k5fPgwFy5coGrVqgUZotwHdzMPMjMzef/993nppZcoVarUvQxT7oM7nQv79u0jPT2dQoUK0bt3b2rVqoW/vz8TJkwgPT39Xoct98CdzoWyZcvStGlTZs6cyV9//cWVK1dYs2YN27Zto1evXvc6bBER+ZfTdj+RR1BycjKurq4Wx93c3EhKSrrldQ4ODhQuXNjsuKurKyaTiaSkJBwdHQs8Xrl37nQu3MhkMhEZGUmpUqV45plnCjJEuQ/uZh4sXbqU1NRUYwuw/Lvd6Vw4f/48ACNHjuT555/n1VdfZffu3UydOhVbW1utsvsXups/F6Kjo3n99deN/x7Y2dkxcuRI2rZte09iFRGRh4eSVCIicteio6P56aefmDNnDs7OztYOR+6TCxcuMHXqVMaNG4eDg4O1wxErysrKAqBJkyYMHz4cgCeffJJ//vmHuXPn8sorr+gfMR4RJpOJiIgIjhw5wqRJk/Dw8OCHH37gww8/xM3NTf+QISIit6QklcgjyNXVlcuXL1scT0pKws3N7ZbXpaWlce3aNbPVVMnJydjY2NzyWnkw3elcuN6nn37KtGnT+OCDD1Qk+V/qTufBlClT8PLyomHDhiQnJwPZ9WgyMjJITk7G2dnZonaZPNju5r8PkJ2Yup6fnx8zZ87k6NGjeHl5FWywck/d6Vz47rvvWLduHV9++aXxnTdu3JgLFy4wduxYJalEROSWVJNK5BFUpUoVi3oSly9f5ty5cxb1J268DrJrD13v0KFDlCtXTv9K/i90p3MhxzfffMO7777L4MGD6dq1670KU+6xO50Hhw8f5pdffqFRo0bGr99++42tW7fSqFEjfvjhh3sduhSwO50Ljz/++C37vXbtWoHEJ/fPnc6Fv/76Czs7O6pXr252vGbNmiQkJJCamnpP4hURkYeDklQij6DmzZvzww8/GCsfANatW4etrS3+/v43va5+/fq4uLiwdu1a41h6ejobNmygefPm9zRmuTfudC4AbN++nfDwcLp168Yrr7xyr0OVe+hO58GIESNYuHCh2a8aNWpQt25dFi5ciI+Pz/0IXwrQnc6F8uXLU716dYvE5A8//ICjo2OeSSx58NzNXMjMzOTgwYNmx/ft20eJEiVwcnK6ZzGLiMi/n41J74IVeeQkJSXxzDPP4OnpSVhYGGfPnmXs2LE8++yzjBo1ymj3wgsvcOrUKb755hvj2KxZs4iOjmbYsGFUr16dZcuWsXXrVlatWkXFihWt8ThyF+50Lvz99990796dsmXLMnr0aGxt//dvHu7u7lSqVOm+P4vcubv5M+FGwcHBODs7Exsbez9ClwJ2N3Nh06ZNvPzyywQHBxMQEMCePXuIiYkhNDSU119/3RqPI3fhTufClStXePbZZ7G3t+eVV16hVKlSbN26lblz5zJo0CBefvllaz2SiIj8C6hQhMgjyM3NjQULFvD+++/zyiuvUKRIEbp27WrxQ0RWVhaZmZlmx/r374/JZGLu3LlcvHiRmjVrEhcXpwTVv9SdzoVdu3Zx+fJlLl++TI8ePczadu7cmbFjx96X+KVg3M2fCfJwuZu50LJlSz766COmT5/OsmXLKFWqFIMGDWLAgAH38xGkgNzpXHBxcWH+/PlMnjyZiRMncvnyZSpUqMDw4cPp3bv3/X4MERH5l9FKKhERERERERERsTrVpBIREREREREREatTkkpERERERERERKxOSSoREREREREREbE6JalERERERERERMTqlKQSERERERERERGrU5JKRERERERERESsTkkqERERERERERGxOiWpRERERERERETE6pSkEhERkXwbPnw4LVu2tHYYBeLf8ixeXl689957d3z9P//8g5+fH19++WUBRlXw0tPTadGiBUuWLLF2KCIiImIlSlKJiMgjY8WKFXh5eeHl5cWOHTsszptMJlq0aIGXlxdhYWFWiDD/WrZsaTyLl5cXdevWpWvXrqxcudLaoT10goODzcbax8eHZ599lvnz55OVlWXt8PK0cOFCihQpwjPPPGMci46OxsvLixo1anD69GmLa65cuYKPj49FguzEiRNmY1GjRg2eeOIJ+vXrx86dOy36yblPzq9atWrRsmVLIiMjSU5ONmtrb2/Piy++yMyZM7l27VoBjoCIiIj8WxSydgAiIiL3W+HChVm9ejUNGzY0O/7zzz9z5swZHBwcrBTZ7alZsyYvvvgiAOfOnWP58uW89dZbpKWl8fzzz9+Te77//vuYTKZ70veDrEyZMoSHhwNw6dIlVq9ezZgxY7h06RKvv/66laO7ufT0dBYuXEjfvn2xs7OzOO/g4MDq1avp37+/2fENGzbcst8OHTrQvHlzsrKyOHLkCEuXLqVPnz589tlneHl5WbR/9913cXZ2JjU1lR9//JFFixaxb98+li1bZtauS5cuTJw4ka+++oquXbvewROLiIjIv5lWUomIyCOnRYsWrFu3joyMDLPjq1evplatWnh4eFgpsttTunRpOnbsSMeOHenXrx/Lli3D2dmZ+fPn37N72tvbF2gSLyUlpcD6upeKFi1qjHXfvn1ZsmQJ5cuXZ9GiRWRmZlo7vJv67rvvuHjxIu3bt8/1fIsWLfj6668tjq9evZqAgICb9uvt7U3Hjh3p3Lkzr7/+OpMmTSItLc0i6ZSjbdu2dOzYkaCgIKZMmcLTTz/Nb7/9xu7du83aubq60rRpU7744ov8P6SIiIg8NJSkEhGRR84zzzxDYmIi27ZtM46lpaWxfv16nn322VyvycrKYv78+TzzzDPUqVOHJk2aMGrUKJKSkszaffvttwwYMICmTZtSu3ZtWrduzbRp0ywSGcHBwXTo0IG//vqL4OBgfH19adasGbNnz77j53J3d6dKlSocO3bsjmIH2Lx5M71796ZevXrUr1+f5557jq+++so4f2Mdp5ztX3FxccyfP5+nnnoKHx8fevfuzZ9//mnW9/Dhw6lXrx7Hjh2jf//+1KtXj2HDhgHZyaqxY8fSokULateuTdu2bYmLi8t11daqVavo2rUrvr6+NGrUiF69erF161bjfH6/g7tRuHBhateuzT///MOFCxeM4wcOHGD48OG0atWKOnXq4O/vT0REBJcuXTK7Pmcb3NGjRxk+fDgNGzakQYMGREREkJqamuf9p0+fTo0aNVi0aNEt23377beUL1+eSpUq5Xq+Q4cO7N+/n7///ts4du7cOX766Sc6dOiQZxw5clYlHj9+/Lba3zhXAZo0acKvv/5KYmJivu8vIiIiDwdt9xMRkUdO+fLlqVu3Ll9//TUtWrQAYMuWLVy+fJmnn3461x/8R40axRdffEGXLl0IDg7mxIkTLFmyhD/++INly5Zhb28PwBdffIGzszMvvvgizs7O/PTTT0ydOpUrV67w1ltvmfWZlJREv379CAwMpH379qxfv56JEydSvXp1I67bkZGRwdmzZ3Fzc7uj2FesWMGIESOoVq0aYWFhFC1alP379/P999/fNHmXY+XKlfzzzz/07NmTa9eusWjRIl544QW++uorSpYsaRZjaGgoDRo04K233sLR0RGTycTAgQPZvn07Xbt2pWbNmnz//feMHz+es2fPMmLECOP6mJgYoqOjqVevHoMHD8be3p5du3bx008/0bRp09v+Du7GyZMnsbGxwdXV1Tj2ww8/cPz4cbp06YKHhwfx8fF8+umn/PXXX3z66afY2NiY9TFkyBAqVKhAeHg4f/zxB8uXL8fd3Z033njjpvedPHkysbGxvPfee3lu69y5cye1atW66flGjRpRpkwZVq9ezWuvvQbAmjVrcHZ2vuVKqhudPHkSwGwsbuXEiRM3bV+rVi1MJhM7d+7kqaeeyncMIiIi8u+nJJWIiDySnn32WSZNmsTVq1dxdHTkq6++olGjRpQuXdqi7Y4dO1i+fDkTJ040S9Y0btyYfv36sW7dOuP4pEmTcHR0NNr06NGDUaNGsWzZMl5//XWzrXIJCQmMGzeOTp06AdC1a1datmzJ559/nq8kVUZGBhcvXgTg/PnzzJkzh3PnztGrV6/bjv3y5ctERkbi4+PDokWLKFy4sNE2PzWojh07xoYNG4zxa968Od26dWP27NlEREQY7dLS0mjXrh1Dhw41jn377bf89NNPDBkyhIEDBwLQq1cvBg8ezMKFC+nduzeVKlXi6NGjTJs2jcDAQKZOnYqt7f8WhF8f4+18B/mVmZlpjHViYiKfffYZe/fuJSAgwOxePXv2JCQkxOzaunXrEh4ezq+//mpRB61mzZp8+OGHxuecvm+WpBo3bhzz589nzJgxdO7c+ZYxZ2RkcOzYMVq1anXLdk8//TRff/21kaT66quvCAwMvOU4paamcvHiRaMm1dixY4HsbX25yVm1l5qayk8//cTSpUtxd3enUaNGFm0rVqwIwF9//aUklYiIyCNG2/1EROSR1L59e65du8b//d//ceXKFb777rubrhZat24dRYsWxd/fn4sXLxq/atWqhbOzM9u3bzfaXp+wuHLlChcvXqRhw4akpqZy6NAhs36dnZ3p2LGj8dnBwYE6derke8vU1q1b8fPzw8/Pj2effZZVq1bRpUsX3nzzzduOfdu2bfzzzz8MGDDALEEFWKz+yU3r1q3NEnw+Pj74+vqyefNmi7Y9evQw+7xlyxbs7OwIDg42Ox4SEoLJZGLLli1AdjIrKyuLV155xSxBdWOMt/Md5NehQ4eMsW7fvj1xcXG0bNmSMWPGmLW7/t7Xrl3j4sWL+Pr6ArBv3z6LfoOCgsw+N2zYkMTERK5cuWJ23GQy8d5777Fw4UImTJiQZ4IKshNDJpMpz9VNzz77LEePHmX37t0cPXqUPXv25LlyLjo6Gj8/P/z9/enVqxd///03w4cPp127drm2b9euHX5+frRs2ZIRI0ZQqVIlZs+ejZOTk0XbnJWAN26RFBERkYefVlKJiMgjyd3dHT8/P1avXs3Vq1fJzMy86SqQo0ePcvnyZfz8/HI9f31Novj4eKKiovjpp58sEg2XL182+1ymTBmLBJCbmxsHDx7M1zP4+voyZMgQMjMziY+PZ8aMGSQnJxvb924n9pzaQNWqVcvXvW9UuXJli2OPPfYYa9euNTtWqFAhypQpY3bs5MmTlCpVChcXF7PjVatWNc7nxGhra2scv5nb+Q7yq3z58kRGRpKVlcWxY8eYOXMmly5dskjoJSYmEhMTw5o1a8zmxc3uXa5cObPPOQmlpKQks/FYuXIlKSkpvPvuu7dVKwryXgnn7e1NlSpVWL16Na6urnh4ePDkk0/e8pru3bvTrl07rl27xk8//ZRnAfno6GhcXFy4ePEiixYt4sSJE2YJvdzizU9yVERERB4uSlKJiMgjq0OHDrzzzjucP3+e5s2b33TFSVZWFiVKlGDixIm5nnd3dwcgOTmZ3r174+LiwuDBg6lUqRKFCxdm3759TJw4kaysLLPr7Ozs7ir+4sWL06RJEwCaNWtGlSpVCAsLY+HChbz44ou3Ffv94uDgYLEKqiDd7neQX87OzsZYA9SvX58uXbowefJkRo4caRwfMmQIO3fuJDQ0lJo1a+Ls7ExWVhb9+vXLNVl0s7G4sW39+vU5cOAAS5YsoX379hQrVizPmN3c3LCxsSE5OTnPth06dGDZsmUUKVKE9u3b5/kdVa5c2RiPp556CltbWyZNmkTjxo2pU6eORfuGDRsac+2pp57i2WefZdiwYaxYscLiXjlbA4sXL55n3CIiIvJw0XY/ERF5ZAUGBmJra8vvv/9+y9UplSpVIjExkfr169OkSROLXzVq1ADg559/JjExkbFjx/LCCy/w1FNP0aRJE4tC5vdKQEAATzzxBDNnziQlJeW2Ys95+1t8fPwd3fvo0aMWx44cOUL58uXzvLZ8+fIkJCRYrHrK2ZqX00elSpXIysoyexPdje7Xd1CjRg3+85//8PHHH3Pq1CkgO7ny448/0r9/fwYPHkxgYCD+/v5GjaW7UblyZeLi4khISKBfv34WY5WbQoUKUalSJaNI+a08++yznDt3jiNHjuS51S83AwcOpEiRIkRFReXZtkiRIrz66qvs37/fYqUd/K+oel4r5kREROThoySViIg8sooUKcK7777LoEGDaNmy5U3btW/fnszMTKZPn25xLiMjw1ipkrMi5PpVMGlpaSxdurSAI7+5fv36kZiYyKeffgrkP/amTZtSpEgRYmNjuXbtmlm7/BRO//bbbzl79qzxeffu3ezatYvmzZvneW3z5s3JzMxkyZIlZsfnz5+PjY2N0Ufr1q2xtbVl2rRpFiuicmK8n99Bv379yMjIYN68ecDNV8YtWLCgQO5Xo0YNZs2axd9//83AgQO5evVqntfUrVuXvXv35tmuUqVKjBgxgqFDh+Lj43Pbsbm6utK9e3e2bt3K/v3782z/7LPPUqZMGWbPnm1xbt++fdjY2FC3bt3bjkNERET+3bTdT0REHmn5KUD9xBNP0L17d2JjY9m/fz/+/v7Y29tz5MgR1q1bx9tvv027du2oV68ebm5uDB8+nODgYGxsbFi1alW+kjwFpUWLFlSvXp358+fTq1evfMfu4uJCREQEI0eOpGvXrnTo0AFXV1cOHDjAHUGzfQAAAx1JREFU1atXGTdu3C3vW6lSJXr06EGPHj1IS0tj4cKFFCtWjH79+uUZc8uWLWncuDGTJ0/m5MmTeHl5sW3bNjZu3MgLL7xgrPKqXLkyL730EtOnT6dnz560adMGBwcH9uzZQ6lSpRg6dOh9/Q4ef/xxWrRowWeffcbLL79M8eLFadSoEXPmzCE9PZ3SpUuzbdu2fK1kyq+6desyffp0BgwYwODBg5k2bZpZDbIbtWrVilWrVnH48GE8PT1v2fcLL7xwV7H16dOHBQsWMGvWLCZPnnzLtvb29vTp04fx48ezZcsWs2TmDz/8QP369bXdT0RE5BGklVQiIiL58N577/H+++9z4cIFJk+ezKRJk/jpp5/4z3/+Q/369YHsGjozZ87Ew8ODqKgo4uLiaNKkCW+88cZ9jTUkJITTp0/z1Vdf5Tt2gG7dujFjxgyKFCny/+3dvUojYRgF4LOilVik0RvwJgQbsRbSWYlCKiu7AW9AG/8QYlKkiKCWdhaKoEWCd2BnJ96AEBCLtRMEVwV3/SD7PPUMvMx053vnTA4ODrK1tZXb29svbUPV6/UsLS3l+Pg47XY709PTOTw8zOTk5Kf3joyMpNVqZXl5OVdXV9nc3Mzd3V2qqsr6+vqba9fW1rKxsZGnp6fs7u5mf38/Dw8Pr8XwP/0OGo1GBoNBjo6OkiTb29uZnZ3NyclJdnZ2Mjo6+u620HfMzMxkb28v/X4/VVV92LM1NzeXWq327md1f9vU1FQWFhZyfn7+WsT/kcXFxUxMTLx5Po+Pj+n1el8KjwGA4fPr908e7wIAQ+X+/j7z8/OpqiqNRqP0OLyj2Wzm9PQ0FxcX3y7r/9e63W46nU4uLy//+Pc/AGB42aQCABhiKysrGQwGOTs7Kz3Kh56fn9PtdrO6uiqgAoD/lE4qAIAhNj4+npubm9JjfGpsbCzX19elxwAACrJJBQAAAEBxOqkAAAAAKM4mFQAAAADFCakAAAAAKE5IBQAAAEBxQioAAAAAihNSAQAAAFCckAoAAACA4oRUAAAAABQnpAIAAACgOCEVAAAAAMW9AP6TZ413XoJbAAAAAElFTkSuQmCC", "text/plain": ["<Figure size 1316.5x500 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["from analysis_code import load_results, plot_results\n", "results = load_results()\n", "plot_results(results)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Analysis\n", "\n", "Even though the retrievers were not trained explicitly on the objective of matching functions with docstrings, they are still able to perform the task to some extent.\n", "\n", "* contrieve models perform better than the commit-contrast model on this task.\n", "* contrieve with batch size 16384 perform better than contrieve with batch size 1024. This is not an apples-to-apples comparison because the model with the larger batch size was trained on more than double the tokens (31.5B vs. 67.1B)"]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Comparing to Published Results\n", "\n", "Note that these CodeSearchNet results aren't meant to be compared against published results.\n", "\n", "One reason is that commit-contrast and contrieve models were not trained on the objective of matching docstrings with functions, so they may not be competitive with models explicitly trained on that objective.\n", "\n", "Beyond that, there are various discrepancies to consider:\n", "\n", "* **Partial Batches:** the number of pairs for a given language is not divisible by the batch size, which needs to be handled in some way. For example, the Ruby dataset contains 2279 Ruby pairs, which is not enough for a single 10K batch. Also, in the case of 1K batches, having one of three batches be substantially smaller (279 rather than 1000) itself can influence the results.\n", "* **Python Docstring/Code Overlap:** in the CodeSearchNet dataset that can be found online, the Python dataset has an issue. The code function also includes the docstring itself, so matching code with a docstring is trivial, assuming that the model can pick up on that. It seems like this filtering was performed after tokenization. The dataset includes tokenized records with the overlap handled, but the BERT tokenization is not invertible due to whitespace handling, so there is no entirely trivial way to resolve the overlap.\n", "\n", "In this evaluation, we make throw away partial batches, so we don't have a score for codesearchnet_ruby_10000 since there are fewer than 10K pairs for <PERSON>. We don't do anything about the Python code/docstring overlap, for expediency reasons.\n", "\n", "In the OpenAI paper [Text and Code Embeddings by Contrastive Pre-Training](https://cdn.openai.com/papers/Text_and_Code_Embeddings_by_Contrastive_Pre_Training.pdf), we believe that partial batches were included and Python code/docstring overlap was also not handled.\n", "\n", "In the original CodeBERT and GraphCodeBERT, the Python code/docstring overlap seems to have been handled."]}, {"attachments": {}, "cell_type": "markdown", "metadata": {}, "source": ["## Appendix: Full Eval Results"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>benchmark</th>\n", "      <th>experiment</th>\n", "      <th>name</th>\n", "      <th>mrr</th>\n", "      <th>mrr_stderr</th>\n", "      <th>mean_rank</th>\n", "      <th>mean_rank_stderr</th>\n", "      <th>exact_match_accuracy</th>\n", "      <th>exact_match_accuracy_stderr</th>\n", "      <th>truncations</th>\n", "      <th>wandb</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>codesearchnet_go_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>commit-contrast-350M-batch1024</td>\n", "      <td>0.454997</td>\n", "      <td>0.039183</td>\n", "      <td>60.658860</td>\n", "      <td>28.265136</td>\n", "      <td>0.338500</td>\n", "      <td>0.029813</td>\n", "      <td>542</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>codesearchnet_java_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>commit-contrast-350M-batch1024</td>\n", "      <td>0.396998</td>\n", "      <td>0.012332</td>\n", "      <td>69.865273</td>\n", "      <td>5.811273</td>\n", "      <td>0.274731</td>\n", "      <td>0.009772</td>\n", "      <td>1279</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>codesearchnet_javascript_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>commit-contrast-350M-batch1024</td>\n", "      <td>0.441396</td>\n", "      <td>0.015982</td>\n", "      <td>53.666336</td>\n", "      <td>4.146258</td>\n", "      <td>0.330000</td>\n", "      <td>0.014973</td>\n", "      <td>413</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>codesearchnet_php_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>commit-contrast-350M-batch1024</td>\n", "      <td>0.376501</td>\n", "      <td>0.010246</td>\n", "      <td>73.307574</td>\n", "      <td>6.341054</td>\n", "      <td>0.261036</td>\n", "      <td>0.009014</td>\n", "      <td>1150</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>codesearchnet_python_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>commit-contrast-350M-batch1024</td>\n", "      <td>0.805459</td>\n", "      <td>0.006889</td>\n", "      <td>6.015409</td>\n", "      <td>0.476847</td>\n", "      <td>0.723818</td>\n", "      <td>0.007857</td>\n", "      <td>2382</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>codesearchnet_ruby_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>commit-contrast-350M-batch1024</td>\n", "      <td>0.460333</td>\n", "      <td>0.007435</td>\n", "      <td>42.589003</td>\n", "      <td>4.448999</td>\n", "      <td>0.330500</td>\n", "      <td>0.010500</td>\n", "      <td>76</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>codesearchnet_go_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>commit-contrast-350M-batch1024</td>\n", "      <td>0.361472</td>\n", "      <td>NaN</td>\n", "      <td>241.220001</td>\n", "      <td>NaN</td>\n", "      <td>0.262700</td>\n", "      <td>NaN</td>\n", "      <td>422</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>codesearchnet_java_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>commit-contrast-350M-batch1024</td>\n", "      <td>0.309099</td>\n", "      <td>0.004623</td>\n", "      <td>555.198883</td>\n", "      <td>19.586517</td>\n", "      <td>0.206850</td>\n", "      <td>0.003850</td>\n", "      <td>1009</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>codesearchnet_php_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>commit-contrast-350M-batch1024</td>\n", "      <td>0.273193</td>\n", "      <td>0.006656</td>\n", "      <td>667.766205</td>\n", "      <td>73.943695</td>\n", "      <td>0.181050</td>\n", "      <td>0.006050</td>\n", "      <td>787</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>codesearchnet_python_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>commit-contrast-350M-batch1024</td>\n", "      <td>0.767810</td>\n", "      <td>0.001308</td>\n", "      <td>38.187300</td>\n", "      <td>0.066900</td>\n", "      <td>0.687400</td>\n", "      <td>0.002400</td>\n", "      <td>2282</td>\n", "      <td>NaN</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>codesearchnet_go_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch1024</td>\n", "      <td>0.533663</td>\n", "      <td>0.043261</td>\n", "      <td>55.807074</td>\n", "      <td>29.110141</td>\n", "      <td>0.414143</td>\n", "      <td>0.033955</td>\n", "      <td>38</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/212p8ix8/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>11</th>\n", "      <td>codesearchnet_java_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch1024</td>\n", "      <td>0.540875</td>\n", "      <td>0.012182</td>\n", "      <td>38.936887</td>\n", "      <td>3.685213</td>\n", "      <td>0.414115</td>\n", "      <td>0.011908</td>\n", "      <td>99</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/212p8ix8/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>12</th>\n", "      <td>codesearchnet_javascript_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch1024</td>\n", "      <td>0.542030</td>\n", "      <td>0.009205</td>\n", "      <td>42.764336</td>\n", "      <td>2.655325</td>\n", "      <td>0.428833</td>\n", "      <td>0.010429</td>\n", "      <td>55</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/212p8ix8/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>13</th>\n", "      <td>codesearchnet_php_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch1024</td>\n", "      <td>0.500612</td>\n", "      <td>0.011351</td>\n", "      <td>52.063431</td>\n", "      <td>6.559058</td>\n", "      <td>0.380464</td>\n", "      <td>0.010311</td>\n", "      <td>95</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/212p8ix8/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>14</th>\n", "      <td>codesearchnet_python_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch1024</td>\n", "      <td>0.857042</td>\n", "      <td>0.005386</td>\n", "      <td>3.430409</td>\n", "      <td>0.235043</td>\n", "      <td>0.784409</td>\n", "      <td>0.007085</td>\n", "      <td>170</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/212p8ix8/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>15</th>\n", "      <td>codesearchnet_ruby_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch1024</td>\n", "      <td>0.558076</td>\n", "      <td>0.004616</td>\n", "      <td>35.894501</td>\n", "      <td>2.862499</td>\n", "      <td>0.433000</td>\n", "      <td>0.001000</td>\n", "      <td>4</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/212p8ix8/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>16</th>\n", "      <td>codesearchnet_go_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch1024</td>\n", "      <td>0.440388</td>\n", "      <td>NaN</td>\n", "      <td>322.979095</td>\n", "      <td>NaN</td>\n", "      <td>0.333900</td>\n", "      <td>NaN</td>\n", "      <td>32</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/212p8ix8/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>17</th>\n", "      <td>codesearchnet_java_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch1024</td>\n", "      <td>0.461436</td>\n", "      <td>0.037928</td>\n", "      <td>334.500443</td>\n", "      <td>60.511642</td>\n", "      <td>0.344450</td>\n", "      <td>0.033150</td>\n", "      <td>88</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/212p8ix8/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>18</th>\n", "      <td>codesearchnet_php_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch1024</td>\n", "      <td>0.400099</td>\n", "      <td>0.002810</td>\n", "      <td>475.772491</td>\n", "      <td>58.350494</td>\n", "      <td>0.293050</td>\n", "      <td>0.004450</td>\n", "      <td>68</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/212p8ix8/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>19</th>\n", "      <td>codesearchnet_python_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch1024</td>\n", "      <td>0.825889</td>\n", "      <td>0.005764</td>\n", "      <td>18.948449</td>\n", "      <td>0.443749</td>\n", "      <td>0.752000</td>\n", "      <td>0.006700</td>\n", "      <td>166</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/212p8ix8/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>20</th>\n", "      <td>codesearchnet_go_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch16384</td>\n", "      <td>0.576657</td>\n", "      <td>0.047737</td>\n", "      <td>49.684574</td>\n", "      <td>28.463627</td>\n", "      <td>0.465643</td>\n", "      <td>0.039628</td>\n", "      <td>38</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/22zy3i66/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>21</th>\n", "      <td>codesearchnet_java_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch16384</td>\n", "      <td>0.554793</td>\n", "      <td>0.012955</td>\n", "      <td>38.654387</td>\n", "      <td>3.655552</td>\n", "      <td>0.429385</td>\n", "      <td>0.012793</td>\n", "      <td>99</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/22zy3i66/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>22</th>\n", "      <td>codesearchnet_javascript_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch16384</td>\n", "      <td>0.573392</td>\n", "      <td>0.008640</td>\n", "      <td>39.036668</td>\n", "      <td>2.353646</td>\n", "      <td>0.461167</td>\n", "      <td>0.009156</td>\n", "      <td>55</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/22zy3i66/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>23</th>\n", "      <td>codesearchnet_php_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch16384</td>\n", "      <td>0.529416</td>\n", "      <td>0.010444</td>\n", "      <td>50.319860</td>\n", "      <td>6.290711</td>\n", "      <td>0.409750</td>\n", "      <td>0.010092</td>\n", "      <td>95</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/22zy3i66/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>24</th>\n", "      <td>codesearchnet_python_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch16384</td>\n", "      <td>0.869197</td>\n", "      <td>0.004824</td>\n", "      <td>3.160727</td>\n", "      <td>0.240511</td>\n", "      <td>0.799364</td>\n", "      <td>0.006882</td>\n", "      <td>170</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/22zy3i66/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>25</th>\n", "      <td>codesearchnet_ruby_1000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch16384</td>\n", "      <td>0.580748</td>\n", "      <td>0.008781</td>\n", "      <td>29.223001</td>\n", "      <td>4.407000</td>\n", "      <td>0.456000</td>\n", "      <td>0.012000</td>\n", "      <td>4</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/22zy3i66/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>26</th>\n", "      <td>codesearchnet_go_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch16384</td>\n", "      <td>0.487527</td>\n", "      <td>NaN</td>\n", "      <td>200.651596</td>\n", "      <td>NaN</td>\n", "      <td>0.383500</td>\n", "      <td>NaN</td>\n", "      <td>32</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/22zy3i66/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>27</th>\n", "      <td>codesearchnet_java_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch16384</td>\n", "      <td>0.474907</td>\n", "      <td>0.038282</td>\n", "      <td>320.013184</td>\n", "      <td>58.437195</td>\n", "      <td>0.357450</td>\n", "      <td>0.034050</td>\n", "      <td>88</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/22zy3i66/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>28</th>\n", "      <td>codesearchnet_php_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch16384</td>\n", "      <td>0.433975</td>\n", "      <td>0.005044</td>\n", "      <td>462.280640</td>\n", "      <td>79.091736</td>\n", "      <td>0.324250</td>\n", "      <td>0.008550</td>\n", "      <td>68</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/22zy3i66/o...</td>\n", "    </tr>\n", "    <tr>\n", "      <th>29</th>\n", "      <td>codesearchnet_python_10000</td>\n", "      <td>https://dev-training.tenant-augment-eng.las1.i...</td>\n", "      <td>contrieve-350M-batch16384</td>\n", "      <td>0.840700</td>\n", "      <td>0.005072</td>\n", "      <td>18.166599</td>\n", "      <td>0.271700</td>\n", "      <td>0.769100</td>\n", "      <td>0.006000</td>\n", "      <td>166</td>\n", "      <td>https://wandb.ai/igoro/codegen/runs/22zy3i66/o...</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                        benchmark   \n", "0           codesearchnet_go_1000  \\\n", "1         codesearchnet_java_1000   \n", "2   codesearchnet_javascript_1000   \n", "3          codesearchnet_php_1000   \n", "4       codesearchnet_python_1000   \n", "5         codesearchnet_ruby_1000   \n", "6          codesearchnet_go_10000   \n", "7        codesearchnet_java_10000   \n", "8         codesearchnet_php_10000   \n", "9      codesearchnet_python_10000   \n", "10          codesearchnet_go_1000   \n", "11        codesearchnet_java_1000   \n", "12  codesearchnet_javascript_1000   \n", "13         codesearchnet_php_1000   \n", "14      codesearchnet_python_1000   \n", "15        codesearchnet_ruby_1000   \n", "16         codesearchnet_go_10000   \n", "17       codesearchnet_java_10000   \n", "18        codesearchnet_php_10000   \n", "19     codesearchnet_python_10000   \n", "20          codesearchnet_go_1000   \n", "21        codesearchnet_java_1000   \n", "22  codesearchnet_javascript_1000   \n", "23         codesearchnet_php_1000   \n", "24      codesearchnet_python_1000   \n", "25        codesearchnet_ruby_1000   \n", "26         codesearchnet_go_10000   \n", "27       codesearchnet_java_10000   \n", "28        codesearchnet_php_10000   \n", "29     codesearchnet_python_10000   \n", "\n", "                                           experiment   \n", "0   https://dev-training.tenant-augment-eng.las1.i...  \\\n", "1   https://dev-training.tenant-augment-eng.las1.i...   \n", "2   https://dev-training.tenant-augment-eng.las1.i...   \n", "3   https://dev-training.tenant-augment-eng.las1.i...   \n", "4   https://dev-training.tenant-augment-eng.las1.i...   \n", "5   https://dev-training.tenant-augment-eng.las1.i...   \n", "6   https://dev-training.tenant-augment-eng.las1.i...   \n", "7   https://dev-training.tenant-augment-eng.las1.i...   \n", "8   https://dev-training.tenant-augment-eng.las1.i...   \n", "9   https://dev-training.tenant-augment-eng.las1.i...   \n", "10  https://dev-training.tenant-augment-eng.las1.i...   \n", "11  https://dev-training.tenant-augment-eng.las1.i...   \n", "12  https://dev-training.tenant-augment-eng.las1.i...   \n", "13  https://dev-training.tenant-augment-eng.las1.i...   \n", "14  https://dev-training.tenant-augment-eng.las1.i...   \n", "15  https://dev-training.tenant-augment-eng.las1.i...   \n", "16  https://dev-training.tenant-augment-eng.las1.i...   \n", "17  https://dev-training.tenant-augment-eng.las1.i...   \n", "18  https://dev-training.tenant-augment-eng.las1.i...   \n", "19  https://dev-training.tenant-augment-eng.las1.i...   \n", "20  https://dev-training.tenant-augment-eng.las1.i...   \n", "21  https://dev-training.tenant-augment-eng.las1.i...   \n", "22  https://dev-training.tenant-augment-eng.las1.i...   \n", "23  https://dev-training.tenant-augment-eng.las1.i...   \n", "24  https://dev-training.tenant-augment-eng.las1.i...   \n", "25  https://dev-training.tenant-augment-eng.las1.i...   \n", "26  https://dev-training.tenant-augment-eng.las1.i...   \n", "27  https://dev-training.tenant-augment-eng.las1.i...   \n", "28  https://dev-training.tenant-augment-eng.las1.i...   \n", "29  https://dev-training.tenant-augment-eng.las1.i...   \n", "\n", "                              name       mrr  mrr_stderr   mean_rank   \n", "0   commit-contrast-350M-batch1024  0.454997    0.039183   60.658860  \\\n", "1   commit-contrast-350M-batch1024  0.396998    0.012332   69.865273   \n", "2   commit-contrast-350M-batch1024  0.441396    0.015982   53.666336   \n", "3   commit-contrast-350M-batch1024  0.376501    0.010246   73.307574   \n", "4   commit-contrast-350M-batch1024  0.805459    0.006889    6.015409   \n", "5   commit-contrast-350M-batch1024  0.460333    0.007435   42.589003   \n", "6   commit-contrast-350M-batch1024  0.361472         NaN  241.220001   \n", "7   commit-contrast-350M-batch1024  0.309099    0.004623  555.198883   \n", "8   commit-contrast-350M-batch1024  0.273193    0.006656  667.766205   \n", "9   commit-contrast-350M-batch1024  0.767810    0.001308   38.187300   \n", "10        contrieve-350M-batch1024  0.533663    0.043261   55.807074   \n", "11        contrieve-350M-batch1024  0.540875    0.012182   38.936887   \n", "12        contrieve-350M-batch1024  0.542030    0.009205   42.764336   \n", "13        contrieve-350M-batch1024  0.500612    0.011351   52.063431   \n", "14        contrieve-350M-batch1024  0.857042    0.005386    3.430409   \n", "15        contrieve-350M-batch1024  0.558076    0.004616   35.894501   \n", "16        contrieve-350M-batch1024  0.440388         NaN  322.979095   \n", "17        contrieve-350M-batch1024  0.461436    0.037928  334.500443   \n", "18        contrieve-350M-batch1024  0.400099    0.002810  475.772491   \n", "19        contrieve-350M-batch1024  0.825889    0.005764   18.948449   \n", "20       contrieve-350M-batch16384  0.576657    0.047737   49.684574   \n", "21       contrieve-350M-batch16384  0.554793    0.012955   38.654387   \n", "22       contrieve-350M-batch16384  0.573392    0.008640   39.036668   \n", "23       contrieve-350M-batch16384  0.529416    0.010444   50.319860   \n", "24       contrieve-350M-batch16384  0.869197    0.004824    3.160727   \n", "25       contrieve-350M-batch16384  0.580748    0.008781   29.223001   \n", "26       contrieve-350M-batch16384  0.487527         NaN  200.651596   \n", "27       contrieve-350M-batch16384  0.474907    0.038282  320.013184   \n", "28       contrieve-350M-batch16384  0.433975    0.005044  462.280640   \n", "29       contrieve-350M-batch16384  0.840700    0.005072   18.166599   \n", "\n", "    mean_rank_stderr  exact_match_accuracy  exact_match_accuracy_stderr   \n", "0          28.265136              0.338500                     0.029813  \\\n", "1           5.811273              0.274731                     0.009772   \n", "2           4.146258              0.330000                     0.014973   \n", "3           6.341054              0.261036                     0.009014   \n", "4           0.476847              0.723818                     0.007857   \n", "5           4.448999              0.330500                     0.010500   \n", "6                NaN              0.262700                          NaN   \n", "7          19.586517              0.206850                     0.003850   \n", "8          73.943695              0.181050                     0.006050   \n", "9           0.066900              0.687400                     0.002400   \n", "10         29.110141              0.414143                     0.033955   \n", "11          3.685213              0.414115                     0.011908   \n", "12          2.655325              0.428833                     0.010429   \n", "13          6.559058              0.380464                     0.010311   \n", "14          0.235043              0.784409                     0.007085   \n", "15          2.862499              0.433000                     0.001000   \n", "16               NaN              0.333900                          NaN   \n", "17         60.511642              0.344450                     0.033150   \n", "18         58.350494              0.293050                     0.004450   \n", "19          0.443749              0.752000                     0.006700   \n", "20         28.463627              0.465643                     0.039628   \n", "21          3.655552              0.429385                     0.012793   \n", "22          2.353646              0.461167                     0.009156   \n", "23          6.290711              0.409750                     0.010092   \n", "24          0.240511              0.799364                     0.006882   \n", "25          4.407000              0.456000                     0.012000   \n", "26               NaN              0.383500                          NaN   \n", "27         58.437195              0.357450                     0.034050   \n", "28         79.091736              0.324250                     0.008550   \n", "29          0.271700              0.769100                     0.006000   \n", "\n", "    truncations                                              wandb  \n", "0           542                                                NaN  \n", "1          1279                                                NaN  \n", "2           413                                                NaN  \n", "3          1150                                                NaN  \n", "4          2382                                                NaN  \n", "5            76                                                NaN  \n", "6           422                                                NaN  \n", "7          1009                                                NaN  \n", "8           787                                                NaN  \n", "9          2282                                                NaN  \n", "10           38  https://wandb.ai/igoro/codegen/runs/212p8ix8/o...  \n", "11           99  https://wandb.ai/igoro/codegen/runs/212p8ix8/o...  \n", "12           55  https://wandb.ai/igoro/codegen/runs/212p8ix8/o...  \n", "13           95  https://wandb.ai/igoro/codegen/runs/212p8ix8/o...  \n", "14          170  https://wandb.ai/igoro/codegen/runs/212p8ix8/o...  \n", "15            4  https://wandb.ai/igoro/codegen/runs/212p8ix8/o...  \n", "16           32  https://wandb.ai/igoro/codegen/runs/212p8ix8/o...  \n", "17           88  https://wandb.ai/igoro/codegen/runs/212p8ix8/o...  \n", "18           68  https://wandb.ai/igoro/codegen/runs/212p8ix8/o...  \n", "19          166  https://wandb.ai/igoro/codegen/runs/212p8ix8/o...  \n", "20           38  https://wandb.ai/igoro/codegen/runs/22zy3i66/o...  \n", "21           99  https://wandb.ai/igoro/codegen/runs/22zy3i66/o...  \n", "22           55  https://wandb.ai/igoro/codegen/runs/22zy3i66/o...  \n", "23           95  https://wandb.ai/igoro/codegen/runs/22zy3i66/o...  \n", "24          170  https://wandb.ai/igoro/codegen/runs/22zy3i66/o...  \n", "25            4  https://wandb.ai/igoro/codegen/runs/22zy3i66/o...  \n", "26           32  https://wandb.ai/igoro/codegen/runs/22zy3i66/o...  \n", "27           88  https://wandb.ai/igoro/codegen/runs/22zy3i66/o...  \n", "28           68  https://wandb.ai/igoro/codegen/runs/22zy3i66/o...  \n", "29          166  https://wandb.ai/igoro/codegen/runs/22zy3i66/o...  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["results"]}], "metadata": {"kernelspec": {"display_name": "Python 3", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.16"}, "orig_nbformat": 4}, "nbformat": 4, "nbformat_minor": 2}