{"results": {"codesearchnet_go_1000": {"mrr": 0.5766571890562773, "mrr_stderr": 0.047737386262752216, "mean_rank": 49.68457439967564, "mean_rank_stderr": 28.46362686534352, "exact_match_accuracy": 0.46564287239951746, "exact_match_accuracy_stderr": 0.03962774786762303, "truncations": 38}, "codesearchnet_java_1000": {"mrr": 0.554792773265105, "mrr_stderr": 0.012954584059846836, "mean_rank": 38.65438674046443, "mean_rank_stderr": 3.65555228228072, "exact_match_accuracy": 0.4293846350449782, "exact_match_accuracy_stderr": 0.012792796475191518, "truncations": 99}, "codesearchnet_javascript_1000": {"mrr": 0.5733920633792877, "mrr_stderr": 0.008640023526181227, "mean_rank": 39.03666845957438, "mean_rank_stderr": 2.353645816793205, "exact_match_accuracy": 0.46116668979326886, "exact_match_accuracy_stderr": 0.009155749621876015, "truncations": 55}, "codesearchnet_php_1000": {"mrr": 0.5294164227587836, "mrr_stderr": 0.010443678416165647, "mean_rank": 50.31985977717808, "mean_rank_stderr": 6.290711027866481, "exact_match_accuracy": 0.409750018801008, "exact_match_accuracy_stderr": 0.010092381086978575, "truncations": 95}, "codesearchnet_python_1000": {"mrr": 0.8691973740404303, "mrr_stderr": 0.004824032598623463, "mean_rank": 3.1607274142178623, "mean_rank_stderr": 0.2405112845017551, "exact_match_accuracy": 0.799363678151911, "exact_match_accuracy_stderr": 0.006881981630076667, "truncations": 170}, "codesearchnet_ruby_1000": {"mrr": 0.5807483792304993, "mrr_stderr": 0.008780837059020996, "mean_rank": 29.22300148010254, "mean_rank_stderr": 4.406999588012695, "exact_match_accuracy": 0.45600003004074097, "exact_match_accuracy_stderr": 0.011999994516372679, "truncations": 4}, "codesearchnet_go_10000": {"mrr": 0.4875268042087555, "mrr_stderr": NaN, "mean_rank": 200.65159606933594, "mean_rank_stderr": NaN, "exact_match_accuracy": 0.38349997997283936, "exact_match_accuracy_stderr": NaN, "truncations": 32}, "codesearchnet_java_10000": {"mrr": 0.4749065935611725, "mrr_stderr": 0.038282185792922974, "mean_rank": 320.01318359375, "mean_rank_stderr": 58.43719482421874, "exact_match_accuracy": 0.3574499934911728, "exact_match_accuracy_stderr": 0.034050002694129944, "truncations": 88}, "codesearchnet_php_10000": {"mrr": 0.4339752346277237, "mrr_stderr": 0.005044385790824889, "mean_rank": 462.2806396484375, "mean_rank_stderr": 79.09173583984375, "exact_match_accuracy": 0.3242499977350235, "exact_match_accuracy_stderr": 0.008550003170967102, "truncations": 68}, "codesearchnet_python_10000": {"mrr": 0.8407000303268433, "mrr_stderr": 0.005072295665740966, "mean_rank": 18.16659927368164, "mean_rank_stderr": 0.2716999053955078, "exact_match_accuracy": 0.7690999805927277, "exact_match_accuracy_stderr": 0.006000012159347533, "truncations": 166}}, "versions": {"codesearchnet_go_1000": 1, "codesearchnet_java_1000": 1, "codesearchnet_javascript_1000": 1, "codesearchnet_php_1000": 1, "codesearchnet_python_1000": 1, "codesearchnet_ruby_1000": 1, "codesearchnet_go_10000": 1, "codesearchnet_java_10000": 1, "codesearchnet_php_10000": 1, "codesearchnet_python_10000": 1}, "config": {"model": "neox", "model_args": {"contrastive": true, "contastive_init_log_scale": -6, "contrastive_similarity": "inner_product", "total_model_params": 304314370, "embedding_model_params": 0, "memory_config": {}, "memory_size": 4096, "memorize_mode": "host", "memory_invalid_query_mode": "ignore_eod", "num_memory_write_heads": null, "memory_save": "/mnt/efs/augment/mem/scratch/exp-7236-trial-7257-0-7236.b098609e-f4ad-4843-ad9a-1fa29a1f4d81-412", "memory_load": "/mnt/efs/augment/mem/scratch/exp-7236-trial-7257-0-7236.b098609e-f4ad-4843-ad9a-1fa29a1f4d81-412", "memory_train_on_gpu": false, "memory_partition_count": null, "memorize_files": null, "memorize_filelist": null, "memory_knn_top_k": 32, "memorize_report": null, "distributed_backend": "nccl", "local_rank": 0, "rank": 0, "lazy_mpu_init": false, "short_seq_prob": 0.1, "attn_mask_mode": "causal", "loss_mask_mode": "none", "adlr_autoresume": false, "adlr_autoresume_interval": 1000, "seed": 1234, "onnx_safe": false, "deepscale": false, "deepscale_config": null, "deepspeed_mpi": false, "user_script": "evaluate.py", "iteration": 2000, "do_train": null, "do_valid": null, "do_test": null, "global_num_gpus": 1, "text_gen_type": "unconditional", "temperature": 0.0, "top_p": 0.0, "top_k": 0, "maximum_tokens": 64, "sample_input_file": null, "sample_output_file": "samples.txt", "num_samples": 1, "recompute": false, "eval_results_prefix": "/mnt/efs/augment/eval/jobs/2  C  i  L  E  5  q  t  /c  1  c  e  d  6  9  3  9  b  4  1  _001", "eval_tasks": ["codesearchnet_go_1000", "codesearchnet_java_1000", "codesearchnet_javascript_1000", "codesearchnet_php_1000", "codesearchnet_python_1000", "codesearchnet_ruby_1000", "codesearchnet_go_10000", "codesearchnet_java_10000", "codesearchnet_php_10000", "codesearchnet_python_10000"], "memory_object_names": null, "memory_object_names_file": null, "eval_batch_size": 1, "eval_with_memories": false, "eval_tags": {"experiment": "https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/2694", "name": "contrieve-350M-batch16384", "wandb": "https://wandb.ai/igoro/codegen/runs/22zy3i66/overview"}, "retrieval_index": null, "similarity_cutoff": 0.5, "ngram_size": 20, "path_delimiter": "\n", "document_delimiter": "<|ret-endofdoc|>", "use_wandb": true, "wandb_group": "c4ASw8hKmQYowZQUH5qhVP", "wandb_team": null, "wandb_project": "codegen", "wandb_host": "https://api.wandb.ai", "wandb_name": null, "wandb_init_all_ranks": false, "git_hash": null, "log_dir": null, "tensorboard_dir": null, "log_interval": 100, "log_grad_pct_zeros": false, "log_param_norm": false, "log_grad_norm": false, "log_optimizer_states": false, "log_gradient_noise_scale": false, "gradient_noise_scale_n_batches": 5, "gradient_noise_scale_cpu_offload": false, "pipe_parallel_size": 1, "model_parallel_size": 1, "pipe_partition_method": "type:transformer|mlp", "world_size": 1, "is_pipe_parallel": true, "data_path": "/mnt/efs/augment/data/processed/the-stack-dense-retrieval/dense_retrieval_indexed_dataset_bs16384/dense_retrieval", "train_data_paths": null, "test_data_paths": null, "valid_data_paths": null, "dataset_type": "gpt2", "train_data_weights": null, "valid_data_weights": null, "test_data_weights": null, "weight_by_num_documents": false, "weighted_sampler_alpha": 0.3, "data_impl": "mmap", "mmap_warmup": false, "save": null, "config_files": {"config.yml": "attention-dropout: 0.0\nbias-gelu-fusion: true\ncheckpoint-activations: true\ncheckpoint-num-layers: 1\ncontastive_init_log_scale: -6\ncontrastive: true\ndata-impl: mmap\ndata-path: /mnt/efs/augment/data/processed/the-stack-dense-retrieval/dense_retrieval_indexed_dataset_bs16384/dense_retrieval\ndistributed-backend: nccl\neval-interval: 40\neval-iters: 32\nfinal_linear_bias: true\nfinetune: true\nfp16:\n  enabled: true\n  hysteresis: 1\n  initial_scale_power: 9\n  loss_scale: 0\n  loss_scale_window: 20\n  min_loss_scale: 0.125\ngpt_j_residual: true\ngradient_accumulation_steps: 1\ngradient_clipping: 1.0\nhidden-dropout: 0.0\nhidden-size: 1024\ninit_method: small_init\nkeep-last-n-checkpoints: 1\nload: /run/determined/checkpoint/1fb59e7c-6e10-4d9d-96ac-131fda053762\nlog-interval: 100\nlr-decay-iters: 15000\nlr-decay-style: cosine\nmake_vocab_size_divisible_by: 51200\nmax-position-embeddings: 2048\nmemorize_mode: train\nmemory_load: /mnt/efs/augment/mem\nmemory_save: /mnt/efs/augment/mem\nmin_lr: 8.0e-06\nno_weight_tying: true\nnorm: layernorm\nnum-attention-heads: 16\nnum-layers: 20\noptimizer:\n  params:\n    betas:\n    - 0.9\n    - 0.95\n    eps: 1.0e-08\n    lr: 8.0e-05\n  type: Adam\noutput_layer_init_method: wang_init\npartition-activations: true\npipe-parallel-size: 1\npos-emb: rotary\nrotary_interleave: true\nrotary_pct: 0.5\nsave-interval: 500\nscaled-upper-triang-masked-softmax-fusion: true\nseq-length: 2048\nsteps_per_print: 10\nsynchronize-each-layer: true\ntokenizer_type: CodeGenTokenizer\ntrain-iters: 15000\ntrain_batch_size: 12\ntrain_micro_batch_size_per_gpu: 12\nuse_post_attn_norm: false\nvocab_file: none\nwall_clock_breakdown: true\nwandb_project: codegen\nwarmup: 0.2\nweight-decay: 0.1\nzero_optimization:\n  allgather_bucket_size: 500000000\n  allgather_partitions: true\n  contiguous_gradients: true\n  cpu_offload: false\n  overlap_comm: true\n  reduce_bucket_size: 500000000\n  reduce_scatter: true\n  stage: 1\n", "tmpgvgzdj3y.yml": "eval_batch_size: 1\neval_tags:\n  experiment: https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/2694\n  name: contrieve-350M-batch16384\n  wandb: https://wandb.ai/igoro/codegen/runs/22zy3i66/overview\ntemperature: 0\n", "results_prefix.yml": "eval_results_prefix: /mnt/efs/augment/eval/jobs/2CiLE5qt/c1ced6939b41_001\n"}, "load": "/run/determined/checkpoint/1fb59e7c-6e10-4d9d-96ac-131fda053762", "checkpoint_validation_with_forward_pass": false, "save_interval": 500, "no_save_optim": false, "no_save_rng": false, "no_load_optim": true, "no_load_rng": true, "finetune": false, "batch_size": 12, "train_iters": 15000, "eval_iters": 32, "keep_last_n_checkpoints": 1, "eval_interval": 40, "early_stopping": false, "early_stopping_metric": "lm_loss", "early_stopping_threshold": 0.01, "split": "969, 30, 1", "vocab_file": "none", "merge_file": null, "num_workers": 2, "exit_interval": null, "attention_dropout": 0.0, "hidden_dropout": 0.0, "weight_decay": 0.1, "checkpoint_activations": false, "checkpoint_num_layers": 1, "deepspeed_activation_checkpointing": true, "contiguous_checkpointing": false, "checkpoint_in_cpu": false, "synchronize_each_layer": true, "profile_backward": false, "partition_activations": false, "gas": 1, "clip_grad": 1.0, "hysteresis": 2, "dynamic_loss_scale": true, "loss_scale": null, "loss_scale_window": 1000.0, "min_scale": 1.0, "char_level_ppl": false, "mem_friendly_batch": false, "train_only": null, "tokenizer_type": "CodeGenTokenizer", "padded_vocab_size": 51200, "optimizer_type": "<PERSON>", "use_bnb_optimizer": false, "zero_stage": 0, "zero_reduce_scatter": true, "zero_contiguous_gradients": false, "zero_reduce_bucket_size": 500000000, "zero_allgather_bucket_size": 500000000, "lr": 8e-05, "lr_decay_style": "cosine", "lr_decay_iters": 15000, "min_lr": 8e-06, "warmup": 0.2, "override_lr_scheduler": false, "use_checkpoint_lr_scheduler": false, "precision": "fp16", "num_layers": 20, "hidden_size": 1024, "num_attention_heads": 16, "seq_length": 2048, "max_position_embeddings": 2048, "norm": "layernorm", "layernorm_epsilon": 1e-05, "rms_norm_epsilon": 1e-08, "scalenorm_epsilon": 1e-08, "pos_emb": "rotary", "rotary_interleave": true, "rpe_num_buckets": 32, "rpe_max_distance": 128, "no_weight_tying": true, "attention_config": ["global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global"], "sparsity_config": {}, "num_unique_layers": null, "param_sharing_style": "grouped", "make_vocab_size_divisible_by": 51200, "activation": "gelu", "scaled_upper_triang_masked_softmax_fusion": true, "scaled_masked_softmax_fusion": false, "bias_gelu_fusion": true, "bias_dropout_fusion": false, "scaled_dot_product_attention_fusion": true, "fp16_lm_cross_entropy": false, "init_method_std": 0.02, "apply_query_key_layer_scaling": false, "use_cpu_initialization": false, "attention_softmax_in_fp32": false, "rotary_pct": 0.5, "rotary_emb_base": 10000, "init_method": "small_init", "output_layer_init_method": "wang_init", "gmlp_attn_dim": 64, "gpt_j_residual": true, "soft_prompt_tuning": null, "output_layer_parallelism": "column", "use_post_attn_norm": false, "final_linear_bias": true, "deepspeed": true, "train_batch_size": 12, "train_micro_batch_size_per_gpu": 12, "gradient_accumulation_steps": 1, "optimizer": {"params": {"betas": [0.9, 0.95], "eps": 1e-08, "lr": 8e-05}, "type": "<PERSON>"}, "scheduler": null, "fp32_allreduce": false, "prescale_gradients": false, "gradient_predivide_factor": 1.0, "sparse_gradients": false, "fp16": {"enabled": true, "hysteresis": 1, "initial_scale_power": 9, "loss_scale": 0, "loss_scale_window": 20, "min_loss_scale": 0.125}, "amp": null, "gradient_clipping": 1.0, "zero_optimization": {"stage": 0, "allgather_partitions": true, "allgather_bucket_size": 500000000, "overlap_comm": false, "reduce_scatter": true, "reduce_bucket_size": 500000000, "contiguous_gradients": false, "cpu_offload": false, "elastic_checkpoint": false}, "steps_per_print": 10, "wall_clock_breakdown": true, "dump_state": false, "flops_profiler": null, "zero_allow_untested_optimizer": false, "hostfile": null, "include": null, "exclude": null, "num_nodes": -1, "num_gpus": null, "master_port": 29500, "master_addr": null, "launcher": "pdsh", "detect_nvlink_pairs": false}, "num_fewshot": 0, "batch_size": 1, "device": "cuda:0", "no_cache": false, "limit": null, "bootstrap_iters": 10000, "description_dict": null, "duration": 2666.6034717559814, "eval_with_memories": false, "eval_tags": {"experiment": "https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/2694", "name": "contrieve-350M-batch16384", "wandb": "https://wandb.ai/igoro/codegen/runs/22zy3i66/overview"}}}