{"cells": [{"cell_type": "markdown", "id": "196e9b52", "metadata": {}, "source": ["# Train models with memory for more iterations\n", "\n", "The purpose of this experiment is to test whether the models trained with memory are undertrained when we train all weights (as opposed to just the memory attention weights) for 6.5B tokens.\n", "\n", "The baseline configuration is as follows:\n", "\n", "```\n", "codegen-2B.ft-repos_sort2.seq1024 batch_s1024 4e-5  min.4e-6 prelayerL16 wh8\n", "    memsize-65536 ignore_eod memory_train_on_gpu fusion\n", "```\n", "\n", "In past experiments, we'd train this baseline configuration for 25000 iterations corresponding to 6.5B tokens. In this experiment, we increase the iterations to 50000 and 100000.\n", "\n", "## Training\n", "\n", "We train multiple the model from scratch with configuration described above for different number of iterations: 25000, 50000, 100000.\n", "\n", "| Iterations | Experiment |\n", "|------------|------------|\n", "|      25000 | [Experiment](https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/804/) |\n", "|      50000 | [Experiment](https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/805/) |\n", "|     100000 | [Experiment](https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/848/) |\n", "\n", "## Evaluation\n", "\n", "We evaluate the trained models on Polycoder. We compare both with memory and without memory, so that we can separate the effects of additional training on improving the model's ability to use memories vs. just general improvements of the model."]}, {"cell_type": "markdown", "id": "b9297ab7", "metadata": {}, "source": ["### Requirements and Functions"]}, {"cell_type": "code", "execution_count": null, "id": "1d285d4d", "metadata": {"scrolled": true}, "outputs": [], "source": ["!git clone https://www.github.com/igor0/lm-plot\n", "!pip install -e lm-plot\n", "!pip install numexpr==2.7.3"]}, {"cell_type": "code", "execution_count": 1, "id": "4dd9e37d", "metadata": {"tags": ["hide-cell", "hide-input"]}, "outputs": [], "source": ["import lm_plot\n", "\n", "def show_results(json_files):\n", "    df = lm_plot.collect(json_files, lm_plot.files.ConfigExtractor()).raw()\n", "    df = df[(df[\"metric\"]==\"byte_perplexity\")].sort_values(\"model\")\n", "\n", "    df_means = df.groupby(\"model\")[\"value\"].agg(\"mean\").reset_index()\n", "    df_means = df_means.rename(columns={\"value\": \"byte perplexity\"})\n", "\n", "    return df_means.sort_values(\"byte perplexity\")"]}, {"cell_type": "markdown", "id": "17d8e53b", "metadata": {}, "source": ["### Evaluate PolyCoder with Memory"]}, {"cell_type": "code", "execution_count": 2, "id": "51dc710a", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>model</th>\n", "      <th>byte perplexity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>test_100000</td>\n", "      <td>1.225927</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>test_50000</td>\n", "      <td>1.232436</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>test_25000</td>\n", "      <td>1.243262</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>codegen-2B.ft.repos_sort2.s1024-memsize-393216...</td>\n", "      <td>1.243509</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>codegen-2B.ft.repos_sort2.s1024-memsize-65536....</td>\n", "      <td>1.243663</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>codegen-2B.ida.batch_distract.2022-10-19</td>\n", "      <td>1.248357</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>codegen-2B.ida.batch_distract.2022-10-11</td>\n", "      <td>1.249369</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>codegen-2B.repos_sort2.s1024-memsize-65536.202...</td>\n", "      <td>1.249973</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>codegen-2B.ida.memsize-32768.wh8.2022-09-27</td>\n", "      <td>1.251377</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>codegen-2B.repos_sort2.s1024-memsize-131072</td>\n", "      <td>1.254059</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>codegen-2B-multi</td>\n", "      <td>1.337328</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                model  byte perplexity\n", "8                                         test_100000         1.225927\n", "10                                         test_50000         1.232436\n", "9                                          test_25000         1.243262\n", "1   codegen-2B.ft.repos_sort2.s1024-memsize-393216...         1.243509\n", "2   codegen-2B.ft.repos_sort2.s1024-memsize-65536....         1.243663\n", "4            codegen-2B.ida.batch_distract.2022-10-19         1.248357\n", "3            codegen-2B.ida.batch_distract.2022-10-11         1.249369\n", "7   codegen-2B.repos_sort2.s1024-memsize-65536.202...         1.249973\n", "5         codegen-2B.ida.memsize-32768.wh8.2022-09-27         1.251377\n", "6         codegen-2B.repos_sort2.s1024-memsize-131072         1.254059\n", "0                                    codegen-2B-multi         1.337328"]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["show_results(\"eval/memory/*/*.json\")"]}, {"cell_type": "markdown", "id": "17ad384c", "metadata": {}, "source": ["### Evaluate Polycoder without Memory"]}, {"cell_type": "code", "execution_count": 3, "id": "f76e2639", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>model</th>\n", "      <th>byte perplexity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>8</th>\n", "      <td>test_100000</td>\n", "      <td>1.319850</td>\n", "    </tr>\n", "    <tr>\n", "      <th>10</th>\n", "      <td>test_50000</td>\n", "      <td>1.325726</td>\n", "    </tr>\n", "    <tr>\n", "      <th>9</th>\n", "      <td>test_25000</td>\n", "      <td>1.331108</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>codegen-2B.ft.repos_sort2.s1024-memsize-65536....</td>\n", "      <td>1.331981</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>codegen-2B.ft.repos_sort2.s1024-memsize-393216...</td>\n", "      <td>1.333964</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>codegen-2B-multi</td>\n", "      <td>1.337328</td>\n", "    </tr>\n", "    <tr>\n", "      <th>6</th>\n", "      <td>codegen-2B.repos_sort2.s1024-memsize-131072</td>\n", "      <td>1.337347</td>\n", "    </tr>\n", "    <tr>\n", "      <th>7</th>\n", "      <td>codegen-2B.repos_sort2.s1024-memsize-65536.202...</td>\n", "      <td>1.337361</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>codegen-2B.ida.batch_distract.2022-10-19</td>\n", "      <td>1.337414</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>codegen-2B.ida.memsize-32768.wh8.2022-09-27</td>\n", "      <td>1.337422</td>\n", "    </tr>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>codegen-2B.ida.batch_distract.2022-10-11</td>\n", "      <td>1.337437</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["                                                model  byte perplexity\n", "8                                         test_100000         1.319850\n", "10                                         test_50000         1.325726\n", "9                                          test_25000         1.331108\n", "2   codegen-2B.ft.repos_sort2.s1024-memsize-65536....         1.331981\n", "1   codegen-2B.ft.repos_sort2.s1024-memsize-393216...         1.333964\n", "0                                    codegen-2B-multi         1.337328\n", "6         codegen-2B.repos_sort2.s1024-memsize-131072         1.337347\n", "7   codegen-2B.repos_sort2.s1024-memsize-65536.202...         1.337361\n", "4            codegen-2B.ida.batch_distract.2022-10-19         1.337414\n", "5         codegen-2B.ida.memsize-32768.wh8.2022-09-27         1.337422\n", "3            codegen-2B.ida.batch_distract.2022-10-11         1.337437"]}, "execution_count": 3, "metadata": {}, "output_type": "execute_result"}], "source": ["show_results(\"eval/no_memory/*/*.json\")"]}, {"cell_type": "markdown", "id": "21f7c980", "metadata": {}, "source": ["## Observations\n", "\n", "**test_25000 roughly matches the baseline.** The test_25000 model should be equivalent to the codegen-2B.ft.repos_sort2.s1024-memsize-65536 baseline, with one small difference: test_25000 was trained with cosine decay to 10% of the original learning rate while the baseline run was run with decay to 0. We attribute the small difference in evaluation scores to that discrepancy.\n", "\n", "**Further training helps memory.** In the runs with memory, byte perplexity improved 1.243 -> 1.226.\n", "\n", "**Further training helps even without memory.** In the runs without memory, byte perplexity improved 1.331 -> 1.320. Note that the models with byte perplexity ~1.337 are exactly those models whose weights match CodeGen-2B, apart from memory attention (and a trained scalar `dummy` factor used as an implementation detail). The remaining models (codegen-2B.ft.\\* and test\\_\\*) had all weights tuned, resulting in a reduced perplexity even when no memory is used.\n", "\n", "## Conclusion\n", "\n", "We see that additional training reduced the model's loss when evaluating with memory. However, the analysis is complicated because the model's performance also improved even when memory was not used during evaluation. Although the improvement in evaluation was greater when memory was incorporated, it is difficult to accurately distinguish the two effects."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 5}