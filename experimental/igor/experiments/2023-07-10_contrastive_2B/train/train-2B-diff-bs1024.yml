# includes is an ordered list of gpt-neox config files to be loaded
includes:
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-2B.yml
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/special/contrastive.yml
- /mnt/efs/augment/user/colin/2023-05-19_contrastive_diffs/special/data_dense_retrieval_bs1024.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-07-10_contrastive_2B/train/configs/2B-12000.yml
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/lr/2e-5.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-07-10_contrastive_2B/train/configs/loss_scale-256.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-07-10_contrastive_2B/train/configs/init_scale_-5.yml

# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: contrastive
  description: null
  workspace: Dev
  project: igor
  perform_initial_validation: True  # Do a validation at iteration 0

# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  podspec_path: "8xA100.yaml"
  gpu_count: 8  # How many GPUs to ask for

  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"

  # Experiment args (comment out for eval)
  enable_checkpoint_gc:  True  # Enable on-the-fly checkpoint GC
overrides: # mandatory; experiment.py crash if unspecified
  #train_micro_batch_size_per_gpu: 4
  #gradient_accumulation_steps: 3
  #train_batch_size: 96
  train_micro_batch_size_per_gpu: 32
  gradient_accumulation_steps: 4
  train_batch_size: 1024
  train_iters: 30000
  lr_decay_iters: 30000
  warmup: 0.0
  load: /mnt/efs/augment/checkpoints/codegen-2B-multi
