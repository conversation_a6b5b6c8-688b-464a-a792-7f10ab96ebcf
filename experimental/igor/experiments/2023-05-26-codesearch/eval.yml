checkpoints:
  - path: /mnt/efs/augment/checkpoints/5df89921-5f51-4af1-abf9-dccb0996aa09


tasks: [
    "codesearchnet_go_1000",
    "codesearchnet_java_1000",
    "codesearchnet_javascript_1000",
    "codesearchnet_php_1000",
    "codesearchnet_python_1000",
    "codesearchnet_ruby_1000",
    "codesearchnet_go_10000",
    "codesearchnet_java_10000",
    "codesearchnet_php_10000",
    "codesearchnet_python_10000",
]

podspec: gpu-small.yaml

neox_args:
  temperature: 0  # need something or else eval.py will complain

determined:
  name: Eval Test
  workspace: Dev
  project: Eval
  metaconfig: jobs/templates/batch-eval.yaml
