determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Hydra - RepoEval 23lines, diffb1m_16b_alphal_fixtoken, ethanol3-01.11_b8192_w8_tg0.01, top1
  workspace: Dev
  project: Eval
system:
    name: basic_rag
    model:
      checkpoint_path: rogue/diffb1m_16b_alphal_fixtoken
      name: rogue
      prompt:
        max_prefix_tokens: 1280
        max_prompt_tokens: 3816
        max_retrieved_chunk_tokens: -1
        max_suffix_tokens: 768
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      name: ethanol
      chunker: line_level
      max_chunk: 40
      checkpoint_path: ethanol/ethanol3-01.11_b8192_w8_tg0.01
      query_formatter:
          name: simple_query
          max_lines: 20
          add_path: true
          retokenize: true
      doc_formatter:
          name: simple_document
          add_path: true
    experimental:
      remove_suffix: False
      retriever_top_k: 1
      trim_on_dedent: False
      trim_on_max_lines: null
task:
    name: hydra
    dataset: repoeval_2-3lines
podspec: 1xA100.yaml
