determined:
  metaconfig: jobs/templates/eval-exec-v2-metaconfig.yaml
  name: Hydra - RepoEval 23lines, diffb1m_7b_alphal_fixtoken, boykin
  workspace: Dev
  project: Eval
system:
    name: rag_with_reranker
    model:
      checkpoint_path: rogue/diffb1m_7b_alphal_fixtoken
      name: rogue
      prompt:
        max_prefix_tokens: 1280
        max_prompt_tokens: 3816
        max_retrieved_chunk_tokens: -1
        max_suffix_tokens: 768
    generation_options:
      temperature: 0
      top_k: 0
      top_p: 0
      max_generated_tokens: 280
    retriever:
      name: diff_boykin
      chunker: line_level
      max_chunk: 40
      max_query_lines: 20
    experimental:
      remove_suffix: False
      retriever_top_k: 256
      trim_on_dedent: False
      trim_on_max_lines: null
    reranker:
        name: oracle_perplexity_reranker
        top_k: 256
        batchsize: 16
task:
    dataset: all_languages_2-3lines_medium_to_hard.v1.0
    name: hydra
    hydra_block_resource_internet_access: True
podspec: 1xA100.yaml
