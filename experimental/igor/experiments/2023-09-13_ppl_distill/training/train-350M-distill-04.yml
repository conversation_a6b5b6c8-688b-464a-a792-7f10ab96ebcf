# includes is an ordered list of gpt-neox config files to be loaded
includes:
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/lr/2e-5.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-07-24_diff_v2_training/train/configs/loss_scale-128-w200.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-07-24_diff_v2_training/train/configs/init_scale_-4.yml

# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: contrastive
  description: null
  workspace: Dev
  project: igor
  perform_initial_validation: True  # Do a validation at iteration 0
  max_restarts: 0

# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  podspec_path: "8xA100.yaml"
  gpu_count: 8  # How many GPUs to ask for

  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"

  # Experiment args (comment out for eval)
  enable_checkpoint_gc:  True  # Enable on-the-fly checkpoint GC
  #source_checkpoint: 1fb59e7c-6e10-4d9d-96ac-131fda053762  # Contrieve-350M
overrides:
  # mode
  ppl_distill: true

  # training batch & schedule
  seq_length: 1024
  train_micro_batch_size_per_gpu: 32
  gradient_accumulation_steps: 4
  train_batch_size: 1024
  train_iters: 5000
  lr_decay_iters: 5000
  warmup: 0.0

  # validation & checkpointing
  eval_interval: 40
  eval_iters: 1
  save_interval: 500

  # datasets
  dataset_type: direct
  shuffle_direct_dataset: false
  #data-path: /mnt/efs/augment/user/igor/data/ppl_distill/distill-04/dataset
  data-path: null
  train_data_paths:
  - /mnt/efs/augment/user/igor/data/ppl_distill/distill-04/dataset
  valid_data_paths:
  - /mnt/efs/augment/user/igor/data/ppl_distill/distill-04/validation_dataset
  test_data_paths:
  - /mnt/efs/augment/user/igor/data/ppl_distill/distill-04/validation_dataset
  max_valid_data_size: 1024
