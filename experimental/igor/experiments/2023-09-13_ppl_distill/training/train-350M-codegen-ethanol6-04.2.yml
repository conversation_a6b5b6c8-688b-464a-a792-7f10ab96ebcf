# includes is an ordered list of gpt-neox config files to be loaded
includes:
- /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/ethanol.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/conan-350M.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/2e-5.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/loss_scale-2048-w8.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/init_scale_-4.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_score_10.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-09-13_ppl_distill/training/configs/temp_gold_score_0.01.yml

# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: contrastive
  description: null
  workspace: Dev
  project: igor
  perform_initial_validation: False  # Do a validation at iteration 0
  max_restarts: 0

# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  podspec_path: "8xA100.yaml"
  gpu_count: 16  # How many GPUs to ask for

  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"

  # Experiment args (comment out for eval)
  enable_checkpoint_gc:  True  # Enable on-the-fly checkpoint GC
  #source_checkpoint: 1fb59e7c-6e10-4d9d-96ac-131fda053762  # Contrieve-350M
overrides:
  # mode
  ppl_distill: true

  # training batch & schedule
  seq_length: 1024
  train_micro_batch_size_per_gpu: 128
  gradient_accumulation_steps: 32
  train_batch_size: 65536
  train_iters: 625
  lr_decay_iters: 625
  warmup: 0.0

  # validation & checkpointing
  eval_interval: 999999999999 # Never
  eval_iters: 1
  save_interval: 125

  # load checkpoint
  load: /mnt/efs/augment/checkpoints/codegen-350M-multi

  # datasets
  dataset_type: direct
  shuffle_direct_dataset: false
  data-path: null
  train_data_paths:
  - /mnt/efs/augment/user/igor/data/ethanol6/ethanol6-04.2/dataset
  valid_data_paths:
  - /mnt/efs/augment/user/igor/data/ethanol6/ethanol6-04.2/validation_dataset
  test_data_paths:
  - /mnt/efs/augment/user/igor/data/ethanol6/ethanol6-04.2/validation_dataset
  max_valid_data_size: 4096
