# includes is an ordered list of gpt-neox config files to be loaded
includes:
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/model/conan-350M.yml
- /mnt/efs/augment/user/igor/configs/2023-04-17_contrastive/lr/2e-5.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-07-24_diff_v2_training/train/configs/loss_scale-128-w200.yml
- /home/<USER>/augment/experimental/igor/experiments/2023-07-24_diff_v2_training/train/configs/init_scale_-4.yml

# determined is a dictionary of determined.ai specific arguments that have no corresponding gpt-neox arguments
determined:
  name: contrastive
  description: null
  workspace: Dev
  project: igor
  perform_initial_validation: True  # Do a validation at iteration 0
  max_restarts: 0

# augment is a dictionary of augment specific arguments for our code extensions
augment:
  # Common args for both training and evaluation
  podspec_path: "8xA100.yaml"
  gpu_count: 8  # How many GPUs to ask for

  # Environment variables to pass to every worker process.
  environment_variables:
  #   ENV_VAR: "<value>"

  # Experiment args (comment out for eval)
  enable_checkpoint_gc:  True  # Enable on-the-fly checkpoint GC
  #source_checkpoint: 1fb59e7c-6e10-4d9d-96ac-131fda053762  # Contrieve-350M
overrides:
  ppl_distill: true
  seq_length: 1024
  train_micro_batch_size_per_gpu: 128
  gradient_accumulation_steps: 1
  train_batch_size: 1024
  train_iters: 5000
  lr_decay_iters: 5000
  warmup: 0.0
  load: /mnt/efs/augment/checkpoints/igor_1fb59e7c-6e10-4d9d-96ac-131fda053762  # Contrieve-350M
  data-path: null
  train_data_paths:
  - /mnt/efs/augment/user/igor/data/ppl_distill/distill-02/dataset
  valid_data_paths:
  - /mnt/efs/augment/user/igor/data/ppl_distill/distill-02/validation_dataset
  test_data_paths:
  - /mnt/efs/augment/user/igor/data/ppl_distill/distill-02/validation_dataset
  max_valid_data_size: 8192
  eval_interval: 100
  eval_iters: 8
  save_interval: 500
