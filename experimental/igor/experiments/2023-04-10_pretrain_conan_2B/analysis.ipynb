{"cells": [{"cell_type": "markdown", "id": "379baed1", "metadata": {}, "source": ["# Conan-2B Pretraining\n", "\n", "In this experiment, we pretrain 2B models from scratch. There are two goals for these experiments:\n", "\n", "1. **Compare our Conan-2B model to CodeGen-2B.** We pretrain Conan-2B for varying number of iterations and then extrapolate to see if we can match or beat the CodeGen-2B performance.\n", "2. **Compare models trained on 1 node vs. 6 nodes.** We want to see if there is any measurable difference between the models.\n", "\n", "\n", "## Training\n", "\n", "| Checkpoint             | Iterations |   Nodes  | Experiment |\n", "|------------------------|------------|----------|------------|\n", "| conan-2B-12000         |      12000 |        1 | [wandb](https://wandb.ai/augment/codegen/runs/c6ckyp38) |\n", "| conan-2B-24000         |      24000 |        1 | [wandb](https://wandb.ai/augment/codegen/runs/3vxovd6o) |\n", "| conan-2B-48000         |      48000 |        1 | [wandb](https://wandb.ai/augment/codegen/runs/343a065w) |\n", "| conan-2B-96000         |      96000 |        1 | [wandb1](https://wandb.ai/augment/codegen/runs/3migiwh3), [wandb2](https://wandb.ai/augment/codegen/runs/10qbwkim/overview) |\n", "| conan-2B-12000-8_nodes |      12000 |        8 | [determined](https://dev-training.tenant-augment-eng.las1.ingress.coreweave.cloud/det/experiments/1228/) |\n", "| codegen-2B-multi       |      N/A   |      N/A |            |\n", "| codegen-2B-mono        |      N/A   |      N/A |            |\n", "\n", "\n", "## Evaluation\n", "\n", "We evaluate the trained models on Polycoder and compare against the CodeGen baseline."]}, {"cell_type": "markdown", "id": "b9297ab7", "metadata": {}, "source": ["### Requirements and Imports"]}, {"cell_type": "code", "execution_count": null, "id": "1d285d4d", "metadata": {"scrolled": true}, "outputs": [], "source": ["!pip install git+https://github.com/igor0/lm-plot.git\n", "!pip install -U numexpr==2.7.3"]}, {"cell_type": "code", "execution_count": 1, "id": "4dd9e37d", "metadata": {"tags": ["hide-cell", "hide-input"]}, "outputs": [], "source": ["import matplotlib as plt\n", "import seaborn as sns\n", "from analysis_code import get_results, plot_results"]}, {"cell_type": "markdown", "id": "17d8e53b", "metadata": {}, "source": ["### Evaluate PolyCoder\n", "\n", "On PolyCoder, the models trained on 1 node vs. 6 nodes seem to perform nearly identically.\n", "\n", "Also, we notice that the Conan model trained for 96000 steps beats the performance CodeGen-2B. Note that the metric we show here is the unweighted arithmetic mean of PolyCoder scores across the 6 languages (C, C++, Go, Java, JavaScript, Python).\n", "\n", "**Note:** CodeGen-2B is trained on 350B tokens of natural language followed by 150B tokens of code. In the comparisons in this experiment, we show CodeGen-2B as having been trained on 500B tokens. Depending on the goals of the comparison, it could also be argued that 150B is the right number to compare against. We currently care about training the best possible code generation model in the smallest number of FLOPS possible, so we use the 500B tokens as the comparison point."]}, {"cell_type": "code", "execution_count": 2, "id": "bc4626a0", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>model name</th>\n", "      <th>model type</th>\n", "      <th>training tokens</th>\n", "      <th>byte perplexity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>conan-2B-12000</td>\n", "      <td>Conan-2B (1 node)</td>\n", "      <td>7077888000</td>\n", "      <td>1.394834</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>conan-2B-24000</td>\n", "      <td>Conan-2B (1 node)</td>\n", "      <td>14155776000</td>\n", "      <td>1.368153</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>conan-2B-48000</td>\n", "      <td>Conan-2B (1 node)</td>\n", "      <td>28311552000</td>\n", "      <td>1.344301</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>conan-2B-12000-6_nodes</td>\n", "      <td>Conan-2B (6 nodes)</td>\n", "      <td>7077888000</td>\n", "      <td>1.394796</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CodeGen-2B-multi</td>\n", "      <td>CodeGen-2B-multi</td>\n", "      <td>500000000000</td>\n", "      <td>1.337313</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CodeGen-2B-mono</td>\n", "      <td>CodeGen-2B-mono</td>\n", "      <td>650000000000</td>\n", "      <td>1.432369</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               model name          model type  training tokens  \\\n", "3          conan-2B-12000   Conan-2B (1 node)       7077888000   \n", "4          conan-2B-24000   Conan-2B (1 node)      14155776000   \n", "5          conan-2B-48000   Conan-2B (1 node)      28311552000   \n", "0  conan-2B-12000-6_nodes  Conan-2B (6 nodes)       7077888000   \n", "2        CodeGen-2B-multi    CodeGen-2B-multi     500000000000   \n", "1         CodeGen-2B-mono     CodeGen-2B-mono     650000000000   \n", "\n", "   byte perplexity  \n", "3         1.394834  \n", "4         1.368153  \n", "5         1.344301  \n", "0         1.394796  \n", "2         1.337313  \n", "1         1.432369  "]}, "execution_count": 2, "metadata": {}, "output_type": "execute_result"}], "source": ["df = get_results(\"eval/*.json\")\n", "df"]}, {"cell_type": "code", "execution_count": 3, "id": "51dc710a", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_results(df)"]}, {"cell_type": "markdown", "id": "e5f5dc8f", "metadata": {}, "source": ["### Evaluate PolyCoder on Python\n", "\n", "The picture changes for different programming languages. On Python, CodeGen-2B significantly outperforms Conan-2B-96000. Another doubling of iterations to 192000 iterations should get us close."]}, {"cell_type": "code", "execution_count": 4, "id": "94a8735f", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>model name</th>\n", "      <th>model type</th>\n", "      <th>training tokens</th>\n", "      <th>byte perplexity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>conan-2B-12000</td>\n", "      <td>Conan-2B (1 node)</td>\n", "      <td>7077888000</td>\n", "      <td>1.394983</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>conan-2B-24000</td>\n", "      <td>Conan-2B (1 node)</td>\n", "      <td>14155776000</td>\n", "      <td>1.368966</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>conan-2B-48000</td>\n", "      <td>Conan-2B (1 node)</td>\n", "      <td>28311552000</td>\n", "      <td>1.348083</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>conan-2B-12000-6_nodes</td>\n", "      <td>Conan-2B (6 nodes)</td>\n", "      <td>7077888000</td>\n", "      <td>1.393876</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CodeGen-2B-multi</td>\n", "      <td>CodeGen-2B-multi</td>\n", "      <td>500000000000</td>\n", "      <td>1.314771</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CodeGen-2B-mono</td>\n", "      <td>CodeGen-2B-mono</td>\n", "      <td>650000000000</td>\n", "      <td>1.301151</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               model name          model type  training tokens  \\\n", "3          conan-2B-12000   Conan-2B (1 node)       7077888000   \n", "4          conan-2B-24000   Conan-2B (1 node)      14155776000   \n", "5          conan-2B-48000   Conan-2B (1 node)      28311552000   \n", "0  conan-2B-12000-6_nodes  Conan-2B (6 nodes)       7077888000   \n", "2        CodeGen-2B-multi    CodeGen-2B-multi     500000000000   \n", "1         CodeGen-2B-mono     CodeGen-2B-mono     650000000000   \n", "\n", "   byte perplexity  \n", "3         1.394983  \n", "4         1.368966  \n", "5         1.348083  \n", "0         1.393876  \n", "2         1.314771  \n", "1         1.301151  "]}, "execution_count": 4, "metadata": {}, "output_type": "execute_result"}], "source": ["df_python = get_results(\"eval/*.json\", \"gitrepo_poly_Python_small\")\n", "df_python"]}, {"cell_type": "code", "execution_count": 5, "id": "be3b359d", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_results(df_python)"]}, {"cell_type": "markdown", "id": "d44f16d6", "metadata": {}, "source": ["### Evaluate PolyCoder on JavaScript\n", "\n", "In comparison, Conan-48000 already beats CodeGen-2B on JavaScript."]}, {"cell_type": "code", "execution_count": 6, "id": "7bd0f4e9", "metadata": {}, "outputs": [{"data": {"text/html": ["<div>\n", "<style scoped>\n", "    .dataframe tbody tr th:only-of-type {\n", "        vertical-align: middle;\n", "    }\n", "\n", "    .dataframe tbody tr th {\n", "        vertical-align: top;\n", "    }\n", "\n", "    .dataframe thead th {\n", "        text-align: right;\n", "    }\n", "</style>\n", "<table border=\"1\" class=\"dataframe\">\n", "  <thead>\n", "    <tr style=\"text-align: right;\">\n", "      <th></th>\n", "      <th>model name</th>\n", "      <th>model type</th>\n", "      <th>training tokens</th>\n", "      <th>byte perplexity</th>\n", "    </tr>\n", "  </thead>\n", "  <tbody>\n", "    <tr>\n", "      <th>3</th>\n", "      <td>conan-2B-12000</td>\n", "      <td>Conan-2B (1 node)</td>\n", "      <td>7077888000</td>\n", "      <td>1.413283</td>\n", "    </tr>\n", "    <tr>\n", "      <th>4</th>\n", "      <td>conan-2B-24000</td>\n", "      <td>Conan-2B (1 node)</td>\n", "      <td>14155776000</td>\n", "      <td>1.381350</td>\n", "    </tr>\n", "    <tr>\n", "      <th>5</th>\n", "      <td>conan-2B-48000</td>\n", "      <td>Conan-2B (1 node)</td>\n", "      <td>28311552000</td>\n", "      <td>1.351057</td>\n", "    </tr>\n", "    <tr>\n", "      <th>0</th>\n", "      <td>conan-2B-12000-6_nodes</td>\n", "      <td>Conan-2B (6 nodes)</td>\n", "      <td>7077888000</td>\n", "      <td>1.412712</td>\n", "    </tr>\n", "    <tr>\n", "      <th>2</th>\n", "      <td>CodeGen-2B-multi</td>\n", "      <td>CodeGen-2B-multi</td>\n", "      <td>500000000000</td>\n", "      <td>1.360979</td>\n", "    </tr>\n", "    <tr>\n", "      <th>1</th>\n", "      <td>CodeGen-2B-mono</td>\n", "      <td>CodeGen-2B-mono</td>\n", "      <td>650000000000</td>\n", "      <td>1.473117</td>\n", "    </tr>\n", "  </tbody>\n", "</table>\n", "</div>"], "text/plain": ["               model name          model type  training tokens  \\\n", "3          conan-2B-12000   Conan-2B (1 node)       7077888000   \n", "4          conan-2B-24000   Conan-2B (1 node)      14155776000   \n", "5          conan-2B-48000   Conan-2B (1 node)      28311552000   \n", "0  conan-2B-12000-6_nodes  Conan-2B (6 nodes)       7077888000   \n", "2        CodeGen-2B-multi    CodeGen-2B-multi     500000000000   \n", "1         CodeGen-2B-mono     CodeGen-2B-mono     650000000000   \n", "\n", "   byte perplexity  \n", "3         1.413283  \n", "4         1.381350  \n", "5         1.351057  \n", "0         1.412712  \n", "2         1.360979  \n", "1         1.473117  "]}, "execution_count": 6, "metadata": {}, "output_type": "execute_result"}], "source": ["df_javascript = get_results(\"eval/*.json\", \"gitrepo_poly_JavaScript_small\")\n", "df_javascript"]}, {"cell_type": "code", "execution_count": 7, "id": "d2f557eb", "metadata": {}, "outputs": [{"data": {"image/png": "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", "text/plain": ["<Figure size 640x480 with 1 Axes>"]}, "metadata": {}, "output_type": "display_data"}], "source": ["plot_results(df_javascript)"]}, {"cell_type": "markdown", "id": "21f7c980", "metadata": {}, "source": ["###### Conclusion\n", "\n", "- **Model trained on 6 nodes is just as good as the model trained on 1 node.**\n", "- **Conan and CodeGen relative comparison varies across programming languages.** Compared to <PERSON>, CodeGen does relatively well on Python and poorly on JavaScript and other languages. <PERSON> was meant to be trained on a dataset rebalanced to match the mixture of CodeGen data, so the rebalancing doesn't seem to be achieving our intended purpose.\n", "- **We can match or beat CodeGen-2B performance.** By extrapolating from the scaling observation, we should be able to match or beat the CodeGen-2B performance across all languages."]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.9.5"}}, "nbformat": 4, "nbformat_minor": 5}