{"results": {"gitrepo_poly_C_small": {"word_perplexity": 55.54925815110133, "byte_perplexity": 1.5702322944271894, "bits_per_byte": 0.6509780019372556, "token_perplexity": 2.8267161266001266}, "gitrepo_poly_C++_small": {"word_perplexity": 30.714710379151818, "byte_perplexity": 1.412957098495069, "bits_per_byte": 0.4987176619011239, "token_perplexity": 2.2312131197082663}, "gitrepo_poly_Go_small": {"word_perplexity": 11.923548210560826, "byte_perplexity": 1.3414084285505, "bits_per_byte": 0.42374857216442613, "token_perplexity": 2.0370305703120044}, "gitrepo_poly_Java_small": {"word_perplexity": 11.273292288113623, "byte_perplexity": 1.2361385014971755, "bits_per_byte": 0.3058403971489912, "token_perplexity": 1.8458672301273618}, "gitrepo_poly_JavaScript_small": {"word_perplexity": 36.94801789518615, "byte_perplexity": 1.4132831787958047, "bits_per_byte": 0.4990505666712316, "token_perplexity": 2.907697550206602}, "gitrepo_poly_Python_small": {"word_perplexity": 58.03416443979274, "byte_perplexity": 1.3949829602767616, "bits_per_byte": 0.48024749963557045, "token_perplexity": 2.424181010100056}}, "versions": {"gitrepo_poly_C_small": 3.0, "gitrepo_poly_C++_small": 3.0, "gitrepo_poly_Go_small": 3.0, "gitrepo_poly_Java_small": 3.0, "gitrepo_poly_JavaScript_small": 3.0, "gitrepo_poly_Python_small": 3.0}, "config": {"model": "neox", "model_args": {"total_model_params": 2779683841, "embedding_model_params": 262195200, "memory_config": {}, "memory_size": 4096, "memorize_mode": "host", "memory_invalid_query_mode": "ignore_eod", "num_memory_write_heads": null, "memory_save": "/mnt/efs/augment/mem/scratch/exp-1347-trial-1381-0-1347.de298e02-1de8-4d27-8cdb-194ba9cf2f46", "memory_load": "/mnt/efs/augment/mem/scratch/exp-1347-trial-1381-0-1347.de298e02-1de8-4d27-8cdb-194ba9cf2f46", "memory_train_on_gpu": false, "memory_partition_count": null, "memorize_files": null, "memorize_filelist": null, "memorize_report": null, "distributed_backend": "nccl", "local_rank": 0, "rank": 0, "lazy_mpu_init": false, "short_seq_prob": 0.1, "eod_mask_loss": false, "attn_mask_mode": "causal", "loss_mask_mode": "all", "adlr_autoresume": false, "adlr_autoresume_interval": 1000, "seed": 1234, "onnx_safe": false, "deepscale": false, "deepscale_config": null, "deepspeed_mpi": false, "user_script": "evaluate.py", "iteration": 15000, "do_train": null, "do_valid": null, "do_test": null, "global_num_gpus": 1, "text_gen_type": "unconditional", "temperature": 0.0, "top_p": 0.0, "top_k": 0, "maximum_tokens": 64, "sample_input_file": null, "sample_output_file": "samples.txt", "num_samples": 1, "recompute": false, "eval_results_prefix": "/mnt/efs/augment/eval/jobs/BrTUZESQ/6244b58d7070_001", "eval_tasks": ["gitrepo_poly_C_small", "gitrepo_poly_C++_small", "gitrepo_poly_Go_small", "gitrepo_poly_Java_small", "gitrepo_poly_JavaScript_small", "gitrepo_poly_Python_small"], "memory_object_names": null, "memory_object_names_file": null, "eval_batch_size": 1, "eval_with_memories": false, "eval_tags": {}, "use_wandb": true, "wandb_group": "Y2MeTsuxDMzWJ98aMinLdK_wi9jobo6", "wandb_team": null, "wandb_project": "codegen", "wandb_host": "https://api.wandb.ai", "wandb_name": "conan-2B 2B-12000", "wandb_init_all_ranks": false, "git_hash": null, "log_dir": "/mnt/efs/augment/jobs/166/logs", "tensorboard_dir": null, "log_interval": 100, "log_grad_pct_zeros": false, "log_param_norm": false, "log_grad_norm": false, "log_optimizer_states": false, "log_gradient_noise_scale": false, "gradient_noise_scale_n_batches": 5, "gradient_noise_scale_cpu_offload": false, "pipe_parallel_size": 1, "model_parallel_size": 1, "pipe_partition_method": "type:transformer|mlp", "world_size": 1, "is_pipe_parallel": true, "data_path": "/mnt/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document", "train_data_paths": null, "test_data_paths": null, "valid_data_paths": null, "train_data_weights": null, "valid_data_weights": null, "test_data_weights": null, "weight_by_num_documents": false, "weighted_sampler_alpha": 0.3, "fim_probability": 0.0, "data_impl": "mmap", "mmap_warmup": false, "save": null, "config_files": {"config.yml": "attention-dropout: 0.0\nbias-gelu-fusion: true\ncheckpoint-activations: true\ncheckpoint-num-layers: 1\ndata-impl: mmap\ndata-path: /mnt/augment/data/processed/the-stack-dedup.2022-11-19/doc_text_document\ndistributed-backend: nccl\neval-interval: 40\neval-iters: 32\nfinal_linear_bias: true\nfinetune: true\nfp16:\n  enabled: true\n  hysteresis: 2\n  initial_scale_power: 12\n  loss_scale: 0\n  loss_scale_window: 1000\n  min_loss_scale: 1\ngpt_j_residual: true\ngradient_accumulation_steps: 24\ngradient_clipping: 1.0\nhidden-dropout: 0.0\nhidden-size: 2560\ninit_method: small_init\nkeep-last-n-checkpoints: 1\nload: /mnt/efs/augment/checkpoints/conan-2B-12000\nlog-interval: 100\nlog_dir: /mnt/efs/augment/jobs/166/logs\nlr-decay-iters: 15000\nlr-decay-style: cosine\nmake_vocab_size_divisible_by: 51200\nmax-position-embeddings: 2048\nmemorize_mode: train\nmemory_load: /mnt/efs/augment/mem\nmemory_save: /mnt/efs/augment/mem\nmin_lr: 1.6e-05\nno_weight_tying: true\nnorm: layernorm\nnum-attention-heads: 32\nnum-layers: 32\noptimizer:\n  params:\n    betas:\n    - 0.9\n    - 0.95\n    eps: 1.0e-08\n    lr: 0.00016\n  type: Adam\noutput_layer_init_method: wang_init\npartition-activations: true\npipe-parallel-size: 1\npos-emb: rotary\nrotary_interleave: true\nrotary_pct: 0.8\nsave-interval: 500\nscaled-upper-triang-masked-softmax-fusion: true\nseq-length: 1024\nsteps_per_print: 10\nsynchronize-each-layer: true\ntokenizer_type: CodeGenTokenizer\ntrain-iters: 15000\ntrain_batch_size: 288\ntrain_micro_batch_size_per_gpu: 12\nuse_post_attn_norm: false\nvocab_file: none\nwall_clock_breakdown: true\nwandb_name: conan-2B 2B-12000\nwandb_project: codegen\nwarmup: 0.2\nweight-decay: 0.1\nzero_optimization:\n  allgather_bucket_size: 500000000\n  allgather_partitions: true\n  contiguous_gradients: true\n  cpu_offload: false\n  overlap_comm: true\n  reduce_bucket_size: 500000000\n  reduce_scatter: true\n  stage: 1\n", "tmpdtu5g7wy.yml": "eval_batch_size: 1\neval_tags: {}\neval_with_memories: false\ntemperature: 0\ntop_k: 0\ntop_p: 0\n", "results_prefix.yml": "eval_results_prefix: /mnt/efs/augment/eval/jobs/BrTUZESQ/6244b58d7070_001\n"}, "load": "/mnt/efs/augment/checkpoints/conan-2B-12000", "checkpoint_validation_with_forward_pass": false, "save_interval": 500, "no_save_optim": false, "no_save_rng": false, "no_load_optim": true, "no_load_rng": true, "finetune": false, "batch_size": 12, "train_iters": 15000, "eval_iters": 32, "keep_last_n_checkpoints": 1, "eval_interval": 40, "early_stopping": false, "early_stopping_metric": "lm_loss", "early_stopping_threshold": 0.01, "split": "969, 30, 1", "vocab_file": "none", "merge_file": null, "num_workers": 2, "exit_interval": null, "attention_dropout": 0.0, "hidden_dropout": 0.0, "weight_decay": 0.1, "checkpoint_activations": false, "checkpoint_num_layers": 1, "deepspeed_activation_checkpointing": true, "contiguous_checkpointing": false, "checkpoint_in_cpu": false, "synchronize_each_layer": true, "profile_backward": false, "partition_activations": false, "gas": 24, "clip_grad": 1.0, "hysteresis": 2, "dynamic_loss_scale": true, "loss_scale": null, "loss_scale_window": 1000.0, "min_scale": 1.0, "char_level_ppl": false, "mem_friendly_batch": false, "train_only": null, "tokenizer_type": "CodeGenTokenizer", "padded_vocab_size": 51200, "optimizer_type": "<PERSON>", "use_bnb_optimizer": false, "zero_stage": 0, "zero_reduce_scatter": true, "zero_contiguous_gradients": false, "zero_reduce_bucket_size": 500000000, "zero_allgather_bucket_size": 500000000, "lr": 0.00016, "lr_decay_style": "cosine", "lr_decay_iters": 15000, "min_lr": 1.6e-05, "warmup": 0.2, "override_lr_scheduler": false, "use_checkpoint_lr_scheduler": false, "precision": "fp16", "num_layers": 32, "hidden_size": 2560, "num_attention_heads": 32, "seq_length": 1024, "max_position_embeddings": 2048, "norm": "layernorm", "layernorm_epsilon": 1e-05, "rms_norm_epsilon": 1e-08, "scalenorm_epsilon": 1e-08, "pos_emb": "rotary", "rotary_interleave": true, "rpe_num_buckets": 32, "rpe_max_distance": 128, "no_weight_tying": true, "attention_config": ["global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global", "global"], "sparsity_config": {}, "num_unique_layers": null, "param_sharing_style": "grouped", "make_vocab_size_divisible_by": 51200, "activation": "gelu", "scaled_upper_triang_masked_softmax_fusion": true, "scaled_masked_softmax_fusion": false, "bias_gelu_fusion": true, "bias_dropout_fusion": false, "fp16_lm_cross_entropy": false, "init_method_std": 0.02, "apply_query_key_layer_scaling": false, "use_cpu_initialization": false, "attention_softmax_in_fp32": false, "rotary_pct": 0.8, "rotary_emb_base": 10000, "init_method": "small_init", "output_layer_init_method": "wang_init", "gmlp_attn_dim": 64, "gpt_j_residual": true, "soft_prompt_tuning": null, "output_layer_parallelism": "row", "use_post_attn_norm": false, "final_linear_bias": true, "deepspeed": true, "train_batch_size": 288, "train_micro_batch_size_per_gpu": 12, "gradient_accumulation_steps": 24, "optimizer": {"params": {"betas": [0.9, 0.95], "eps": 1e-08, "lr": 0.00016}, "type": "<PERSON>"}, "scheduler": null, "fp32_allreduce": false, "prescale_gradients": false, "gradient_predivide_factor": 1.0, "sparse_gradients": false, "fp16": {"enabled": true, "hysteresis": 2, "initial_scale_power": 12, "loss_scale": 0, "loss_scale_window": 1000, "min_loss_scale": 1}, "amp": null, "gradient_clipping": 1.0, "zero_optimization": {"stage": 0, "allgather_partitions": true, "allgather_bucket_size": 500000000, "overlap_comm": false, "reduce_scatter": true, "reduce_bucket_size": 500000000, "contiguous_gradients": false, "cpu_offload": false}, "steps_per_print": 10, "wall_clock_breakdown": true, "dump_state": false, "flops_profiler": null, "zero_allow_untested_optimizer": false, "hostfile": null, "include": null, "exclude": null, "num_nodes": -1, "num_gpus": null, "master_port": 29500, "master_addr": null, "launcher": "pdsh", "detect_nvlink_pairs": false}, "num_fewshot": 0, "batch_size": 1, "device": "cuda:0", "no_cache": false, "limit": null, "bootstrap_iters": 10000, "description_dict": null, "duration": 134.45155143737793, "eval_with_memories": false, "eval_tags": {}}}