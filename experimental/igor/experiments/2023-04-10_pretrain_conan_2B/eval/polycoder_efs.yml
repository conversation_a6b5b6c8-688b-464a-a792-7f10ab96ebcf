model_path: "/mnt/efs/augment/checkpoints"

eval_tags: {}

neox_args: {
        "temperature": 0,
        "top_p": 0,
        "top_k": 0,
        "eval_with_memories": False,
}

models: [
        {"name": "codegen-2B-mono"},
        {"name": "codegen-2B-multi"},
        {"name": "conan-2B-12000"},
        {"name": "conan-2B-24000"},
        {"name": "conan-2B-48000"},
        {"name": "conan-2B-96000"},
]

tasks: [
"gitrepo_poly_C_small",
"gitrepo_poly_C++_small",
"gitrepo_poly_Go_small",
"gitrepo_poly_Java_small",
"gitrepo_poly_JavaScript_small",
"gitrepo_poly_Python_small",
]
