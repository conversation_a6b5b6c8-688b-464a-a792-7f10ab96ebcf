"""Experiment: Pretrain Conan-2B."""

import lm_plot
import matplotlib.pyplot as plt
import seaborn as sns

codegen_multi_data_tokens_2B = 500000000000
codegen_mono_data_tokens_2B = 650000000000
conan_batch_tokens = 589824


def _conan2B(steps, nodes):
    return {
        "training tokens": conan_batch_tokens * steps,
        "model type": f"Conan-2B ({nodes} node{'s' if nodes > 1 else ''})",
        "model name": f"conan-2B-{steps}{f'-{nodes}_nodes' if nodes > 1 else ''}",
    }


checkpoints = {
    "conan-2B-12000": _conan2B(12000, nodes=1),
    "conan-2B-24000": _conan2B(24000, nodes=1),
    "conan-2B-48000": _conan2B(48000, nodes=1),
    "conan-2B-96000": _conan2B(96000, nodes=1),
    "32b732a7-b384-4e57-8659-1597150b4b5e": _conan2B(12000, nodes=6),
    "codegen-2B-multi": {
        "training tokens": codegen_multi_data_tokens_2B,
        "model type": "CodeGen-2B-multi",
        "model name": "CodeGen-2B-multi",
    },
    "codegen-2B-mono": {
        "training tokens": codegen_mono_data_tokens_2B,
        "model type": "CodeGen-2B-mono",
        "model name": "CodeGen-2B-mono",
    },
}


def get_results(json_files, task=None):
    """Loads results from json files and returns them as a pandas DataFrame."""
    df = lm_plot.collect(json_files, lm_plot.files.ConfigExtractor()).raw()
    df = df[(df["metric"] == "byte_perplexity")]
    if task is not None:
        df = df[(df["task"] == task)]

    df_means = df.groupby("model")["value"].agg("mean").reset_index()
    df_means = df_means.rename(columns={"value": "byte perplexity"})

    return _postprocess_results(df_means)


def plot_results(df):
    """Plots perplexity vs. training tokens for different models."""
    sns.scatterplot(data=df, x="training tokens", y="byte perplexity", hue="model type")
    plt.xscale("log")


def _postprocess_results(df):
    for col in ["training tokens", "model type", "model name"]:
        df[col] = df["model"].map({k: v[col] for k, v in checkpoints.items()})

    # Sort the checkpoints in the order in which they appear in the checkpoints map
    order_id = {chk: idx for idx, chk in enumerate(checkpoints.keys())}
    df["order_id"] = df["model"].map(order_id)
    df = df.sort_values("order_id")

    return df[["model name", "model type", "training tokens", "byte perplexity"]]
