# Workspace Stop and Resume Design

## 1. Overview

This document outlines the design for implementing the ability to stop and resume remote agent workspaces. The goal is to optimize resource usage by stopping inactive workspaces after a period of inactivity, while preserving the ability to quickly resume them when needed.

## 2. Background

Currently, remote agent workspaces run continuously once created, consuming resources even when idle. The current implementation has a 1:1 mapping between agent sessions and workspaces, with each workspace running as a Kubernetes StatefulSet containing a pod with the beachhead container.

The architecture consists of multiple layers:
1. GCP Virtual Machine
2. Kubernetes Pod (Outie)
3. Firecracker microVM
4. Beachhead Container (Innie)

Persistent storage is handled via a Persistent Volume Claim (PVC) that is mounted as `/dev/persist` in the Firecracker VM and as `/mnt/persist` in the beachhead container.

## 3. Requirements

1. Automatically stop workspaces after a period of inactivity (15 minutes)
2. Define "inactivity" as the agent being in IDLE state AND no active SSH connections
3. Preserve all workspace state on persistent storage
4. Allow quick resumption of stopped workspaces
5. Maintain the same agent ID and chat history across stop/resume cycles
6. Ensure a seamless user experience when resuming a workspace

## 4. Design

### 4.1 Agent Status Enhancements

We will extend the `AgentStatus` enum to include new states:

```protobuf
enum AgentStatus {
  AGENT_STATUS_UNSPECIFIED = 0;
  AGENT_STATUS_PENDING = 5;
  AGENT_STATUS_STARTING = 1;
  AGENT_STATUS_RUNNING = 2;
  AGENT_STATUS_IDLE = 3;
  AGENT_STATUS_FAILED = 4;
  AGENT_STATUS_STOPPING = 6;  // New: The agent is in the process of being stopped
  AGENT_STATUS_STOPPED = 7;   // New: The agent is stopped but can be resumed
}
```

### 4.2 Inactivity Detection

The remote_agents service will implement a periodic check for inactive workspaces:

1. Query all agents in the IDLE state
2. For each idle agent, check the last activity timestamp
3. If the agent has been idle for more than 15 minutes, check for active SSH connections
4. If no active SSH connections exist, initiate the stopping process

### 4.3 SSH Connection Tracking

To detect active SSH connections, we'll implement a tracking mechanism:

1. Add an endpoint to the beachhead container to report SSH connection status
2. Modify the SSH daemon to log connection events
3. Implement a connection tracker in the beachhead container that monitors SSH connections
4. Expose the connection status via the `/health` endpoint

```typescript
// Add to the health endpoint response
app.get("/health", (req: express.Request, res: express.Response) => {
    res.status(200).json({
        status: "OK",
        agent_working: agentLoop.status() === RemoteAgentStatus.agentRunning,
        active_ssh_connections: sshManager.getActiveConnectionCount()
    });
});
```

### 4.4 Stopping Process

When a workspace is identified for stopping:

1. The remote_agents service marks the agent as STOPPING in BigTable
2. A stop command is sent to the beachhead container via the workspace stream
3. The beachhead container:
   - Ensures all state is saved to persistent storage
   - Gracefully shuts down the agent loop
   - Reports its status as STOPPING
   - Exits with a special exit code (e.g., 143 for SIGTERM)
4. The Kubernetes controller detects the container termination
5. The remote_agents service detects the container termination and marks the agent as STOPPED
6. The RemoteWorkspaceController scales the StatefulSet to 0 replicas while retaining the PVC

#### StatefulSet Scaling Implementation

The scaling of the StatefulSet to 0 replicas will be handled by the RemoteWorkspaceController, which runs as part of the remote_agents service. The RemoteWorkspaceController will be extended with a new method to handle workspace stopping:

```go
// Add to RemoteWorkspaceController interface
type RemoteWorkspaceController interface {
    // Existing methods...

    // StopWorkspace scales the StatefulSet to 0 replicas but retains the PVC
    StopWorkspace(ctx context.Context, user, id string) error
}

// Implementation in remoteWorkspaceControllerImpl
func (c *remoteWorkspaceControllerImpl) StopWorkspace(ctx context.Context, user, id string) error {
    // Get the StatefulSet
    sts, err := c.k8s.GetStatefulSet(ctx, buildName(id))
    if err != nil {
        return err
    }

    // Scale to 0
    stsAC := k8s.NewStatefulSetApplyConfig(sts.Name(), c.k8s.Namespace())
    stsAC.WithSpec(appsv1a.StatefulSetSpec().WithReplicas(0))

    // Apply the change
    _, err = c.k8s.ApplyStatefulSet(ctx, stsAC, k8s.ApplyForce(true))
    return err
}
```

The PVC retention is handled by the StatefulSet's `persistentVolumeClaimRetentionPolicy`, which is already configured in the current implementation to retain PVCs when scaling down:

```go
pvcpolicy := appsv1a.StatefulSetPersistentVolumeClaimRetentionPolicy()
pvcpolicy.WithWhenDeleted(appsv1.DeletePersistentVolumeClaimRetentionPolicyType)
pvcpolicy.WithWhenScaled(appsv1.RetainPersistentVolumeClaimRetentionPolicyType)
stsspec.WithPersistentVolumeClaimRetentionPolicy(pvcpolicy)
```

#### Protocol Changes for Stop Command

```go
// New workspace update type for stopping
enum WorkspaceUpdateType {
  // Existing types...
  WORKSPACE_UPDATE_INTERRUPT = 1;
  WORKSPACE_UPDATE_CHAT_REQUEST = 2;
  // New type
  WORKSPACE_UPDATE_STOP = 3;
}

// New update message
message WorkspaceStopRequest {
  // Optional reason for stopping
  string reason = 1;
}
```

### 4.5 Resuming Process

When a user attempts to interact with a stopped agent:

1. The remote_agents service detects that the agent is in STOPPED state
2. The service scales the StatefulSet back to 1 replica
3. The beachhead container starts up and mounts the existing persistent storage
4. The container detects it's resuming from a stopped state and loads the previous state
5. The agent reports its status as STARTING, then IDLE once ready
6. The user's request is processed once the agent is ready

### 4.6 State Persistence

To ensure all state is preserved across stop/resume cycles:

1. Ensure all critical state is stored on the persistent volume:
   - Agent state (chat history, tool state)
   - SSH host keys
   - Repository contents
   - Any user-generated files
2. Implement a checkpoint mechanism in the agent loop to save state before stopping
3. Implement a recovery mechanism to restore state on startup
