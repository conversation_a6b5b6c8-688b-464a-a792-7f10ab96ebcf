# Remote Agent Architecture Discussion - April 28, 2025

## Executive Summary

This document summarizes the architecture discussion regarding remote agent workspaces, focusing on the current implementation's layered architecture and potential recovery options for implementing agent pause/resume functionality. The discussion builds on previous research into improving agent startup time and workspace lifecycle management.

## Current Architecture

The remote agent architecture consists of multiple layers, each serving a specific purpose in the overall system:

```mermaid
graph TD
    subgraph "Google Cloud Platform"
        GCP["GCP VM"]

        subgraph "Kubernetes Pod (Outie)"
            K8S["Kubernetes Management Layer"]

            subgraph "Firecracker microVM"
                FC["Firecracker VM"]

                subgraph "Container (Innie)"
                    BC["Beachhead Container"]
                end
            end

            PVC["Persistent Volume Claim"]
        end
    end

    GCP --> K8S
    K8S --> FC
    FC --> BC
    K8S --> PVC
    PVC --> FC
```

### Layer 1: GCP Virtual Machine
- **Purpose**: Provides compute resources from Google Cloud
- **Future Support**: Will support different VM types (Linux/Windows) in various sizes
- **Capacity**: Each physical node hosts approximately 18 workspace pods

### Layer 2: Kubernetes on VM (Outie)
- **Purpose**: Provides logging and management capabilities
- **Configuration**: Each pod holds a single workspace
- **Storage**:
  - **PVCs**: Attach GCP block devices that support snapshots
  - **Root Volume**:
    - Ephemeral storage backed by local SSD (128GiB total)
    - Contains VM root filesystem and image
  - **Persist Volume**:
    - GCP block device with snapshot capability (16GiB, performance SSDs)
    - Mounted as `/dev/persist`

### Layer 3: Firecracker microVM
- **Purpose**: Provide a secure and isolated environment with a strict boundary
- **Mounts**:
  - **Root Volume**: Contains the VM operating system
  - **Persist Volume**: Mounted as `/mnt/persist`
    - Contains cloned repository, SSH keys, and agent loop state file
    - State file maintains chat history and enables recovery after crashes
- **Capability**: In principle, supports VM snapshots

### Layer 4: Beachhead Container
- **Purpose**: Deploys our beachhead component within the Firecracker VM
- **Current Implementation**: Uses our image with the beachhead component baked in
- **Future Support**:
  - Will support customer-provided Docker images
  - Requirement: OpenSSH server must be installed
  - Beachhead binary would be injected externally (e.g., via SSH)

## Infrastructure Organization

- **Workspace Nodes**: Located in different Google projects from the rest of our infrastructure
  - **Production & Staging**: augment-research-gsc
  - **Development**: augment-research-gsc

## Recovery Options

To implement agent pause/resume functionality and recovery after VM loss, several options were discussed:

### Option 1: Snapshot of Firecracker VM and Persist Volume
- **Pros**: Highest fidelity pause/unpause with complete state preservation
- **Cons**: Loses state on VM loss, requires VM snapshot capability

### Option 2: Snapshot Root Volume and Persist Volume
- **Pros**: More practical than Option 1, preserves filesystem state
- **Cons**: Requires root volume to be on a snapshotable block device
- **Limitations**: Loses environment variables and running processes
- **Note**: Potentially the best option for implementation

### Option 3: Start from Clean State
- **Pros**: Simplest implementation, no snapshot management required
- **Cons**: Requires rerunning setup scripts, which could take significant time
- **Note**: Similar to current implementation but with workspace reuse

## Workspace Lifecycle Considerations

- **Aging Out**: Important to have a mechanism for aging out old workspaces
- **Current Limitation**: "Stopping" a workspace currently means the agent is deleted in the user experience
- **Desired Upgrade Path**:
  - Stop an agent after a period of inactivity
  - On restart, upgrade the agent to the latest version
  - Preserve user context and chat history

## Other Capabilities Discussed

- **Custom container images.** It is important to support custom container images, e.g., via Dockerfile. Our competitors already support this.
- **Running additional customer-specific containers.** In some customer environments, even just running unit tests requires additional containers, e.g., running databases or other services. We envision supporting additional containers side-by-side with the beachhead container, but we didn't discuss details.
- **Custom runners.** We will also need to support custom runners. In that environment, we will distribute a beachhead binary (e.g., via GitHub Releases) that the customer will be responsible for installing into their VM or container. In this setup, none of the four layers discussed in this document apply.

## Secret Management Requirements

A follow-up discussion identified the need for a secret management system for remote agents:

- **Functionality**: Key-value store that exposes secrets as environment variables
- **Similar To**: GitHub Actions secrets system (https://docs.github.com/en/actions/security-for-github-actions/security-guides/using-secrets-in-github-actions)
- **Use Case**: Common requirement for configuring API keys and other credentials on dev machines
- **Scope**:
  - Small number of secrets per user
  - Potential for both user-specific and shared secrets
  - No current concept of teams or groups for sharing secrets

### Implementation Considerations:
- Integration into agent creation flow or user-level settings page
- Potential to store secrets locally and send on each agent creation initially
- May need to move to backend eventually to support browsers/multi-device/repository level access
- Could leverage existing admin web UI
- Introducing a repository concept in the backend could provide value for other use cases

### Priority:
- Identified as more urgent than other components due to common developer needs

## Appendix: Raw Notes

```
# Architecture Discussion - 4/28/2025

Layers:
	- VM in GCP
		- Purpose: compute we get from Google Cloud
		- Over time, will support different types of VMs
			* Linux / Windows, a few T-shirt sizes

	- k8s on VM
		- Purpose: logging, management
		- holds a single workspace
		- ~18 per physical node
		- also called "outie", as an outer container
		- PVCs - attaches the GCP block device
			- block device - snapshots
		- /dev/persist -> 16GiB -> performance SSDs
		- root volume
			- ephemeral storage, backed by local ssd and is currently 128GiB total.
			- contains VM root filesystem and image
		- persist volume
			- GCP block device with snapshots, 16GiB, performance SSDs
			- contains

	- Firecracker VM -> customer-supplied VM image
		- Mounts
			- root volume
			- /dev/persist -> /mnt/persist
				- contains cloned repo, ssh keys, agent loop state file (chat history, resumed on crash)
		- In principle, supports VM snapshots
	- beachhead container
		- Purpose: deploys our beachhead component
		- normally our image with our beachhead component baked in
 		- in the future, want to support customer-provided Docker image
			- requirement: openssh server needs to be installed
			- beachhead binary would be injected externally, e.g., via ssh

Recovery Options:
	- needed to pause/unpause agents, recovery on GCP VM loss
	- Options:
		(1) snapshot of firecracker VM and persist volume
			- highest fidelity pause / unpause
			- loses state on VM loss
			- requires VM snapshots
		(2) snapshot root volume and persist volume
			- requires root volume to be on a snapshotable block device
			- loses env vars, running processes
			- maybe best option?
		(3) start from clean state
			- need to rerun the setup script, which could take a long time

GCP clusters
	- Workspace nodes are a in different Google projects from the rest of our infrastructure
		- Production & Staging: augment-research-gsc
		- Dev: augment-research-gsc


Issue: Old Workspaces
	- Workspace lifecycle is also important to be able to age out old workspaces
	- Currently, "stopping" a workspace means that the agent is deleted in the user UX
	- Ultimately want to handle upgrades by:
		- stopping an agent after some time (through whatever stopping mechanism we have)
		- on restart, upgrading
```
