# Objective

Research and design a new lifecycle for remote agent workspaces that addresses the following issues:

1. Long agent startup time (~2 minutes) due to on-demand workspace creation
2. Loss of agent session access when an agent is deleted

The goal is to design a system that:
- Maintains a pool of pre-created workspaces
- Decouples workspaces from agents to allow assigning existing workspaces to agents
- Supports "stopping" an agent (pausing the workspace but preserving storage)
- Supports "resuming" an agent (assigning a new workspace and restoring state)
- Supports "archiving" an agent (deleting storage but keeping chat history in BigTable)
- Automatically stops agents after a period of inactivity (e.g., 15 minutes)

# Plan

- [X] Study the current implementation of agent workspaces
- [X] Understand how workspaces are created and managed in Kubernetes
- [X] Investigate how agent sessions are mapped to workspaces
- [X] Research how chat history is persisted in BigTable
- [X] Explore Kubernetes storage options for workspace persistence
- [X] Investigate snapshot capabilities for preserving workspace state
- [X] Research workspace pool management approaches
- [X] Study agent lifecycle management and inactivity detection
- [X] Analyze the decoupling of agents from workspaces
- [X] Design the new workspace lifecycle

# Current Implementation of Agent Workspaces

The current implementation of agent workspaces has a 1:1 mapping between agent sessions and workspaces. Each workspace is a Kubernetes StatefulSet that contains a pod running the agent.

Key components:

1. **RemoteWorkspaceController**: Manages the lifecycle of workspaces
   - Located in `services/remote_agents/server/ws/controller.go`
   - Provides methods for creating, listing, getting, and interrupting workspaces
   - Uses Kubernetes to manage the underlying resources

2. **RemoteWorkspace**: Interface for interacting with a workspace
   - Located in `services/remote_agents/server/ws/workspace.go`
   - Provides methods for getting workspace status, agent configuration, and interacting with the agent

3. **Kubernetes Resources**:
   - **StatefulSet**: Main resource for the workspace
   - **Secret**: Stores configuration data
   - **Service**: Exposes the workspace for communication
   - **Pod**: The actual container running the agent

The workspace creation process:
1. When a user requests a new agent (`CreateAgentRequest`), a new workspace is created
2. The workspace is identified by the agent ID (a UUID)
3. The agent configuration is stored in a Kubernetes Secret
4. A StatefulSet is created with the agent container
5. The workspace takes about 2 minutes to start up

When an agent is stopped, the workspace is deleted, and the user loses access to the agent session. However, the chat history is persisted in BigTable.

# Kubernetes Workspace Management

Workspaces are implemented as Kubernetes resources, primarily using StatefulSets. The key components include:

1. **StatefulSet**: Provides stable, unique network identifiers, persistent storage, and ordered deployment and scaling
   - Created in `buildStatefulSet` function in `services/remote_agents/server/ws/build.go`
   - Includes configuration for the container, volumes, and networking

2. **PersistentVolumeClaim (PVC)**: Provides storage for the workspace
   - The StatefulSet includes a volumeClaimTemplate that creates a PVC for each pod
   - This provides persistent storage that survives pod restarts

3. **Secret**: Stores configuration data for the workspace
   - Created in `buildConfigSecret` function in `services/remote_agents/server/ws/build.go`
   - Contains the agent configuration, GitHub tokens, and other sensitive data

4. **Service**: Exposes the workspace for communication
   - Created in `buildService` function in `services/remote_agents/server/ws/build.go`
   - Provides a stable endpoint for communicating with the workspace

The workspace is created using the Kubernetes Apply API, which creates or updates resources as needed. The Secret is created first and is set as the owner of the other resources, so when the Secret is deleted, the other resources are automatically deleted as well.

# Agent Session to Workspace Mapping

Agent sessions are currently mapped 1:1 with workspaces. The mapping is established when an agent is created:

1. In `CreateAgent` method of `RemoteAgentsServer` (`services/remote_agents/server/server.go`):
   - A new agent ID is generated
   - The agent configuration is written to BigTable
   - A workspace is created with the same ID as the agent

2. The workspace is created using `ApplyWorkspace` method of `RemoteWorkspaceController`:
   - Creates Kubernetes resources for the workspace
   - Sets the agent ID as part of the workspace name and labels

3. The agent status is tracked in both BigTable and the workspace:
   - BigTable stores the agent configuration and status
   - The workspace provides real-time status through the Kubernetes API

This tight coupling means that when a workspace is deleted, the agent session is effectively terminated, even though the chat history remains in BigTable.

# Chat History Persistence in BigTable

Chat history is persisted in BigTable, independent of the workspace. This allows the chat history to survive even if the workspace is deleted.

Key aspects of chat history persistence:

1. **Row Key Structure**:
   - `ExchangeHistory#{agentID}#{sequenceID}`: Stores chat exchanges with proper ordering
   - `RemoteAgent#{agentID}`: Stores agent configuration and status
   - `UserAgentMapping#{userID}`: Maintains a list of agent IDs associated with each user
   - `UpdateSequenceID#{agentID}`: Tracks the latest sequence ID for agent updates

2. **Column Families**:
   - `Config`: Contains user-defined configuration for the agent
   - `Status`: Contains runtime info about the agent
   - `Output`: Contains chat dialog and exchanges
   - `UserMapping`: Maintains user-to-agent mappings

3. **Chat History Writing**:
   - When an agent generates a response, it's written to BigTable using `writeChatHistoryChunk` function
   - Each exchange is identified by a sequence ID for proper ordering
   - The agent periodically reports its chat history to the server

4. **Chat History Reading**:
   - When a user requests chat history, it's read from BigTable using row key prefix scanning
   - The exchanges are ordered by sequence ID and returned to the user

This separation of chat history from the workspace allows for the possibility of resuming an agent with a new workspace while maintaining the chat history.

# Kubernetes Storage Options for Workspace Persistence

Kubernetes provides several options for persistent storage that could be used for workspace persistence:

1. **PersistentVolumeClaims (PVCs)**:
   - Already used in the current implementation
   - Provides persistent storage that survives pod restarts
   - Can be retained even if the pod or StatefulSet is deleted
   - Supports various storage classes with different performance characteristics

2. **StorageClasses**:
   - Define the type of storage to use for PVCs
   - GKE provides several storage classes:
     - `standard-rwx`: HDD storage, 1-64 TiB
     - `premium-rwx`: SSD storage, 2.5-64 TiB
     - `enterprise-rwx`: SSD storage, 1-10 TiB
     - `enterprise-multishare-rwx`: SSD storage, 1-10 TiB

3. **VolumeSnapshotClasses**:
   - Allow for creating snapshots of volumes
   - Defined in `infra/cfg/lib/k8s/k8s-storage.jsonnet`
   - Support different snapshot policies (Delete or Retain)

4. **PersistentVolumeReclaimPolicy**:
   - Controls what happens to a PersistentVolume when its claim is deleted
   - Options include `Delete` (delete the volume) and `Retain` (keep the volume)
   - The current implementation uses `Retain` for some volumes

These storage options provide the foundation for implementing workspace persistence, allowing for stopping and resuming agents without losing their state.

# Snapshot Capabilities for Preserving Workspace State

Kubernetes supports volume snapshots, which can be used to preserve the state of a workspace:

1. **VolumeSnapshot Resource**:
   - Defined in `infra/lib/k8s/k8s-volume-snapshot.go`
   - Creates a point-in-time copy of a volume
   - Can be used to create a new volume with the same data

2. **Snapshot Creation**:
   - Can be triggered programmatically using the Kubernetes API
   - Example in `experimental/marcmac/firecracker-go/pkg/server/server.go` shows creating a snapshot of a VM

3. **Snapshot Restoration**:
   - A new volume can be created from a snapshot
   - The new volume will have the same data as the original volume at the time the snapshot was taken

4. **Snapshot Management**:
   - Snapshots can be listed, described, and deleted using the Kubernetes API
   - Snapshots can be retained even if the original volume is deleted

Using volume snapshots would allow for preserving the state of a workspace when an agent is stopped, and restoring that state when the agent is resumed.

# Workspace Pool Management Approaches

Managing a pool of workspaces involves creating, maintaining, and assigning workspaces as needed. Several approaches could be used:

1. **Pre-creation of Workspaces**:
   - Create a pool of workspaces in advance
   - Workspaces would be in a "ready" state, waiting to be assigned to an agent
   - When a new agent is requested, assign an existing workspace instead of creating a new one

2. **Dynamic Pool Sizing**:
   - Monitor usage patterns and adjust the pool size accordingly
   - Increase the pool size during peak hours and decrease it during off-hours
   - Ensure there are always enough workspaces available to handle incoming requests

3. **Workspace Recycling**:
   - When an agent is stopped, the workspace can be reset and returned to the pool
   - This would involve cleaning up any agent-specific data and resetting the workspace to a known state
   - The workspace could then be assigned to a new agent

4. **Workspace Templates**:
   - Define templates for different types of workspaces
   - When a new workspace is needed, create it from a template
   - This would allow for faster workspace creation compared to starting from scratch

5. **Workspace Preheating**:
   - Periodically "preheat" workspaces by running common initialization tasks
   - This would reduce the time needed to start an agent on a new workspace

These approaches could be combined to create an efficient workspace pool management system that reduces agent startup time and improves resource utilization.

# Agent Lifecycle Management and Inactivity Detection

Agent lifecycle management involves tracking the state of agents and managing transitions between states:

1. **Agent States**:
   - Defined in `services/remote_agents/remote_agents.proto`
   - States include `STARTING`, `RUNNING`, `IDLE`, and `FAILED`
   - The current state is stored in BigTable and can be queried through the API

2. **State Transitions**:
   - Agents transition between states based on user actions and system events
   - For example, when a user sends a chat request, the agent transitions from `IDLE` to `RUNNING`
   - When an agent completes a task, it transitions from `RUNNING` to `IDLE`

3. **Inactivity Detection**:
   - Currently, there's no automatic inactivity detection
   - Could be implemented by tracking the last activity time for each agent
   - After a period of inactivity (e.g., 15 minutes), the agent could be automatically stopped

4. **Agent Interruption**:
   - Implemented in `InterruptAgent` method of `RemoteAgentsServer`
   - Writes an interrupt request to BigTable
   - The agent periodically checks for interrupt requests and stops processing if one is found

5. **Agent Deletion**:
   - Currently, when an agent is deleted, the workspace is also deleted
   - This could be modified to support "archiving" an agent (keeping chat history but deleting the workspace)

Implementing automatic inactivity detection would require adding a timestamp for the last activity and periodically checking for inactive agents.

# Decoupling Agents from Workspaces

Decoupling agents from workspaces would allow for more flexible lifecycle management:

1. **Separate Identifiers**:
   - Currently, the agent ID is used as the workspace name
   - Could introduce a separate workspace ID
   - The agent would store a reference to its current workspace ID

2. **Workspace Assignment**:
   - When an agent is created, assign it an existing workspace from the pool
   - Store the workspace ID in the agent configuration
   - When the agent is stopped, the workspace can be returned to the pool

3. **Workspace State**:
   - The workspace state (files, processes, etc.) would be separate from the agent state (chat history, configuration)
   - When an agent is resumed, it would be assigned a workspace and its state would be restored

4. **Agent Migration**:
   - An agent could be migrated from one workspace to another
   - This would involve taking a snapshot of the current workspace, stopping the agent, and restoring the snapshot to a new workspace

5. **Workspace Sharing**:
   - In the future, multiple agents could potentially share a workspace
   - This would require careful isolation between agents

Decoupling agents from workspaces would provide the foundation for implementing the desired features of stopping, resuming, and archiving agents.

# New Workspace Lifecycle Design

Based on the research, here's a design for the new workspace lifecycle:

1. **Workspace Pool**:
   - Maintain a pool of pre-created workspaces
   - Workspaces are in a "ready" state, waiting to be assigned to an agent
   - The pool size is dynamically adjusted based on usage patterns

2. **Agent Creation**:
   - When a new agent is requested, assign it an existing workspace from the pool
   - Store the workspace ID in the agent configuration
   - Initialize the workspace with the agent's configuration

3. **Agent Operation**:
   - The agent operates normally, using the assigned workspace
   - Chat history is periodically written to BigTable
   - The agent's state is tracked in both BigTable and the workspace

4. **Agent Stopping**:
   - When an agent is stopped (either manually or due to inactivity), take a snapshot of the workspace
   - Store the snapshot ID in the agent configuration
   - Return the workspace to the pool after resetting it

5. **Agent Resuming**:
   - When an agent is resumed, assign it a workspace from the pool
   - Restore the workspace from the snapshot
   - The agent continues operation with its previous state

6. **Agent Archiving**:
   - When an agent is archived, delete its snapshots
   - The chat history remains in BigTable
   - The agent can still be viewed but cannot be resumed

7. **Inactivity Management**:
   - Track the last activity time for each agent
   - After a period of inactivity (e.g., 15 minutes), automatically stop the agent
   - This frees up resources while preserving the agent's state

This design addresses the issues with the current implementation by reducing agent startup time and allowing for more flexible agent lifecycle management.
