# Remote Agent Workspaces Lifecycle Design

## Executive Summary

This document outlines a new design for the lifecycle of remote agent workspaces that addresses two key issues with the current implementation:

1. **Long agent startup time** (~2 minutes) due to on-demand workspace creation
2. **Loss of agent session access** when an agent is deleted

The proposed design introduces a workspace pool, decouples agents from workspaces, and adds support for stopping, resuming, and archiving agents. This will significantly improve the user experience by reducing agent startup time and providing more flexible agent lifecycle management.

## Current Implementation

The current implementation has a 1:1 mapping between agent sessions and workspaces:

- Each workspace is a Kubernetes StatefulSet containing a pod running the agent
- Workspaces are created on demand when a new agent is requested
- When an agent is stopped, the workspace is deleted
- Chat history is persisted in BigTable, independent of the workspace

This tight coupling leads to the issues mentioned above, as creating a new workspace takes significant time, and deleting a workspace means losing access to the agent session.

## Proposed Design

### 1. Workspace Pool

Maintain a pool of pre-created workspaces that are ready to be assigned to agents:

```mermaid
graph TD
    subgraph "Workspace Pool"
        W1["Workspace #1"]
        W2["Workspace #2"]
        W3["..."]
    end
```

- Workspaces are created in advance and kept in a "ready" state
- The pool size is dynamically adjusted based on usage patterns
- When a new agent is requested, it's assigned an existing workspace from the pool
- This reduces agent startup time from ~2 minutes to seconds

### 2. Decoupling Agents from Workspaces

Separate the agent identity from the workspace identity:

```mermaid
graph LR
    A["Agent #A"] --> W1["Workspace #1"]
```

- Introduce separate identifiers for agents and workspaces
- Store the workspace ID in the agent configuration
- This allows for more flexible lifecycle management, including workspace reassignment

### 3. Agent Lifecycle States

Expand the agent lifecycle to include new states:

```mermaid
stateDiagram-v2
    STARTING --> RUNNING
    RUNNING --> IDLE
    IDLE --> STOPPED
    STOPPED --> ARCHIVED
```

- **STARTING**: Agent is being initialized with a workspace
- **RUNNING**: Agent is actively processing a request
- **IDLE**: Agent is waiting for user input
- **STOPPED**: Agent is inactive, but its state is preserved
- **ARCHIVED**: Agent's workspace state is deleted, but chat history is preserved

### 4. Workspace State Preservation

Use Kubernetes volume snapshots to preserve workspace state:

```mermaid
graph LR
    W1["Workspace #1"] --> S1["Snapshot #A1"]
```

- When an agent is stopped, take a snapshot of its workspace
- Store the snapshot ID in the agent configuration
- When the agent is resumed, restore the workspace from the snapshot
- This preserves the agent's state (files, processes, etc.) between sessions

### 5. Inactivity Management

Automatically stop inactive agents to free up resources:

```mermaid
stateDiagram-v2
    IDLE --> TIMEOUT
    TIMEOUT --> STOPPED
```

- Track the last activity time for each agent
- After a period of inactivity (e.g., 15 minutes), automatically stop the agent
- This frees up resources while preserving the agent's state

## Implementation Details

### Workspace Pool Management

1. **Pool Creation and Maintenance**:
   - Create a new Kubernetes controller for managing the workspace pool
   - The controller monitors the number of available workspaces and creates new ones as needed
   - Workspaces are created using the same templates as the current implementation

2. **Workspace Assignment**:
   - When a new agent is requested, the controller assigns it an available workspace
   - The workspace ID is stored in the agent configuration in BigTable
   - The workspace is labeled with the agent ID for tracking purposes

3. **Workspace Disposal**:
   - When an agent is stopped or archived, the workspace is discarded (deleted)
   - No attempt is made to reset or reuse workspaces
   - The pool controller creates new workspaces to maintain the desired pool size

### Agent Lifecycle Management

1. **Agent Creation**:
   - When a new agent is created, it's assigned a workspace from the pool
   - The agent configuration is stored in BigTable as before
   - The agent starts in the STARTING state and transitions to RUNNING when ready

2. **Agent Stopping**:
   - When an agent is stopped, a snapshot of its workspace is taken
   - The snapshot ID is stored in the agent configuration
   - The workspace is discarded (deleted)
   - The agent transitions to the STOPPED state

3. **Agent Resuming**:
   - When a stopped agent is resumed, it's assigned a fresh workspace from the pool
   - The workspace is restored from the snapshot
   - The agent transitions to the RUNNING state
   - This allows the agent to continue from where it left off with a new workspace

4. **Agent Archiving**:
   - When an agent is archived, its snapshots are deleted
   - The agent configuration and chat history remain in BigTable
   - The agent transitions to the ARCHIVED state
   - Archived agents can be viewed but cannot be resumed

5. **Inactivity Detection**:
   - The last activity time for each agent is tracked in BigTable
   - A background process periodically checks for inactive agents
   - After a period of inactivity, agents are automatically stopped

### API Changes

1. **New Endpoints**:
   - `StopAgent`: Stops an agent and preserves its state
   - `ResumeAgent`: Resumes a stopped agent
   - `ArchiveAgent`: Archives an agent (deletes workspace state but keeps chat history)

2. **Modified Endpoints**:
   - `CreateAgent`: Assigns an existing workspace instead of creating a new one
   - `DeleteAgent`: Renamed to `ArchiveAgent` for clarity

3. **New Fields**:
   - `workspace_id`: The ID of the workspace assigned to the agent
   - `snapshot_id`: The ID of the snapshot for a stopped agent
   - `last_activity_time`: The timestamp of the last activity for the agent

## Benefits

1. **Reduced Agent Startup Time**:
   - Assigning pre-created workspaces reduces startup time from ~2 minutes to seconds
   - This significantly improves the user experience, especially for new agents

2. **Improved Resource Utilization**:
   - Stopping inactive agents frees up resources
   - The workspace pool can be sized based on actual usage patterns

3. **Enhanced Agent Lifecycle Management**:
   - Users can stop and resume agents without losing their state
   - Archiving agents preserves chat history while freeing up resources

4. **Better User Experience**:
   - Users can access their agents even after they've been stopped
   - The system automatically manages resources based on usage patterns

## Conclusion

The proposed design addresses the key issues with the current implementation by introducing a workspace pool, decoupling agents from workspaces, and adding support for stopping, resuming, and archiving agents. This will significantly improve the user experience and resource utilization.

The design follows a "create and discard" model for workspaces:
- Fresh workspaces are created for the pool
- Workspaces are assigned to agents when needed
- Workspaces are discarded (deleted) when no longer needed
- No attempt is made to reset or reuse workspaces

Implementation can be phased, starting with the workspace pool and basic lifecycle management, followed by snapshot support and inactivity detection. The design is compatible with the existing architecture and can be implemented with minimal changes to the client-facing APIs.
