# Remote Agent Workspaces: Phase 1 Implementation Plan

## Overview

This document outlines the implementation plan for the phase 1 of the remote agent workspaces redesign, focusing on decoupling agents from workspaces and introducing workspace IDs from the workspace controller.

### Key Changes

1. **Remote Agents Service**:
   - Update the `CreateAgent` RPC to accept and store workspace IDs from the workspace controller
   - Modify BigTable storage to record workspace IDs for agents
   - Implement backward compatibility for existing agents without workspace IDs

2. **BigTable Schema Updates**:
   - Enhance the agent record structure to include workspace references
   - Create a `WorkspaceRef` structure that initially contains just the workspace ID
   - Design the schema to be extensible for future additions (e.g., cluster name)

3. **Backward Compatibility**:
   - For existing agents, maintain the current naming convention
   - When retrieving agent data, check if a workspace ID exists in BigTable
   - If no workspace ID is found, fall back to the legacy naming convention

### Implementation Details

#### BigTable Schema

Update the BigTable schema to store workspace references:

```go
// WorkspaceRef contains information about a workspace assigned to an agent
type WorkspaceRef struct {
    WorkspaceID string `json:"workspace_id"`
    // Future fields can be added here (e.g., ClusterName, Region, etc.)
}

// AgentRecord will be updated to include workspace references
type AgentR<PERSON>ord struct {
    // Existing fields...
    CurrentWorkspace *WorkspaceRef   `json:"current_workspace,omitempty"`
    PastWorkspaces   []*WorkspaceRef `json:"past_workspaces,omitempty"`
}
```

#### CreateAgent RPC Handler

Update the CreateAgent RPC handler to store workspace IDs:
- Modify the handler to accept workspace IDs from the workspace controller
- Store the workspace ID in the agent record in BigTable
- Implement proper error handling for workspace creation failures

#### Agent Retrieval

Update agent retrieval logic to use workspace IDs when available:
- Check if a workspace ID exists in the agent record
- If a workspace ID exists, use it for workspace operations
- If no workspace ID exists, fall back to the legacy naming convention (using agent ID)

### Future Considerations

1. **Multi-Cluster Support**:
   - Extend `WorkspaceRef` to include cluster information
   - Implement cross-cluster agent migration

2. **Workspace Usage Tracking**:
   - Track workspace usage for billing purposes
   - Implement workspace quotas and limits

3. **Custom Container Support**:
   - Support customer-provided Docker images
   - Implement security measures for custom containers

## Responsibilities

- **Remote Agents Changes**: Mike
- **Infrastructure Changes**: Matt M


## What Comes After

Phase 2 will focus on implementing preallocated warmed-up workspace pools to improve agent startup time. This phase will build on the workspace ID integration from Phase 1.
