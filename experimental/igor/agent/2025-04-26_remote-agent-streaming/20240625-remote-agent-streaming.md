# Remote Agent Streaming Implementation

## Overview

This document describes the implementation of streaming for remote agents. The streaming feature allows clients to receive real-time updates from remote agents, including incremental text updates, agent status changes, and session summaries.

## Protocol Changes

### ChatHistoryUpdateType Enum

```protobuf
enum ChatHistoryUpdateType {
  UNSPECIFIED = 0;
  CHAT_HISTORY_EXCHANGE = 1;
  TEXT_UPDATE = 2;
  AGENT_STATUS = 3;
  SESSION_SUMMARY = 4;
}
```

### ChatHistoryUpdate Message

```protobuf
message ChatHistoryUpdate {
  // The sequence ID of the exchange being updated
  uint32 sequence_id = 1;
  ChatHistoryUpdateType type = 2;
  // For CHAT_HISTORY_EXCHANGE updates
  optional ChatHistoryExchange exchange = 3;
  // For AGENT_STATUS updates
  optional Agent agent = 4;
  // For TEXT_UPDATE updates
  optional string text_update = 5;
  // For SESSION_SUMMARY updates
  optional string session_summary = 6;
}
```

### ChatHistoryStreamResponse Message

```protobuf
message ChatHistoryStreamResponse {
  // Structured updates
  repeated ChatHistoryUpdate updates = 1;
}
```

## Server Implementation

The server implementation is in `services/remote_agents/server/chat_history_streamer.go`. The main changes include:

1. Replacing `ChatHistoryNode` with `ChatHistoryUpdate`
2. Removing `EXCHANGE_COMPLETE` node type
3. Adding `TEXT_UPDATE` and `SESSION_SUMMARY` as dedicated update types
4. Only delivering Agent updates when the status changes
5. Extracting session summary from the first exchange's turn_summary

## Client Implementation

The client implementation is in `experimental/igor/agent/agent-tools/remote_agent_cli.py`. The main changes include:

1. Updating the `get_chat_history_stream` function to handle the new message format
2. Updating the update type constants to match the new enum values
3. Extracting the sequence ID from the update instead of the exchange

## Testing

To test the remote agent streaming feature:

1. Deploy the server with:
   ```
   bazel run //services/deploy:dev_deploy -- --services chat agents default remote_agents_all --operation Apply
   ```

2. Test with the CLI tool:
   ```
   python3.11 experimental/igor/agent/agent-tools/remote_agent_cli.py stream <agent-id>
   ```

## Future Improvements

1. Consider combining `ChatHistoryStreamResponse` and `ChatHistoryUpdate` to simplify the protocol
2. Add support for more update types, such as tool calls and file changes
3. Improve error handling and reconnection logic
