# Remote Agent Chat History Streaming Design Discussion

This document describes the challenges with the current architecture of remote agent chat history
and compares two options for going forward:

- Continue with the polling-based architecture
- Move to a streaming-based architecture

We can either try to mitigate the issues in the current polling-based architecture or chart a
course to a fundamentally more efficient streaming-based architecture.

## Issues with Current Implementation

The current polling-based implementation has several critical issues that impact user experience
and scalability.

### User Experience

#### Choppy Streaming

Due to multiple stages of polling, the streaming experience is choppy.

#### First Message Latency

There is a delay before the first message appears in the chat history. This is also because
the signal needs to propagate through layers of polling.

### Possible Scaling Challenges

#### API Proxy Load
- Beachhead polling accounts for approximately 90% of all API Proxy requests
- This is with very light Remote Agent usage
- It's not clear if API Proxy was designed for this level of load. Each request is recorded in Request Insight, logged, etc.
- There are other types of polling in the Remote Agent system that may also contribute

#### BigTable Persistence
- High write frequency during agent streaming (one write per chunk)
- High read frequency due to polling reads

### Architectural Inconsistency
- Remote agent chat uses polling while local agent chat uses streaming
- Different code paths for similar functionality increases maintenance burden


## Current Implementation

The current implementation of remote agent chat history uses polling at various stages
of the workflow. Below are the key polling workflows illustrated with diagrams.

```mermaid
flowchart LR
    IDE <--> API_Proxy <--> Remote_Agents <--> BigTable
    Beachhead <--> API_Proxy <--> Remote_Agents
    Beachhead <--> API_Proxy <--> Chat_Host <--> Claude

    classDef component fill:#f9f,stroke:#333,stroke-width:2px
    class IDE,API_Proxy,Remote_Agents,BigTable,Beachhead,Chat_Host,Claude component
```

### 1. IDE submits an agent request

```mermaid
flowchart LR
    IDE --> API_Proxy --> Remote_Agents --> BigTable

    classDef component fill:#f9f,stroke:#333,stroke-width:2px
    class IDE,API_Proxy,Remote_Agents,BigTable component
```

**Details:**
- User initiates a chat request from the VSCode extension or web UI
- The IDE sends the request to the API Proxy
- API Proxy forwards the request to the Remote Agents service
- Remote Agents service stores the request in BigTable with a unique sequence ID
- The request is marked as pending in BigTable, waiting for the Beachhead to pick it up

**Issues:**
- Data flow terminates with the request stored in BigTable, requiring subsequent polling by the Beachhead to continue the workflow

### 2. Beachhead polls for work

```mermaid
flowchart LR
    Beachhead <--> API_Proxy <--> Remote_Agents <--> BigTable

    classDef component fill:#f9f,stroke:#333,stroke-width:2px
    class Beachhead,API_Proxy,Remote_Agents,BigTable component
```

**Details:**
- Beachhead periodically polls for pending work
- The request goes through API Proxy to the Remote Agents service
- Remote Agents service queries BigTable for pending requests
- If a pending request is found, it's returned to the Beachhead
- The request status is updated in BigTable to indicate it's being processed

**Issues:**
- High rate of requests via API Proxy - already accounting for approximately 90% of *all* API Proxy requests

### 3. Beachhead streams response from Claude

```mermaid
flowchart LR
    Beachhead <--> API_Proxy <--> Chat_Host <--> Claude

    classDef component fill:#f9f,stroke:#333,stroke-width:2px
    class Beachhead,API_Proxy,Chat_Host,Claude component
```

**Details:**
- Beachhead processes the request and sends it to Claude via Chat Host
- Chat Host manages the interaction with Claude's API
- Claude generates a response, which is streamed back to the Beachhead
- The streaming response allows Beachhead to receive chunks of the response as they're generated
- Beachhead processes these chunks and prepares them for storage

**Issues:**
This dataflow is OK because it's the same as in local chat or agent.

### 4. Beachhead publishes results

```mermaid
flowchart LR
    Beachhead --> API_Proxy --> Remote_Agents --> BigTable

    classDef component fill:#f9f,stroke:#333,stroke-width:2px
    class Beachhead,API_Proxy,Remote_Agents,BigTable component
```

**Details:**
- As Beachhead receives response chunks from Claude, it publishes them to BigTable
- Each update overwrites the previous version of the exchange in BigTable
- The complete exchange (request + current response) is written each time
- The sequence ID remains the same throughout this process
- When the response is complete, a status flag is set to "complete"

**Issues:**
- High rate of writes into BigTable (one write per chunk of response)
- Data flow terminates with results in BigTable, requiring subsequent polling by the IDE

### 5. IDE polls chat history

```mermaid
flowchart LR
    IDE <--> API_Proxy <--> Remote_Agents <--> BigTable

    classDef component fill:#f9f,stroke:#333,stroke-width:2px
    class IDE,API_Proxy,Remote_Agents,BigTable component
```

**Details:**
- IDE polls for chat history updates at regular intervals (every 1 second)
- The request includes the last processed sequence ID
- Remote Agents service queries BigTable for exchanges with sequence IDs greater than the last processed ID
- BigTable returns the matching exchanges
- Remote Agents service formats and returns the chat history
- IDE updates its UI with the new exchanges
- This polling continues as long as the IDE is active

**Issues:**
- Polling-based implementation diverges from streaming-based implementation of local agents/chat
- High frequency of requests (every 1 second) creates unnecessary load on API Proxy and Remote Agents service

## Possible Architectures

We have two main architectural options to address the current issues:

### Option 1: Optimize Polling-Based Architecture

Continue with the current polling-based architecture but implement optimizations.

**Required Work:**
1. **Frontend Optimizations:**
   - Implement client-side smoothing for chat updates
   - Reduce perceived latency for first message display

2. **Backend Optimizations:**
   - Reduce Beachhead polling frequency while maintaining responsiveness
   - Optimize API Proxy request handling for polling endpoints
   - Implement BigTable read/write optimizations
   - Add caching layers to reduce direct BigTable access

3. **Challenges:**
   - May require significant optimization work with diminishing returns
   - Doesn't address the fundamental architectural inconsistency
   - Scaling issues may resurface as usage grows

### Option 2: Migrate to Streaming Architecture

Implement a comprehensive streaming architecture that aligns with the local agent implementation.

**Implementation Phases:**
1. **Phase 1: Backend Chat History Streaming**
   - Move chat history polling from frontend to backend
   - Implement streaming endpoint in Remote Agents service
   - Update frontend to use streaming connection
   - This provides some efficiency gains while setting foundation for further changes

2. **Phase 2: Real-time BigTable Notifications**
   - Implement notification system for BigTable updates (Redis PubSub, API Proxy -> Remote Agents broadcast or similar)
   - Enable Remote Agents to push updates to clients immediately when available
   - Significantly reduces latency and improves responsiveness

3. **Phase 3: End-to-End Streaming**
   - Implement streaming from Beachhead to Remote Agents
   - Requires Client->Server streaming via API Proxy (WebSockets or HTTP/2)
   - Completes the streaming architecture for maximum efficiency

## Related Considerations

While this document focuses on chat history streaming, several related aspects of the remote agent system are also related:

* **Agent Setup and Logs Streaming**: Agent logs during creation/setup are polled every 500ms
* **Agent Overviews Polling**: Agent overviews are polled every 5 seconds

Both of these data flows can be handled in a way consistent with chat history architecture.
