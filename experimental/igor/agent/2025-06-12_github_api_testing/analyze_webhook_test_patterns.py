#!/usr/bin/env python3
"""
Analyze webhook test patterns from GetMatchingEntities API test data.
Extracts entity samples and filtering patterns for webhook validation testing.
"""

import json
import os
from typing import Dict, List, Any, Set
from dataclasses import dataclass
from datetime import datetime


@dataclass
class EntitySample:
    """Sample entity for testing webhook filtering."""

    entity_id: str
    entity_type: str  # "pull_request" or "workflow_run"
    data: Dict[str, Any]


@dataclass
class FilterTestCase:
    """Test case for webhook filtering validation."""

    filter_name: str
    filter_field: str
    filter_value: Any
    should_match_entities: List[str]  # Entity IDs that should match
    should_not_match_entities: List[str]  # Entity IDs that should NOT match
    total_expected_matches: int


def load_test_results(test_dir: str) -> Dict[str, Dict[str, Any]]:
    """Load all test results from directory."""
    results = {}

    for filename in os.listdir(test_dir):
        if (
            filename.endswith(".json")
            and not filename.startswith("test_summary")
            and not filename.startswith("webhook_reference")
        ):
            filepath = os.path.join(test_dir, filename)
            with open(filepath, "r") as f:
                data = json.load(f)
                test_name = data.get("test_name", filename.replace(".json", ""))
                results[test_name] = data

    return results


def extract_entity_samples(
    test_results: Dict[str, Dict[str, Any]],
) -> Dict[str, List[EntitySample]]:
    """Extract diverse entity samples for testing."""
    pr_samples = []
    wr_samples = []

    # Get baseline entities for comprehensive coverage
    if "pr_baseline" in test_results:
        for entity in test_results["pr_baseline"]["entities"]:
            pr_samples.append(
                EntitySample(
                    entity_id=entity["id"], entity_type="pull_request", data=entity
                )
            )

    if "wr_baseline" in test_results:
        for entity in test_results["wr_baseline"]["entities"]:
            wr_samples.append(
                EntitySample(
                    entity_id=entity["id"], entity_type="workflow_run", data=entity
                )
            )

    return {"pull_request": pr_samples, "workflow_run": wr_samples}


def analyze_filter_patterns(
    test_results: Dict[str, Dict[str, Any]],
) -> List[FilterTestCase]:
    """Analyze filtering patterns to create webhook test cases."""
    test_cases = []

    # Pull Request filter patterns
    pr_filters = [
        ("author", "pr_author_me", "pr_author_specific", "pr_author_nonexistent"),
        ("assignee", "pr_assignee_me", "pr_assignee_real", "pr_assignee_nonexistent"),
        (
            "reviewer",
            "pr_reviewer_me",
            "pr_reviewer_specific",
            "pr_reviewer_nonexistent",
        ),
        ("base_branch", "pr_base_branch_main", None, "pr_base_branch_nonexistent"),
        ("head_branch", "pr_head_branch_real", None, "pr_head_branch_nonexistent"),
        ("draft", "pr_draft_true", "pr_draft_false", None),
        (
            "labels",
            "pr_labels_real_single",
            "pr_labels_real_multiple",
            "pr_labels_nonexistent",
        ),
    ]

    for filter_field, positive_test, alternative_test, negative_test in pr_filters:
        if positive_test in test_results:
            positive_data = test_results[positive_test]
            positive_entities = [e["id"] for e in positive_data["entities"]]

            # Get entities that should NOT match from baseline
            baseline_entities = [
                e["id"] for e in test_results["pr_baseline"]["entities"]
            ]
            negative_entities = [
                eid for eid in baseline_entities if eid not in positive_entities
            ]

            test_cases.append(
                FilterTestCase(
                    filter_name=f"pr_{filter_field}_positive",
                    filter_field=filter_field,
                    filter_value=positive_data["filter_value"],
                    should_match_entities=positive_entities,
                    should_not_match_entities=negative_entities[
                        :5
                    ],  # Limit for test efficiency
                    total_expected_matches=positive_data["total_count"],
                )
            )

    # Workflow Run filter patterns
    wr_filters = [
        ("actor", "wr_actor_me", "wr_actor_specific", "wr_actor_nonexistent"),
        (
            "triggering_actor",
            "wr_triggering_actor_me",
            "wr_triggering_actor_specific",
            "wr_triggering_actor_nonexistent",
        ),
        (
            "workflows",
            "wr_workflows_real_single",
            "wr_workflows_real_multiple",
            "wr_workflows_nonexistent",
        ),
        (
            "events",
            "wr_events_real_single",
            "wr_events_real_multiple",
            "wr_events_nonexistent",
        ),
        (
            "statuses",
            "wr_status_real_single",
            "wr_status_real_multiple",
            "wr_status_nonexistent",
        ),
        (
            "conclusions",
            "wr_conclusion_real_single",
            "wr_conclusion_real_multiple",
            "wr_conclusion_nonexistent",
        ),
        ("branches", "wr_branches_main", None, "wr_branches_nonexistent"),
    ]

    for filter_field, positive_test, alternative_test, negative_test in wr_filters:
        if positive_test in test_results:
            positive_data = test_results[positive_test]
            positive_entities = [e["id"] for e in positive_data["entities"]]

            # Get entities that should NOT match from baseline
            baseline_entities = [
                e["id"] for e in test_results["wr_baseline"]["entities"]
            ]
            negative_entities = [
                eid for eid in baseline_entities if eid not in positive_entities
            ]

            test_cases.append(
                FilterTestCase(
                    filter_name=f"wr_{filter_field}_positive",
                    filter_field=filter_field,
                    filter_value=positive_data["filter_value"],
                    should_match_entities=positive_entities,
                    should_not_match_entities=negative_entities[
                        :5
                    ],  # Limit for test efficiency
                    total_expected_matches=positive_data["total_count"],
                )
            )

    return test_cases


def generate_webhook_test_data(test_dir: str) -> Dict[str, Any]:
    """Generate comprehensive webhook test data."""
    print(f"🔍 Analyzing test data from {test_dir}")

    # Load test results
    test_results = load_test_results(test_dir)
    print(f"📊 Loaded {len(test_results)} test results")

    # Extract entity samples
    entity_samples = extract_entity_samples(test_results)
    print(
        f"📋 Extracted {len(entity_samples['pull_request'])} PR samples, {len(entity_samples['workflow_run'])} WR samples"
    )

    # Analyze filter patterns
    filter_test_cases = analyze_filter_patterns(test_results)
    print(f"🧪 Generated {len(filter_test_cases)} filter test cases")

    # Create comprehensive webhook test data
    webhook_test_data = {
        "generation_timestamp": datetime.now().isoformat(),
        "source_directory": test_dir,
        "entity_samples": {
            "pull_request": [
                {
                    "entity_id": sample.entity_id,
                    "entity_type": sample.entity_type,
                    "data": sample.data,
                }
                for sample in entity_samples["pull_request"]
            ],
            "workflow_run": [
                {
                    "entity_id": sample.entity_id,
                    "entity_type": sample.entity_type,
                    "data": sample.data,
                }
                for sample in entity_samples["workflow_run"]
            ],
        },
        "filter_test_cases": [
            {
                "filter_name": case.filter_name,
                "filter_field": case.filter_field,
                "filter_value": case.filter_value,
                "should_match_entities": case.should_match_entities,
                "should_not_match_entities": case.should_not_match_entities,
                "total_expected_matches": case.total_expected_matches,
            }
            for case in filter_test_cases
        ],
        "summary": {
            "total_pr_entities": len(entity_samples["pull_request"]),
            "total_wr_entities": len(entity_samples["workflow_run"]),
            "total_filter_test_cases": len(filter_test_cases),
            "pr_filter_cases": len(
                [c for c in filter_test_cases if c.filter_name.startswith("pr_")]
            ),
            "wr_filter_cases": len(
                [c for c in filter_test_cases if c.filter_name.startswith("wr_")]
            ),
        },
    }

    return webhook_test_data


def main():
    """Main function."""
    test_dir = "./webhook_validation_test_data"

    if not os.path.exists(test_dir):
        print(f"❌ Test directory not found: {test_dir}")
        return 1

    # Generate webhook test data
    webhook_test_data = generate_webhook_test_data(test_dir)

    # Save webhook test data
    output_file = os.path.join(test_dir, "webhook_test_patterns.json")
    with open(output_file, "w") as f:
        json.dump(webhook_test_data, f, indent=2)

    print(f"\n✅ Webhook test patterns saved to: {output_file}")
    print("📊 Summary:")
    print(f"   - {webhook_test_data['summary']['total_pr_entities']} PR entity samples")
    print(f"   - {webhook_test_data['summary']['total_wr_entities']} WR entity samples")
    print(
        f"   - {webhook_test_data['summary']['total_filter_test_cases']} filter test cases"
    )
    print(f"   - {webhook_test_data['summary']['pr_filter_cases']} PR filter cases")
    print(f"   - {webhook_test_data['summary']['wr_filter_cases']} WR filter cases")

    return 0


if __name__ == "__main__":
    exit(main())
