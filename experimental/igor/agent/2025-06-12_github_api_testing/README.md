# GitHub API Testing and Webhook Validation

This directory contains tools and documentation for comprehensive testing of GitHub API filtering and webhook validation for remote agent triggers.

## Contents

### Scripts
- `analyze_webhook_test_patterns.py` - Extracts entity samples and filtering patterns from API test data for webhook validation

### Documentation
- `WEBHOOK_VALIDATION_GUIDE.md` - Comprehensive guide for webhook validation testing process

### Related Files
- Main testing script: `experimental/igor/agent/agent-tools/test_matching_entities.py`
- Analysis script: `experimental/igor/agent/agent-tools/analyze_test_results.py`
- Unit tests: `services/remote_agent_actions/server/github_entity_matcher_webhook_test.go`

## Purpose

This testing framework validates that webhook filtering behavior matches the GetMatchingEntities API behavior, ensuring consistency in GitHub remote agent trigger processing.

## Usage

See `WEBHOOK_VALIDATION_GUIDE.md` for detailed usage instructions and the complete validation process.

## Date

Created: 2025-06-12
