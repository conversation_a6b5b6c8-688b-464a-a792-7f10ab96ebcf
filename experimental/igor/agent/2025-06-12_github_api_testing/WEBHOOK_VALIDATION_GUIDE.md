# Webhook Validation Testing Guide

This guide documents the comprehensive webhook validation testing process for GitHub remote agent triggers. The validation ensures that webhook filtering behavior matches the GetMatchingEntities API behavior.

## Overview

The webhook validation process consists of several components:

1. **API Testing Suite** - Comprehensive tests of GetMatchingEntities API filtering
2. **Test Data Generation** - Real entity data extraction for validation
3. **Unit Tests** - Go unit tests for webhook filtering functions
4. **Analysis Tools** - Scripts to analyze filtering effectiveness and consistency

## Components

### 1. API Testing Suite

**Location**: `experimental/igor/agent/agent-tools/test_matching_entities.py`

**Purpose**: Tests all filtering parameters of the GetMatchingEntities API with systematic test cases.

**Test Categories**:
- **Pull Request Filters**: author, assignee, reviewer, labels, draft, base_branch, head_branch
- **Workflow Run Filters**: actor, triggering_actor, workflows, events, statuses, conclusions, branches
- **Edge Cases**: @me resolution, nonexistent values, empty results

**Usage**:
```bash
# Run all tests
python3.11 experimental/igor/agent/agent-tools/test_matching_entities.py \
  --repository augmentcode/augment \
  --test-type all \
  --output-dir ./webhook_validation_test_data

# Run specific test type
python3.11 experimental/igor/agent/agent-tools/test_matching_entities.py \
  --repository augmentcode/augment \
  --test-type pull_request \
  --output-dir ./test_output
```

**Test Structure**:
Each test case follows this pattern:
- **Baseline**: Unfiltered query to establish total entity count
- **Positive Tests**: Filters with real values that should return results
- **Negative Tests**: Filters with nonexistent values that should return empty results
- **Edge Cases**: Special values like @me, complex combinations

### 2. Test Data Analysis

**Location**: `experimental/igor/agent/agent-tools/analyze_test_results.py`

**Purpose**: Analyzes test results to identify filtering effectiveness and generate reference data.

**Features**:
- Filter effectiveness analysis (reduction from baseline)
- Error detection and reporting
- Empty results analysis
- @me resolution validation
- Reference data generation for webhook testing

**Usage**:
```bash
python3.11 experimental/igor/agent/agent-tools/analyze_test_results.py \
  ./webhook_validation_test_data \
  --generate-reference ./webhook_validation_test_data/webhook_reference_data.json
```

### 3. Webhook Pattern Analysis

**Location**: `analyze_webhook_test_patterns.py`

**Purpose**: Extracts entity samples and filtering patterns from API test data for webhook validation.

**Output**:
- Entity samples for testing webhook filtering
- Filter test cases with expected match/no-match entities
- Summary statistics

**Usage**:
```bash
python3.11 analyze_webhook_test_patterns.py
```

### 4. Unit Tests

**Location**: `services/remote_agent_actions/server/github_entity_matcher_webhook_test.go`

**Purpose**: Go unit tests for webhook filtering functions using real entity data.

**Test Functions**:
- `TestMatchesPullRequestConditions` - Tests PR-specific filtering
- `TestMatchesWorkflowRunConditions` - Tests workflow run filtering
- `TestMatchesConditions` - Tests main filtering function
- `TestMatchesConditionsWithUserLogin` - Tests @me resolution
- `TestWebhookFilteringEdgeCases` - Tests edge cases
- `TestWebhookFilteringConsistency` - Validates consistency with API
- `TestSpecificWebhookScenarios` - Tests production scenarios

**Usage**:
```bash
bazel test //services/remote_agent_actions/server:github_entity_matcher_webhook_test --test_output=all
```

## Validation Process

### Step 1: Generate Fresh Test Data

```bash
# Clean up previous test data
rm -rf webhook_validation_test_data comprehensive_test_results_* test_*

# Run comprehensive API tests
python3.11 experimental/igor/agent/agent-tools/test_matching_entities.py \
  --repository augmentcode/augment \
  --test-type all \
  --output-dir ./webhook_validation_test_data
```

### Step 2: Analyze Test Results

```bash
# Generate analysis and reference data
python3.11 experimental/igor/agent/agent-tools/analyze_test_results.py \
  ./webhook_validation_test_data \
  --generate-reference ./webhook_validation_test_data/webhook_reference_data.json

# Extract webhook test patterns
python3.11 analyze_webhook_test_patterns.py
```

### Step 3: Run Unit Tests

```bash
# Run webhook filtering unit tests
bazel test //services/remote_agent_actions/server:github_entity_matcher_webhook_test --test_output=all
```

### Step 4: Validate Consistency

The validation process checks:

1. **API Filtering Accuracy**: Each filter parameter works as expected
2. **Webhook Filtering Consistency**: Webhook filtering matches API behavior
3. **Edge Case Handling**: @me resolution, empty results, nil conditions
4. **Production Scenarios**: Real-world filtering patterns

## Key Findings

### Filter Effectiveness

**Pull Request Filters** (from baseline of 30 entities):
- `assignee = nonexistent-user`: 100% reduction (0 entities)
- `author = @me`: 90% reduction (3 entities)
- `reviewer = @me`: 86.7% reduction (4 entities)
- `author = specific-user`: 70% reduction (9 entities)
- `draft = True`: 70% reduction (9 entities)
- `labels = ['clients', 'intellij']`: 56.7% reduction (13 entities)

**Workflow Run Filters** (from baseline of 30 entities):
- `workflows = ['experimental-approval']`: 100% reduction (0 entities)
- `triggering_actor = @me`: 100% reduction (0 entities)
- `statuses = ['in_progress']`: 63.3% reduction (11 entities)

### @me Resolution

The @me resolution works correctly in API queries:
- `pr_author_me`: 3 entities (resolved to igor0)
- `pr_reviewer_me`: 4 entities (resolved to igor0)
- `wr_actor_me`: 30 entities (resolved to igor0)
- `pr_assignee_me`: 0 entities (no PRs assigned to igor0)
- `wr_triggering_actor_me`: 0 entities (no workflow runs triggered by igor0)

## Files Generated

### Test Data Files
- `webhook_validation_test_data/` - Directory containing all test results
- `webhook_validation_test_data/webhook_reference_data.json` - Reference data for validation
- `webhook_validation_test_data/webhook_test_patterns.json` - Patterns for webhook testing
- `webhook_validation_test_data/test_summary.json` - Test execution summary

### Individual Test Results
- `pr_*_YYYY-MM-DDTHH-MM-SS.json` - Pull request filter test results
- `wr_*_YYYY-MM-DDTHH-MM-SS.json` - Workflow run filter test results

## Troubleshooting

### Common Issues

1. **Empty Repository Lists**: Dev deployments may return empty repository lists
   - **Solution**: Use production API or configure dev deployment properly

2. **@me Resolution Failures**: @me not resolving to correct username
   - **Solution**: Verify authentication and user context

3. **Filter Inconsistencies**: Webhook filtering differs from API
   - **Solution**: Check entity structure mapping and field names

4. **Test Data Staleness**: Using outdated test data
   - **Solution**: Regenerate test data regularly

### Debugging Commands

```bash
# Check API connectivity
python3.11 experimental/igor/agent/agent-tools/trigger_cli.py matching-entities \
  --repository augmentcode/augment \
  --entity-type pull_request \
  --limit 5

# Debug specific filter
python3.11 experimental/igor/agent/agent-tools/trigger_cli.py matching-entities \
  --repository augmentcode/augment \
  --entity-type pull_request \
  --author @me \
  --json

# Run single unit test
bazel test //services/remote_agent_actions/server:github_entity_matcher_webhook_test \
  --test_filter=TestMatchesPullRequestConditions \
  --test_output=all
```

## Maintenance

### Regular Tasks

1. **Weekly**: Regenerate test data to capture latest entities
2. **Before Releases**: Run full validation suite
3. **After API Changes**: Update test cases and validation logic
4. **Monthly**: Review filter effectiveness and update documentation

### Updating Tests

When adding new filter parameters:

1. Add test cases to `test_matching_entities.py`
2. Update condition creation functions in unit tests
3. Add validation logic to analysis scripts
4. Update documentation

## Conclusion

This comprehensive validation process ensures that webhook filtering behavior remains consistent with the GetMatchingEntities API, providing confidence in the reliability of GitHub remote agent triggers.
