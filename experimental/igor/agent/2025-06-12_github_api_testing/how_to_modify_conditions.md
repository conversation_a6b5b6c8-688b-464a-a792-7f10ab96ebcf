# How to Modify Trigger Condition Types

This document outlines all the places that need to be modified when changing trigger condition field types (e.g., changing from `conclusions` (plural array) to `conclusion` (singular optional)).

## Backend Changes

### 1. Proto Definitions
- **`services/remote_agent_actions/remote_agent_actions.proto`** - Core proto definition (source of truth)
- **`services/api_proxy/public_api.proto`** - Public API proto (should match remote_agent_actions.proto)

### 2. API Proxy Handlers (Rust)
- **`services/api_proxy/server/src/handlers_remote_agent_actions.rs`**
  - Update `TryFrom<public_api_proto::GitHubWorkflowRunTriggerConditions>` conversion
  - Update `From<remote_agent_actions::GitHubWorkflowRunTriggerConditions>` conversion

### 3. CLI Tools
- **`tools/remote_agent_actions_cli/main.go`**
  - Update flag definitions (e.g., `--events` → `--event`)
  - Update struct field assignments
  - Update function signatures
- **`tools/remote_agent_actions_cli/test_workflow_run.go`**
  - Update test data structures
  - Update field assignments

## Frontend Changes

### 4. TypeScript Types
- **`clients/sidecar/libs/src/triggers/trigger-types.ts`**
  - Update `GitHubWorkflowRunTriggerConditions` interface
  - Update `WorkflowRunConditions` interface
  - Update any related frontend condition types

### 5. Template System
- **`clients/common/webviews/src/apps/remote-agent-manager/components/triggers/shared/template-generator.ts`**
  - Ensure both `pullRequest` and `workflowRun` conditions are mapped
- **`clients/common/webviews/src/apps/remote-agent-manager/components/triggers/shared/personal-templates.ts`**
  - Update template parameter definitions (array → choice)
  - Update trigger spec field references
  - Update prompt templates to use singular parameter names
- **`clients/common/webviews/src/apps/remote-agent-manager/components/triggers/shared/custom-trigger-generator.ts`**
  - Update field assignments in condition generation
- **`clients/common/webviews/src/apps/remote-agent-manager/components/triggers/shared/trigger-utils.ts`**
  - Update default condition values

### 6. UI Parameters
- **`clients/common/webviews/src/apps/remote-agent-manager/components/triggers/github/parameters/workflow-run-parameters.ts`**
  - Update parameter definitions to use singular field names

### 7. Tests
- **`clients/common/webviews/src/apps/remote-agent-manager/components/triggers/shared/template-generator.test.ts`**
  - Update test data to use singular values
  - Update test expectations
  - Update parameter names in test cases

## Key Principles

### Field Naming Convention
- **Backend (Proto)**: Use snake_case (`event`, `status`, `conclusion`, `branch`)
- **Frontend (TypeScript)**: Use camelCase (`event`, `status`, `conclusion`, `branch`)
- **Conversion**: Automatic via `camelToSnake()` in `clients/vscode/src/augment-api.ts`

### Parameter Design
- **Prefer single-valued parameters** over arrays with transforms
- **Use `choice` type** instead of `array` or `multi-choice` for single values
- **Update prompt templates** to use singular parameter names

### Testing
- **Run frontend tests**: `bazel test //clients/common/webviews:vitest_test`
- **Build API proxy**: `bazel build //services/api_proxy/server:api_proxy_server`
- **Test CLI tools**: Build with `go build` in `tools/remote_agent_actions_cli/`

## Example Change: `conclusions` → `conclusion`

1. **Proto**: `repeated string conclusions` → `optional string conclusion`
2. **Rust**: `from.conclusions.first().cloned()` → `from.conclusion`
3. **TypeScript**: `conclusions?: string[]` → `conclusion?: string`
4. **Templates**: `conclusions: ["failure"]` → `conclusion: "failure"`
5. **CLI**: `--conclusions failure` → `--conclusion failure`
6. **Tests**: `conclusions: ["failure"]` → `conclusion: "failure"`

This ensures consistency across all layers while maintaining the GitHub API's single-value constraints.
