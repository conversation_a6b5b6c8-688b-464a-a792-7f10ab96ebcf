#!/usr/bin/env python3.11
"""
Command-line tool for testing remote agent triggers.

This script provides a command-line interface for creating, listing, updating,
and deleting remote agent triggers. It's designed for testing and validation
of the trigger system implementation.
"""

import argparse
import datetime
import json
import logging
import os
import sys
import time
import traceback
from typing import List, Optional, Dict, Any

# Add the repository root to the Python path
sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
)

from base.augment_client.client import AugmentClient

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


def get_username_from_config() -> str:
    """Get the username from the user config file."""
    user_config_path = os.path.expanduser("~/.augment/user.json")

    if not os.path.exists(user_config_path):
        raise ValueError(
            "User config file not found. Create ~/.augment/user.json with your username."
        )

    try:
        with open(user_config_path, "r") as f:
            user_config = json.load(f)

        if "name" not in user_config:
            raise ValueError("Username not found in config file.")

        return user_config["name"]
    except json.JSONDecodeError:
        raise ValueError("Invalid JSON in user config file.")
    except Exception as e:
        raise ValueError(f"Error reading username from config: {e}")


def get_api_url(api_url: Optional[str] = None) -> str:
    """Get the API URL from the provided URL or construct it from the username."""
    if api_url:
        return api_url

    try:
        username = get_username_from_config()
        namespace = f"dev-{username}"
        return f"https://{namespace}.us-central.api.augmentcode.com"
    except ValueError as e:
        logger.warning(f"Could not construct API URL from username: {e}")
        logger.warning("Using default API URL: https://api.augment.dev")
        return "https://api.augment.dev"


def get_api_token(token_file: Optional[str] = None) -> str:
    """Get the API token from the token file, environment, or default locations."""
    # Check if token file is provided
    if token_file and os.path.exists(token_file):
        with open(token_file, "r") as f:
            return f.read().strip()

    # Check environment variable
    token = os.environ.get("AUGMENT_API_TOKEN")
    if token:
        return token

    # Check dev-deploy token file (new default)
    dev_deploy_token_path = os.path.expanduser("~/.augment/dev-deploy-oauth2-token.txt")
    if os.path.exists(dev_deploy_token_path):
        with open(dev_deploy_token_path, "r") as f:
            return f.read().strip()

    # Check default token file
    token_path = os.path.expanduser("~/.augment/token")
    if os.path.exists(token_path):
        with open(token_path, "r") as f:
            return f.read().strip()

    raise ValueError(
        "API token not found. Set AUGMENT_API_TOKEN environment variable, provide --token-file argument, "
        "or create ~/.augment/dev-deploy-oauth2-token.txt file using dev_deploy_signin.js."
    )


def create_trigger(client: AugmentClient, trigger_config: Dict[str, Any]) -> None:
    """Create a new trigger."""
    logger.info("Creating trigger...")
    logger.info(f"Trigger config: {json.dumps(trigger_config, indent=2)}")

    try:
        # Wrap the trigger config in the expected CreateTriggerRequest structure
        request_data = {"configuration": trigger_config}

        # Use the client's _post method to make a direct API call
        response, _ = client._post(
            "remote-agent-actions/triggers/create",
            json=request_data,
        )

        if not response.ok:
            logger.error(
                f"HTTP error creating trigger: {response.status_code} {response.text}"
            )
            return

        result = response.json()
        logger.info("Trigger created successfully!")
        logger.info(
            f"Trigger ID: {result.get('trigger', {}).get('trigger_id', 'Unknown')}"
        )
        logger.info(f"Trigger Name: {result.get('trigger', {}).get('name', 'Unknown')}")

    except Exception as e:
        logger.error(f"Error creating trigger: {e}")
        logger.error(traceback.format_exc())


def _display_trigger_info(trigger: Dict[str, Any], verbose: bool = False) -> None:
    """Helper function to display information for a single trigger."""
    # Try multiple possible field names for the trigger name
    # Check both top-level and nested configuration fields
    trigger_name = (
        trigger.get("name")
        or trigger.get("trigger_name")
        or trigger.get("configuration", {}).get("name")
        or "Unnamed Trigger"
    )

    # Also try to get description from configuration if not at top level
    description = (
        trigger.get("description")
        or trigger.get("configuration", {}).get("description")
        or "None"
    )

    # Also try to get event_source from configuration if not at top level
    event_source = (
        trigger.get("event_source")
        or trigger.get("configuration", {}).get("event_source")
        or "Unknown"
    )

    # Convert numeric event source to readable name if it's a number
    if isinstance(event_source, int):
        event_source_names = {
            0: "UNSPECIFIED",
            1: "GITHUB",
            2: "LINEAR",
            3: "SCHEDULE",
            4: "SCHEDULE_TEMP",  # Temporary value for near-term backwards compatibility
        }
        event_source = event_source_names.get(event_source, f"UNKNOWN({event_source})")

    # Debug: Show available keys if name is missing and verbose mode is on
    if verbose and trigger_name == "Unnamed Trigger":
        logger.debug(f"  Debug - Available trigger keys: {list(trigger.keys())}")
        if "configuration" in trigger:
            logger.debug(
                f"  Debug - Configuration keys: {list(trigger['configuration'].keys())}"
            )

    logger.info(f"  ID: {trigger.get('trigger_id', 'Unknown')}")
    logger.info(f"  Name: {trigger_name}")
    logger.info(f"  Description: {description}")
    # Also try to get enabled status from configuration if not at top level
    enabled = (
        trigger.get("enabled")
        if trigger.get("enabled") is not None
        else trigger.get("configuration", {}).get("enabled")
        if trigger.get("configuration", {}).get("enabled") is not None
        else False
    )

    logger.info(f"  Event Source: {event_source}")
    logger.info(f"  Enabled: {enabled}")
    logger.info(f"  Created: {trigger.get('created_at', 'Unknown')}")

    if verbose:
        # Show detailed information in verbose mode
        logger.info(f"  Updated: {trigger.get('updated_at', 'Unknown')}")

        # Show conditions details
        conditions = trigger.get("conditions", {}) or trigger.get(
            "configuration", {}
        ).get("conditions", {})
        if conditions:
            logger.info(f"  Conditions Type: {conditions.get('type', 'Unknown')}")

            # Show GitHub conditions if present
            github_conditions = conditions.get("github", {})
            if github_conditions:
                logger.info("  GitHub Conditions:")
                logger.info(
                    f"    Entity Type: {github_conditions.get('entity_type', 'Unknown')}"
                )

                # Show pull request conditions
                pr_conditions = github_conditions.get("pull_request", {})
                if pr_conditions:
                    logger.info("    Pull Request Conditions:")
                    if "repository" in pr_conditions:
                        logger.info(f"      Repository: {pr_conditions['repository']}")
                    if "author" in pr_conditions:
                        logger.info(f"      Author: {pr_conditions['author']}")
                    if "assignee" in pr_conditions:
                        logger.info(f"      Assignee: {pr_conditions['assignee']}")
                    if "reviewer" in pr_conditions:
                        logger.info(f"      Reviewer: {pr_conditions['reviewer']}")
                    if "labels" in pr_conditions:
                        logger.info(f"      Labels: {pr_conditions['labels']}")
                    if "activity_types" in pr_conditions:
                        logger.info(
                            f"      Activity Types: {pr_conditions['activity_types']}"
                        )
                    if "draft" in pr_conditions:
                        logger.info(f"      Draft: {pr_conditions['draft']}")
                    if "title_contains" in pr_conditions:
                        logger.info(
                            f"      Title Contains: {pr_conditions['title_contains']}"
                        )
                    if "base_branch" in pr_conditions:
                        logger.info(
                            f"      Base Branch: {pr_conditions['base_branch']}"
                        )
                    if "head_branch" in pr_conditions:
                        logger.info(
                            f"      Head Branch: {pr_conditions['head_branch']}"
                        )

                # Show issue conditions
                issue_conditions = github_conditions.get("issue", {})
                if issue_conditions:
                    logger.info("    Issue Conditions:")
                    if "repository" in issue_conditions:
                        logger.info(
                            f"      Repository: {issue_conditions['repository']}"
                        )
                    if "labels" in issue_conditions:
                        logger.info(f"      Labels: {issue_conditions['labels']}")

                # Show commit conditions
                commit_conditions = github_conditions.get("commit", {})
                if commit_conditions:
                    logger.info("    Commit Conditions:")
                    if "repository" in commit_conditions:
                        logger.info(
                            f"      Repository: {commit_conditions['repository']}"
                        )
                    if "branch" in commit_conditions:
                        logger.info(f"      Branch: {commit_conditions['branch']}")

            # Show Linear conditions if present
            linear_conditions = conditions.get("linear", {})
            if linear_conditions:
                logger.info("  Linear Conditions:")
                logger.info(
                    f"    Entity Type: {linear_conditions.get('entity_type', 'Unknown')}"
                )

                # Show issue conditions
                issue_conditions = linear_conditions.get("issue", {})
                if issue_conditions:
                    logger.info("    Issue Conditions:")
                    if "team" in issue_conditions:
                        logger.info(f"      Team: {issue_conditions['team']}")
                    if "creator" in issue_conditions:
                        logger.info(f"      Creator: {issue_conditions['creator']}")
                    if "assignee" in issue_conditions:
                        logger.info(f"      Assignee: {issue_conditions['assignee']}")
                    if "states" in issue_conditions:
                        logger.info(f"      States: {issue_conditions['states']}")
                    if "state_types" in issue_conditions:
                        logger.info(
                            f"      State Types: {issue_conditions['state_types']}"
                        )
                    if "priorities" in issue_conditions:
                        logger.info(
                            f"      Priorities: {issue_conditions['priorities']}"
                        )
                    if "labels" in issue_conditions:
                        logger.info(f"      Labels: {issue_conditions['labels']}")
                    if "project" in issue_conditions:
                        logger.info(f"      Project: {issue_conditions['project']}")
                    if "title_contains" in issue_conditions:
                        logger.info(
                            f"      Title Contains: {issue_conditions['title_contains']}"
                        )
                    if "min_estimate" in issue_conditions:
                        logger.info(
                            f"      Min Estimate: {issue_conditions['min_estimate']}"
                        )
                    if "max_estimate" in issue_conditions:
                        logger.info(
                            f"      Max Estimate: {issue_conditions['max_estimate']}"
                        )

        # Show workspace setup details
        workspace_setup = trigger.get("workspace_setup", {}) or trigger.get(
            "configuration", {}
        ).get("agent_config", {}).get("workspace_setup", {})
        if workspace_setup:
            logger.info("  Workspace Setup:")
            starting_files = workspace_setup.get("starting_files", {})
            if starting_files:
                github_ref = starting_files.get("github_ref", {})
                if github_ref:
                    logger.info("    Starting Files (GitHub):")
                    logger.info(
                        f"      Repository URL: {github_ref.get('url', 'Unknown')}"
                    )
                    logger.info(f"      Git Ref: {github_ref.get('ref', 'Unknown')}")

        # Show initial request details (legacy field, now part of agent_config)
        request_details = trigger.get("initial_request_details", {}) or trigger.get(
            "configuration", {}
        ).get("agent_config", {})
        if request_details:
            logger.info("  Initial Request Details:")
            logger.info(f"    Model ID: {request_details.get('model_id', 'Unknown')}")
            if "user_guidelines" in request_details:
                guidelines = request_details["user_guidelines"]
                # Truncate long guidelines for readability
                if len(guidelines) > 100:
                    guidelines = guidelines[:100] + "..."
                logger.info(f"    User Guidelines: {guidelines}")

            request_nodes = request_details.get("request_nodes", [])
            if request_nodes:
                logger.info(f"    Request Nodes: {len(request_nodes)} nodes")

        # Show template information if present
        if trigger.get("template_id"):
            logger.info(f"  Template ID: {trigger.get('template_id', 'Unknown')}")
            logger.info(f"  Template Name: {trigger.get('template_name', 'Unknown')}")


def list_triggers(
    client: AugmentClient, verbose: bool = False, json_output: bool = False
) -> None:
    """List all triggers."""
    if not json_output:
        logger.info("Listing triggers...")

    try:
        # Use _post with empty JSON for list operation (following remote agent CLI pattern)
        response, _ = client._post("remote-agent-actions/triggers/list", json={})

        if not response.ok:
            logger.error(
                f"HTTP error listing triggers: {response.status_code} {response.text}"
            )
            return

        result = response.json()

        if json_output:
            # Output raw JSON for debugging
            print(json.dumps(result, indent=2))
            return

        triggers = result.get("triggers", [])

        if not triggers:
            logger.info("No triggers found.")
            return

        logger.info(f"Found {len(triggers)} triggers:")
        for trigger in triggers:
            _display_trigger_info(trigger, verbose)
            logger.info("  ---")

    except Exception as e:
        logger.error(f"Error listing triggers: {e}")
        logger.error(traceback.format_exc())


def get_trigger(
    client: AugmentClient,
    trigger_id: str,
    verbose: bool = False,
    json_output: bool = False,
    json_config: bool = False,
) -> None:
    """Get a single trigger by ID."""
    if not json_output and not json_config:
        logger.info(f"Getting trigger {trigger_id}...")

    try:
        # Use the list endpoint and filter for the specific trigger ID
        # since there might not be a dedicated get endpoint
        response, _ = client._post("remote-agent-actions/triggers/list", json={})

        if not response.ok:
            logger.error(
                f"HTTP error getting trigger: {response.status_code} {response.text}"
            )
            return

        result = response.json()

        triggers = result.get("triggers", [])
        trigger = next((t for t in triggers if t.get("trigger_id") == trigger_id), None)

        if not trigger:
            if json_output or json_config:
                print(json.dumps({"error": "Trigger not found"}, indent=2))
            else:
                logger.info("Trigger not found.")
            return

        if json_config:
            # Output just the configuration for use with create command
            configuration = trigger.get("configuration", {})
            print(json.dumps(configuration, indent=2))
            return

        if json_output:
            # Output the full trigger information
            print(json.dumps({"trigger": trigger}, indent=2))
            return

        # Display trigger information in human-readable format
        _display_trigger_info(trigger, verbose=verbose)

    except Exception as e:
        logger.error(f"Error getting trigger: {e}")
        logger.error(traceback.format_exc())


def delete_trigger(client: AugmentClient, trigger_id: str) -> None:
    """Delete a trigger."""
    logger.info(f"Deleting trigger {trigger_id}...")

    try:
        # Use _post for delete operation (following remote agent CLI pattern)
        response, _ = client._post(
            "remote-agent-actions/triggers/delete", json={"trigger_id": trigger_id}
        )

        if not response.ok:
            logger.error(
                f"HTTP error deleting trigger: {response.status_code} {response.text}"
            )
            return

        logger.info("Trigger deleted successfully!")

    except Exception as e:
        logger.error(f"Error deleting trigger: {e}")
        logger.error(traceback.format_exc())


def delete_all_triggers(client: AugmentClient) -> None:
    """Delete all triggers."""
    logger.info("Deleting all triggers...")

    try:
        # First, get the list of all triggers
        response, _ = client._post("remote-agent-actions/triggers/list", json={})

        if not response.ok:
            logger.error(
                f"HTTP error listing triggers: {response.status_code} {response.text}"
            )
            return

        result = response.json()
        triggers = result.get("triggers", [])

        if not triggers:
            logger.info("No triggers found to delete.")
            return

        logger.info(f"Found {len(triggers)} triggers to delete.")

        # Delete each trigger
        deleted_count = 0
        failed_count = 0

        for trigger in triggers:
            trigger_id = trigger.get("trigger_id")
            if not trigger_id:
                logger.warning("Skipping trigger with missing ID")
                failed_count += 1
                continue

            trigger_name = (
                trigger.get("name")
                or trigger.get("trigger_name")
                or trigger.get("configuration", {}).get("name")
                or "Unnamed Trigger"
            )

            logger.info(f"Deleting trigger: {trigger_name} (ID: {trigger_id})")

            try:
                delete_response, _ = client._post(
                    "remote-agent-actions/triggers/delete",
                    json={"trigger_id": trigger_id},
                )

                if delete_response.ok:
                    logger.info(f"Successfully deleted trigger: {trigger_name}")
                    deleted_count += 1
                else:
                    logger.error(
                        f"Failed to delete trigger {trigger_name}: {delete_response.status_code} {delete_response.text}"
                    )
                    failed_count += 1
            except Exception as e:
                logger.error(f"Error deleting trigger {trigger_name}: {e}")
                failed_count += 1

        logger.info(
            f"Deletion complete. Successfully deleted: {deleted_count}, Failed: {failed_count}"
        )

    except Exception as e:
        logger.error(f"Error deleting all triggers: {e}")
        logger.error(traceback.format_exc())


def get_trigger_executions(
    client: AugmentClient, trigger_id: str, json_output: bool = False
) -> None:
    """Get execution history for a trigger."""
    if not json_output:
        logger.info(f"Getting execution history for trigger {trigger_id}...")

    try:
        # Use _post for get executions operation
        response, _ = client._post(
            "remote-agent-actions/triggers/executions", json={"trigger_id": trigger_id}
        )

        if not response.ok:
            logger.error(
                f"HTTP error getting executions: {response.status_code} {response.text}"
            )
            return

        result = response.json()
        executions = result.get("executions", [])

        if json_output:
            print(json.dumps(result, indent=2))
            return

        if not executions:
            logger.info("No executions found.")
            return

        logger.info(f"Found {len(executions)} executions:")
        for execution in executions:
            logger.info(f"  Execution ID: {execution.get('execution_id', 'Unknown')}")
            logger.info(f"  Status: {execution.get('status', 'Unknown')}")
            logger.info(f"  Agent ID: {execution.get('remote_agent_id', 'None')}")
            logger.info(f"  Started: {execution.get('started_at', 'Unknown')}")
            logger.info(f"  Completed: {execution.get('completed_at', 'Unknown')}")
            logger.info("  ---")

    except Exception as e:
        logger.error(f"Error getting trigger executions: {e}")
        logger.error(traceback.format_exc())


def get_matching_entities(
    client: AugmentClient,
    trigger_id: Optional[str] = None,
    event_source: int = 1,
    limit: int = 25,
    offset: int = 0,
    show_dismissed: bool = False,
    json_output: bool = False,
) -> None:
    """Get entities that match trigger conditions."""
    logger.info("Getting matching entities...")

    try:
        request_data = {
            "event_source": event_source,
            "limit": limit,
            "offset": offset,
            "show_dismissed": show_dismissed,
        }

        if trigger_id:
            request_data["trigger_id"] = trigger_id
        else:
            # If no trigger_id provided, use default conditions for testing
            request_data["conditions"] = {
                "type": 1,  # TRIGGER_CONDITION_GITHUB
                "github": {
                    "entity_type": 1,  # GITHUB_ENTITY_TYPE_PULL_REQUEST
                    "pull_request": {"repository": "igor0/augment"},
                },
            }

        # Debug: log the request data
        logger.debug(f"Request data: {request_data}")

        # Use _post for get matching entities operation
        response, _ = client._post(
            "remote-agent-actions/triggers/matching-entities", json=request_data
        )

        if not response.ok:
            logger.error(
                f"HTTP error getting matching entities: {response.status_code} {response.text}"
            )
            return

        result = response.json()
        # Handle new response format with separate github_entities, linear_entities, and schedule_entities
        github_entities = result.get("github_entities", [])
        linear_entities = result.get("linear_entities", [])
        schedule_entities = result.get("schedule_entities", [])
        entities = (
            github_entities + linear_entities + schedule_entities
        )  # Combine all types
        total_count = result.get("total_count", 0)
        has_more = result.get("has_more", False)
        query_explanation = result.get("query_explanation", "")

        if json_output:
            # Output raw JSON for debugging
            import json

            print(json.dumps(result, indent=2))
            return

        logger.info(
            f"Found {len(entities)} entities (total: {total_count}, has_more: {has_more})"
        )
        if query_explanation:
            logger.info(f"Query explanation: {query_explanation}")

        for entity in entities:
            entity_type = entity.get("type", "unknown")
            entity_id = entity.get("id", "unknown")
            timestamp = entity.get("timestamp", "unknown")
            is_dismissed = entity.get("is_dismissed", False)

            logger.info(f"  Entity ID: {entity_id}")
            logger.info(f"  Type: {entity_type}")
            logger.info(f"  Timestamp: {timestamp}")
            if is_dismissed:
                logger.info("  Dismissed")

            # Show type-specific details
            if (
                entity_type == "GITHUB_ENTITY_TYPE_PULL_REQUEST"
                and "pull_request" in entity
            ):
                pr = entity["pull_request"]
                logger.info(
                    f"  PR #{pr.get('number', 'N/A')}: {pr.get('title', 'No title')}"
                )
                logger.info(f"  Author: {pr.get('user', {}).get('login', 'Unknown')}")
                logger.info(f"  State: {pr.get('state', 'Unknown')}")
            elif entity_type == "GITHUB_ENTITY_TYPE_ISSUE" and "issue" in entity:
                issue = entity["issue"]
                logger.info(
                    f"  Issue #{issue.get('number', 'N/A')}: {issue.get('title', 'No title')}"
                )
                logger.info(
                    f"  Author: {issue.get('user', {}).get('login', 'Unknown')}"
                )
                logger.info(f"  State: {issue.get('state', 'Unknown')}")
            elif entity_type == "GITHUB_ENTITY_TYPE_COMMIT" and "commit" in entity:
                commit = entity["commit"]
                logger.info(f"  Commit: {commit.get('sha', 'Unknown')[:8]}")
                logger.info(f"  Message: {commit.get('message', 'No message')[:50]}...")
                logger.info(
                    f"  Author: {commit.get('author', {}).get('login', 'Unknown')}"
                )

            logger.info("  ---")

    except Exception as e:
        logger.error(f"Error getting matching entities: {e}")
        logger.error(traceback.format_exc())


def execute_trigger_manually(
    client: AugmentClient,
    trigger_id: str,
    entity_id: str,
    extra_prompt: str = None,
) -> None:
    """Execute a trigger manually on a specific entity."""
    logger.info(f"Executing trigger {trigger_id} manually on entity {entity_id}...")

    try:
        request_data = {
            "trigger_id": trigger_id,
            "entity_id": entity_id,
        }
        if extra_prompt:
            request_data["extra_prompt"] = extra_prompt

        # Use _post for manual execution operation
        response, _ = client._post(
            "remote-agent-actions/triggers/execute-manually", json=request_data
        )

        if not response.ok:
            logger.error(
                f"HTTP error executing trigger manually: {response.status_code} {response.text}"
            )
            return

        result = response.json()
        execution_id = result.get("execution_id", "Unknown")
        remote_agent_id = result.get("remote_agent_id", "None")
        status = result.get("status", "Unknown")
        message = result.get("message", "")

        logger.info("Trigger executed successfully!")
        logger.info(f"  Execution ID: {execution_id}")
        logger.info(f"  Remote Agent ID: {remote_agent_id}")
        logger.info(f"  Status: {status}")
        if message:
            logger.info(f"  Message: {message}")

    except Exception as e:
        logger.error(f"Error executing trigger manually: {e}")
        logger.error(traceback.format_exc())


def execute_manual_agent(
    client: AugmentClient,
    entity_id: str,
    instructions: str,
    github_entity_type: int = None,
    linear_entity_type: int = None,
) -> None:
    """Execute a manual agent on a specific entity (no trigger required)."""
    logger.info(f"Executing manual agent on entity {entity_id}...")

    try:
        # Create a default agent configuration with required workspace setup
        default_agent_config = {
            "workspace_setup": {
                "starting_files": {
                    "github_ref": {
                        "url": "https://github.com/augmentcode/augment",
                        "ref": "main",
                    }
                }
            },
            "starting_nodes": [
                {
                    "id": 0,
                    "type": 1,  # TEXT type
                    "text_node": {"content": instructions},
                }
            ],
        }

        request_data = {
            "entity_id": entity_id,
            "agent_config": default_agent_config,
        }

        if github_entity_type is not None:
            request_data["github_entity_type"] = github_entity_type
        elif linear_entity_type is not None:
            request_data["linear_entity_type"] = linear_entity_type
        else:
            logger.error(
                "Either github_entity_type or linear_entity_type must be specified"
            )
            return

        # Use _post for manual agent execution
        response, _ = client._post(
            "remote-agent-actions/execute-manual-agent", json=request_data
        )

        if not response.ok:
            logger.error(
                f"HTTP error executing manual agent: {response.status_code} {response.text}"
            )
            return

        result = response.json()
        logger.info("Manual agent execution started successfully:")
        logger.info(f"  Execution ID: {result.get('execution_id', 'N/A')}")
        logger.info(f"  Remote Agent ID: {result.get('remote_agent_id', 'N/A')}")
        logger.info(f"  Message: {result.get('message', 'N/A')}")

    except Exception as e:
        logger.error(f"Error executing manual agent: {e}")
        logger.error(traceback.format_exc())


def get_entity_details(
    client: AugmentClient,
    entity_id: str,
    event_source: str,
    github_entity_type: int = None,
    linear_entity_type: int = None,
    repository: str = None,
    include_entity_details: bool = False,
    json_output: bool = False,
) -> None:
    """Get details of a specific entity by ID."""
    logger.info(f"Getting details for entity {entity_id}...")

    try:
        request_data = {
            "entity_id": entity_id,
            "event_source": event_source,
        }

        if github_entity_type is not None:
            request_data["github_entity_type"] = github_entity_type
        if linear_entity_type is not None:
            request_data["linear_entity_type"] = linear_entity_type
        if repository:
            request_data["repository"] = repository
        if include_entity_details:
            request_data["include_related_entities"] = include_entity_details

        # Use _post for entity details
        response, _ = client._post(
            "remote-agent-actions/get-entity-details", json=request_data
        )

        if not response.ok:
            logger.error(
                f"HTTP error getting entity details: {response.status_code} {response.text}"
            )
            return

        result = response.json()
        if json_output:
            print(json.dumps(result, indent=2))
        else:
            logger.info("Entity details retrieved:")
            logger.info(f"  Found: {result.get('found', False)}")
            if result.get("error_message"):
                logger.error(f"  Error: {result.get('error_message')}")
            elif result.get("github_entity"):
                entity = result["github_entity"]
                logger.info("  GitHub Entity:")
                logger.info(f"    Type: {entity.get('type', 'N/A')}")
                if entity.get("pull_request"):
                    pr = entity["pull_request"]
                    logger.info(
                        f"    PR #{pr.get('number', 'N/A')}: {pr.get('title', 'N/A')}"
                    )
                    logger.info(f"    State: {pr.get('state', 'N/A')}")
                    logger.info(f"    URL: {pr.get('html_url', 'N/A')}")
                    if include_entity_details and pr.get("related_entities"):
                        related = pr["related_entities"]
                        logger.info("    Related Entities:")
                        if related.get("review_comments"):
                            logger.info(f"      Review Comments: {len(related['review_comments'])}")
                        if related.get("failed_check_suites"):
                            logger.info(f"      Failed Check Suites: {len(related['failed_check_suites'])}")
                        if related.get("failed_statuses"):
                            logger.info(f"      Failed Statuses: {len(related['failed_statuses'])}")
                elif entity.get("workflow_run"):
                    wr = entity["workflow_run"]
                    logger.info(f"    Workflow: {wr.get('name', 'N/A')}")
                    logger.info(f"    Status: {wr.get('status', 'N/A')}")
                    logger.info(f"    Conclusion: {wr.get('conclusion', 'N/A')}")
                    logger.info(f"    URL: {wr.get('html_url', 'N/A')}")
            elif result.get("linear_entity"):
                entity = result["linear_entity"]
                logger.info("  Linear Entity:")
                logger.info(f"    Type: {entity.get('type', 'N/A')}")
                if entity.get("issue"):
                    issue = entity["issue"]
                    logger.info(f"    Issue: {issue.get('title', 'N/A')}")
                    logger.info(f"    State: {issue.get('state', 'N/A')}")
                    logger.info(f"    URL: {issue.get('url', 'N/A')}")

    except Exception as e:
        logger.error(f"Error getting entity details: {e}")
        logger.error(traceback.format_exc())


def dismiss_entity(
    client: AugmentClient,
    trigger_id: str,
    entity_id: str,
    dismissed: bool = True,
) -> None:
    """Dismiss or un-dismiss an entity for a trigger."""
    action = "Dismissing" if dismissed else "Un-dismissing"
    logger.info(f"{action} entity {entity_id} for trigger {trigger_id}...")

    try:
        request_data = {
            "trigger_id": trigger_id,
            "entity_id": entity_id,
            "dismissed": dismissed,
        }

        # Use _post for dismiss entity operation
        response, _ = client._post(
            "remote-agent-actions/triggers/dismiss-entity", json=request_data
        )

        if not response.ok:
            logger.error(
                f"HTTP error {action.lower()} entity: {response.status_code} {response.text}"
            )
            return

        logger.info(
            f"Successfully {action.lower()} entity {entity_id} for trigger {trigger_id}!"
        )

    except Exception as e:
        logger.error(f"Error {action.lower()} entity: {e}")
        logger.error(traceback.format_exc())


def create_sample_pr_trigger() -> Dict[str, Any]:
    """Create a sample pull request trigger configuration."""
    return {
        "name": "Test Trigger",
        "description": "Test trigger",
        "event_source": 1,  # EVENT_SOURCE_GITHUB
        "conditions": {
            "type": 1,  # TRIGGER_CONDITION_GITHUB
            "github": {
                "entity_type": 1,  # GITHUB_ENTITY_TYPE_PULL_REQUEST
                "pull_request": {"repository": "igor0/augment"},
            },
        },
        "workspace_setup": {
            "starting_files": {
                "github_ref": {
                    "url": "@{pr_head_repo_url}",
                    "ref": "@{pr_head_ref}",
                }
            }
        },
        "initial_request_details": {
            "request_nodes": [],
            "user_guidelines": "Test guidelines",
        },
        "enabled": True,
    }


def create_sample_pr_opened_trigger() -> Dict[str, Any]:
    """Create a sample pull request opened trigger configuration."""
    return {
        "name": "PR Opened Trigger",
        "description": "Automatically analyze new pull requests",
        "event_source": 1,  # EVENT_SOURCE_GITHUB
        "conditions": {
            "type": 1,  # TRIGGER_CONDITION_GITHUB
            "github": {
                "entity_type": 1,  # GITHUB_ENTITY_TYPE_PULL_REQUEST
                "pull_request": {
                    "repository": "igor0/augment",
                    "labels": ["needs-review"],
                },
            },
        },
        "agent_config": {
            "workspace_setup": {
                "starting_files": {
                    "github_ref": {
                        "url": "@{pr_head_repo_url}",
                        "ref": "@{pr_head_ref}",
                    }
                }
            },
            "starting_nodes": [],
            "user_guidelines": "Analyze this new pull request. Provide initial feedback on the changes and suggest improvements.",
        },
        "enabled": True,
    }


def create_sample_linear_issue_trigger() -> Dict[str, Any]:
    """Create a sample Linear issue trigger configuration."""
    return {
        "name": "Linear Issue Trigger",
        "description": "Automatically process Linear issues",
        "event_source": 2,  # EVENT_SOURCE_LINEAR
        "conditions": {
            "type": 2,  # TRIGGER_CONDITION_LINEAR
            "linear": {
                "entity_type": 1,  # LINEAR_ENTITY_TYPE_ISSUE
                "issue": {
                    "team": "AUG",
                    "assignee": "@me",
                    "state_types": ["started"],
                    "priorities": [1, 2],  # Urgent and High priority
                },
            },
        },
        "agent_config": {
            "workspace_setup": {
                "starting_files": {
                    "github_ref": {
                        "url": "https://github.com/igor0/augment",
                        "ref": "main",
                    }
                }
            },
            "starting_nodes": [],
            "user_guidelines": "Analyze this Linear issue and provide implementation suggestions or help resolve it.",
        },
        "enabled": True,
    }


def create_sample_pr_repo_variable_trigger() -> Dict[str, Any]:
    """Create a sample pull request trigger that uses @{pr_head_repo} variable."""
    return {
        "name": "Dynamic Repository PR Review",
        "description": "Automatically review PRs using dynamic repository references - demonstrates @{pr_head_repo} and @{pr_base_repo} variables for cross-repo PRs",
        "event_source": 1,  # EVENT_SOURCE_GITHUB
        "conditions": {
            "type": 1,  # TRIGGER_CONDITION_GITHUB
            "github": {
                "entity_type": 1,  # GITHUB_ENTITY_TYPE_PULL_REQUEST
                "pull_request": {
                    "repository": "augmentcode/augment",  # Base repository to monitor
                    "activity_types": ["opened", "synchronize"],
                    "draft": False,
                },
            },
        },
        "agent_config": {
            "workspace_setup": {
                "starting_files": {
                    "github_ref": {
                        "url": "@{pr_head_repo_url}",  # Dynamic repository from PR head
                        "ref": "@{pr_head_ref}",  # Dynamic branch from PR head
                    }
                }
            },
            "starting_nodes": [],
            "user_guidelines": "You are reviewing a pull request. The workspace contains the HEAD repository (@{pr_head_repo}) and branch (@{pr_head_ref}). Run `git remote -v` to see the repository setup, then `git log --oneline -10` to see recent commits. Analyze the changes and provide feedback.",
        },
        "enabled": True,
    }


def create_sample_linear_bug_trigger() -> Dict[str, Any]:
    """Create a sample Linear bug issue trigger configuration."""
    return {
        "name": "Linear Bug Trigger",
        "description": "Automatically handle bug reports in Linear",
        "event_source": 2,  # EVENT_SOURCE_LINEAR
        "conditions": {
            "type": 2,  # TRIGGER_CONDITION_LINEAR
            "linear": {
                "entity_type": 1,  # LINEAR_ENTITY_TYPE_ISSUE
                "issue": {
                    "labels": ["bug"],
                    "priorities": [1],  # Urgent priority only
                    "activity_types": ["created", "updated"],
                },
            },
        },
        "agent_config": {
            "workspace_setup": {
                "starting_files": {
                    "github_ref": {
                        "url": "https://github.com/igor0/augment",
                        "ref": "main",
                    }
                }
            },
            "starting_nodes": [],
            "user_guidelines": "This is a bug report. Analyze the issue, reproduce if possible, and provide a fix or detailed investigation.",
        },
        "enabled": True,
    }


def create_sample_schedule_trigger() -> Dict[str, Any]:
    """Create a sample schedule trigger configuration that runs daily at 9 AM."""
    return {
        "name": "Daily Status Check",
        "description": "Daily schedule trigger that runs at 9 AM UTC",
        "event_source": 4,  # EVENT_SOURCE_SCHEDULE
        "conditions": {
            "type": 3,  # TRIGGER_CONDITION_SCHEDULE
            "schedule": {
                "cron_expression": "0 9 * * *",  # Daily at 9 AM UTC
                "timezone": "UTC",
            },
        },
        "agent_config": {
            "workspace_setup": {
                "starting_files": {
                    "github_ref": {
                        "url": "https://github.com/igor0/augment",
                        "ref": "main",
                    }
                }
            },
            "starting_nodes": [
                {
                    "id": 0,
                    "type": 1,  # TEXT type
                    "text_node": {
                        "content": "Please provide a daily status report. Check for any critical issues and summarize the current state of the system."
                    },
                }
            ],
            "user_guidelines": "This is a scheduled daily check. Please provide a comprehensive status report.",
        },
        "enabled": True,
    }


def create_sample_schedule_2min_trigger() -> Dict[str, Any]:
    """Create a sample schedule trigger configuration that runs every 2 minutes."""
    return {
        "name": "2-Minute Status Check",
        "description": "Schedule trigger that runs every 2 minutes for testing",
        "event_source": 4,  # EVENT_SOURCE_SCHEDULE
        "conditions": {
            "type": 3,  # TRIGGER_CONDITION_SCHEDULE
            "schedule": {
                "cron_expression": "*/2 * * * *",  # Every 2 minutes
                "timezone": "UTC",
            },
        },
        "agent_config": {
            "workspace_setup": {
                "starting_files": {
                    "github_ref": {
                        "url": "https://github.com/igor0/augment",
                        "ref": "main",
                    }
                }
            },
            "starting_nodes": [
                {
                    "id": 0,
                    "type": 1,  # TEXT type
                    "text_node": {
                        "content": "This is a 2-minute test trigger. Please provide a brief status update or acknowledgment."
                    },
                }
            ],
            "user_guidelines": "This is a frequent test trigger that runs every 2 minutes. Keep responses brief.",
        },
        "enabled": True,
    }


def create_sample_pr_bot_ci_failure_trigger() -> Dict[str, Any]:
    """Create a sample pr_bot CI failure trigger configuration."""
    return {
        "name": "CI Failure Assistant",
        "description": "Automatically helps when CI checks fail on pull requests",
        "event_source": 1,  # EVENT_SOURCE_GITHUB
        "conditions": {
            "type": 1,  # TRIGGER_CONDITION_GITHUB
            "github": {
                "entity_type": 1,  # GITHUB_ENTITY_TYPE_PULL_REQUEST
                "pull_request": {
                    "repository": "augmentcode/augment",
                    "activity_types": [
                        "pr_bot_check_suite_failure",
                        "pr_bot_status_failure",
                    ],
                    "author": "@me",
                },
            },
        },
        "agent_config": {
            "workspace_setup": {
                "starting_files": {
                    "github_ref": {
                        "url": "@{pr_head_repo_url}",
                        "ref": "@{pr_head_ref}",
                    }
                }
            },
            "starting_nodes": [],
            "user_guidelines": "A CI check has failed on this pull request. Please analyze the failure, identify the root cause, and provide specific suggestions to fix the issue. If possible, suggest code changes or configuration updates.",
        },
        "enabled": True,
    }


def create_sample_pr_bot_comment_trigger() -> Dict[str, Any]:
    """Create a sample pr_bot comment trigger configuration."""
    return {
        "name": "PR Bot - Comment Assistant",
        "description": "Responds to @pr_bot mentions in PR comments",
        "event_source": 1,  # EVENT_SOURCE_GITHUB
        "conditions": {
            "type": 1,  # TRIGGER_CONDITION_GITHUB
            "github": {
                "entity_type": 1,  # GITHUB_ENTITY_TYPE_PULL_REQUEST
                "pull_request": {
                    "repository": "augmentcode/augment",
                    "activity_types": ["pr_bot_comment"],
                    "author": "@me",
                },
            },
        },
        "agent_config": {
            "workspace_setup": {
                "starting_files": {
                    "github_ref": {
                        "url": "@{pr_head_repo_url}",
                        "ref": "@{pr_head_ref}",
                    }
                }
            },
            "starting_nodes": [],
            "user_guidelines": "Someone has mentioned @pr_bot in a comment on this pull request. Please read the comment and respond appropriately. Provide helpful analysis, suggestions, or answers to their questions about the PR.",
        },
        "enabled": True,
    }


def create_sample_pr_bot_review_my_pr_trigger() -> Dict[str, Any]:
    """Create a sample pr_bot review_my_pr trigger configuration."""
    return {
        "name": "PR Bot - Review My PR",
        "description": "Provides comprehensive PR reviews when requested by the author",
        "event_source": 1,  # EVENT_SOURCE_GITHUB
        "conditions": {
            "type": 1,  # TRIGGER_CONDITION_GITHUB
            "github": {
                "entity_type": 1,  # GITHUB_ENTITY_TYPE_PULL_REQUEST
                "pull_request": {
                    "repository": "augmentcode/augment",
                    "author": "@me",
                    "activity_types": ["pr_bot_review_my_pr"],
                },
            },
        },
        "agent_config": {
            "workspace_setup": {
                "starting_files": {
                    "github_ref": {
                        "url": "@{pr_head_repo_url}",
                        "ref": "@{pr_head_ref}",
                    }
                }
            },
            "starting_nodes": [],
            "user_guidelines": "The PR author has requested a comprehensive review of their pull request. Please analyze the code changes, check for potential issues, suggest improvements, verify that tests are adequate, and provide constructive feedback. Focus on code quality, best practices, and potential bugs.",
        },
        "enabled": True,
    }


def create_sample_pr_bot_help_review_trigger() -> Dict[str, Any]:
    """Create a sample pr_bot help_review trigger configuration."""
    return {
        "name": "PR Bot - Help Review",
        "description": "Assists reviewers by providing analysis and insights for PR review",
        "event_source": 1,  # EVENT_SOURCE_GITHUB
        "conditions": {
            "type": 1,  # TRIGGER_CONDITION_GITHUB
            "github": {
                "entity_type": 1,  # GITHUB_ENTITY_TYPE_PULL_REQUEST
                "pull_request": {
                    "repository": "augmentcode/augment",
                    "activity_types": ["pr_bot_help_review"],
                    "reviewer": "@me",
                },
            },
        },
        "agent_config": {
            "workspace_setup": {
                "starting_files": {
                    "github_ref": {
                        "url": "@{pr_head_repo_url}",
                        "ref": "@{pr_head_ref}",
                    }
                }
            },
            "starting_nodes": [],
            "user_guidelines": "A reviewer has requested help reviewing this pull request. Please provide a detailed analysis of the changes, highlight important areas to focus on, identify potential risks or concerns, and summarize the key changes to help the reviewer understand what to look for.",
        },
        "enabled": True,
    }


def main():
    """Main function to handle command-line arguments and execute commands."""
    parser = argparse.ArgumentParser(
        description="CLI tool for testing remote agent triggers",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # List all triggers (summary view)
  python trigger_cli.py list

  # List all triggers with full details
  python trigger_cli.py list --verbose

  # Get a single trigger by ID
  python trigger_cli.py get abc-123

  # Get a single trigger with full details
  python trigger_cli.py get abc-123 --verbose

  # Get a single trigger as JSON
  python trigger_cli.py get abc-123 --json

  # Get a single trigger's configuration as JSON (for use with create)
  python trigger_cli.py get abc-123 --json-config

  # Create a sample PR review trigger
  python trigger_cli.py create --sample pr-review

  # Create a sample PR opened trigger
  python trigger_cli.py create --sample pr-opened

  # Create a sample PR trigger with repository variables (for cross-repo PRs)
  python trigger_cli.py create --sample pr-repo-vars

  # Create a sample Linear issue trigger
  python trigger_cli.py create --sample linear-issue

  # Create a sample Linear bug trigger
  python trigger_cli.py create --sample linear-bug

  # Create a sample schedule trigger (runs daily at 9 AM UTC)
  python trigger_cli.py create --sample schedule

  # Create a sample schedule trigger (runs every 2 minutes for testing)
  python trigger_cli.py create --sample schedule-2min

  # Create a sample pr_bot check suite failure trigger
  python trigger_cli.py create --sample pr-bot-ci-failure

  # Create a sample pr_bot comment trigger
  python trigger_cli.py create --sample pr-bot-comment

  # Create a sample pr_bot review my PR trigger
  python trigger_cli.py create --sample pr-bot-review-my-pr

  # Create a sample pr_bot help review trigger
  python trigger_cli.py create --sample pr-bot-help-review

  # Create a custom trigger from JSON file
  python trigger_cli.py create --config trigger.json

  # Delete a trigger
  python trigger_cli.py delete abc-123

  # Delete all triggers
  python trigger_cli.py delete --all

  # Get execution history
  python trigger_cli.py executions abc-123

  # Get matching entities (with mock data)
  python trigger_cli.py matching-entities

  # Get matching entities for a specific trigger
  python trigger_cli.py matching-entities abc-123

  # Execute a trigger manually on an entity (auto-generates entity ID)
  python trigger_cli.py execute-manually abc-123

  # Execute a trigger manually on a specific entity
  python trigger_cli.py execute-manually abc-123 --entity-id pr-1234

  # Dismiss an entity for a trigger
  python trigger_cli.py dismiss abc-123 --entity-id pr-1234

  # Un-dismiss an entity for a trigger
  python trigger_cli.py undismiss abc-123 --entity-id pr-1234

  # Test with custom API URL
  python trigger_cli.py list --api-url https://dev-igor.us-central.api.augmentcode.com

  # List triggers with verbose output and custom API URL
  python trigger_cli.py list --verbose --api-url https://dev-igor.us-central.api.augmentcode.com
        """,
    )

    parser.add_argument(
        "command",
        choices=[
            "list",
            "get",
            "create",
            "delete",
            "executions",
            "matching-entities",
            "execute-manually",
            "execute-manual-agent",
            "get-entity-details",
            "dismiss",
            "undismiss",
        ],
        help="Command to execute",
    )

    parser.add_argument(
        "--api-url",
        type=str,
        help="API URL (default: constructed from username or https://api.augment.dev)",
    )

    parser.add_argument(
        "--token-file",
        type=str,
        help="Path to API token file (default: ~/.augment/dev-deploy-oauth2-token.txt)",
    )

    parser.add_argument(
        "trigger_id",
        nargs="?",
        type=str,
        help="Trigger ID (required for get, delete, executions, execute-manually, dismiss, and undismiss commands unless --all is used)",
    )

    parser.add_argument(
        "--all",
        action="store_true",
        help="Delete all triggers (use with delete command)",
    )

    parser.add_argument(
        "--sample",
        choices=[
            "pr-review",
            "pr-opened",
            "pr-repo-vars",
            "linear-issue",
            "linear-bug",
            "schedule",
            "schedule-2min",
            "pr-bot-ci-failure",
            "pr-bot-comment",
            "pr-bot-review-my-pr",
            "pr-bot-help-review",
        ],
        help="Create a sample trigger configuration",
    )

    parser.add_argument(
        "--config",
        type=str,
        help="Path to JSON file containing trigger configuration",
    )

    parser.add_argument(
        "--verbose",
        action="store_true",
        help="Enable verbose output (shows detailed trigger information for list command) and verbose logging",
    )

    parser.add_argument(
        "--json",
        action="store_true",
        help="Output raw JSON response (for debugging)",
    )
    parser.add_argument(
        "--json-config",
        action="store_true",
        help="Output just the trigger configuration as JSON (for get command, suitable for create command)",
    )

    # Arguments for new APIs
    parser.add_argument(
        "--entity-id",
        type=str,
        help="Entity ID (optional for execute-manually command - will auto-generate if not provided; required for dismiss/undismiss commands)",
    )

    parser.add_argument(
        "--extra-prompt",
        type=str,
        help="Additional instructions to append to the agent prompt (for execute-manually command)",
    )

    parser.add_argument(
        "--event-source",
        type=int,
        default=1,
        help="Event source (default: 1 for EVENT_SOURCE_GITHUB)",
    )

    parser.add_argument(
        "--limit",
        type=int,
        default=25,
        help="Limit for matching entities (default: 25)",
    )

    parser.add_argument(
        "--offset",
        type=int,
        default=0,
        help="Offset for pagination (default: 0)",
    )

    parser.add_argument(
        "--instructions",
        type=str,
        help="Instructions for manual agent execution",
    )

    parser.add_argument(
        "--github-entity-type",
        type=int,
        help="GitHub entity type (1=PR, 2=Workflow Run)",
    )

    parser.add_argument(
        "--linear-entity-type",
        type=int,
        help="Linear entity type (1=Issue)",
    )

    parser.add_argument(
        "--repository",
        type=str,
        help="Repository in format 'owner/repo' (required for GitHub entities)",
    )

    parser.add_argument(
        "--show-dismissed",
        action="store_true",
        help="Show dismissed entities (for matching-entities command)",
    )

    parser.add_argument(
        "--include-entity-details",
        action="store_true",
        help="Include related entities such as review comments, failed check suites, and failed statuses (for get-entity-details command)",
    )

    args = parser.parse_args()

    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)
    elif args.json:
        # Suppress all logging for clean JSON output
        logging.getLogger().setLevel(logging.ERROR)

    try:
        # Get API credentials
        api_url = get_api_url(args.api_url)
        api_token = get_api_token(args.token_file)

        if not args.json:
            logger.info(f"Using API URL: {api_url}")

        # Create client
        client = AugmentClient(url=api_url, token=api_token)

        # Execute command
        if args.command == "list":
            list_triggers(client, verbose=args.verbose, json_output=args.json)

        elif args.command == "get":
            if not args.trigger_id:
                logger.error("trigger_id is required for get command")
                sys.exit(1)
            get_trigger(
                client,
                args.trigger_id,
                verbose=args.verbose,
                json_output=args.json,
                json_config=args.json_config,
            )

        elif args.command == "create":
            if args.sample:
                if args.sample == "pr-review":
                    trigger_config = create_sample_pr_trigger()
                elif args.sample == "pr-opened":
                    trigger_config = create_sample_pr_opened_trigger()
                elif args.sample == "pr-repo-vars":
                    trigger_config = create_sample_pr_repo_variable_trigger()
                elif args.sample == "linear-issue":
                    trigger_config = create_sample_linear_issue_trigger()
                elif args.sample == "linear-bug":
                    trigger_config = create_sample_linear_bug_trigger()
                elif args.sample == "schedule":
                    trigger_config = create_sample_schedule_trigger()
                elif args.sample == "schedule-2min":
                    trigger_config = create_sample_schedule_2min_trigger()
                elif args.sample == "pr-bot-ci-failure":
                    trigger_config = create_sample_pr_bot_ci_failure_trigger()
                elif args.sample == "pr-bot-comment":
                    trigger_config = create_sample_pr_bot_comment_trigger()
                elif args.sample == "pr-bot-review-my-pr":
                    trigger_config = create_sample_pr_bot_review_my_pr_trigger()
                elif args.sample == "pr-bot-help-review":
                    trigger_config = create_sample_pr_bot_help_review_trigger()
                create_trigger(client, trigger_config)
            elif args.config:
                if not os.path.exists(args.config):
                    logger.error(f"Config file not found: {args.config}")
                    sys.exit(1)
                with open(args.config, "r") as f:
                    trigger_config = json.load(f)
                create_trigger(client, trigger_config)
            else:
                logger.error(
                    "Either --sample or --config must be specified for create command"
                )
                sys.exit(1)

        elif args.command == "delete":
            if args.all:
                delete_all_triggers(client)
            elif args.trigger_id:
                delete_trigger(client, args.trigger_id)
            else:
                logger.error(
                    "Either trigger_id or --all is required for delete command"
                )
                sys.exit(1)

        elif args.command == "executions":
            if not args.trigger_id:
                logger.error("trigger_id is required for executions command")
                sys.exit(1)
            get_trigger_executions(client, args.trigger_id, json_output=args.json)

        elif args.command == "matching-entities":
            get_matching_entities(
                client,
                trigger_id=args.trigger_id,
                event_source=args.event_source,
                limit=args.limit,
                offset=args.offset,
                show_dismissed=args.show_dismissed,
                json_output=args.json,
            )

        elif args.command == "execute-manually":
            if not args.trigger_id:
                logger.error("trigger_id is required for execute-manually command")
                sys.exit(1)

            # Auto-generate entity ID if not provided
            entity_id = args.entity_id
            if not entity_id:
                timestamp = datetime.datetime.now(datetime.timezone.utc).strftime(
                    "%Y-%m-%dT%H-%M-%SZ"
                )
                entity_id = f"manual-execution-{timestamp}"
                logger.info(f"Auto-generated entity ID: {entity_id}")

            execute_trigger_manually(
                client,
                args.trigger_id,
                entity_id,
                extra_prompt=args.extra_prompt,
            )

        elif args.command == "execute-manual-agent":
            if not args.instructions:
                logger.error(
                    "--instructions is required for execute-manual-agent command"
                )
                sys.exit(1)

            # Auto-generate entity ID if not provided
            entity_id = args.entity_id
            if not entity_id:
                timestamp = datetime.datetime.now(datetime.timezone.utc).strftime(
                    "%Y-%m-%dT%H-%M-%SZ"
                )
                entity_id = f"manual-execution-{timestamp}"
                logger.info(f"Auto-generated entity ID: {entity_id}")

            execute_manual_agent(
                client,
                entity_id,
                args.instructions,
                github_entity_type=args.github_entity_type,
                linear_entity_type=args.linear_entity_type,
            )

        elif args.command == "get-entity-details":
            if not args.entity_id:
                logger.error("--entity-id is required for get-entity-details command")
                sys.exit(1)
            get_entity_details(
                client,
                args.entity_id,
                args.event_source,
                github_entity_type=args.github_entity_type,
                linear_entity_type=args.linear_entity_type,
                repository=args.repository,
                include_entity_details=args.include_entity_details,
                json_output=args.json,
            )

        elif args.command == "dismiss":
            if not args.trigger_id:
                logger.error("trigger_id is required for dismiss command")
                sys.exit(1)
            if not args.entity_id:
                logger.error("--entity-id is required for dismiss command")
                sys.exit(1)
            dismiss_entity(client, args.trigger_id, args.entity_id, dismissed=True)

        elif args.command == "undismiss":
            if not args.trigger_id:
                logger.error("trigger_id is required for undismiss command")
                sys.exit(1)
            if not args.entity_id:
                logger.error("--entity-id is required for undismiss command")
                sys.exit(1)
            dismiss_entity(client, args.trigger_id, args.entity_id, dismissed=False)

    except ValueError as e:
        logger.error(f"Configuration error: {e}")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Unexpected error: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
