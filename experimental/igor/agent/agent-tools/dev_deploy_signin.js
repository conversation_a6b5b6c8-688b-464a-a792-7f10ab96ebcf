#!/usr/bin/env node

const crypto = require('crypto');
const readline = require('readline');
const https = require('https');
const http = require('http');
const url = require('url');
const fs = require('fs');
const path = require('path');

// Constants
const CLIENT_ID = 'v'; // Using the same client ID as the Vim client

// Read username from ~/.augment/user.json
function getUsernameFromConfig() {
    console.log('Reading username from ~/.augment/user.json...');
    const userConfigPath = path.join(process.env.HOME || process.env.USERPROFILE, '.augment', 'user.json');

    try {
        if (fs.existsSync(userConfigPath)) {
            const userConfig = JSON.parse(fs.readFileSync(userConfigPath, 'utf8'));
            if (userConfig.name) {
                console.log(`Found username: ${userConfig.name}`);
                return userConfig.name;
            }
        }
        throw new Error('Username not found in config file');
    } catch (error) {
        console.error(`Error reading username from config: ${error.message}`);
        throw error;
    }
}

// PKCE utilities
function base64URLEncode(buffer) {
    return buffer.toString('base64')
        .replace(/\+/g, '-')
        .replace(/\//g, '_')
        .replace(/=/g, '');
}

function sha256(buffer) {
    return crypto.createHash('sha256').update(buffer).digest();
}

// Generate a code verifier and challenge
function generatePKCE() {
    const codeVerifier = base64URLEncode(crypto.randomBytes(32));
    const codeChallenge = base64URLEncode(sha256(Buffer.from(codeVerifier)));
    return { codeVerifier, codeChallenge };
}

// Create a readline interface for user input
const rl = readline.createInterface({
    input: process.stdin,
    output: process.stdout
});

// Function to make the token request
function makeTokenRequest(baseURL, params) {
    return new Promise((resolve, reject) => {
        const postData = JSON.stringify(params);
        const parsedUrl = new URL(`${baseURL}/token`);

        const options = {
            hostname: parsedUrl.hostname,
            port: parsedUrl.port || (parsedUrl.protocol === 'https:' ? 443 : 80),
            path: parsedUrl.pathname,
            method: 'POST',
            headers: {
                'Content-Type': 'application/json',
                'Content-Length': Buffer.byteLength(postData)
            }
        };

        const req = (parsedUrl.protocol === 'https:' ? https : http).request(options, (res) => {
            let data = '';

            res.on('data', (chunk) => {
                data += chunk;
            });

            res.on('end', () => {
                if (res.statusCode >= 200 && res.statusCode < 300) {
                    try {
                        const jsonResponse = JSON.parse(data);
                        resolve(jsonResponse);
                    } catch (e) {
                        reject(new Error(`Failed to parse response: ${e.message}`));
                    }
                } else {
                    reject(new Error(`Request failed with status code ${res.statusCode}: ${data}`));
                }
            });
        });

        req.on('error', (e) => {
            reject(new Error(`Request error: ${e.message}`));
        });

        req.write(postData);
        req.end();
    });
}

// Write token to file
function writeTokenToFile(token) {
    const tokenPath = path.join(process.env.HOME || process.env.USERPROFILE, '.augment', 'dev-deploy-oauth2-token.txt');

    // Ensure the .augment directory exists
    const augmentDir = path.dirname(tokenPath);
    if (!fs.existsSync(augmentDir)) {
        fs.mkdirSync(augmentDir, { recursive: true });
    }

    fs.writeFileSync(tokenPath, token);
    console.log(`Token written to ${tokenPath}`);
}

async function main() {
    try {
        // Get username from config
        const username = getUsernameFromConfig();

        // Construct namespace and auth server URL
        const namespace = `dev-${username}`;
        console.log(`Using namespace: ${namespace}`);

        const AUTH_SERVER_URL = `https://auth-central.${namespace}.us-central1.dev.augmentcode.com`;
        console.log(`Using auth server URL: ${AUTH_SERVER_URL}`);

        // Generate PKCE code verifier and challenge
        const { codeVerifier, codeChallenge } = generatePKCE();

        // Generate a random state
        const state = base64URLEncode(crypto.randomBytes(16));

        // Construct the authorization URL
        const authParams = new URLSearchParams({
            response_type: 'code',
            code_challenge: codeChallenge,
            client_id: CLIENT_ID,
            state: state,
            prompt: 'login'
        });

        const authURL = `${AUTH_SERVER_URL}/authorize?${authParams.toString()}`;

        console.log('\nPlease open the following URL in your browser:');
        console.log('\n' + authURL + '\n');
        console.log('After authenticating, you will receive a code. Paste it below.');

        // Wait for the user to input the code
        const userInput = await new Promise((resolve) => {
            rl.question('\nEnter the authentication code: ', (answer) => {
                resolve(answer.trim());
            });
        });

        // Extract the code from the input
        // The input could be a plain code or a JSON object with a code property
        let code = userInput;
        try {
            // Check if the input is a JSON object
            const jsonInput = JSON.parse(userInput);
            if (jsonInput && jsonInput.code) {
                console.log('Detected JSON input with code property');
                code = jsonInput.code;
            }
        } catch (e) {
            // Not JSON, assume it's just the code
            console.log('Using input as plain code');
        }

        console.log('\nExchanging code for access token...');

        // Prepare the token request
        const tokenParams = {
            grant_type: 'authorization_code',
            client_id: CLIENT_ID,
            code_verifier: codeVerifier,
            redirect_uri: '', // Empty for Vim client
            code: code
        };

        // Make the token request using the AUTH_SERVER_URL
        const tokenResponse = await makeTokenRequest(AUTH_SERVER_URL, tokenParams);

        // Write the token to file
        writeTokenToFile(tokenResponse.access_token);

        console.log('\nAuthentication successful!');
        console.log('Token Type:', tokenResponse.token_type);
        console.log('Expires In:', tokenResponse.expires_in, 'seconds');

    } catch (error) {
        console.error('Error:', error.message);
    } finally {
        rl.close();
    }
}

main();
