# Message Edit Feature Investigation

This document investigates how to implement a "message edit" feature for remote agents, where:

1. User edits a past message in chat history
2. The response to that message and any subsequent messages get clipped from the chat history
3. The AI continues with responding to the edited message and continues forward with a new history

## Current Architecture

### Chat History Structure

The remote agents system uses a sequence-based approach for chat history, with beachhead as the source of truth:

1. **Source of Truth**:
   - The beachhead is the authoritative source of truth for chat history
   - It maintains the complete chat history in `AgentState._chatHistory` and persists it locally
   - BigTable serves as a replica that is updated by the beachhead

2. **Chat History Storage**:
   - Each exchange has a unique `sequence_id` that is monotonically increasing
   - In beachhead, exchanges are stored in memory and persisted to local storage
   - In BigTable, exchanges are stored with row keys in the format `ExchangeHistory#{agentID}#{sequenceID}`

3. **Sequence ID Requirements**:
   - The beachhead manages sequence IDs and ensures they are monotonically increasing
   - When reporting chat history, the beachhead includes only exchanges with sequence IDs greater than `lastUploadedSequenceId`

4. **Chat History Synchronization**:
   - The beachhead periodically pushes chat history updates to the remote-agents service
   - The service writes these updates to BigTable, which acts as a persistent replica
   - Clients can retrieve chat history from the service with pagination using `lastProcessedSequenceId`

### Current Limitations

1. **No Direct Update Mechanism**:
   - There is no API to update or delete specific chat history entries
   - The only deletion mechanism is when deleting an entire agent

2. **Sequence ID Constraints**:
   - Sequence IDs must be monotonically increasing
   - The server validates this constraint when receiving chat history updates

3. **Beachhead State**:
   - The beachhead maintains its own state that includes chat history
   - This state is persisted locally and synchronized with the service

## Implementation Approach

To implement the message edit feature, we need to modify both the client and server components, with beachhead as the source of truth. Here's a proposed approach:

### 1. Client-Side Changes (VSCode Extension)

1. **UI for Message Editing**:
   - Add an "Edit" button or context menu option for user messages
   - Implement a message editing interface

2. **Send Edit Message Request**:
   - When a user edits a message, send an edit request to the API proxy
   - Include the agent ID, sequence ID of the message being edited, and the new message content

### 2. Remote-Agents Service Changes

1. **Add Message Edit API**:
   - Create a new API endpoint for editing messages: `EditChatMessage`
   - This endpoint should:
     - Accept the agent ID, message sequence ID, and new message content
     - Create a pending update for the beachhead to poll

2. **Create Pending Update**:
   - Add a new update type for message edits
   - Store this update in BigTable for the beachhead to poll

### 3. Beachhead Changes (Source of Truth)

1. **Poll for Edit Message Updates**:
   - Beachhead periodically polls for updates including the new edit message type
   - When an edit update is received, process it locally

2. **Local State Modification**:
   - Truncate the local chat history at the edited message
   - Replace the edited message with the new content
   - Save the updated state to local persistent storage

3. **Push Changes to Remote-Agents Service**:
   - After updating local state, push the modified chat history to the remote-agents service
   - The service then updates BigTable with the new chat history

4. **Handle Ongoing Conversations**:
   - If the agent is currently processing a message, interrupt it
   - Reset the agent state to be ready for a new response to the edited message

## Multi-Client Considerations

When implementing the message edit feature, we need to consider scenarios where multiple clients are following the same chat history and one of them performs an edit that rolls back part of the conversation. This introduces additional complexity beyond simply clipping the history in the beachhead.

### Active Rollback Record Approach

Instead of (or in addition to) simply clipping the history, we should implement an active rollback record mechanism:

1. **Rollback Record Creation**:
   - When a message is edited, create a special "rollback record" with a higher sequence ID
   - This record would mark all messages between the edited message's sequence ID and the rollback record as obsolete
   - The rollback record would contain:
     - The sequence ID of the edited message
     - The new content of the edited message
     - A flag indicating this is a rollback/edit operation

2. **Client Handling of Rollback Records**:
   - When a client receives a rollback record, it should:
     - Identify the affected message by its sequence ID
     - Replace the message content with the new content
     - Mark all messages between the edited message and the rollback record as obsolete
     - Visually indicate to the user that part of the conversation has been edited

3. **Beachhead Processing**:
   - The beachhead would still truncate its local state at the edited message
   - It would then add the edited message and create a new conversation branch
   - When reporting to BigTable, it would include the rollback record

4. **Sequence ID Continuity**:
   - The rollback record ensures sequence ID continuity across all clients
   - New messages after the edit would have sequence IDs higher than the rollback record
   - This maintains the monotonically increasing sequence ID requirement
