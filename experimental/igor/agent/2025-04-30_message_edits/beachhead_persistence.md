# Remote Agents Beachhead State Persistence

This document describes how persistent state is managed in the remote agents beachhead, including what state is stored locally, how it's synchronized with the remote-agents service, and the overall architecture of state persistence.

## Overview

The remote agents architecture uses a dual-layer persistence model:

1. **Local State Persistence (Beachhead)**: Maintains the current state of the agent in the beachhead container
2. **Remote State Persistence (BigTable)**: Stores the authoritative record of agent configuration, status, and chat history

This approach provides resilience against container restarts while maintaining a consistent view of agent state across multiple clients.

## Local State Persistence in Beachhead

### What's Stored Locally

The beachhead maintains a local state file (`agent_state.json`) that contains:

1. **Agent State Object**: An instance of `AgentState` class that includes:
   - `_remoteAgentId`: Unique identifier for the agent
   - `_conversationId`: UUID for the current conversation
   - `_requestNodes`: Current request nodes being processed
   - `_requestId`: ID of the current request
   - `_responseChunks`: Chunks of the current response
   - `_toolCalls`: Tool calls from the last exchange
   - `_chatHistory`: Array of completed exchanges (ExchangeState objects)
   - `_status`: Current agent status (STARTING, RUNNING, IDLE, etc.)
   - `_lastProcessedServiceUpdateSequenceId`: Last update sequence ID processed from the service
   - `_lastUploadedChatExchangeSequenceId`: Last chat exchange sequence ID uploaded to the service
   - `_changedFiles`: Files changed during the current exchange
   - `_userGuidelines`: User-provided guidelines
   - `_workspaceGuidelines`: Workspace-specific guidelines
   - `_agentMemories`: Agent memories

2. **Metadata**:
   - `timestamp`: When the state was last saved

### Storage Format and Location

- **Format**: JSON file
- **Location**: `<persistentRoot>/agent_state/agent_state.json`
- **Serialization**: The entire `AgentState` object is serialized to JSON and stored with a timestamp

### When State is Saved Locally

The beachhead saves state to disk at several key points:

1. **During LLM Response Streaming**: Every `REPORT_STREAMED_CHAT_EVERY_CHUNK` chunks
2. **After Completing a Response**: When `finishResponse()` is called
3. **After Tool Call Results**: When `pushToolCallResult()` is called

### Implementation Details

The `StatePersistence` class in `clients/beachhead/src/agent_loop/state-persistence.ts` handles all local state persistence operations:

- `save(state)`: Serializes and writes the state to disk
- `loadLatestAgentState()`: Loads and deserializes the most recent state
- `checkSavedStateExists()`: Checks if a saved state exists

## Remote State Persistence in BigTable

### BigTable Structure

The remote-agents service uses BigTable with the following structure:

| Row Key Pattern | Column Families | Description |
|-----------------|-----------------|-------------|
| `RemoteAgent#{agentID}` | Config | Agent configuration (workspace setup, prompt, SSH config) |
| `RemoteAgent#{agentID}` | Status | Runtime info (agent status, container name) |
| `UserAgentMapping#{userID}` | UserMapping | List of agent IDs associated with each user |
| `ExchangeHistory#{agentID}#{sequenceID}` | Output | Chat dialog and exchanges with proper ordering |
| `UpdateSequenceID#{agentID}` | Output | Latest sequence ID for agent updates |
| `PendingUpdates#{agentID}#{sequenceID}` | Output | Updates waiting to be processed by the agent |

### What's Stored in BigTable

1. **Agent Configuration** (`AgentConfig`):
   - User-defined configuration
   - User ID
   - Creation timestamp
   - SSH configuration
   - Kubernetes namespace

2. **Agent Status** (`AgentStatus`):
   - Current status (STARTING, RUNNING, IDLE, etc.)

3. **User-Agent Mapping** (`UserAgentMapping`):
   - List of agent IDs associated with each user

4. **Chat History** (`ChatHistoryExchange`):
   - Complete chat exchanges
   - Each exchange has a sequence ID for ordering

5. **Pending Updates** (`PendingAgentUpdate`):
   - Updates waiting to be processed by the agent
   - Each update has a sequence ID

## Synchronization Between Beachhead and Remote-Agents Service

### Beachhead to Remote-Agents Service (Push)

1. **Chat History Reporting**:
   - The beachhead calls `reportChatHistory()` which:
     - Gets the chat history with `chatHistory(true)` (incremental only)
     - Calls `apiServer.reportRemoteAgentChatHistory()` to send it to the service
     - Updates `_lastUploadedChatExchangeSequenceId` to track what's been uploaded

2. **Status Reporting**:
   - The beachhead calls `reportAgentStatus()` which:
     - Calls `apiServer.agentWorkspaceReportStatus()` to update the agent status in the service

### Remote-Agents Service to Beachhead (Poll)

1. **Instruction Updates**:
   - The beachhead periodically calls `pollInstructionUpdate()` which:
     - Calls `apiServer.agentWorkspacePollUpdate()` with the last processed sequence ID
     - Processes any new updates (interrupts, chat requests)
     - Updates `_lastProcessedServiceUpdateSequenceId` to track what's been processed

2. **Update Types**:
   - **Interrupt**: Stops the current agent execution
   - **ChatRequest**: Initiates a new chat exchange

## Key Workflows

### Agent Startup and State Recovery

1. When the beachhead starts:
   - It checks if a saved state exists with `statePersistence.checkSavedStateExists()`
   - If it exists, it loads the state with `statePersistence.loadLatestAgentState()`
   - This allows the agent to recover its state after container restarts

### Chat Exchange Flow

1. User sends a message to the remote-agents service
2. Service writes a pending update to BigTable
3. Beachhead polls for updates and receives the chat request
4. Beachhead processes the request:
   - Calls the LLM API
   - Streams response chunks
   - Periodically saves state locally and reports to the service
   - Executes tool calls
   - Finalizes the exchange
   - Reports the completed exchange to the service

### Interruption Flow

1. User sends an interrupt request to the remote-agents service
2. Service writes a pending update to BigTable
3. Beachhead polls for updates and receives the interrupt
4. Beachhead cancels the current execution and reports idle status

## Summary of State Management

The remote agents architecture uses a hybrid approach to state management:

1. **Local State (Beachhead)**:
   - Provides fast access to the current state
   - Enables recovery after container restarts
   - Contains the complete agent state including in-progress exchanges

2. **Remote State (BigTable)**:
   - Serves as the authoritative record
   - Enables persistence beyond the lifecycle of the beachhead container
   - Facilitates user interfaces that can display agent status and chat history

3. **Synchronization**:
   - Beachhead pushes updates to the service (chat history, status)
   - Beachhead polls the service for instructions (chat requests, interrupts)
   - Sequence IDs ensure proper ordering and prevent duplicate processing

This architecture ensures that:
- Chat history persists even if the beachhead container is terminated
- The agent can recover its state after restarts
- Multiple clients (VSCode, web UI) can interact with the same agent
- The service maintains a consistent view of all agents
