# GitHub Webhooks in Augment

This document explains how GitHub webhook registration and management works in the Augment system, with implications for the triggers feature.

## Webhook Architecture Overview

### GitHub App-Based Webhooks

Augment uses a **GitHub App** approach for webhook management, which differs significantly from traditional repository-level webhooks:

- **Single Global Webhook URL**: GitHub Apps can only specify one webhook endpoint
- **App-Level Event Configuration**: Event subscriptions are set at the GitHub App level, not per repository
- **Installation-Based Routing**: Events are routed internally using GitHub App installation mappings

### Current Implementation

Based on codebase analysis, here's how webhooks currently work:

```go
// services/integrations/github/webhook_listener/deploy.jsonnet
// This is a global service. Right now the only integration is github, which
// only allows specifying one webhook per app, so we have one global webhook
// handler.
```

**Global webhook endpoint**: `github-app-webhook.{environment}.augmentcode.com`

## Webhook Registration Process

### Who Does What?

#### Augment's Responsibilities (One-Time Setup)

**Augment handles all webhook infrastructure:**

1. **GitHub App Creation**: Augment creates and owns the GitHub App
2. **Webhook URL Configuration**: Points to Augment's global webhook service
3. **Event Subscriptions**: Configured in GitHub App settings (manual process)
4. **App Permissions**: Set required repository and organization permissions
5. **Webhook Secret**: Manages webhook validation secrets

**Evidence from deployment:**
```jsonnet
local ingressHostname = {
  PROD: 'github-app-webhook.%s' % domainSuffix,
  STAGING: 'github-app-webhook.staging.%s' % domainSuffix,
  DEV: 'github-app-webhook.%s.%s' % [std.substr(namespace, 0, 10), domainSuffix],
}[env];
```

#### User's Responsibilities (Per-Installation)

**Users only install the GitHub App:**

1. **App Installation**: Install Augment's GitHub App on repositories/organizations
2. **Repository Selection**: Choose which repositories to grant access to
3. **Permission Approval**: Approve the permissions Augment's app requests

**No webhook configuration required by users.**

### Installation Mapping

When a GitHub App is installed, the system creates a webhook tenant mapping:

```go
// services/integrations/github/processor/server/server.go
_, err = s.webhookTenantMappingResource.Update(ctx,
    &webhookmapping.WebhookTenantMappingSpec{
        WebhookType:  "github",
        WebhookValue: string(webhookValue),
        TenantID:     tenantID,
    },
```

This mapping allows the global webhook handler to route events to the correct tenant.

## Event Processing Flow

```
GitHub.com → Global Webhook → Event Router → Tenant Lookup → Event Processing
```

### Detailed Flow

1. **Event Generation**: GitHub generates an event (push, PR, issue, etc.)
2. **Webhook Delivery**: GitHub sends POST request to Augment's webhook URL
3. **Signature Validation**: Webhook handler validates GitHub's signature
4. **Installation Lookup**: System maps installation ID to Augment tenant
5. **Event Routing**: Event is published to tenant-specific pubsub topic
6. **Event Processing**: Tenant services process the event

### Current Event Handler

```go
// services/integrations/github/webhook_listener/listener.go
switch incomingEvent {
case "ping":
    // Ignore ping events
case "installation":
    // Handle app installation/uninstallation
case "push":
    // Handle repository pushes
default:
    // Currently ignores other event types
}
```

**Limited event support** - only handles ping, installation, and push events.

## Webhook Persistence and Updates

### How Webhook Definitions Work

**Webhook configurations are very persistent:**

1. **Static Configuration**: Webhook URL and event subscriptions are set in GitHub App settings
2. **Manual Updates Required**: Cannot programmatically change event subscriptions
3. **No Re-registration Needed**: Webhook URL remains constant across updates

### Adding New Event Types

To support new events for triggers:

1. **Update GitHub App Settings**: Manually add event subscriptions in GitHub developer console
2. **Deploy Code Changes**: Update webhook handler to process new event types
3. **No Webhook Re-registration**: Existing webhook URL continues to work

**You would NOT re-register webhooks based on trigger configuration.**

## Implications for Triggers Feature

### Event Subscription Strategy

**Subscribe to all possible events upfront** at the GitHub App level:

#### Core Repository Events
- `pull_request` (opened, closed, review_requested, synchronize, etc.)
- `pull_request_review` (submitted, edited, dismissed)
- `pull_request_review_comment` (created, edited, deleted)
- `issues` (opened, closed, assigned, labeled, etc.)
- `issue_comment` (created, edited, deleted)
- `push` (already supported)
- `create` (branch/tag creation)
- `delete` (branch/tag deletion)

#### CI/CD Events
- `check_run` (created, completed, rerequested)
- `check_suite` (completed, requested, rerequested)
- `workflow_run` (completed, requested, in_progress)
- `workflow_job` (queued, in_progress, completed)
- `status` (commit status updates)

#### Security Events
- `security_advisory` (published, updated)
- `code_scanning_alert` (created, fixed, dismissed)
- `secret_scanning_alert` (created, resolved)
- `dependabot_alert` (created, dismissed, fixed)

#### Repository Management
- `release` (published, edited, deleted)
- `fork` (repository forked)
- `star` (repository starred/unstarred)
- `watch` (repository watched/unwatched)
- `repository` (created, deleted, archived, etc.)

### Internal Event Filtering

The triggers system would handle filtering internally:

1. **Receive All Events**: Global webhook receives all subscribed events
2. **Route to Triggers Service**: Events forwarded to new triggers service
3. **Evaluate User Triggers**: Check which user triggers match the event
4. **Apply Conditions**: Filter based on repository, user permissions, trigger conditions
5. **Spawn Agents**: Create remote agents for matching triggers

### No Dynamic Webhook Management

**Key insight**: With GitHub Apps, webhook registration is a one-time setup. The triggers system must work within this constraint by:

- **Static event subscriptions** at the app level
- **Dynamic filtering** at the application level
- **No per-trigger webhook registration**

## Current Limitations

Based on codebase analysis:

### Limited Event Support
```go
// Only handles these events currently:
case "ping":     // Health check
case "installation": // App install/uninstall
case "push":     // Repository pushes
```

### Basic Event Processing
- Simple event routing without complex trigger evaluation
- No framework for user-defined triggers
- Limited event filtering capabilities

### Missing Infrastructure
- No trigger configuration storage
- No condition evaluation engine
- No user permission integration for triggers

## Architecture Benefits

The GitHub App approach provides:

1. **Higher Rate Limits**: GitHub Apps get higher API rate limits than OAuth apps
2. **Simplified User Experience**: One-time app installation vs per-repo webhook setup
3. **Centralized Management**: All webhook handling in Augment's infrastructure
4. **Scalability**: Single endpoint handles events for all installations
5. **Security**: Centralized secret management and signature validation

## Implementation Recommendations

### For Triggers Feature

1. **Audit Current GitHub App**: Review current event subscriptions
2. **Plan Event Expansion**: Identify all events needed for comprehensive triggers
3. **Update GitHub App Configuration**: Add new event subscriptions manually
4. **Extend Webhook Handler**: Add support for new event types
5. **Build Trigger Engine**: Create trigger evaluation and routing system
6. **Design for Future Growth**: Plan for adding events without webhook changes

### Event Processing Architecture

```
GitHub Webhook → Event Router → Trigger Evaluator → Agent Spawner
                      ↓
                 Tenant Lookup
                      ↓
                 Permission Check
                      ↓
                 Condition Matching
```

### Monitoring and Reliability

- **Webhook Delivery Monitoring**: Track delivery success rates
- **Event Processing Metrics**: Monitor trigger evaluation performance
- **Error Handling**: Robust retry mechanisms for failed events
- **Rate Limiting**: Handle high-volume event scenarios

## Security Considerations

### Webhook Validation
```go
// Current signature validation
signature := r.Header.Get("X-Hub-Signature-256")
mac := hmac.New(sha256.New, []byte(gl.webhookSecret.Expose()))
expectedMAC := "sha256=" + hex.EncodeToString(mac.Sum(nil))
```

### Permission Inheritance
- Triggers inherit user permissions for repository access
- Events only processed for authorized users
- Tenant isolation through installation mapping

## Summary

**Key Takeaways:**

- **Webhook registration is static** at the GitHub App level, managed by Augment
- **Users only install the app** - no webhook configuration required
- **Event subscriptions must be planned upfront** and configured manually
- **The triggers system handles all dynamic filtering and routing**
- **Adding new events requires GitHub App updates + code deployment**
- **No webhook re-registration needed** when expanding trigger capabilities

This architecture provides a solid foundation for building a comprehensive triggers system while maintaining simplicity for end users and centralized control for Augment.

## Dev Environment Notes

### Setting Up Real GitHub Integration for Testing

When developing and testing the triggers feature, you may need to configure your dev environment to receive real GitHub webhook events instead of using the CLI testing tools.

#### Finding Your GitHub App Installation ID

1. **Access GitHub App Settings**: Go to https://github.com/organizations/augmentcode/settings/apps/augment-eng
2. **Check Installations**: Look for existing installations or install the app on your test repository
3. **Note Installation ID**: The installation ID appears in the URL when viewing an installation (e.g., `https://github.com/settings/installations/********`)

#### Configuring Webhook Tenant Mapping

Your dev environment uses a webhook tenant mapping to route GitHub events to your specific tenant. To enable real GitHub integration:

```bash
# Update webhook mapping with real installation ID
kubectl -n dev-igor patch webhooktenantmapping github-test-installation \
  --type='merge' \
  -p='{"spec":{"webhook_value":"{\"installation_id\":REAL_INSTALLATION_ID}"}}'

# Example with actual installation ID:
kubectl -n dev-igor patch webhooktenantmapping github-test-installation \
  --type='merge' \
  -p='{"spec":{"webhook_value":"{\"installation_id\":********}"}}'
```

#### Verifying the Configuration

```bash
# Check current webhook mapping
kubectl -n dev-igor get webhooktenantmapping github-test-installation -o jsonpath='{.spec.webhook_value}'

# Monitor webhook events
kubectl -n dev-igor logs -f github-webhook-d798d8c4d-827dz

# Check remote agents processing
kubectl -n dev-igor logs -l app=remote-agents --since=5m
```

#### Reverting to Test Mode

To return to CLI-based testing (recommended when not actively testing real GitHub events):

```bash
# Revert to test installation ID
kubectl -n dev-igor patch webhooktenantmapping github-test-installation \
  --type='merge' \
  -p='{"spec":{"webhook_value":"{\"installation_id\":12345678}"}}'
```

#### Important Notes

- **Environment Isolation**: Each developer has their own webhook tenant mapping in their dev namespace (e.g., `dev-igor`, `dev-aswin`, etc.)
- **Production Safety**: Changes only affect your dev environment - production and other developers' environments are completely isolated
- **Webhook Endpoints**: The webhook listener uses `/postreceive` endpoint, not `/webhook`
- **Event Processing**: Real GitHub events will only trigger agents if you have matching triggers configured
- **Testing Tools**: Use the GitHub Webhook CLI tool (`github_webhook_cli.py`) for controlled testing without affecting real repositories

#### Troubleshooting

If webhook events aren't being received:

1. **Check Installation**: Verify the GitHub app is installed on the target repository
2. **Verify Mapping**: Ensure the webhook tenant mapping has the correct installation ID
3. **Monitor Logs**: Watch webhook listener logs for incoming events and errors
4. **Test Connectivity**: Use the simple webhook test script (`test_webhook_simple.py`) to verify basic connectivity

#### Security Considerations

- **Real Events**: When using real installation IDs, your dev environment will receive actual GitHub events from the configured repositories
- **Event Filtering**: Only events matching your configured triggers will create agents
- **Repository Access**: Ensure you have appropriate permissions on repositories you're testing with
- **Cleanup**: Remember to revert to test mode when finished to avoid processing unintended events
