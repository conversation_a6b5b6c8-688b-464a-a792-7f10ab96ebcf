# Slack Webhooks in Augment

This document explains how Slack webhook registration and management works, and how it compares to GitHub, Linear, and JIRA webhooks for the triggers feature.

## Slack Webhook Architecture Overview

### Slack App-Based Events API

Slack uses a **Slack App with Events API** approach that is similar to GitHub Apps but with some key differences:

- **Single Request URL**: Slack Apps can specify one webhook URL for all events
- **Event Subscriptions**: Configure which events to receive at the app level
- **Workspace Installation**: Apps are installed per workspace, not per repository
- **OAuth Scopes**: Event access is controlled by OAuth permission scopes
- **Bot User Integration**: Events can be received on behalf of bot users

### Current Slack Integration

**Your current Slack integration DOES use webhooks** via the Events API:

```go
// services/integrations/slack_bot/webhook/main.go
r.HandleFunc("/slack/events", slackWebHandler.HandleEvent)
r.HandleFunc("/slack/commands", slackWebHandler.HandleCommand)
r.Handle<PERSON>un<PERSON>("/slack/interactivity", slackWebHandler.HandleInteractivityEvent)
```

**Current architecture**:
- **Events API webhooks** for real-time Slack events
- **OAuth authentication** for workspace installation
- **Webhook tenant mapping** for routing events to tenants
- **Push-based** event handling with pubsub routing

## Slack Webhook Registration Process

### How Slack Events API Works

Slack webhooks are registered **per Slack App** with workspace-level installations:

#### App-Level Configuration (Done by Augment)
1. **Slack App Creation**: Augment creates and owns the Slack App
2. **Request URL Configuration**: Set webhook URL in Slack App settings
3. **Event Subscriptions**: Configure which events the app receives
4. **OAuth Scopes**: Define required permissions for the app
5. **Bot User Setup**: Configure bot user capabilities

#### Workspace Installation (Done by Users)
1. **App Installation**: Users install Augment's Slack App in their workspace
2. **OAuth Authorization**: Users grant requested permissions
3. **Webhook Mapping**: System creates tenant mapping for the workspace

### Current Slack App Configuration

Based on the codebase, your Slack app is configured with:

```go
// services/integrations/slack_bot/webhook/slack_web_handler.go
func (h *SlackWebHandler) handleEventHelper(w http.ResponseWriter, r *http.Request) {
    eventsAPIEvent, err := slackevents.ParseEvent(json.RawMessage(body), slackevents.OptionNoVerifyToken())
    // Handle different event types
    if eventsAPIEvent.Type == slackevents.CallbackEvent {
        innerEvent := eventsAPIEvent.InnerEvent
        err := h.eventHandler.HandleEvent(ctx, eventsAPIEvent.TeamID,
            eventsAPIEvent.EnterpriseID, &innerEvent)
    }
}
```

### Slack Event Types Currently Supported

Your system currently handles these Slack events:

```go
// services/integrations/slack_bot/slack_event.proto
message SlackEvent {
  oneof event {
    AppMentionEvent app_mention = 3;           // Bot mentioned in channel
    MessageEvent message = 4;                  // Message sent in channel
    ReactionAddedEvent reaction_added = 5;     // Reaction added to message
    MemberJoinedChannelEvent member_joined_channel = 6; // User joins channel
    AppUninstalledEvent app_uninstalled = 7;   // App uninstalled
    AppHomeOpenedEvent app_home_opened = 8;    // Bot home tab opened
  }
}
```

### Slack Events API Payload Structure

Slack webhook payloads follow this structure:

```json
{
    "type": "event_callback",
    "token": "XXYYZZ",
    "team_id": "T123ABC456",
    "api_app_id": "A123ABC456",
    "event": {
        "type": "app_mention",
        "user": "U123ABC456",
        "text": "@augment help me with this code",
        "ts": "1234567890.123456",
        "channel": "C123ABC456",
        "event_ts": "1234567890.123456"
    },
    "event_context": "EC123ABC456",
    "event_id": "Ev123ABC456",
    "event_time": 1234567890,
    "authorizations": [
        {
            "enterprise_id": "E123ABC456",
            "team_id": "T123ABC456",
            "user_id": "U123ABC456",
            "is_bot": false,
            "is_enterprise_install": false
        }
    ],
    "is_ext_shared_channel": false,
    "context_team_id": "T123ABC456"
}
```

## Comparison: Slack vs Other Platform Webhooks

| Aspect | GitHub Webhooks | Linear Webhooks | JIRA Webhooks | Slack Webhooks |
|--------|-----------------|-----------------|---------------|----------------|
| **Registration Model** | App-level (one per GitHub App) | Workspace-level | Instance-level | App-level (one per Slack App) |
| **Event Configuration** | Static at app level | Dynamic per webhook | Dynamic per webhook | Static at app level |
| **User Setup Required** | Install GitHub App only | Configure webhook URL + events | Configure webhook URL + events + JQL | Install Slack App only |
| **Management** | Centralized by Augment | Distributed per workspace | Distributed per instance | Centralized by Augment |
| **API Registration** | Not supported for events | Fully supported via GraphQL | Fully supported via REST | Not needed (app-level config) |
| **Event Filtering** | All-or-nothing per event type | Configurable per webhook | Advanced JQL filtering | OAuth scope-based |
| **Authentication** | GitHub App installation | OAuth + admin permissions | OAuth + admin permissions | OAuth + workspace permissions |
| **Webhook Lifecycle** | Static (manual app updates) | Dynamic (create/update/delete) | Dynamic (create/update/delete) | Static (manual app updates) |
| **Current Implementation** | ✅ Implemented | ❌ Not implemented | ❌ Not implemented | ✅ **Implemented** |

## Slack Webhook Advantages for Triggers

### 1. Already Implemented
- **Existing infrastructure** for webhook handling
- **Proven event routing** to tenant-specific pubsub topics
- **OAuth flow** already working
- **Webhook tenant mapping** system in place

### 2. Rich Event Types Available

Slack offers extensive event types for triggers:

#### Message Events
- `message.channels` - Messages in public channels
- `message.groups` - Messages in private channels
- `message.im` - Direct messages to bot
- `message.mpim` - Messages in group DMs
- `app_mention` - Bot mentioned in any channel

#### User Activity Events
- `reaction_added` / `reaction_removed` - Emoji reactions
- `member_joined_channel` / `member_left_channel` - Channel membership
- `user_change` - User profile updates
- `presence_change` - User online/offline status

#### Channel Events
- `channel_created` / `channel_deleted` - Channel lifecycle
- `channel_rename` / `channel_archive` - Channel changes
- `channel_shared` / `channel_unshared` - Slack Connect events

#### File Events
- `file_created` / `file_deleted` - File uploads/deletions
- `file_shared` / `file_unshared` - File sharing changes
- `file_public` / `file_private` - File visibility changes

#### App Events
- `app_uninstalled` - App removed from workspace
- `tokens_revoked` - OAuth tokens revoked

### 3. OAuth Scope-Based Filtering

Slack's permission model provides natural event filtering:

```go
// Example: Only receive file events if granted files:read scope
// Only receive message events if granted channels:read scope
```

### 4. Enterprise Grid Support

Slack supports Enterprise Grid organizations with:
- **Cross-workspace events** for large organizations
- **Shared channel events** across workspaces
- **Enterprise-level installations**

## Current Slack Integration Architecture

### Webhook Flow

```
Slack Workspace → Global Webhook → Event Router → Tenant Lookup → Pubsub → Processor
```

### Detailed Flow

1. **Event Generation**: User performs action in Slack (mentions bot, sends message, etc.)
2. **Webhook Delivery**: Slack sends POST request to Augment's webhook URL
3. **Signature Validation**: Webhook handler validates Slack's signing secret
4. **Tenant Lookup**: System maps team ID to Augment tenant
5. **Event Routing**: Event published to tenant-specific pubsub topic
6. **Event Processing**: Slack processor handles the event

### Current Implementation

```go
// services/integrations/slack_bot/webhook/slack_handler.go
func (h *SlackEventHandlerImpl) HandleEvent(ctx context.Context,
    teamId string, enterpriseId string, event *slackevents.EventsAPIInnerEvent,
) error {
    tenant, err := h.tenantLookup.LookupTenant(enterpriseId, teamId)
    if err != nil {
        return err
    }

    // Create event proto and publish to tenant's pubsub topic
    err = h.pubSubClient.PublishWithOrderingKey(ctx, tenant.Namespace, eventData, orderingKey)
    return err
}
```

### Webhook Tenant Mapping

```go
// services/integrations/slack_bot/processor/server/server.go
_, err = s.webhookTenantMappingResource.Update(ctx, &webhookmapping.WebhookTenantMappingSpec{
    WebhookType:  "slack",
    WebhookValue: fmt.Sprintf("%s-%s", accessToken.Enterprise.Id, accessToken.Team.Id),
    TenantID:     tenantID,
})
```

## Implications for Triggers Feature

### Slack Triggers Implementation Effort

**Implementation Effort**: ✅ **Low** (infrastructure already exists)

### Advantages for Triggers

1. **Minimal Additional Infrastructure**: Webhook handling already implemented
2. **Rich Event Types**: Comprehensive coverage of Slack activities
3. **Proven Scalability**: Already handling production Slack events
4. **OAuth Integration**: User authorization flow already working
5. **Enterprise Support**: Works with Enterprise Grid organizations

### Required Changes for Triggers

#### 1. Expand Event Subscriptions
Currently limited events - would need to add:
- More message events (`message.channels`, `message.groups`)
- Channel events (`channel_created`, `channel_archive`)
- File events (`file_created`, `file_shared`)
- User events (`user_change`, `presence_change`)

#### 2. Trigger Evaluation Integration
```go
// Add trigger evaluation to existing event handler
func (h *SlackEventHandlerImpl) HandleEvent(ctx context.Context, ...) error {
    // Existing tenant lookup and event creation...

    // NEW: Evaluate triggers for this event
    triggers := h.triggerService.EvaluateSlackTriggers(tenant.TenantID, event)
    for _, trigger := range triggers {
        h.spawnRemoteAgent(trigger, event)
    }

    // Existing pubsub publishing...
}
```

#### 3. Slack-Specific Trigger Conditions
- **Channel-based triggers**: "When message posted in #engineering"
- **User-based triggers**: "When @john mentions the bot"
- **Reaction-based triggers**: "When someone reacts with :bug:"
- **File-based triggers**: "When file uploaded to #design"

### Slack Event Filtering Capabilities

Unlike other platforms, Slack filtering is primarily scope-based:

#### OAuth Scope Filtering
```json
{
    "scopes": [
        "channels:read",    // Receive public channel events
        "groups:read",      // Receive private channel events
        "files:read",       // Receive file events
        "reactions:read",   // Receive reaction events
        "users:read"        // Receive user events
    ]
}
```

#### Channel Membership Filtering
- Bot only receives events from channels it's a member of
- Natural privacy boundaries based on channel membership
- Enterprise Grid shared channel support

### Slack Triggers Use Cases

#### Development Team Triggers
- **Code Review Alerts**: "When PR mentioned in #engineering, create code review agent"
- **Bug Reports**: "When :bug: reaction added, create debugging agent"
- **Deploy Notifications**: "When 'deployed' mentioned in #releases, create monitoring agent"

#### Project Management Triggers
- **Status Updates**: "When project status shared in #updates, create summary agent"
- **Meeting Notes**: "When file uploaded to #meetings, create action items agent"
- **Escalations**: "When 'urgent' mentioned, create escalation agent"

#### Customer Support Triggers
- **Issue Tracking**: "When customer issue mentioned, create support agent"
- **Feedback Collection**: "When feedback shared, create analysis agent"

## Security Considerations

### Webhook Validation

Slack provides robust webhook validation:

```go
// services/integrations/slack_bot/webhook/slack_web_handler.go
func (h *SlackWebHandler) VerifyRequest(r *http.Request) ([]byte, error) {
    // Slack signature validation using signing secret
    signature := r.Header.Get("X-Slack-Signature")
    timestamp := r.Header.Get("X-Slack-Request-Timestamp")

    // Validate signature and timestamp
    return body, nil
}
```

### Tenant Isolation

- **Team ID mapping**: Events routed to correct tenant based on Slack team ID
- **Enterprise ID support**: Handles Enterprise Grid organizations
- **OAuth scope enforcement**: Only receive events for granted permissions

### Privacy and Permissions

- **Channel membership**: Bot only sees events from channels it's in
- **Private channel protection**: Requires explicit invitation to private channels
- **DM privacy**: Direct messages only if user initiates conversation with bot

## Migration Path for Triggers

### Phase 1: Extend Current Implementation
1. **Expand event subscriptions** in Slack App settings
2. **Add trigger evaluation** to existing event handler
3. **Implement Slack-specific trigger conditions**
4. **Test with existing Slack integration**

### Phase 2: Advanced Trigger Features
1. **Channel-specific triggers** with membership validation
2. **User mention triggers** with @user parsing
3. **File type triggers** with content analysis
4. **Reaction-based triggers** with emoji matching

### Phase 3: Enterprise Features
1. **Enterprise Grid support** for cross-workspace triggers
2. **Shared channel triggers** for Slack Connect
3. **Advanced permission handling** for complex organizations

## Monitoring and Reliability

### Current Monitoring

```go
// services/integrations/slack_bot/webhook/slack_web_handler.go
var eventWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
    Name: "slack_event_webhook_request_total",
    Help: "Counter of handled Slack event webhook requests",
}, []string{"status_code"})
```

### Slack Rate Limiting

- **Events API rate limit**: 30,000 events per workspace per hour
- **Automatic rate limiting**: Slack sends `app_rate_limited` events when exceeded
- **Graceful degradation**: System handles rate limiting notifications

### Error Handling

Slack provides robust error handling:
- **3-second response timeout**
- **Exponential backoff retries** (3 attempts)
- **Failure rate monitoring** (95% failure rate triggers disabling)
- **Automatic re-enabling** when issues resolved

## Implementation Effort Comparison (Updated)

| Approach | GitHub | Linear | JIRA Cloud | Slack |
|----------|---------|---------|------------|-------|
| **User Setup** | ✅ Simple | ⚠️ Medium | ⚠️ Medium | ✅ **Simple** |
| **Event Filtering** | ❌ Basic | ⚠️ Medium | ✅ Advanced | ⚠️ **Scope-based** |
| **API Complexity** | ❌ Limited | ✅ Single GraphQL | ✅ Single REST | ✅ **Events API** |
| **Auth Complexity** | ✅ Simple | ⚠️ OAuth | ⚠️ OAuth | ✅ **Simple OAuth** |
| **Infrastructure** | ✅ Existing | ❌ New needed | ❌ New needed | ✅ **Existing** |
| **Implementation Effort** | ✅ Low | ⚠️ Medium | ⚠️ Medium | ✅ **Lowest** |
| **Market Coverage** | ✅ ~90% | ⚠️ ~60% | ⚠️ ~70% | ✅ **~95%** |

## Summary

**Key Advantages of Slack for Triggers**:
- **Lowest implementation effort** - infrastructure already exists
- **Highest market coverage** - nearly universal Slack adoption
- **Rich event ecosystem** - comprehensive activity coverage
- **Simple user experience** - just install the Slack app
- **Enterprise-ready** - supports large organizations
- **Proven scalability** - already handling production events

**Recommended Approach**:
- **Start with Slack triggers** as the first triggers implementation
- **Leverage existing infrastructure** for fastest time to market
- **Expand event subscriptions** to cover trigger use cases
- **Use as foundation** for other platform integrations

**Slack Implementation Requirements**:
- Expand Slack App event subscriptions
- Add trigger evaluation to existing event handler
- Implement Slack-specific trigger conditions
- Extend current monitoring and error handling

**Benefits of Slack-First Approach**:
- **Immediate value** with minimal development effort
- **Validates triggers concept** with real users
- **Establishes patterns** for other platform integrations
- **Leverages existing user base** already using Slack integration

Slack provides the ideal foundation for launching the triggers feature, offering the lowest implementation effort while delivering immediate value to the largest user base.
