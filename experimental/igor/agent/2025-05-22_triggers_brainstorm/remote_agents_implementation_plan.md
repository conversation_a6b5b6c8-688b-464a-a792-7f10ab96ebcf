# Remote Agent Triggers Implementation Plan

## Overview

This document outlines the incremental implementation plan for the Remote Agent Triggers feature, starting from a basic prototype to a fully-featured, shippable product. The implementation is designed to build on existing infrastructure while providing immediate value to users.

## ✅ **COMPLETED WORK (December 2024)**

### GitHub Webhook CLI Tool ✅
**Status**: **COMPLETE** - End-to-end testing tool for GitHub webhook events

**What was built**:
- **GitHub Webhook CLI Tool** (`experimental/igor/agent/agent-tools/github_webhook_cli.py`)
  - ✅ Fetches real PR data from GitHub API
  - ✅ Generates realistic webhook events for different PR actions (opened, closed, ready_for_review, etc.)
  - ✅ Posts events to webhook endpoints (both fake and real endpoints)
  - ✅ Supports both pull request and push events
  - ✅ Mock data fallback when no GitHub token available
  - ✅ Dry-run mode for testing event generation
  - ✅ URL parsing for GitHub PR URLs
  - ✅ Integration with existing webhook infrastructure

**End-to-End Validation** ✅:
- ✅ **Webhook Event Generation**: CLI tool generates realistic GitHub webhook events
- ✅ **Webhook Reception**: Events received and processed by GitHub webhook listener
- ✅ **Pubsub Integration**: Events published to pubsub system correctly
- ✅ **Remote Agents Processing**: Service consumes events and performs trigger lookups
- ✅ **Trigger Matching**: System correctly identifies and executes matching triggers
- ✅ **Agent Creation**: Remote agents successfully created in response to webhook events
- ✅ **Complete Flow**: Webhook → Pubsub → Remote Agents → Trigger Evaluation → Agent Spawning

**Testing Infrastructure** ✅:
- ✅ **Trigger CLI Tool**: Create, list, delete, and manage triggers
- ✅ **Remote Agent CLI Tool**: List and delete spawned agents
- ✅ **Dev Environment Testing**: Full end-to-end testing in dev deployment
- ✅ **Event Validation**: Confirmed events match GitHub webhook format
- ✅ **Cleanup Tools**: Automated cleanup of test artifacts

**Key Insights from Implementation**:
- ✅ **Existing Infrastructure Works**: Current webhook → pubsub → remote agents flow handles triggers correctly
- ✅ **Trigger System Functional**: Trigger creation, matching, and agent spawning works end-to-end
- ✅ **BigTable Storage**: Trigger storage and retrieval working in production
- ✅ **Event Processing**: GitHub webhook events properly processed and matched against trigger conditions
- ✅ **Agent Lifecycle**: Agents created by triggers follow same lifecycle as manually created agents

**Documentation** ✅:
- ✅ **Comprehensive README**: Updated with GitHub Webhook CLI tool documentation
- ✅ **Usage Examples**: Multiple examples for different event types and scenarios
- ✅ **Integration Guide**: How to use with existing testing infrastructure

## 🚀 **NEXT STEPS & OPTIONS**

Based on the completed work, here are the recommended next steps and options for continuing the Remote Agent Triggers implementation:

### **Option 1: Complete Phase 1 - IDE Integration (Recommended)**
**Timeline**: 2-3 weeks
**Goal**: Make triggers accessible to end users through the IDE

**What to build**:
- ✅ **Backend is ready** - All APIs and infrastructure exist
- ❌ **IDE Extension Integration** - Add triggers panel to VS Code extension
- ❌ **Trigger Management UI** - Create, edit, delete triggers through IDE
- ❌ **Execution Monitoring** - View trigger history and spawned agents
- ❌ **Template System** - Pre-built trigger configurations

**Benefits**:
- **Immediate user value** - Developers can start using triggers
- **Complete Phase 1** - Achieve all original Phase 1 goals
- **User feedback** - Get real usage data to guide future development
- **Low risk** - Building on proven backend infrastructure

### **Option 2: Enhanced Testing & Developer Experience**
**Timeline**: 1-2 weeks
**Goal**: Improve the developer experience for trigger development and testing

**What to build**:
- ✅ **GitHub Webhook CLI** - Already complete
- ❌ **Trigger Testing Framework** - Automated testing for trigger conditions
- ❌ **Event Simulation** - Generate test events for different scenarios
- ❌ **Trigger Debugging** - Better logging and debugging tools
- ❌ **Documentation** - Comprehensive guides for trigger development

**Benefits**:
- **Better developer experience** - Easier to develop and test triggers
- **Quality assurance** - Reduce bugs in trigger logic
- **Team enablement** - Help other teams adopt triggers
- **Foundation for scaling** - Better tools for managing many triggers

### **Option 3: Additional Event Sources**
**Timeline**: 2-3 weeks
**Goal**: Expand beyond GitHub to other event sources

**What to build**:
- ❌ **Linear Integration** - Support Linear issue events
- ❌ **JIRA Integration** - Support JIRA ticket events
- ❌ **Slack Integration** - Support Slack message events
- ❌ **Custom Webhooks** - Generic webhook support
- ❌ **Event Source Framework** - Pluggable event source architecture

**Benefits**:
- **Broader applicability** - Triggers work with more tools
- **Increased adoption** - More use cases for different teams
- **Platform approach** - Position triggers as a general automation platform
- **Competitive advantage** - Unique integration capabilities

### **Option 4: Advanced Trigger Features**
**Timeline**: 3-4 weeks
**Goal**: Add sophisticated trigger capabilities

**What to build**:
- ❌ **Complex Conditions** - AND/OR logic, expressions
- ❌ **Multi-step Workflows** - Chain multiple agents together
- ❌ **Scheduled Triggers** - Time-based trigger execution
- ❌ **Conditional Logic** - Dynamic behavior based on event data
- ❌ **Trigger Analytics** - Usage metrics and optimization

**Benefits**:
- **Power user features** - Support complex automation scenarios
- **Enterprise readiness** - Advanced features for large teams
- **Differentiation** - Unique capabilities vs competitors
- **Scalability** - Handle complex organizational workflows

## Implementation Phases

### Phase 1: Core Prototype (4-6 weeks)

**Goal**: Basic trigger functionality accessible through the IDE extension with manual trigger configuration.

**Status**: **PARTIALLY COMPLETE** - Backend infrastructure exists, CLI tools working, IDE integration needed

#### 1.1 Backend Foundation (2 weeks) ✅ **COMPLETE**

**Remote Agents Service Extension**:
```
services/remote_agents/
├── proto/
│   └── remote_agents.proto     # Extended with trigger RPCs
├── server/
│   ├── agent_service.py        # Existing agent management
│   ├── trigger_service.py      # New trigger management
│   ├── trigger_handler.py      # Event processing and condition evaluation
│   └── storage.py             # Extended for trigger storage
└── client/
    └── client.py              # Extended with trigger client methods
```

**Key Components** ✅:
- ✅ **Trigger Storage**: BigTable-based storage for user triggers and execution history (integrated with existing agent storage)
- ✅ **Event Processing**: Basic event router that can receive GitHub webhooks and evaluate trigger conditions
- ✅ **Agent Spawning**: Direct integration with existing agent creation logic
- ✅ **Unified API**: Extended remote agents API with trigger CRUD operations
- ✅ **CLI Tools**: Comprehensive CLI tools for trigger and agent management

**BigTable Schema**:
```python
# BigTable table design for trigger storage
# Table: user_triggers
# Row key pattern: user_id#trigger_id
# Column families: metadata, config, status

"""
BigTable Schema Design:

Table: user_triggers
Row Key: {user_id}#{trigger_id}
Column Families:
  - metadata: Basic trigger information
  - config: Configuration data
  - status: Status and timestamps

Example row:
Row Key: "user123#pr-review-assistant"
metadata:name = "PR Review Assistant"
metadata:description = "Automatically review PRs assigned to me"
metadata:event_source = "github"
metadata:event_type = "pull_request.review_requested"
metadata:enabled = "true"
config:conditions = '{"assignee": "@me", "repository": "augmentcode/augment"}'
config:agent_config = '{"prompt": "...", "tools": ["github-api"], "model": "claude-3-sonnet"}'
status:created_at = "2024-01-01T00:00:00Z"
status:updated_at = "2024-01-01T00:00:00Z"

Table: trigger_executions
Row Key: {user_id}#{trigger_id}#{execution_timestamp_reverse}#{execution_id}
Column Families:
  - execution: Execution details
  - agent: Agent information
  - status: Status tracking

Example row:
Row Key: "user123#pr-review-assistant#9999999999999-1704067200#exec456"
execution:event_id = "github_event_789"
execution:started_at = "2024-01-01T00:00:00Z"
execution:completed_at = "2024-01-01T00:05:30Z"
agent:remote_agent_id = "agent_abc123"
status:status = "completed"
status:error_message = ""

Table: trigger_events (for deduplication and replay)
Row Key: {event_source}#{event_type}#{event_id}
Column Families:
  - event: Event data
  - processing: Processing status

Example row:
Row Key: "github#pull_request.review_requested#event_789"
event:payload = '{"pull_request": {...}, "assignee": {...}}'
event:received_at = "2024-01-01T00:00:00Z"
processing:processed = "true"
processing:trigger_matches = "user123#pr-review-assistant,user456#code-reviewer"
"""

# BigTable client configuration
from google.cloud import bigtable
from google.cloud.bigtable import column_family

class TriggerStorage:
    def __init__(self, project_id: str, instance_id: str):
        self.client = bigtable.Client(project=project_id, admin=True)
        self.instance = self.client.instance(instance_id)

        # Table references
        self.user_triggers_table = self.instance.table('user_triggers')
        self.trigger_executions_table = self.instance.table('trigger_executions')
        self.trigger_events_table = self.instance.table('trigger_events')

    def create_tables(self):
        """Create BigTable tables with appropriate column families"""

        # User triggers table
        if not self.user_triggers_table.exists():
            self.user_triggers_table.create()

            # Column families for user triggers
            metadata_cf = column_family.MaxVersionsGCRule(1)
            config_cf = column_family.MaxVersionsGCRule(1)
            status_cf = column_family.MaxVersionsGCRule(1)

            self.user_triggers_table.column_family('metadata', metadata_cf)
            self.user_triggers_table.column_family('config', config_cf)
            self.user_triggers_table.column_family('status', status_cf)

        # Trigger executions table
        if not self.trigger_executions_table.exists():
            self.trigger_executions_table.create()

            # Column families for executions (keep more versions for history)
            execution_cf = column_family.MaxVersionsGCRule(10)
            agent_cf = column_family.MaxVersionsGCRule(10)
            status_cf = column_family.MaxVersionsGCRule(10)

            self.trigger_executions_table.column_family('execution', execution_cf)
            self.trigger_executions_table.column_family('agent', agent_cf)
            self.trigger_executions_table.column_family('status', status_cf)

        # Trigger events table (for deduplication)
        if not self.trigger_events_table.exists():
            self.trigger_events_table.create()

            # TTL for events (keep for 30 days)
            event_cf = column_family.MaxAgeGCRule(datetime.timedelta(days=30))
            processing_cf = column_family.MaxAgeGCRule(datetime.timedelta(days=30))

            self.trigger_events_table.column_family('event', event_cf)
            self.trigger_events_table.column_family('processing', processing_cf)
```

**BigTable Data Access Layer**:
```python
import json
import uuid
from datetime import datetime
from typing import List, Optional, Dict, Any

class TriggerDataAccess:
    def __init__(self, storage: TriggerStorage):
        self.storage = storage

    def create_trigger(self, user_id: str, trigger_config: Dict[str, Any]) -> str:
        """Create a new trigger configuration"""
        trigger_id = str(uuid.uuid4())
        row_key = f"{user_id}#{trigger_id}"

        row = self.storage.user_triggers_table.direct_row(row_key)

        # Metadata columns
        row.set_cell('metadata', 'name', trigger_config['name'])
        row.set_cell('metadata', 'description', trigger_config.get('description', ''))
        row.set_cell('metadata', 'event_source', trigger_config['event_source'])
        row.set_cell('metadata', 'event_type', trigger_config['event_type'])
        row.set_cell('metadata', 'enabled', str(trigger_config.get('enabled', True)))

        # Config columns (JSON serialized)
        row.set_cell('config', 'conditions', json.dumps(trigger_config.get('conditions', {})))
        row.set_cell('config', 'agent_config', json.dumps(trigger_config['agent_config']))

        # Status columns
        now = datetime.utcnow().isoformat()
        row.set_cell('status', 'created_at', now)
        row.set_cell('status', 'updated_at', now)

        row.commit()
        return trigger_id

    def get_user_triggers(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all triggers for a user"""
        start_key = f"{user_id}#"
        end_key = f"{user_id}#\xff"

        rows = self.storage.user_triggers_table.read_rows(
            start_key=start_key.encode(),
            end_key=end_key.encode()
        )

        triggers = []
        for row in rows:
            trigger_data = self._parse_trigger_row(row)
            triggers.append(trigger_data)

        return triggers

    def get_trigger(self, user_id: str, trigger_id: str) -> Optional[Dict[str, Any]]:
        """Get a specific trigger"""
        row_key = f"{user_id}#{trigger_id}"
        row = self.storage.user_triggers_table.read_row(row_key.encode())

        if row is None:
            return None

        return self._parse_trigger_row(row)

    def update_trigger(self, user_id: str, trigger_id: str, updates: Dict[str, Any]) -> bool:
        """Update trigger configuration"""
        row_key = f"{user_id}#{trigger_id}"
        row = self.storage.user_triggers_table.direct_row(row_key)

        # Update metadata if provided
        if 'name' in updates:
            row.set_cell('metadata', 'name', updates['name'])
        if 'description' in updates:
            row.set_cell('metadata', 'description', updates['description'])
        if 'enabled' in updates:
            row.set_cell('metadata', 'enabled', str(updates['enabled']))

        # Update config if provided
        if 'conditions' in updates:
            row.set_cell('config', 'conditions', json.dumps(updates['conditions']))
        if 'agent_config' in updates:
            row.set_cell('config', 'agent_config', json.dumps(updates['agent_config']))

        # Update timestamp
        row.set_cell('status', 'updated_at', datetime.utcnow().isoformat())

        row.commit()
        return True

    def delete_trigger(self, user_id: str, trigger_id: str) -> bool:
        """Delete a trigger"""
        row_key = f"{user_id}#{trigger_id}"
        row = self.storage.user_triggers_table.row(row_key.encode())
        row.delete()
        row.commit()
        return True

    def record_execution(self, user_id: str, trigger_id: str, execution_data: Dict[str, Any]) -> str:
        """Record a trigger execution"""
        execution_id = str(uuid.uuid4())

        # Use reverse timestamp for chronological ordering (newest first)
        timestamp = int(datetime.utcnow().timestamp())
        reverse_timestamp = 9999999999999 - timestamp

        row_key = f"{user_id}#{trigger_id}#{reverse_timestamp}#{execution_id}"
        row = self.storage.trigger_executions_table.direct_row(row_key)

        # Execution details
        row.set_cell('execution', 'event_id', execution_data['event_id'])
        row.set_cell('execution', 'started_at', execution_data['started_at'])
        if 'completed_at' in execution_data:
            row.set_cell('execution', 'completed_at', execution_data['completed_at'])

        # Agent information
        if 'remote_agent_id' in execution_data:
            row.set_cell('agent', 'remote_agent_id', execution_data['remote_agent_id'])

        # Status
        row.set_cell('status', 'status', execution_data['status'])
        if 'error_message' in execution_data:
            row.set_cell('status', 'error_message', execution_data['error_message'])

        row.commit()
        return execution_id

    def get_trigger_executions(self, user_id: str, trigger_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get execution history for a trigger"""
        start_key = f"{user_id}#{trigger_id}#"
        end_key = f"{user_id}#{trigger_id}#\xff"

        rows = self.storage.trigger_executions_table.read_rows(
            start_key=start_key.encode(),
            end_key=end_key.encode(),
            limit=limit
        )

        executions = []
        for row in rows:
            execution_data = self._parse_execution_row(row)
            executions.append(execution_data)

        return executions

    def _parse_trigger_row(self, row) -> Dict[str, Any]:
        """Parse BigTable row into trigger dictionary"""
        cells = row.cells

        # Extract trigger_id from row key
        row_key = row.row_key.decode()
        _, trigger_id = row_key.split('#', 1)

        trigger = {'id': trigger_id}

        # Parse metadata
        if 'metadata' in cells:
            for column, cell_list in cells['metadata'].items():
                if cell_list:
                    trigger[column.decode()] = cell_list[0].value.decode()

        # Parse config (JSON)
        if 'config' in cells:
            for column, cell_list in cells['config'].items():
                if cell_list:
                    column_name = column.decode()
                    trigger[column_name] = json.loads(cell_list[0].value.decode())

        # Parse status
        if 'status' in cells:
            for column, cell_list in cells['status'].items():
                if cell_list:
                    trigger[column.decode()] = cell_list[0].value.decode()

        return trigger

    def _parse_execution_row(self, row) -> Dict[str, Any]:
        """Parse BigTable execution row into execution dictionary"""
        cells = row.cells

        # Extract execution_id from row key
        row_key = row.row_key.decode()
        parts = row_key.split('#')
        execution_id = parts[-1]

        execution = {'id': execution_id}

        # Parse all column families
        for family_name, family_cells in cells.items():
            for column, cell_list in family_cells.items():
                if cell_list:
                    execution[column.decode()] = cell_list[0].value.decode()

        return execution
```

**API Endpoints**:
```python
# REST API endpoints using BigTable data access layer
POST /api/triggers                    # Create trigger
GET /api/triggers                     # List user triggers
PUT /api/triggers/{id}                # Update trigger
DELETE /api/triggers/{id}             # Delete trigger
GET /api/triggers/{id}/executions     # Get execution history

# Row key format: RemoteAgentTrigger#{user_id}#{trigger_id}
# Example: "RemoteAgentTrigger#user123#trigger-abc-def"

COLUMN_FAMILIES = {
    'metadata': {
        'name': str,           # Trigger name
        'description': str,    # Trigger description
        'enabled': bool,       # Whether trigger is active
        'created_at': int,     # Unix timestamp
        'updated_at': int,     # Unix timestamp
    },
    'config': {
        'event_source': str,   # 'github', 'linear', etc.
        'event_type': str,     # 'pull_request.review_requested', etc.
        'conditions': str,     # JSON-encoded event filtering conditions
        'agent_config': str,   # JSON-encoded prompt, tools, workspace setup
    },
    'status': {
        'last_execution': int,     # Unix timestamp of last execution
        'execution_count': int,    # Total number of executions
        'last_status': str,        # Last execution status
    }
}

# Table: trigger_executions
# Row key: user_id#trigger_id#execution_timestamp (reverse timestamp for recent-first ordering)
# Column families:
#   - execution: Execution details
#   - agent: Agent information
#   - timing: Execution timing data

# Row key format: RemoteAgentTriggerExecution#{user_id}#{trigger_id}#{reverse_timestamp}#{execution_id}
# Example: "RemoteAgentTriggerExecution#user123#trigger-abc#9223372036854775807-1640995200#exec-xyz"

EXECUTION_COLUMN_FAMILIES = {
    'execution': {
        'event_id': str,           # Original event identifier
        'status': str,             # 'pending', 'running', 'completed', 'failed'
        'error_message': str,      # Error details if failed
    },
    'agent': {
        'remote_agent_id': str,    # Reference to spawned agent
        'agent_status': str,       # Agent execution status
    },
    'timing': {
        'started_at': int,         # Unix timestamp
        'completed_at': int,       # Unix timestamp
        'duration_ms': int,        # Execution duration in milliseconds
    }
}
```

**BigTable Storage Implementation**:
```python
# storage.py - BigTable storage layer
from google.cloud import bigtable
import json
import time
from typing import List, Optional, Dict, Any

class TriggerStorage:
    def __init__(self, project_id: str, instance_id: str):
        self.client = bigtable.Client(project=project_id)
        self.instance = self.client.instance(instance_id)
        self.triggers_table = self.instance.table('user_triggers')
        self.executions_table = self.instance.table('trigger_executions')

    def create_trigger(self, user_id: str, trigger_config: Dict[str, Any]) -> str:
        """Create a new trigger configuration"""
        trigger_id = generate_trigger_id()
        row_key = f"RemoteAgentTrigger#{user_id}#{trigger_id}"

        row = self.triggers_table.direct_row(row_key)
        timestamp = int(time.time() * 1000000)  # Microseconds

        # Metadata family
        row.set_cell('metadata', 'name', trigger_config['name'], timestamp)
        row.set_cell('metadata', 'description', trigger_config.get('description', ''), timestamp)
        row.set_cell('metadata', 'enabled', str(trigger_config.get('enabled', True)), timestamp)
        row.set_cell('metadata', 'created_at', str(timestamp), timestamp)
        row.set_cell('metadata', 'updated_at', str(timestamp), timestamp)

        # Config family
        row.set_cell('config', 'event_source', trigger_config['event_source'], timestamp)
        row.set_cell('config', 'event_type', trigger_config['event_type'], timestamp)
        row.set_cell('config', 'conditions', json.dumps(trigger_config.get('conditions', {})), timestamp)
        row.set_cell('config', 'agent_config', json.dumps(trigger_config['agent_config']), timestamp)

        # Status family
        row.set_cell('status', 'execution_count', '0', timestamp)
        row.set_cell('status', 'last_status', 'created', timestamp)

        row.commit()
        return trigger_id

    def get_user_triggers(self, user_id: str) -> List[Dict[str, Any]]:
        """Get all triggers for a user"""
        prefix = f"RemoteAgentTrigger#{user_id}#"
        rows = self.triggers_table.read_rows(row_set=bigtable.row_set.RowSet())
        rows.row_filter = bigtable.row_filters.RowKeyRegexFilter(f"^{prefix}".encode())

        triggers = []
        for row in rows:
            trigger = self._parse_trigger_row(row)
            triggers.append(trigger)

        return triggers

    def record_execution(self, user_id: str, trigger_id: str, execution_data: Dict[str, Any]) -> str:
        """Record a trigger execution"""
        execution_id = generate_execution_id()
        reverse_timestamp = 9223372036854775807 - int(time.time())  # For recent-first ordering
        row_key = f"RemoteAgentTriggerExecution#{user_id}#{trigger_id}#{reverse_timestamp}#{execution_id}"

        row = self.executions_table.direct_row(row_key)
        timestamp = int(time.time() * 1000000)

        # Execution family
        row.set_cell('execution', 'event_id', execution_data['event_id'], timestamp)
        row.set_cell('execution', 'status', execution_data['status'], timestamp)
        if 'error_message' in execution_data:
            row.set_cell('execution', 'error_message', execution_data['error_message'], timestamp)

        # Agent family
        if 'remote_agent_id' in execution_data:
            row.set_cell('agent', 'remote_agent_id', execution_data['remote_agent_id'], timestamp)

        # Timing family
        row.set_cell('timing', 'started_at', str(execution_data['started_at']), timestamp)
        if 'completed_at' in execution_data:
            row.set_cell('timing', 'completed_at', str(execution_data['completed_at']), timestamp)
            duration = execution_data['completed_at'] - execution_data['started_at']
            row.set_cell('timing', 'duration_ms', str(duration * 1000), timestamp)

        row.commit()
        return execution_id

    def get_execution_history(self, user_id: str, trigger_id: str, limit: int = 50) -> List[Dict[str, Any]]:
        """Get execution history for a trigger"""
        prefix = f"RemoteAgentTriggerExecution#{user_id}#{trigger_id}#"
        rows = self.executions_table.read_rows(
            row_set=bigtable.row_set.RowSet(),
            limit=limit
        )
        rows.row_filter = bigtable.row_filters.RowKeyRegexFilter(f"^{prefix}".encode())

        executions = []
        for row in rows:
            execution = self._parse_execution_row(row)
            executions.append(execution)

        return executions
```

**Extended API Endpoints**:
```python
# Existing remote agents endpoints
POST /api/remote-agents               # Create agent (existing)
GET /api/remote-agents                # List agents (existing)
GET /api/remote-agents/{id}           # Get agent details (existing)

# New trigger management endpoints
POST /api/remote-agents/triggers                    # Create trigger
GET /api/remote-agents/triggers                     # List user triggers
PUT /api/remote-agents/triggers/{id}                # Update trigger
DELETE /api/remote-agents/triggers/{id}             # Delete trigger
GET /api/remote-agents/triggers/{id}/executions     # Get execution history

# Event processing (integrated with existing webhook infrastructure)
POST /api/webhooks/github             # Extended to handle trigger events
POST /api/webhooks/linear             # Extended to handle trigger events
```

#### 1.2 IDE Extension Integration (2 weeks) ❌ **NOT STARTED**

**VS Code Extension Updates**:
```typescript
// Add to existing remote agents panel
interface TriggersPanel {
  // Trigger management
  listTriggers(): Promise<Trigger[]>;
  createTrigger(config: TriggerConfig): Promise<void>;
  updateTrigger(id: string, config: TriggerConfig): Promise<void>;
  deleteTrigger(id: string): Promise<void>;

  // Execution monitoring
  showExecutionHistory(triggerId: string): void;
  showRunningTriggers(): void;
}

interface TriggerConfig {
  name: string;
  description: string;
  eventSource: 'github' | 'linear' | 'jira';
  eventType: string;
  conditions: Record<string, any>;
  agentConfig: {
    prompt: string;
    tools: string[];
    model?: string;
    workspaceSetup?: any;
  };
}
```

**UI Components**:
- **Triggers Tab**: New section in Remote Agents panel
- **Trigger List**: Shows enabled triggers with status indicators
- **Create Trigger Form**: Simple form for manual trigger creation
- **Execution History**: List of recent trigger executions with links to agents

**Initial UI Layout**:
```
Remote Agents Panel
├── Running (existing)
├── Ready (existing)
└── Triggers (new)
    ├── [+ Create Trigger]
    ├── Trigger: "PR Review Assistant" ✅ (3 executions this week)
    ├── Trigger: "Security Alert Handler" ✅ (0 executions)
    └── Recent Executions
        ├── PR Review Assistant → Agent #abc123 (2 hours ago) ✅
        └── Security Alert Handler → Agent #def456 (1 day ago) ✅
```

#### 1.3 Basic Event Processing (1 week) ✅ **COMPLETE**

**GitHub Integration** ✅:
- ✅ Extend existing GitHub webhook handler to evaluate trigger conditions
- ✅ Support basic PR events: `pull_request.review_requested`, `pull_request.opened`
- ✅ Simple condition matching (repository, assignee, labels)
- ✅ **Validated**: End-to-end testing confirms webhook → trigger → agent flow works

**Event Flow**:
```
GitHub Webhook → Extended Webhook Handler → Trigger Evaluation → Direct Agent Creation
```

#### 1.4 Manual Trigger Configuration (1 week) ✅ **COMPLETE** (via CLI)

**Trigger Creation** ✅:
1. ✅ **CLI-based Creation**: Trigger CLI supports JSON configuration files and sample templates
2. ✅ **Event Selection**: Support for GitHub events (pull_request.opened, etc.)
3. ✅ **Conditions**: Repository, assignee, and label filtering working
4. ✅ **Agent Configuration**: Full agent configuration including prompt, tools, workspace setup
5. ❌ **UI Wizard**: Not implemented - currently CLI-only

**Example Manual Configuration**:
```yaml
# User creates this through the UI form
name: "My PR Review Assistant"
description: "Help me review PRs assigned to me"
event_source: "github"
event_type: "pull_request.review_requested"
conditions:
  assignee: "@me"
  repository: "augmentcode/augment"
agent_config:
  prompt: |
    You've been assigned to review a PR. Please:
    1. Analyze the code changes
    2. Check for potential issues
    3. Provide constructive feedback
    4. Approve or request changes as appropriate
  tools: ["github-api", "codebase-retrieval"]
  model: "claude-3-sonnet"
```

**Deliverables**:
- ✅ Extended remote agents service with integrated trigger functionality
- ✅ BigTable storage for triggers within existing remote agents infrastructure
- ❌ IDE extension with triggers panel integrated into remote agents view
- ✅ Manual trigger creation and management through unified API (CLI-based)
- ✅ GitHub PR review trigger working end-to-end
- ✅ Basic execution history and monitoring
- ✅ **BONUS**: Comprehensive testing infrastructure with GitHub Webhook CLI tool

---

### Phase 2: Enhanced UX and Reliability (3-4 weeks)

**Goal**: Improve user experience, add more event sources, and ensure production reliability.

#### 2.1 Enhanced IDE Experience (2 weeks)

**Improved UI Components**:
- **Trigger Templates**: Pre-built trigger configurations for common use cases
- **Event Preview**: Show sample events and test trigger conditions
- **Execution Details**: Detailed view of trigger executions with agent links
- **Status Indicators**: Real-time status updates for running triggers

**Trigger Templates**:
```typescript
const TRIGGER_TEMPLATES = [
  {
    id: 'pr-review-assistant',
    name: 'PR Review Assistant',
    description: 'Automatically review PRs assigned to you',
    eventSource: 'github',
    eventType: 'pull_request.review_requested',
    defaultConditions: { assignee: '@me' },
    defaultPrompt: 'You\'ve been assigned to review a PR...',
    defaultTools: ['github-api', 'codebase-retrieval']
  },
  {
    id: 'ci-failure-investigator',
    name: 'CI Failure Investigator',
    description: 'Investigate when your PRs fail CI',
    eventSource: 'github',
    eventType: 'check_run.completed',
    defaultConditions: { conclusion: 'failure', author: '@me' },
    defaultPrompt: 'A CI check failed on your PR...',
    defaultTools: ['github-api', 'codebase-retrieval']
  }
];
```

**Real-time Updates**:
- WebSocket connection for live trigger execution updates
- Notifications when triggers execute
- Integration with existing agent status updates

#### 2.2 Additional Event Sources (1 week)

**Linear Integration**:
- Issue assignment events
- Issue status changes
- Comment notifications

**JIRA Integration**:
- Ticket assignment
- Status transitions
- Comment events

**Event Source Abstraction**:
```python
class EventProcessor:
    def process_github_event(self, event_type: str, payload: dict) -> List[TriggerExecution]:
        pass

    def process_linear_event(self, event_type: str, payload: dict) -> List[TriggerExecution]:
        pass

    def process_jira_event(self, event_type: str, payload: dict) -> List[TriggerExecution]:
        pass
```

#### 2.3 Production Reliability (1 week)

**Error Handling**:
- Retry logic for failed trigger executions
- Dead letter queues for problematic events
- Comprehensive logging and monitoring

**Rate Limiting**:
- Per-user trigger execution limits
- Cooldown periods for similar events
- Resource usage monitoring

**Monitoring**:
- Prometheus metrics for trigger executions
- BigTable read/write latency and throughput monitoring
- Row key distribution analysis to prevent hotspotting
- Alerting for failed triggers and BigTable performance issues
- Performance dashboards with BigTable-specific metrics

**Deliverables**:
- Polished IDE experience with templates and real-time updates
- Support for Linear and JIRA events
- Production-ready reliability and monitoring
- Comprehensive error handling and rate limiting

---

### Phase 3: Repository-Based Templates (3-4 weeks)

**Goal**: Enable teams to define and share trigger templates through repository configuration files.

#### 3.1 Template Discovery System (2 weeks)

**Repository Scanner**:
```python
class RepositoryScanner:
    def scan_repository(self, repo_url: str) -> List[TriggerTemplate]:
        """Scan .augment/triggers/ directory for template files"""
        pass

    def validate_template(self, template: dict) -> ValidationResult:
        """Validate template schema and configuration"""
        pass
```

**Template Schema**:
```yaml
# .augment/triggers/templates/pr-review.yaml
template:
  id: "team-pr-review"
  name: "Team PR Review Assistant"
  description: "Standard PR review process for the team"
  category: "code-review"
  default_enabled: false

  customizable_fields:
    - "agent.prompt"
    - "event.filters.labels"

  event:
    source: "github"
    type: "pull_request.review_requested"
    filters:
      repository: "{{repository}}"
      assignee: "@me"

  agent:
    prompt: |
      Team PR Review Guidelines:
      {{team_guidelines}}

      Please review this PR following our team standards...
    tools: ["github-api", "codebase-retrieval"]
    model: "claude-3-sonnet"
```

**IDE Integration**:
- Automatic discovery of team templates when opening repositories
- Template comparison with user's existing triggers
- One-click enablement with customization options

#### 3.2 Team Configuration (1 week)

**Team Settings**:
```yaml
# .augment/triggers/config.yaml
team:
  name: "Backend Team"
  guidelines: |
    - Focus on security and performance
    - Ensure proper error handling
    - Verify test coverage

  integrations:
    linear_team_id: "backend-team"
    slack_channel: "#backend-alerts"

  policies:
    default_enablement: "opt-in"
    categories:
      security:
        default_enabled: true
        allow_disable: false
```

**Template Variables**:
- Automatic injection of team configuration into templates
- Repository-specific variables (name, URL, default branch)
- User-specific variables (role, team membership)

#### 3.3 Template Management UI (1 week)

**Team Dashboard**:
- View available team templates
- See adoption rates across team members
- Manage team configuration and policies

**Template Customization**:
- Visual editor for customizing template fields
- Preview of customized trigger behavior
- Validation of custom configurations

**Deliverables**:
- Repository-based template discovery and management
- Team configuration system with policies
- Enhanced IDE experience for team templates
- Team dashboard for template management

---

### Phase 4: Advanced Features (4-5 weeks)

**Goal**: Add sophisticated features for power users and enterprise adoption.

#### 4.1 Advanced Condition System (2 weeks)

**Complex Conditions**:
```yaml
conditions:
  - operator: "and"
    conditions:
      - field: "pull_request.changed_files"
        operator: "greater_than"
        value: 10
      - field: "pull_request.labels"
        operator: "contains"
        value: "needs-review"
  - operator: "or"
    conditions:
      - field: "pull_request.author"
        operator: "in"
        value: ["junior-dev-1", "junior-dev-2"]
      - field: "pull_request.priority"
        operator: "equals"
        value: "high"
```

**Expression Language**:
- Support for JavaScript-like expressions
- Built-in functions for common patterns
- Safe evaluation sandbox

#### 4.2 Trigger Analytics (1 week)

**Metrics Dashboard**:
- Trigger execution frequency and success rates
- Time saved estimates
- User satisfaction scores
- Performance analytics

**A/B Testing**:
- Test different prompts or configurations
- Automatic optimization based on outcomes
- Statistical significance testing

#### 4.3 Advanced Integrations (1-2 weeks)

**Scheduled Triggers**:
- Cron-based scheduling for maintenance tasks
- Recurring analysis and reporting
- Time-based condition evaluation

**Multi-Agent Workflows**:
- Triggers that spawn multiple coordinated agents
- Sequential and parallel execution patterns
- Inter-agent communication

**Deliverables**:
- Advanced condition system with expression language
- Comprehensive analytics and A/B testing
- Scheduled triggers and multi-agent workflows
- Enterprise-ready feature set

---

## Technical Architecture

### Service Integration

```
┌─────────────────┐    ┌─────────────────────────────────────┐
│   IDE Extension │    │      Remote Agents Service          │
│                 │    │                                     │
│ ┌─────────────┐ │    │ ┌─────────────┐ ┌─────────────────┐ │
│ │  Triggers   │◄┼────┼►│   Trigger   │ │     Agent       │ │
│ │    Panel    │ │    │ │   Handler   │ │   Management    │ │
│ └─────────────┘ │    │ └─────────────┘ └─────────────────┘ │
│                 │    │ ┌─────────────┐ ┌─────────────────┐ │
│ ┌─────────────┐ │    │ │   Event     │ │     Agent       │ │
│ │  Template   │◄┼────┼►│ Processor   │ │    Creation     │ │
│ │ Discovery   │ │    │ └─────────────┘ └─────────────────┘ │
│ └─────────────┘ │    │ ┌─────────────────────────────────┐ │
└─────────────────┘    │ │        Unified Storage          │ │
                       │ │   (Agents + Triggers + History) │ │
┌─────────────────┐    │ └─────────────────────────────────┘ │
│   Webhook       │    └─────────────────────────────────────┘
│   Handlers      │             │
│                 │             │
│ ┌─────────────┐ │    ┌─────────▼─────────┐
│ │   GitHub    │◄┼────┤                  │
│ │  Webhook    │ │    │   Event Router   │
│ └─────────────┘ │    │   (Extended)     │
│ ┌─────────────┐ │    └──────────────────┘
│ │   Linear    │◄┼────┘
│ │  Webhook    │ │
│ └─────────────┘ │
└─────────────────┘
```

### Data Flow

```
1. Event occurs (PR assigned, issue created, etc.)
2. Webhook handler receives event
3. Extended event router evaluates trigger conditions within remote agents service
4. Matching triggers directly spawn agents using existing agent creation logic
5. Agent and trigger executions tracked in unified storage
6. IDE extension shows real-time updates through existing remote agents API
```

## Future Service Extraction Strategy

### When to Consider Separation
The integrated approach is designed for Phases 1-2. Consider extracting to a separate `triggers` service if:

- **Scaling Divergence**: Trigger evaluation becomes a bottleneck affecting agent performance
- **Team Ownership**: Different teams need to own trigger management vs agent execution
- **Resource Contention**: Trigger workload competes significantly with agent workload
- **Deployment Independence**: Need to deploy trigger changes without affecting running agents

### Migration Path
```python
# Phase 1-2: Integrated approach
services/remote_agents/
├── server/
│   ├── agent_service.py      # Agent management
│   ├── trigger_service.py    # Trigger management
│   └── storage.py           # Unified storage

# Phase 3+: Extract if needed
services/triggers/           # New dedicated service
├── server/
│   ├── trigger_service.py   # Moved from remote_agents
│   └── storage.py          # Trigger-specific storage
services/remote_agents/      # Simplified service
├── server/
│   ├── agent_service.py     # Focus on agent management
│   └── storage.py          # Agent-specific storage
```

### Extraction Benefits
- **Clear Separation**: Each service has a single responsibility
- **Independent Scaling**: Scale trigger evaluation separately from agent execution
- **Team Ownership**: Different teams can own different services
- **Deployment Safety**: Changes to trigger logic don't affect running agents

### Extraction Costs
- **Increased Complexity**: More services to deploy and monitor
- **Network Latency**: Service-to-service calls for agent spawning
- **Data Consistency**: Need to coordinate state across services
- **Operational Overhead**: More monitoring, logging, and alerting

## Success Metrics

### Phase 1 Success Criteria
- ❌ Users can create and manage triggers through IDE (CLI-only currently)
- ✅ GitHub PR review triggers work end-to-end
- ✅ Basic execution history and monitoring
- ❌ 10+ internal users actively using triggers (needs IDE integration for adoption)

### Phase 2 Success Criteria
- [ ] Support for Linear and JIRA events
- [ ] Trigger templates reduce setup time by 80%
- [ ] 99.9% trigger execution reliability
- [ ] Real-time status updates in IDE

### Phase 3 Success Criteria
- [ ] Teams can define and share trigger templates
- [ ] 50%+ adoption rate within teams using templates
- [ ] Template customization reduces support requests
- [ ] Team dashboard provides actionable insights

### Phase 4 Success Criteria
- [ ] Advanced conditions support complex use cases
- [ ] Analytics show measurable productivity improvements
- [ ] Enterprise customers adopt triggers at scale
- [ ] Feature ready for public launch

## Risk Mitigation

### Technical Risks
- **Event Processing Reliability**: Implement robust retry logic and dead letter queues
- **Agent Spawning Limits**: Rate limiting and resource management
- **Template Security**: Validation and sandboxing of user-defined templates
- **BigTable Performance**: Monitor row key distribution to prevent hotspotting; implement proper row key design with user_id prefix for even distribution
- **BigTable Scaling**: Design for eventual consistency and handle potential read-after-write delays in trigger execution status updates

### Product Risks
- **User Adoption**: Start with simple, high-value use cases
- **Complexity Creep**: Maintain focus on core workflows
- **Performance Impact**: Careful monitoring and optimization

### Operational Risks
- **Support Burden**: Comprehensive documentation and self-service tools
- **Scaling Challenges**: Design for horizontal scaling from the start
- **Integration Maintenance**: Automated testing for all integrations

## BigTable Migration Summary

### Key Changes from SQL to BigTable

**Schema Design Principles**:
- **Row Key Design**: Hierarchical keys using `#` delimiter for efficient range queries
- **Column Families**: Logical grouping of related data with appropriate versioning and TTL policies
- **Denormalization**: Store related data together to minimize read operations
- **Time-based Ordering**: Use reverse timestamps for chronological ordering (newest first)

**Performance Optimizations**:
- **Efficient Queries**: Row key design enables fast prefix scans for user-specific data
- **Batch Operations**: Use batch reads/writes for better performance
- **Caching Strategy**: Implement application-level caching for frequently accessed triggers
- **Connection Pooling**: Reuse BigTable connections across requests

**Operational Considerations**:
- **Backup Strategy**: Regular backups of trigger configurations and execution history
- **Monitoring**: Track read/write latencies, error rates, and resource usage
- **Scaling**: BigTable automatically handles scaling based on load
- **Cost Optimization**: Use appropriate column family settings to manage storage costs

**Migration Path**:
1. **Phase 1**: Implement BigTable schema alongside existing infrastructure
2. **Phase 2**: Migrate existing trigger data (if any) to BigTable
3. **Phase 3**: Switch all reads/writes to BigTable
4. **Phase 4**: Remove old SQL-based storage

**Additional BigTable Features**:
```python
# Advanced BigTable operations for triggers

class AdvancedTriggerOperations:
    def __init__(self, storage: TriggerStorage):
        self.storage = storage

    def batch_get_triggers(self, user_ids: List[str]) -> Dict[str, List[Dict[str, Any]]]:
        """Efficiently fetch triggers for multiple users"""
        row_keys = []
        for user_id in user_ids:
            start_key = f"{user_id}#"
            end_key = f"{user_id}#\xff"
            row_keys.extend([start_key, end_key])

        # Use batch read for better performance
        rows = self.storage.user_triggers_table.read_rows(
            row_keys=row_keys
        )

        # Group results by user_id
        results = {}
        for row in rows:
            user_id = row.row_key.decode().split('#')[0]
            if user_id not in results:
                results[user_id] = []
            results[user_id].append(self._parse_trigger_row(row))

        return results

    def get_execution_metrics(self, user_id: str, time_range_days: int = 30) -> Dict[str, Any]:
        """Get execution metrics for analytics"""
        # Calculate time range for row key filtering
        end_time = datetime.utcnow()
        start_time = end_time - timedelta(days=time_range_days)

        start_timestamp = int(start_time.timestamp())
        end_timestamp = int(end_time.timestamp())

        reverse_start = 9999999999999 - end_timestamp
        reverse_end = 9999999999999 - start_timestamp

        start_key = f"{user_id}#"
        end_key = f"{user_id}#\xff"

        rows = self.storage.trigger_executions_table.read_rows(
            start_key=start_key.encode(),
            end_key=end_key.encode()
        )

        # Aggregate metrics
        total_executions = 0
        successful_executions = 0
        failed_executions = 0
        trigger_counts = {}

        for row in rows:
            execution = self._parse_execution_row(row)
            total_executions += 1

            if execution.get('status') == 'completed':
                successful_executions += 1
            elif execution.get('status') == 'failed':
                failed_executions += 1

            # Count by trigger
            row_key = row.row_key.decode()
            trigger_id = row_key.split('#')[1]
            trigger_counts[trigger_id] = trigger_counts.get(trigger_id, 0) + 1

        return {
            'total_executions': total_executions,
            'successful_executions': successful_executions,
            'failed_executions': failed_executions,
            'success_rate': successful_executions / total_executions if total_executions > 0 else 0,
            'trigger_execution_counts': trigger_counts
        }

    def cleanup_old_executions(self, retention_days: int = 90):
        """Clean up old execution records to manage storage costs"""
        cutoff_time = datetime.utcnow() - timedelta(days=retention_days)
        cutoff_timestamp = int(cutoff_time.timestamp())
        reverse_cutoff = 9999999999999 - cutoff_timestamp

        # Scan for old executions and delete them
        # This would be implemented as a background job
        pass
```

This implementation plan provides a clear path from prototype to production, with each phase building on the previous one while delivering immediate value to users. The BigTable-based storage system ensures scalability, performance, and reliability for the triggers feature.
