# GitHub Triggers and Webhook Registration

This document explains how GitHub webhook registration works in the Augment system and its implications for the triggers feature.

## Current Webhook Architecture

### GitHub App-Based Approach

Augment uses a **GitHub App** architecture rather than individual repository webhooks. This is a fundamental design decision that affects how webhook registration and event handling works.

Key characteristics:
- **Single Global Webhook**: GitHub Apps can only have one webhook URL configured at the app level
- **App-Level Event Subscriptions**: Event types are configured once at the GitHub App level, not per installation
- **Installation-Based Routing**: Events are routed internally based on GitHub App installation mappings

### Webhook Registration Process

#### 1. One-Time Setup (Done by Augment)

**Augment handles all webhook infrastructure setup:**

- **GitHub App Creation**: Augment creates and owns the GitHub App
- **Webhook URL Configuration**: Points to Augment's infrastructure (e.g., `github-app-webhook.augmentcode.com`)
- **Event Subscriptions**: Configured once in GitHub App settings
- **Permissions**: Set at the app level for required repository access

**Evidence from codebase:**
```go
// services/integrations/github/webhook_listener/deploy.jsonnet
// This is a global service. Right now the only integration is github, which
// only allows specifying one webhook per app, so we have one global webhook
// handler.
```

#### 2. Per-Installation Setup (Done by Users)

**Users only need to install the GitHub App:**

1. **App Installation**: Users install Augment's GitHub App on their repositories/organizations
2. **Repository Selection**: Users choose which repositories to grant access to
3. **Permission Approval**: Users approve the permissions Augment's app requests

**No webhook configuration required by users.**

### Event Processing Flow

```
GitHub.com → Augment's Global Webhook → Event Router → Trigger Service → Remote Agents
```

1. **Event Occurs**: GitHub generates an event (PR opened, push, etc.)
2. **Webhook Delivery**: GitHub sends event to Augment's global webhook URL
3. **Installation Mapping**: System maps event to tenant using installation ID
4. **Event Routing**: Event is routed to appropriate tenant's trigger service
5. **Trigger Evaluation**: System evaluates which user triggers should fire
6. **Agent Spawning**: Matching triggers spawn remote agents

### Current Event Support

Based on the codebase analysis, the system currently handles:

```go
// services/integrations/github/webhook_listener/listener.go
switch incomingEvent {
case "ping":
    // Ignore ping events
case "installation":
    // Handle installation events
case "push":
    // Handle push events
```

**Limited event types currently supported.**

## Implications for Triggers Feature

### Webhook Registration Strategy

**You would NOT re-register webhooks based on trigger configuration.**

Instead, you would:

1. **Subscribe to All Needed Events Upfront**: Configure the GitHub App to subscribe to all events you might need for triggers
2. **Handle Filtering Internally**: The triggers service filters and routes events based on user trigger configurations
3. **No Dynamic Webhook Changes**: Event subscriptions are static at the GitHub App level

### Required Event Subscriptions

For a comprehensive triggers system, you would need to add these event subscriptions to your GitHub App:

#### Core Events
- `pull_request` (all actions: opened, closed, review_requested, etc.)
- `pull_request_review`
- `pull_request_review_comment`
- `issues` (opened, closed, assigned, etc.)
- `issue_comment`
- `push`
- `create` (branch/tag creation)
- `delete` (branch/tag deletion)

#### CI/CD Events
- `check_run` (completed, failed, etc.)
- `check_suite` (completed, failed, etc.)
- `workflow_run` (completed, failed, etc.)
- `workflow_job` (completed, failed, etc.)
- `status` (commit status changes)

#### Security Events
- `security_advisory`
- `code_scanning_alert`
- `secret_scanning_alert`
- `dependabot_alert`

#### Repository Events
- `release` (published, etc.)
- `fork`
- `star`
- `watch`

### Event Persistence and Updates

**Webhook definitions are very persistent:**

1. **App-Level Configuration**: Webhook URL and event subscriptions are configured at the GitHub App level in GitHub's developer settings
2. **Manual Configuration Required**: You cannot programmatically change which events a GitHub App subscribes to
3. **No Deletion/Recreation Needed**: To add new event types, you would:
   - Manually update GitHub App settings to subscribe to new events
   - Deploy code changes to handle new event types
   - No webhook URL changes required

### Architecture Benefits

This GitHub App approach provides several advantages:

1. **Higher Rate Limits**: GitHub Apps have higher API rate limits than OAuth apps
2. **Simplified User Experience**: Users only need to install the app once
3. **Centralized Management**: All webhook handling is centralized in Augment's infrastructure
4. **Scalable**: Can handle events for thousands of installations through a single webhook endpoint

### Implementation Considerations

#### Event Volume Management
- **Rate Limiting**: Need to handle potentially high event volumes
- **Filtering**: Efficient filtering to only process relevant events
- **Queuing**: Use message queues for reliable event processing

#### Tenant Isolation
- **Installation Mapping**: Robust mapping from GitHub installation to Augment tenant
- **Permission Inheritance**: Triggers inherit user permissions for repository access
- **Security**: Ensure events are only processed for authorized users

#### Monitoring and Reliability
- **Event Tracking**: Monitor webhook delivery and processing success rates
- **Error Handling**: Robust error handling and retry mechanisms
- **Alerting**: Alert on webhook delivery failures or processing errors

## Current Limitations

Based on codebase analysis:

1. **Limited Event Types**: Currently only handles `ping`, `installation`, and `push` events
2. **Basic Event Processing**: Simple event routing without complex trigger evaluation
3. **No Trigger Framework**: No existing infrastructure for user-defined triggers

## Recommendations for Triggers Implementation

1. **Audit Current GitHub App**: Review what events your GitHub App is currently subscribed to
2. **Plan Event Expansion**: Identify all events needed for triggers and update GitHub App configuration
3. **Extend Event Handler**: Expand the webhook listener to handle new event types
4. **Build Trigger Engine**: Create the trigger evaluation and routing system
5. **Design for Growth**: Plan for adding new event types without requiring webhook changes

## Summary

- **Webhook registration is a one-time setup at the GitHub App level** managed by Augment
- **Users only install the app** - no webhook configuration required
- **Event subscriptions are static** and configured manually in GitHub App settings
- **The triggers system handles all filtering and routing internally**
- **Adding new trigger events requires updating GitHub App settings and deploying code changes**
- **No webhook deletion/recreation is needed** when adding new event types

This architecture provides a scalable foundation for the triggers system while keeping the user experience simple and the infrastructure centralized.
