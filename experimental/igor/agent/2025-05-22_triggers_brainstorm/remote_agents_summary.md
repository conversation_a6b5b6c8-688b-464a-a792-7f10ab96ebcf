# Remote Agents and Integrations Summary

This document provides a comprehensive overview of the remote agents system and external service integrations in the Augment codebase, serving as a foundation for designing a triggers feature.

## Remote Agents System

### Overview
The remote agents system is a highly experimental service that manages the lifecycle of AI agents running in isolated, remote environments. These agents can execute code, interact with repositories, and perform complex tasks in secure, containerized workspaces.

### Core Components

#### 1. Remote Agents Service (`services/remote_agents/`)
- **Location**: `services/remote_agents/server/`
- **Purpose**: Manages remote agent lifecycles and metadata
- **Key Features**:
  - Agent creation, monitoring, and deletion
  - Workspace management through Kubernetes
  - Real-time streaming of agent status and updates
  - Integration with BigTable for persistence
  - Support for GitHub repository setup

#### 2. Remote Workspace Controller (`services/remote_agents/server/ws/`)
- **Purpose**: Manages Kubernetes-based workspaces for remote agents
- **Key Capabilities**:
  - Creates/updates/deletes Kubernetes workspaces (StatefulSets, Services, Secrets)
  - Handles workspace lifecycle (start, stop, resume, pause)
  - SSH access configuration for remote development
  - Resource management and isolation
  - Health checking and status reporting

#### 3. Agent Lifecycle States
```mermaid
stateDiagram
    [*] --> Starting: User starts new agent
    Starting --> Running: Agent is created
    Running --> Idle: Finished task, waiting for user input, or interrupted by user
    Running --> Error: Unrecoverable error
    Idle --> Running: User sends chat request
    Idle --> [*]: User destroys agent and artifacts
    Error --> [*]: User destroys agent and artifacts
```

### Key Features

#### Workspace Setup Options
- **GitHub Repository**: Clone from specific commit/branch
- **Local Files**: Upload files to workspace
- **Setup Scripts**: Custom initialization scripts
- **Environment Configuration**: Custom Docker images and configurations

#### Agent Capabilities
- **Chat Interface**: Interactive communication with agents
- **Tool Access**: Agents can use various tools (see Integrations section)
- **File Operations**: Read, write, and modify files in workspace
- **Code Execution**: Run commands and scripts
- **Version Control**: Git operations and repository management

#### Streaming and Real-time Updates
- **Workspace Streaming**: Real-time updates on workspace status
- **Agent History**: Streaming of agent conversation history
- **Status Monitoring**: Live monitoring of agent and workspace health

## External Service Integrations

The system provides extensive integration capabilities through a modular tool system. Each integration follows a consistent pattern with client libraries, server implementations, and agent tools.

### 1. GitHub Integration (`services/integrations/github/`)

#### Components
- **GitHub Processor**: Handles GitHub App authentication and API calls
- **Webhook Listener**: Processes GitHub webhooks for repository events
- **Agent Tools**: GitHub API tool for agents to interact with repositories

#### Capabilities
- Repository access and management
- Issue and PR operations
- Commit and branch operations
- GitHub App authentication
- Webhook event processing
- File operations within repositories

#### Key Files
- `services/integrations/github/agent_tools/github_api_tool.py`
- `services/integrations/github/processor/server/`
- `services/integrations/github/webhook_listener/`

### 2. Linear Integration (`services/integrations/linear/`)

#### Components
- **Linear Client**: GraphQL API client for Linear
- **Agent Tools**: Linear search and issue management tools
- **Authentication**: OAuth-based authentication handling

#### Capabilities
- Issue search and retrieval
- Project and team information
- Issue creation and updates
- GraphQL query execution

#### Key Files
- `services/integrations/linear/agent_tools/linear_tools.py`
- `services/integrations/linear/server/linear_handler.py`

### 3. Notion Integration (`services/integrations/notion/`)

#### Components
- **Notion Client**: Official Notion API client wrapper
- **Agent Tools**: Page reading, searching, and database operations
- **Authentication**: Notion OAuth integration

#### Capabilities
- Page content retrieval and search
- Database querying
- Content formatting (Markdown conversion)
- Page and database metadata access

#### Key Files
- `services/integrations/notion/agent_tools/notion_tools.py`
- `services/integrations/notion/server/notion_client_wrapper.py`

### 4. Atlassian Integration (`services/integrations/atlassian/`)

#### Components
- **JIRA Client**: JIRA REST API integration
- **Confluence Client**: Confluence REST API integration
- **Agent Tools**: Issue management and content retrieval tools

#### Capabilities
- **JIRA**: Issue search, retrieval, project information
- **Confluence**: Content search, page retrieval, space navigation
- Basic authentication and API token support

#### Key Files
- `services/integrations/atlassian/agent_tools/jira_tools.py`
- `services/integrations/atlassian/agent_tools/confluence_tools.py`
- `services/integrations/atlassian/server/`

### 5. Supabase Integration (`services/integrations/supabase/`)

#### Components
- **Supabase Client**: Management API client
- **Agent Tools**: Database and project management tools

#### Capabilities
- Project management operations
- Database queries and schema operations
- Authentication configuration
- Storage bucket management

#### Key Files
- `services/integrations/supabase/agent_tools/supabase_tool.py`
- `services/integrations/supabase/server/`

### 6. Glean Integration (`services/integrations/glean/`)

#### Components
- **Glean Client**: Enterprise search API client
- **Agent Tools**: Search across company data sources

#### Capabilities
- Cross-platform search (Slack, documents, etc.)
- Content retrieval and formatting
- Authentication and access control

#### Key Files
- `services/integrations/glean/agent_tools/glean_tools.py`
- `services/integrations/glean/server/`

### 7. Web Search Integration (`services/integrations/google_search/`)

#### Components
- **Google Custom Search**: Web search capabilities
- **Agent Tools**: Web search with result formatting

#### Capabilities
- Web search with customizable result count
- Result formatting and snippet extraction
- Rate limiting and API key management

#### Key Files
- `services/integrations/google_search/agent_tools/web_search_tool.py`

### 8. Slack Bot Integration (`services/integrations/slack_bot/`)

#### Components
- **Slack Webhook Handler**: Processes Slack events
- **Slack Processor**: Handles Slack API interactions

#### Capabilities
- Slack event processing
- Bot interactions
- Channel and user management

## Agent Tools System

### Tool Architecture
All integrations follow a consistent tool pattern:

```python
class IntegrationTool(ValidatedTool[InputModel, ExtraToolInput]):
    id = agents_pb2.RemoteToolId.INTEGRATION_NAME
    name = "integration-name"
    description = "Tool description for AI agents"

    def run_validated(self, validated_input, extra_tool_input, request_context):
        # Tool implementation
        pass
```

### Tool Registration
Tools are registered in `services/agents/server/agents_tools.py` and made available to agents based on configuration and permissions.

### Authentication Patterns
- **OAuth**: Linear, Notion, Atlassian
- **API Keys**: GitHub (App), Supabase, Glean
- **Service Accounts**: Internal services
- **User Settings**: Stored in settings service with encryption

## Infrastructure and Deployment

### Kubernetes Integration
- Remote agents run as Kubernetes StatefulSets
- Each agent gets isolated namespace and resources
- SSH proxy for secure access
- Persistent storage for workspace data

### Data Storage
- **BigTable**: Agent metadata, history, and state
- **GCS**: File storage and workspace artifacts
- **Memstore**: Caching and session data

### Monitoring and Observability
- Prometheus metrics for all services
- Request tracing and logging
- Health checks and status reporting
- Real-time streaming of agent status

## Security Considerations

### Isolation
- Each remote agent runs in isolated Kubernetes environment
- Network policies and resource limits
- Secure credential management

### Authentication
- Multi-tenant authentication system
- Service-to-service authentication via mTLS
- User credential encryption and secure storage

### Access Control
- User-based workspace ownership
- Integration-specific permission models
- Tool access based on user authentication

## Implications for Triggers Feature

### Potential Trigger Sources
1. **GitHub Events**: Push, PR, issue updates via webhooks
2. **Linear Updates**: Issue status changes, new issues
3. **Notion Changes**: Page updates, database changes
4. **JIRA Events**: Issue transitions, comments
5. **Slack Events**: Messages, mentions, reactions
6. **Agent Events**: Agent completion, errors, status changes
7. **Workspace Events**: File changes, build completions

### Integration Points
1. **Webhook Infrastructure**: Existing webhook listeners can be extended
2. **Event Processing**: Current pubsub system can handle trigger events
3. **Agent Creation**: Existing remote agent creation APIs
4. **Tool Access**: Agents created by triggers can use all existing tools
5. **Authentication**: Leverage existing OAuth and credential management

### Architecture Considerations
1. **Event Routing**: Need system to route events to appropriate triggers
2. **Trigger Configuration**: UI and API for managing trigger rules
3. **Rate Limiting**: Prevent trigger spam and resource exhaustion
4. **Audit Trail**: Track trigger executions and outcomes
5. **Error Handling**: Robust error handling and retry mechanisms

This foundation provides a solid base for implementing a comprehensive triggers system that can respond to events across all integrated services and automatically spawn remote agents to handle various automation tasks.
