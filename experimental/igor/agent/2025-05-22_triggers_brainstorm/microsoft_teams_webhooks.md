# Microsoft Teams Webhooks in Augment

This document explains how Microsoft Teams webhook registration and management works, and how it compares to other platform webhooks for the triggers feature.

## Microsoft Teams Webhook Architecture Overview

### Teams App-Based Events Model

Microsoft Teams uses a **Teams App with Bot Framework** approach that is similar to Slack but with Microsoft's ecosystem integration:

- **Teams App Registration**: Apps registered in Microsoft App Store or custom deployment
- **Bot Framework Integration**: Uses Azure Bot Service for real-time events
- **Graph API Events**: Microsoft Graph webhooks for comprehensive Teams events
- **Tenant-Level Installation**: Apps installed per Microsoft 365 tenant
- **OAuth Scopes**: Event access controlled by Microsoft 365 permissions

### Current Microsoft Teams Integration Landscape

**Microsoft Teams webhook evolution**:
- **Legacy Connectors**: Office 365 Connectors (being deprecated)
- **Incoming Webhooks**: Simple webhook receivers (limited functionality)
- **Teams Apps with Bots**: Full-featured real-time event handling
- **Graph API Webhooks**: Comprehensive Microsoft 365 event coverage
- **Power Automate Integration**: Workflow-based webhook handling

## Microsoft Teams Webhook Registration Process

### How Teams Webhooks Work

Microsoft Teams webhooks can be implemented through multiple approaches:

#### Approach 1: Teams App with Bot Framework (Recommended)

**Teams App Registration**:
```json
{
    "manifestVersion": "1.16",
    "version": "1.0.0",
    "id": "augment-triggers-app-id",
    "packageName": "com.augmentcode.teams",
    "developer": {
        "name": "Augment Code",
        "websiteUrl": "https://augmentcode.com",
        "privacyUrl": "https://augmentcode.com/privacy",
        "termsOfUseUrl": "https://augmentcode.com/terms"
    },
    "name": {
        "short": "Augment Triggers",
        "full": "Augment Code Triggers for Teams"
    },
    "description": {
        "short": "Automated agents triggered by Teams events",
        "full": "Create intelligent agents that respond to Teams messages, meetings, and activities"
    },
    "bots": [
        {
            "botId": "augment-bot-id",
            "scopes": ["personal", "team", "groupchat"],
            "supportsFiles": false,
            "isNotificationOnly": false
        }
    ],
    "permissions": [
        "identity",
        "messageTeamMembers"
    ],
    "validDomains": [
        "teams-webhook.augmentcode.com"
    ]
}
```

#### Approach 2: Microsoft Graph Webhooks

**Graph API Webhook Registration**:
```json
POST /subscriptions
{
    "changeType": "created,updated,deleted",
    "notificationUrl": "https://teams-webhook.augmentcode.com/webhook/{tenant-id}",
    "resource": "/teams/{team-id}/channels/{channel-id}/messages",
    "expirationDateTime": "2023-02-15T10:30:00Z",
    "clientState": "tenant-secret-state"
}
```

### Microsoft Teams Event Types

Teams supports comprehensive event types through different APIs:

#### Bot Framework Events (Teams App)
- **message** - Messages sent in channels or chats
- **messageReaction** - Reactions added/removed from messages
- **membersAdded** - Users added to team or chat
- **membersRemoved** - Users removed from team or chat
- **channelCreated** - New channel created
- **channelRenamed** - Channel renamed
- **teamRenamed** - Team renamed
- **installationUpdate** - App installed/uninstalled

#### Graph API Events (Webhooks)
- **chatMessage** - Messages in channels and chats
- **channel** - Channel lifecycle events
- **team** - Team lifecycle events
- **chatMember** - Membership changes
- **presence** - User presence changes
- **calendarEvent** - Meeting and calendar events
- **call** - Voice and video call events

### Microsoft Teams Webhook Payload Structure

Teams webhook payloads vary by event source:

#### Bot Framework Payload
```json
{
    "type": "message",
    "id": "message-id",
    "timestamp": "2023-01-15T10:30:00Z",
    "serviceUrl": "https://smba.trafficmanager.net/teams/",
    "channelId": "msteams",
    "from": {
        "id": "user-id",
        "name": "John Doe",
        "aadObjectId": "aad-user-id"
    },
    "conversation": {
        "conversationType": "channel",
        "tenantId": "tenant-id",
        "id": "channel-conversation-id"
    },
    "recipient": {
        "id": "bot-id",
        "name": "Augment Bot"
    },
    "text": "@Augment help me debug this issue",
    "attachments": [],
    "entities": [
        {
            "type": "mention",
            "text": "@Augment",
            "mentioned": {
                "id": "bot-id",
                "name": "Augment Bot"
            }
        }
    ],
    "channelData": {
        "teamsChannelId": "channel-id",
        "teamsTeamId": "team-id",
        "channel": {
            "id": "channel-id",
            "name": "General"
        },
        "team": {
            "id": "team-id",
            "name": "Engineering Team"
        },
        "tenant": {
            "id": "tenant-id"
        }
    }
}
```

#### Graph API Webhook Payload
```json
{
    "value": [
        {
            "subscriptionId": "subscription-id",
            "changeType": "created",
            "tenantId": "tenant-id",
            "clientState": "tenant-secret-state",
            "subscriptionExpirationDateTime": "2023-02-15T10:30:00Z",
            "resource": "teams/team-id/channels/channel-id/messages/message-id",
            "resourceData": {
                "id": "message-id",
                "@odata.type": "#Microsoft.Graph.chatMessage",
                "@odata.id": "teams/team-id/channels/channel-id/messages/message-id"
            }
        }
    ]
}
```

## Comparison: Microsoft Teams vs Other Platform Webhooks

| Aspect | Slack Webhooks | GitHub Webhooks | Sentry Webhooks | Microsoft Teams Webhooks |
|--------|----------------|-----------------|-----------------|--------------------------|
| **Registration Model** | App-level | App-level | Organization-level | App-level + Graph subscriptions |
| **Event Configuration** | Static at app level | Static at app level | Dynamic per webhook | Hybrid (app + subscriptions) |
| **User Setup Required** | Install Slack App | Install GitHub App | Configure webhook + events | Install Teams App + Graph permissions |
| **Management** | Centralized by Augment | Centralized by Augment | Distributed per organization | Centralized + subscription management |
| **API Registration** | Not needed | Not supported | Fully supported | Hybrid (app + Graph API) |
| **Event Filtering** | OAuth scope-based | All-or-nothing | Project + event type filtering | Scope + resource filtering |
| **Authentication** | OAuth + workspace permissions | GitHub App installation | API token + webhook secret | Azure AD + app permissions |
| **Webhook Lifecycle** | Static (manual app updates) | Static (manual app updates) | Dynamic (create/update/delete) | Hybrid (static app + dynamic subscriptions) |
| **Current Implementation** | ✅ Implemented | ✅ Implemented | ❌ Not implemented | ❌ **Not implemented** |

## Microsoft Teams Webhook Advantages for Triggers

### 1. Enterprise Integration
- **Microsoft 365 ecosystem** integration with Office, Outlook, SharePoint
- **Azure Active Directory** authentication and user management
- **Enterprise security** and compliance features
- **Single sign-on** with corporate identity systems
- **Tenant-wide deployment** and management

### 2. Rich Communication Events
- **Message events** with rich formatting and attachments
- **Meeting integration** with calendar and call events
- **Presence awareness** for user availability
- **File sharing** events and collaboration
- **Channel and team** lifecycle management

### 3. Graph API Integration
- **Comprehensive Microsoft 365** event coverage
- **Cross-application events** (Teams, Outlook, SharePoint, OneDrive)
- **User activity** tracking across Microsoft services
- **Calendar and meeting** integration
- **File and document** collaboration events

### 4. Enterprise Adoption
- **High enterprise adoption** (~250M+ monthly active users)
- **Corporate communication** standard
- **IT department support** and management
- **Compliance and security** requirements met

## Microsoft Teams Triggers Use Cases

### Communication Triggers
- **"When mentioned in Teams channel, create response agent"**
- **"When urgent message posted, create escalation agent"**
- **"When meeting scheduled, create preparation agent"**
- **"When file shared in channel, create review agent"**

### Collaboration Triggers
- **"When new team created, create onboarding agent"**
- **"When user joins team, create welcome agent"**
- **"When channel created, create setup agent"**
- **"When meeting ends, create follow-up agent"**

### Enterprise Workflow Triggers
- **"When executive message posted, create priority agent"**
- **"When incident channel created, create response agent"**
- **"When project status updated, create tracking agent"**
- **"When approval requested, create review agent"**

### Integration Triggers
- **"When calendar event updated, create notification agent"**
- **"When SharePoint file modified, create sync agent"**
- **"When Outlook email flagged, create action agent"**

## Implementation Strategy

### Implementation Effort: ⚠️ **Medium-High**

**Advantages**:
- **Large enterprise market** with high adoption
- **Rich Microsoft 365 integration** opportunities
- **Comprehensive event coverage** across Microsoft ecosystem
- **Strong enterprise security** and compliance features
- **Well-documented APIs** (Bot Framework + Graph API)

**Challenges**:
- **Complex authentication** with Azure AD and app permissions
- **Dual API approach** (Bot Framework + Graph API)
- **Enterprise deployment** requirements and approval processes
- **Subscription management** for Graph API webhooks
- **Microsoft ecosystem** learning curve

**Implementation Requirements**:

#### 1. Teams App Development
```javascript
// Teams Bot Framework integration
const { TeamsActivityHandler, MessageFactory } = require('botbuilder');

class AugmentTeamsBot extends TeamsActivityHandler {
    constructor() {
        super();

        this.onMessage(async (context, next) => {
            // Handle Teams message events
            const message = context.activity.text;
            const channelData = context.activity.channelData;

            // Route to tenant-specific processing
            await this.routeToTenant(context, message, channelData);
            await next();
        });

        this.onMembersAdded(async (context, next) => {
            // Handle member added events
            await this.handleMemberAdded(context);
            await next();
        });
    }
}
```

#### 2. Graph API Webhook Service
```go
// services/integrations/teams/webhook_listener/main.go
type teamsWebhookListener struct {
    publishClient pubsub.PublishClient
    tenantLookup  TenantLookup
    graphClient   *msgraph.GraphServiceClient
}

func (tl *teamsWebhookListener) handleGraphWebhook(w http.ResponseWriter, r *http.Request) {
    // Verify Graph API webhook signature
    // Parse Graph webhook payload
    // Extract tenant ID from subscription mapping
    // Fetch full resource data from Graph API
    // Publish to tenant-specific pubsub topic
}
```

#### 3. Azure AD Integration
```go
// Azure AD OAuth and app registration
func (s *TeamsServer) RegisterApp(tenantID string) error {
    // Register Teams app in Azure AD
    // Configure bot framework endpoint
    // Set up Graph API permissions
    // Create webhook subscriptions
}
```

### Required Microsoft 365 Permissions

For comprehensive Teams triggers, the app would need:

#### Bot Framework Permissions
- **Team member** access for channel events
- **Chat member** access for direct messages
- **Message read** permissions for content access

#### Graph API Permissions
- **ChannelMessage.Read.All** - Read channel messages
- **Chat.Read.All** - Read chat messages
- **Team.ReadBasic.All** - Read team information
- **Channel.ReadBasic.All** - Read channel information
- **Presence.Read.All** - Read user presence
- **Calendars.Read** - Read calendar events
- **Files.Read.All** - Read shared files

### Webhook Lifecycle Management

#### Teams App Installation
- User installs Teams app from Microsoft App Store or custom deployment
- App requests necessary permissions during installation
- Bot framework endpoint configured automatically
- Tenant mapping created for routing

#### Graph API Subscription Management
```go
func (s *TeamsServer) CreateGraphSubscriptions(tenantID string, accessToken string) error {
    subscriptions := []GraphSubscription{
        {
            Resource:      "/teams/{team-id}/channels/{channel-id}/messages",
            ChangeType:    "created,updated",
            ExpirationDateTime: time.Now().Add(24 * time.Hour),
            NotificationURL: fmt.Sprintf("https://teams-webhook.%s/graph/%s", s.config.Domain, tenantID),
        },
        {
            Resource:      "/teams/{team-id}/channels",
            ChangeType:    "created,updated,deleted",
            ExpirationDateTime: time.Now().Add(24 * time.Hour),
            NotificationURL: fmt.Sprintf("https://teams-webhook.%s/graph/%s", s.config.Domain, tenantID),
        },
    }

    // Create subscriptions via Graph API
    // Store subscription IDs for renewal
}
```

#### Subscription Renewal
- Graph API subscriptions expire (max 4230 minutes for most resources)
- Automatic renewal process required
- Handle subscription failures and re-creation
- Monitor subscription health

## Security Considerations

### Azure AD Authentication

Teams integration requires robust Azure AD handling:

```go
// Azure AD token validation
func (h *TeamsWebhookHandler) ValidateAzureADToken(token string) (*AzureADClaims, error) {
    // Validate JWT token from Azure AD
    // Check audience, issuer, and signature
    // Extract tenant and user information
    return claims, nil
}
```

### Bot Framework Security

```go
// Bot Framework authentication
func (h *TeamsWebhookHandler) ValidateBotFrameworkAuth(r *http.Request) error {
    authHeader := r.Header.Get("Authorization")
    // Validate Bot Framework JWT token
    // Verify against Microsoft's public keys
    return nil
}
```

### Tenant Isolation

- **Azure AD tenant** mapping to Augment tenants
- **Teams team/channel** access control
- **Graph subscription** scoping per tenant
- **Bot conversation** context isolation

## Monitoring and Reliability

### Webhook Health Monitoring
```go
var teamsWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
    Name: "teams_webhook_request_total",
    Help: "Counter of handled Teams webhook requests",
}, []string{"status_code", "event_type", "source"})
```

### Subscription Management
- **Automatic subscription renewal** before expiration
- **Subscription failure** detection and recovery
- **Rate limiting** compliance with Graph API limits
- **Batch processing** for high-volume events

### Error Recovery
- **Bot framework** connection recovery
- **Graph API** subscription re-creation
- **Azure AD** token refresh handling
- **Tenant mapping** consistency checks

## Advanced Features

### Microsoft 365 Integration
- **Cross-application triggers** spanning Teams, Outlook, SharePoint
- **Calendar integration** for meeting-based triggers
- **File collaboration** events from OneDrive and SharePoint
- **Email integration** with Outlook events

### Enterprise Features
- **Multi-tenant support** for large organizations
- **Compliance integration** with Microsoft compliance tools
- **Security integration** with Microsoft security services
- **Analytics integration** with Microsoft Viva Insights

### Adaptive Cards
- **Rich interactive responses** using Adaptive Cards
- **Action-based triggers** from card interactions
- **Form submissions** and approval workflows
- **Dynamic content** updates

## Migration Path

### Phase 1: Basic Teams App
1. Create Teams app with Bot Framework
2. Implement basic message event handling
3. Set up Azure AD authentication
4. Support core communication triggers

### Phase 2: Graph API Integration
1. Add Graph API webhook subscriptions
2. Implement subscription management and renewal
3. Support team and channel lifecycle events
4. Add presence and calendar integration

### Phase 3: Advanced Enterprise Features
1. Multi-tenant deployment support
2. Cross-application Microsoft 365 triggers
3. Compliance and security integration
4. Advanced analytics and reporting

### Phase 4: Ecosystem Integration
1. SharePoint and OneDrive integration
2. Outlook and calendar deep integration
3. Power Platform integration
4. Microsoft Viva integration

## Implementation Effort Comparison (Updated)

| Approach | Slack | GitHub | Sentry | CircleCI | Microsoft Teams |
|----------|-------|--------|--------|----------|-----------------|
| **User Setup** | ✅ Simple | ✅ Simple | ⚠️ Medium | ⚠️ Medium | ❌ **Complex** |
| **Event Richness** | ⚠️ Medium | ⚠️ Medium | ✅ Very High | ✅ High | ✅ **Very High** |
| **API Quality** | ✅ Good | ❌ Limited | ✅ Excellent | ✅ Excellent | ⚠️ **Good (Dual)** |
| **Auth Complexity** | ✅ Simple | ✅ Simple | ⚠️ OAuth + API | ⚠️ OAuth + API | ❌ **Azure AD + Complex** |
| **Infrastructure** | ✅ Existing | ✅ Existing | ❌ New needed | ❌ New needed | ❌ **New needed** |
| **Implementation Effort** | ✅ Low | ✅ Low | ⚠️ Medium | ⚠️ Medium | ❌ **Medium-High** |
| **Market Coverage** | ✅ ~95% | ✅ ~90% | ⚠️ ~70% | ⚠️ ~40% | ✅ **~80% (Enterprise)** |
| **Enterprise Value** | ⚠️ Medium | ⚠️ Medium | ⚠️ Medium | ⚠️ Medium | ✅ **Very High** |

## Summary

**Key Advantages of Microsoft Teams for Triggers**:
- **Highest enterprise adoption** - dominant in corporate environments
- **Microsoft 365 ecosystem** - comprehensive integration opportunities
- **Rich event coverage** - communication, collaboration, and productivity events
- **Enterprise security** - Azure AD integration and compliance features
- **Cross-application triggers** - spans entire Microsoft productivity suite

**Key Challenges**:
- **Complex implementation** - dual API approach and Azure AD complexity
- **Enterprise deployment** - requires IT approval and configuration
- **Subscription management** - Graph API webhook lifecycle complexity
- **Authentication complexity** - Azure AD and Bot Framework integration

**Recommended Approach**:
- **High priority for enterprise customers** - essential for enterprise adoption
- **Phase implementation** - start with basic Teams app, add Graph API later
- **Focus on enterprise use cases** - leverage Microsoft 365 integration
- **Partner with Microsoft** - consider Microsoft partnership for easier deployment

**Microsoft Teams Implementation Requirements**:
- Teams app development with Bot Framework
- Graph API webhook subscription management
- Azure AD authentication and permission handling
- Dual webhook listener (Bot Framework + Graph API)
- Enterprise deployment and tenant management

**Benefits of Microsoft Teams Integration**:
- **Enterprise market access** - opens doors to large corporate customers
- **Comprehensive productivity triggers** - covers entire work communication spectrum
- **Microsoft ecosystem leverage** - integrates with full Microsoft 365 suite
- **Enterprise compliance** - meets corporate security and compliance requirements

Microsoft Teams provides the highest enterprise value for triggers, offering comprehensive Microsoft 365 integration and access to the largest corporate communication platform, though with significantly higher implementation complexity.
