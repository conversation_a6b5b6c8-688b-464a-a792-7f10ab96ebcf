# AWS CloudWatch Webhooks in Augment

This document explains how AWS CloudWatch webhook integration works and how it compares to other platform webhooks for the triggers feature.

## AWS CloudWatch Webhook Architecture Overview

### SNS-Based Webhook Model

AWS CloudWatch uses an **SNS (Simple Notification Service) based webhook** approach that provides comprehensive AWS monitoring and alerting events:

- **CloudWatch Alarms**: Monitor metrics and trigger SNS notifications
- **EventBridge Integration**: Custom events and AWS service events
- **SNS HTTP/HTTPS Endpoints**: Webhook delivery via SNS subscriptions
- **Cross-Service Integration**: Events from multiple AWS services
- **Account-Level Configuration**: Webhooks configured per AWS account

### AWS CloudWatch Integration Characteristics

**CloudWatch webhook model**:
- **Alarm-driven**: Webhooks primarily triggered by CloudWatch alarms
- **Event-based**: Real-time notifications for AWS service events
- **Rich AWS context**: Comprehensive AWS resource and service metadata
- **Multi-service**: Integration across entire AWS ecosystem
- **SNS delivery**: Reliable webhook delivery with retry mechanisms

## AWS CloudWatch Webhook Registration Process

### How CloudWatch Webhooks Work

CloudWatch webhooks are implemented through **SNS HTTP/HTTPS subscriptions**:

#### Manual Registration (AWS Console)
1. **Create SNS Topic**: Set up SNS topic for webhook notifications
2. **HTTP/HTTPS Subscription**: Add webhook URL as SNS subscription
3. **CloudWatch Alarm**: Configure alarms to publish to SNS topic
4. **EventBridge Rules**: Set up rules for custom and service events
5. **Subscription Confirmation**: Confirm webhook endpoint subscription

#### Infrastructure as Code (CloudFormation/Terraform)

**CloudFormation Template**:
```yaml
Resources:
  AugmentWebhookTopic:
    Type: AWS::SNS::Topic
    Properties:
      TopicName: augment-triggers-webhook
      DisplayName: Augment Triggers Webhook

  AugmentWebhookSubscription:
    Type: AWS::SNS::Subscription
    Properties:
      Protocol: https
      TopicArn: !Ref AugmentWebhookTopic
      Endpoint: https://cloudwatch-webhook.augmentcode.com/webhook/{tenant-id}

  HighCPUAlarm:
    Type: AWS::CloudWatch::Alarm
    Properties:
      AlarmName: HighCPUUtilization
      AlarmDescription: Alarm when CPU exceeds 80%
      MetricName: CPUUtilization
      Namespace: AWS/EC2
      Statistic: Average
      Period: 300
      EvaluationPeriods: 2
      Threshold: 80
      ComparisonOperator: GreaterThanThreshold
      AlarmActions:
        - !Ref AugmentWebhookTopic
```

**Terraform Configuration**:
```hcl
resource "aws_sns_topic" "augment_webhook" {
  name = "augment-triggers-webhook"
}

resource "aws_sns_topic_subscription" "augment_webhook" {
  topic_arn = aws_sns_topic.augment_webhook.arn
  protocol  = "https"
  endpoint  = "https://cloudwatch-webhook.augmentcode.com/webhook/${var.tenant_id}"
}

resource "aws_cloudwatch_metric_alarm" "high_cpu" {
  alarm_name          = "high-cpu-utilization"
  comparison_operator = "GreaterThanThreshold"
  evaluation_periods  = "2"
  metric_name         = "CPUUtilization"
  namespace           = "AWS/EC2"
  period              = "300"
  statistic           = "Average"
  threshold           = "80"
  alarm_description   = "This metric monitors ec2 cpu utilization"
  alarm_actions       = [aws_sns_topic.augment_webhook.arn]
}
```

### AWS CloudWatch Event Types

CloudWatch supports comprehensive AWS monitoring event types:

#### CloudWatch Alarms
- **Metric alarms** - Threshold-based alerts on AWS metrics
- **Composite alarms** - Alarms based on multiple alarm states
- **Anomaly detection** - Machine learning-based anomaly alerts

#### EventBridge Events
- **AWS service events** - Events from EC2, RDS, Lambda, etc.
- **Custom application events** - User-defined events
- **Scheduled events** - Time-based triggers
- **Cross-account events** - Events from other AWS accounts

#### AWS Service Events
- **EC2 events** - Instance state changes, spot interruptions
- **RDS events** - Database events, backup completion
- **Lambda events** - Function errors, duration alerts
- **S3 events** - Object creation, deletion, access
- **ELB events** - Load balancer health, target failures

### AWS CloudWatch Webhook Payload Structure

CloudWatch webhook payloads via SNS provide rich AWS context:

#### CloudWatch Alarm Payload
```json
{
  "Type": "Notification",
  "MessageId": "alarm-message-id",
  "TopicArn": "arn:aws:sns:us-east-1:************:augment-triggers-webhook",
  "Subject": "ALARM: \"HighCPUUtilization\" in US East (N. Virginia)",
  "Message": "{\"AlarmName\":\"HighCPUUtilization\",\"AlarmDescription\":\"Alarm when CPU exceeds 80%\",\"AWSAccountId\":\"************\",\"NewStateValue\":\"ALARM\",\"NewStateReason\":\"Threshold Crossed: 1 out of the last 1 datapoints [85.0 (15/01/23 10:30:00)] was greater than the threshold (80.0).\",\"StateChangeTime\":\"2023-01-15T10:30:00.000+0000\",\"Region\":\"US East (N. Virginia)\",\"AlarmArn\":\"arn:aws:cloudwatch:us-east-1:************:alarm:HighCPUUtilization\",\"OldStateValue\":\"OK\",\"Trigger\":{\"MetricName\":\"CPUUtilization\",\"Namespace\":\"AWS/EC2\",\"StatisticType\":\"Statistic\",\"Statistic\":\"Average\",\"Unit\":null,\"Dimensions\":[{\"value\":\"i-1234567890abcdef0\",\"name\":\"InstanceId\"}],\"Period\":300,\"EvaluationPeriods\":2,\"ComparisonOperator\":\"GreaterThanThreshold\",\"Threshold\":80.0,\"TreatMissingData\":\"\",\"EvaluateLowSampleCountPercentile\":\"\"}}",
  "Timestamp": "2023-01-15T10:30:00.000Z",
  "SignatureVersion": "1",
  "Signature": "signature-hash",
  "SigningCertURL": "https://sns.us-east-1.amazonaws.com/SimpleNotificationService-cert.pem",
  "UnsubscribeURL": "https://sns.us-east-1.amazonaws.com/?Action=Unsubscribe&SubscriptionArn=arn:aws:sns:us-east-1:************:augment-triggers-webhook:subscription-id"
}
```

#### EventBridge Event Payload
```json
{
  "Type": "Notification",
  "MessageId": "event-message-id",
  "TopicArn": "arn:aws:sns:us-east-1:************:augment-triggers-webhook",
  "Message": "{\"version\":\"0\",\"id\":\"event-id\",\"detail-type\":\"EC2 Instance State-change Notification\",\"source\":\"aws.ec2\",\"account\":\"************\",\"time\":\"2023-01-15T10:30:00Z\",\"region\":\"us-east-1\",\"detail\":{\"instance-id\":\"i-1234567890abcdef0\",\"state\":\"terminated\",\"previous-state\":\"running\"}}",
  "Timestamp": "2023-01-15T10:30:00.000Z",
  "SignatureVersion": "1",
  "Signature": "signature-hash",
  "SigningCertURL": "https://sns.us-east-1.amazonaws.com/SimpleNotificationService-cert.pem"
}
```

## Comparison: AWS CloudWatch vs Other Platform Webhooks

| Aspect | Datadog Webhooks | Sentry Webhooks | CircleCI Webhooks | AWS CloudWatch Webhooks |
|--------|------------------|-----------------|-------------------|-------------------------|
| **Registration Model** | Integration-level | Organization-level | Project-level | Account-level (SNS) |
| **Event Configuration** | Monitor-driven | Dynamic per webhook | Dynamic per webhook | Alarm + EventBridge driven |
| **User Setup Required** | Configure integration + monitors | Configure webhook + events | Configure webhook + events | Configure SNS + alarms/rules |
| **Management** | Distributed per organization | Distributed per organization | Distributed per project | Distributed per AWS account |
| **API Registration** | Fully supported | Fully supported | Fully supported | Infrastructure as Code |
| **Event Filtering** | Monitor + tag filtering | Project + event type filtering | Branch + event type filtering | Alarm + EventBridge filtering |
| **Authentication** | API key + custom headers | API token + webhook secret | API token + signing secret | SNS signature verification |
| **Webhook Lifecycle** | Dynamic (create/update/delete) | Dynamic (create/update/delete) | Dynamic (create/update/delete) | Infrastructure as Code |
| **Current Implementation** | ❌ Not implemented | ❌ Not implemented | ❌ Not implemented | ❌ **Not implemented** |

## AWS CloudWatch Webhook Advantages for Triggers

### 1. Comprehensive AWS Coverage
- **All AWS services** - monitoring across entire AWS ecosystem
- **Native AWS integration** - deep integration with AWS services
- **Cross-service correlation** - events spanning multiple AWS services
- **Account-wide monitoring** - centralized monitoring for entire AWS account
- **Multi-region support** - monitoring across all AWS regions

### 2. Rich AWS Context
- **Resource metadata** - detailed AWS resource information
- **Service-specific data** - specialized data for each AWS service
- **Account and region** information for multi-account setups
- **Cost and billing** integration with AWS cost monitoring
- **Compliance and security** integration with AWS security services

### 3. Infrastructure as Code Integration
- **CloudFormation support** - declarative webhook configuration
- **Terraform integration** - infrastructure automation
- **CDK support** - programmatic infrastructure definition
- **Version control** - webhook configuration in source control
- **Automated deployment** - webhook setup as part of infrastructure deployment

### 4. Enterprise AWS Adoption
- **Highest cloud adoption** - dominant cloud platform
- **Enterprise infrastructure** - critical business systems
- **DevOps integration** - core part of DevOps workflows
- **Compliance requirements** - meets enterprise compliance needs

## AWS CloudWatch Triggers Use Cases

### Infrastructure Triggers
- **"When EC2 instance fails, create recovery agent"**
- **"When RDS database performance degrades, create optimization agent"**
- **"When Lambda function errors spike, create debugging agent"**
- **"When ELB health checks fail, create incident response agent"**

### Cost and Resource Triggers
- **"When AWS costs exceed budget, create cost analysis agent"**
- **"When resource utilization low, create optimization agent"**
- **"When spot instances terminated, create replacement agent"**
- **"When storage costs spike, create cleanup agent"**

### Security and Compliance Triggers
- **"When unauthorized API calls detected, create security investigation agent"**
- **"When security group changes, create compliance review agent"**
- **"When root account used, create security alert agent"**
- **"When compliance violation detected, create remediation agent"**

### Application and Service Triggers
- **"When application errors increase, create debugging agent"**
- **"When API Gateway throttling occurs, create scaling agent"**
- **"When S3 access patterns anomalous, create analysis agent"**
- **"When CloudFront errors spike, create investigation agent"**

### Deployment and Operations Triggers
- **"When deployment fails, create rollback agent"**
- **"When auto-scaling events frequent, create capacity planning agent"**
- **"When backup failures occur, create recovery agent"**
- **"When maintenance windows approach, create preparation agent"**

## Implementation Strategy

### Implementation Effort: ⚠️ **Medium**

**Advantages**:
- **Native AWS integration** - leverages existing AWS infrastructure
- **Comprehensive coverage** - monitors entire AWS ecosystem
- **Infrastructure as Code** - declarative configuration and deployment
- **Enterprise adoption** - widely used in enterprise environments
- **Reliable delivery** - SNS provides robust webhook delivery

**Challenges**:
- **AWS-specific setup** - requires AWS account configuration
- **Infrastructure complexity** - SNS topics, alarms, and EventBridge rules
- **Multi-account management** - complex for large organizations
- **AWS expertise required** - need understanding of AWS services

**Implementation Requirements**:

#### 1. CloudWatch Webhook Service
```go
// services/integrations/cloudwatch/webhook_listener/main.go
type cloudWatchWebhookListener struct {
    publishClient pubsub.PublishClient
    tenantLookup  TenantLookup
}

func (cl *cloudWatchWebhookListener) handle(w http.ResponseWriter, r *http.Request) {
    // Verify SNS signature
    // Parse SNS message payload
    // Extract CloudWatch alarm or EventBridge event
    // Extract tenant ID from URL path or topic mapping
    // Look up tenant from topic mapping
    // Publish to tenant-specific pubsub topic
}
```

#### 2. SNS Signature Verification
```go
// SNS message signature verification
func (h *CloudWatchWebhookHandler) VerifySNSSignature(r *http.Request) (*SNSMessage, error) {
    var snsMessage SNSMessage
    if err := json.NewDecoder(r.Body).Decode(&snsMessage); err != nil {
        return nil, err
    }

    // Download and verify SNS signing certificate
    // Verify message signature
    // Handle subscription confirmation if needed

    return &snsMessage, nil
}
```

#### 3. Infrastructure as Code Templates
```go
// Generate CloudFormation/Terraform templates for users
func (s *CloudWatchServer) GenerateInfrastructureTemplate(tenantID string, templateType string) (string, error) {
    webhookURL := fmt.Sprintf("https://cloudwatch-webhook.%s/webhook/%s",
        s.config.Domain, tenantID)

    switch templateType {
    case "cloudformation":
        return s.generateCloudFormationTemplate(webhookURL)
    case "terraform":
        return s.generateTerraformTemplate(webhookURL)
    case "cdk":
        return s.generateCDKTemplate(webhookURL)
    }
}
```

### Required AWS Permissions

For CloudWatch webhook setup, users would need:
- **SNS permissions** - create topics and subscriptions
- **CloudWatch permissions** - create and manage alarms
- **EventBridge permissions** - create and manage rules
- **IAM permissions** - create service roles if needed

### Webhook Lifecycle Management

#### Infrastructure Setup
- User deploys CloudFormation/Terraform template
- SNS topic and subscription created automatically
- CloudWatch alarms and EventBridge rules configured
- Webhook endpoint automatically subscribed and confirmed

#### Template Generation
```go
func (s *CloudWatchServer) HandleSetup(tenantID string, awsAccountID string) error {
    // Generate infrastructure templates
    // Provide setup instructions
    // Create tenant mapping for AWS account
    // Monitor webhook subscription status
}
```

#### Subscription Management
- Handle SNS subscription confirmations automatically
- Monitor subscription health and status
- Re-confirm subscriptions if needed
- Handle subscription deletions

#### Error Handling
- Handle SNS delivery failures and retries
- Monitor webhook delivery success rates
- Alert on subscription failures
- Provide troubleshooting guidance

## Security Considerations

### SNS Signature Verification

AWS SNS provides robust message authentication:

```go
// SNS signature verification
func (h *CloudWatchWebhookHandler) VerifySignature(message *SNSMessage) error {
    // Download SNS signing certificate
    cert, err := h.downloadSNSCertificate(message.SigningCertURL)
    if err != nil {
        return err
    }

    // Verify certificate chain
    // Verify message signature using certificate
    // Check message timestamp for replay protection

    return nil
}
```

### Tenant Isolation

- **AWS account mapping**: Events routed to correct tenant based on AWS account
- **Topic-based routing**: Use SNS topic ARN for tenant identification
- **Cross-account security**: Handle multi-account AWS setups securely
- **Resource tagging**: Use AWS tags for additional filtering

### AWS Security Best Practices

- **IAM least privilege** - minimal required permissions
- **VPC endpoints** - secure SNS communication
- **Encryption in transit** - HTTPS webhook endpoints
- **Access logging** - CloudTrail integration for audit

## Monitoring and Reliability

### Webhook Health Monitoring
```go
var cloudWatchWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
    Name: "cloudwatch_webhook_request_total",
    Help: "Counter of handled CloudWatch webhook requests",
}, []string{"status_code", "event_type", "aws_account"})
```

### SNS Delivery Monitoring
- **SNS delivery status** - monitor successful deliveries
- **Subscription health** - track subscription status
- **Message processing** - monitor webhook processing latency
- **Error rates** - track and alert on processing failures

### AWS Integration Monitoring
- **CloudWatch metrics** - monitor alarm and event volumes
- **EventBridge metrics** - track rule execution and failures
- **Cross-service correlation** - monitor AWS service dependencies

## Advanced Features

### Multi-Account Support
- **Organization-wide** webhook management
- **Cross-account** event aggregation
- **Centralized monitoring** for AWS Organizations
- **Account-specific** routing and processing

### Custom Metrics and Events
- **Application metrics** - custom CloudWatch metrics
- **Business events** - custom EventBridge events
- **Log-based alarms** - CloudWatch Logs metric filters
- **Composite monitoring** - multi-metric alarm conditions

### AWS Service Integration
- **AWS Config** - configuration change events
- **AWS Security Hub** - security finding events
- **AWS Cost Explorer** - cost anomaly detection
- **AWS Systems Manager** - operational events

## Migration Path

### Phase 1: Basic CloudWatch Integration
1. Create CloudWatch webhook listener service
2. Implement SNS signature verification
3. Support basic CloudWatch alarm events
4. Generate infrastructure templates

### Phase 2: EventBridge Integration
1. Add EventBridge event support
2. Implement custom event processing
3. Support AWS service events
4. Add multi-service correlation

### Phase 3: Advanced AWS Features
1. Multi-account support and management
2. Custom metrics and events integration
3. AWS security and compliance events
4. Cost and billing integration

### Phase 4: Enterprise Features
1. AWS Organizations integration
2. Advanced monitoring and analytics
3. Custom dashboard and reporting
4. Enterprise compliance and governance

## Implementation Effort Comparison (Updated)

| Approach | Datadog | Sentry | CircleCI | Microsoft Teams | AWS CloudWatch |
|----------|---------|--------|----------|-----------------|----------------|
| **User Setup** | ⚠️ Medium | ⚠️ Medium | ⚠️ Medium | ❌ Complex | ❌ **Complex** |
| **Event Richness** | ✅ Very High | ✅ Very High | ✅ High | ✅ Very High | ✅ **Very High** |
| **API Quality** | ✅ Excellent | ✅ Excellent | ✅ Excellent | ⚠️ Good (Dual) | ⚠️ **Good (IaC)** |
| **Auth Complexity** | ⚠️ API Key + App Key | ⚠️ OAuth + API | ⚠️ OAuth + API | ❌ Azure AD + Complex | ⚠️ **AWS IAM** |
| **Infrastructure** | ❌ New needed | ❌ New needed | ❌ New needed | ❌ New needed | ❌ **New needed** |
| **Implementation Effort** | ⚠️ Medium | ⚠️ Medium | ⚠️ Medium | ❌ Medium-High | ⚠️ **Medium** |
| **Market Coverage** | ⚠️ ~60% | ⚠️ ~70% | ⚠️ ~40% | ✅ ~80% (Enterprise) | ✅ **~85% (Cloud)** |
| **Trigger Value** | ✅ Very High | ✅ Very High | ✅ High | ✅ Very High (Enterprise) | ✅ **Very High** |

## Summary

**Key Advantages of AWS CloudWatch for Triggers**:
- **Highest cloud adoption** - dominant cloud platform with massive user base
- **Comprehensive AWS coverage** - monitors entire AWS ecosystem
- **Native AWS integration** - deep integration with all AWS services
- **Infrastructure as Code** - declarative configuration and automation
- **Enterprise reliability** - robust SNS delivery and AWS infrastructure

**Key Challenges**:
- **Complex setup** - requires AWS infrastructure configuration
- **AWS expertise required** - need understanding of SNS, CloudWatch, EventBridge
- **Multi-account complexity** - challenging for large AWS Organizations
- **Infrastructure as Code dependency** - less dynamic than API-based webhooks

**Recommended Approach**:
- **High priority for AWS customers** - essential for AWS-heavy organizations
- **Infrastructure-focused triggers** provide immediate value for DevOps teams
- **Cost and security triggers** help with AWS governance and optimization
- **Template-based setup** to simplify user configuration

**AWS CloudWatch Implementation Requirements**:
- New CloudWatch webhook listener service
- SNS signature verification and message processing
- Infrastructure as Code template generation
- AWS account-to-tenant mapping and routing
- CloudWatch alarm and EventBridge event processing

**Benefits of AWS CloudWatch Integration**:
- **Comprehensive AWS monitoring** - covers entire AWS infrastructure
- **Automated incident response** - immediate reaction to AWS service issues
- **Cost optimization** - automated response to cost and resource anomalies
- **Security automation** - automated response to security and compliance events

AWS CloudWatch provides the highest value triggers for AWS-centric organizations, offering comprehensive cloud infrastructure monitoring that enables effective automated incident response and infrastructure optimization, though with higher setup complexity than other platforms.
