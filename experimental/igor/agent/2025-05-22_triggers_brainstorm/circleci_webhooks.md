# CircleCI Webhooks in Augment

This document explains how CircleCI webhook registration and management works, and how it compares to other platform webhooks for the triggers feature.

## CircleCI Webhook Architecture Overview

### Project-Level Webhook Model

CircleCI uses a **project-level webhook** approach that provides comprehensive CI/CD pipeline events:

- **Project Webhooks**: Configure webhooks per CircleCI project
- **Organization Webhooks**: Organization-wide webhook support (Enterprise)
- **Event Type Selection**: Choose specific pipeline and workflow events
- **Branch Filtering**: Filter events by specific branches
- **Flexible Payload**: Rich build and deployment context

### CircleCI Integration Characteristics

**CircleCI webhook model**:
- **Project-scoped**: Webhooks configured per CircleCI project
- **Pipeline-driven**: Real-time notifications for builds, tests, and deployments
- **Rich metadata**: Comprehensive build context and artifact information
- **Branch awareness**: Branch-specific event filtering
- **Workflow support**: Multi-job workflow events

## CircleCI Webhook Registration Process

### How CircleCI Webhooks Work

CircleCI webhooks are registered **per project** with flexible event filtering:

#### Manual Registration (CircleCI UI)
1. **Project Settings**: Navigate to Project Settings > Webhooks
2. **Webhook Creation**: Create webhook with URL and event subscriptions
3. **Event Selection**: Choose specific event types to receive
4. **Branch Filtering**: Optionally filter to specific branches
5. **Secret Configuration**: Set webhook signing secret for verification

#### API Registration (Programmatic Approach)

**CircleCI Webhook API v2**:
```json
POST /api/v2/webhook
{
    "name": "Augment Triggers Webhook",
    "url": "https://circleci-webhook.augmentcode.com/webhook/{tenant-id}",
    "events": [
        "workflow-completed",
        "job-completed",
        "pipeline-completed"
    ],
    "scope": {
        "id": "project-uuid",
        "type": "project"
    },
    "signing-secret": "webhook-secret-key" // pragma: allowlist secret
}
```

### CircleCI Event Types

CircleCI supports comprehensive CI/CD event types for triggers:

#### Pipeline Events
- **pipeline-completed** - Pipeline finished (success/failure)

#### Workflow Events
- **workflow-completed** - Workflow finished (success/failure)

#### Job Events
- **job-completed** - Individual job finished (success/failure)

### CircleCI Webhook Payload Structure

CircleCI webhook payloads provide rich CI/CD context:

```json
{
    "id": "webhook-uuid",
    "happened_at": "2023-01-15T10:30:00Z",
    "webhook": {
        "id": "webhook-id",
        "name": "Augment Triggers Webhook"
    },
    "type": "workflow-completed",
    "project": {
        "id": "project-uuid",
        "name": "my-app",
        "slug": "github/myorg/my-app"
    },
    "organization": {
        "id": "org-uuid",
        "name": "myorg"
    },
    "pipeline": {
        "id": "pipeline-uuid",
        "number": 123,
        "created_at": "2023-01-15T10:25:00Z",
        "trigger": {
            "type": "webhook",
            "received_at": "2023-01-15T10:25:00Z"
        },
        "vcs": {
            "provider_name": "GitHub",
            "origin_repository_url": "https://github.com/myorg/my-app",
            "target_repository_url": "https://github.com/myorg/my-app",
            "revision": "abc123def456",
            "commit": {
                "subject": "Fix critical bug in user authentication",
                "body": "- Fixed null pointer exception\n- Added additional validation",
                "author": {
                    "name": "John Doe",
                    "email": "<EMAIL>"
                }
            },
            "branch": "main"
        }
    },
    "workflow": {
        "id": "workflow-uuid",
        "name": "build-test-deploy",
        "created_at": "2023-01-15T10:25:00Z",
        "stopped_at": "2023-01-15T10:30:00Z",
        "url": "https://app.circleci.com/pipelines/github/myorg/my-app/123/workflows/workflow-uuid",
        "status": "failed"
    },
    "job": {
        "id": "job-uuid",
        "name": "test",
        "type": "build",
        "number": 456,
        "status": "failed",
        "started_at": "2023-01-15T10:27:00Z",
        "stopped_at": "2023-01-15T10:29:00Z"
    }
}
```

## Comparison: CircleCI vs Other Platform Webhooks

| Aspect | GitHub Webhooks | Slack Webhooks | Sentry Webhooks | CircleCI Webhooks |
|--------|-----------------|----------------|-----------------|-------------------|
| **Registration Model** | App-level | App-level | Organization-level | Project-level |
| **Event Configuration** | Static at app level | Static at app level | Dynamic per webhook | Dynamic per webhook |
| **User Setup Required** | Install GitHub App | Install Slack App | Configure webhook + events | Configure webhook + events |
| **Management** | Centralized by Augment | Centralized by Augment | Distributed per organization | Distributed per project |
| **API Registration** | Not supported | Not needed | Fully supported | Fully supported |
| **Event Filtering** | All-or-nothing | OAuth scope-based | Project + event type filtering | Branch + event type filtering |
| **Authentication** | GitHub App installation | OAuth + workspace permissions | API token + webhook secret | API token + signing secret |
| **Webhook Lifecycle** | Static (manual app updates) | Static (manual app updates) | Dynamic (create/update/delete) | Dynamic (create/update/delete) |
| **Current Implementation** | ✅ Implemented | ✅ Implemented | ❌ Not implemented | ❌ **Not implemented** |

## CircleCI Webhook Advantages for Triggers

### 1. Rich CI/CD Context
- **Complete pipeline information** with build status and timing
- **Git context** including commit details and branch information
- **Job-level details** for granular failure analysis
- **Artifact information** and test results
- **Environment details** and configuration

### 2. Workflow Intelligence
- **Multi-job workflows** with dependency tracking
- **Parallel execution** status and coordination
- **Approval workflows** for deployment gates
- **Matrix builds** across multiple environments

### 3. Deployment Tracking
- **Environment-specific deployments** (staging, production)
- **Deployment status** and rollback capabilities
- **Release correlation** with code changes
- **Infrastructure provisioning** events

### 4. Branch-Based Filtering
- **Branch-specific triggers** (main, develop, feature branches)
- **Pull request builds** vs merge builds
- **Tag-based releases** and versioning
- **Environment promotion** workflows

## CircleCI Triggers Use Cases

### Build Failure Triggers
- **"When build fails on main branch, create debugging agent"**
- **"When test suite fails, create test analysis agent"**
- **"When deployment fails, create rollback agent"**
- **"When security scan fails, create security review agent"**

### Deployment Triggers
- **"When deployment succeeds, create monitoring agent"**
- **"When production deployment completes, create smoke test agent"**
- **"When staging deployment ready, create QA notification agent"**
- **"When rollback triggered, create incident response agent"**

### Performance Triggers
- **"When build time exceeds threshold, create optimization agent"**
- **"When test coverage drops, create coverage improvement agent"**
- **"When artifact size increases significantly, create analysis agent"**

### Team Workflow Triggers
- **"When PR build passes, create merge readiness agent"**
- **"When release build completes, create release notes agent"**
- **"When critical pipeline fails, create escalation agent"**

## Implementation Strategy

### Implementation Effort: ⚠️ **Medium**

**Advantages**:
- **Well-documented API** for webhook management
- **Rich CI/CD payload** with comprehensive build context
- **Flexible filtering** by project, branch, and event type
- **Strong authentication** with signing secrets
- **Growing adoption** (~40% of teams use CircleCI)

**Implementation Requirements**:

#### 1. CircleCI Webhook Service
```go
// services/integrations/circleci/webhook_listener/main.go
type circleCIWebhookListener struct {
    publishClient pubsub.PublishClient
    tenantLookup  TenantLookup
}

func (cl *circleCIWebhookListener) handle(w http.ResponseWriter, r *http.Request) {
    // Verify webhook signature using CircleCI signing secret
    // Parse CircleCI webhook payload
    // Extract tenant ID from URL path or project mapping
    // Look up tenant from project mapping
    // Publish to tenant-specific pubsub topic
}
```

#### 2. Webhook Management Service
```go
// CircleCI webhook registration
func (s *CircleCIServer) CreateWebhook(tenantID string, projectID string, accessToken string) error {
    webhookURL := fmt.Sprintf("https://circleci-webhook.%s/webhook/%s",
        s.config.Domain, tenantID)

    payload := map[string]interface{}{
        "name": "Augment Triggers Webhook",
        "url": webhookURL,
        "events": []string{
            "workflow-completed",
            "job-completed",
            "pipeline-completed",
        },
        "scope": map[string]interface{}{
            "id":   projectID,
            "type": "project",
        },
        "signing-secret": generateWebhookSecret(),
    }

    // POST to /api/v2/webhook
    // Store webhook ID in tenant settings
}
```

#### 3. CircleCI OAuth Integration
```go
// Enhanced OAuth flow for CircleCI integration
func (s *CircleCIServer) HandleOAuth(tenantID string, code string) error {
    // Exchange code for access token
    // Get user's projects
    // Create webhooks for selected projects
    // Store project mappings for tenant
}
```

### Required CircleCI Permissions

For Augment-managed webhooks, users would need to grant:
- **Project admin** permissions
- **Webhook management** access
- **API access** for webhook registration
- **Organization read** access for project listing

### Webhook Lifecycle Management

#### Webhook Creation
- Triggered when user connects CircleCI with project permissions
- Create webhooks for selected projects
- Store webhook IDs and project mappings in tenant settings
- Handle webhook secret generation and storage

#### Webhook Updates
- Update event subscriptions when user modifies trigger configurations
- Update branch filters based on trigger conditions
- Handle webhook URL changes if infrastructure changes

#### Webhook Deletion
- Delete webhooks when user disconnects CircleCI integration
- Clean up webhook mappings and configurations
- Handle webhook deletion via CircleCI API

#### Error Handling
- Handle webhook delivery failures
- Retry failed webhook registrations
- Alert on webhook configuration errors
- Graceful fallback to API polling if webhooks fail

## Security Considerations

### Webhook Validation

CircleCI provides webhook signature validation:

```go
// Webhook signature verification
func (h *CircleCIWebhookHandler) VerifySignature(r *http.Request, secret string) ([]byte, error) {
    signature := r.Header.Get("circleci-signature")

    // Verify HMAC-SHA256 signature with signing secret
    // CircleCI uses v1=<signature> format
    return body, nil
}
```

### Tenant Isolation

- **Project mapping**: Events routed to correct tenant based on CircleCI project
- **Organization filtering**: Only process events for authorized projects
- **API token scoping**: Limit access to specific projects
- **Webhook secret validation**: Ensure webhook authenticity

### Privacy and Data Handling

- **Source code privacy**: Handle commit messages and code references carefully
- **Environment variables**: Protect sensitive build configuration
- **Artifact security**: Handle build artifacts according to security policies
- **Access control**: Respect CircleCI project permissions

## Monitoring and Reliability

### Webhook Health Monitoring
```go
var circleCIWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
    Name: "circleci_webhook_request_total",
    Help: "Counter of handled CircleCI webhook requests",
}, []string{"status_code", "event_type", "project"})
```

### Error Recovery
- Automatic webhook re-registration on failures
- Retry mechanisms for failed deliveries
- Handle CircleCI project changes/renames
- Graceful degradation to API polling if webhooks fail

### Rate Limiting
- **CircleCI rate limits**: Respect CircleCI's API rate limits
- **Webhook frequency**: Handle high-frequency build events
- **Burst protection**: Manage build spikes gracefully

## Advanced Features

### Multi-Project Support
- **Organization-wide** webhook management
- **Project selection** during setup
- **Bulk webhook** creation and management
- **Project grouping** for team-based triggers

### Branch Strategy Integration
- **GitFlow support** with branch-specific triggers
- **Feature branch** builds and testing
- **Release branch** deployment workflows
- **Hotfix branch** emergency procedures

### Environment Promotion
- **Multi-environment** deployment tracking
- **Promotion workflows** from staging to production
- **Approval gates** and manual interventions
- **Rollback procedures** and automation

## Migration Path

### Phase 1: Basic Webhook Infrastructure
1. Create CircleCI webhook listener service
2. Implement basic event routing to pubsub
3. Support core pipeline events (workflow-completed, job-completed)

### Phase 2: Webhook Management
1. Add webhook registration via CircleCI API
2. Implement webhook lifecycle management
3. Update CircleCI OAuth flow for webhook permissions
4. Handle project-to-tenant mapping

### Phase 3: Triggers Integration
1. Connect webhook events to triggers system
2. Implement CircleCI-specific trigger conditions
3. Add project and branch filtering
4. Support deployment and test events

### Phase 4: Advanced Features
1. Multi-project webhook management
2. Branch strategy integration
3. Environment promotion workflows
4. Advanced filtering and transformation

## Implementation Effort Comparison (Updated)

| Approach | GitHub | Slack | Sentry | JIRA Cloud | CircleCI |
|----------|---------|-------|--------|------------|----------|
| **User Setup** | ✅ Simple | ✅ Simple | ⚠️ Medium | ⚠️ Medium | ⚠️ **Medium** |
| **Event Richness** | ⚠️ Medium | ⚠️ Medium | ✅ Very High | ⚠️ Medium | ✅ **High** |
| **API Quality** | ❌ Limited | ✅ Good | ✅ Excellent | ✅ Good | ✅ **Excellent** |
| **Auth Complexity** | ✅ Simple | ✅ Simple | ⚠️ OAuth + API | ⚠️ OAuth | ⚠️ **OAuth + API** |
| **Infrastructure** | ✅ Existing | ✅ Existing | ❌ New needed | ❌ New needed | ❌ **New needed** |
| **Implementation Effort** | ✅ Low | ✅ Low | ⚠️ Medium | ⚠️ Medium | ⚠️ **Medium** |
| **Market Coverage** | ✅ ~90% | ✅ ~95% | ⚠️ ~70% | ⚠️ ~70% | ⚠️ **~40%** |
| **Trigger Value** | ⚠️ Medium | ⚠️ Medium | ✅ Very High | ⚠️ Medium | ✅ **High** |

## Summary

**Key Advantages of CircleCI for Triggers**:
- **High trigger value** - build failures and deployments are highly actionable
- **Rich CI/CD context** - comprehensive pipeline and deployment information
- **Branch awareness** - sophisticated branch-based filtering
- **Workflow intelligence** - multi-job pipeline coordination
- **Strong API** - well-documented webhook and management APIs
- **DevOps focus** - specifically targets CI/CD automation needs

**Recommended Approach**:
- **Medium priority** for triggers implementation
- **CI/CD-focused triggers** provide immediate value for DevOps teams
- **Build failure triggers** help with rapid issue resolution
- **Deployment triggers** enable automated monitoring and rollback

**CircleCI Implementation Requirements**:
- New CircleCI webhook listener service
- Webhook management via CircleCI API v2
- Enhanced CircleCI OAuth flow with webhook permissions
- Project-to-tenant mapping and routing
- Pipeline and deployment event processing

**Benefits of CircleCI Integration**:
- **Automated build failure response** - immediate debugging for failed builds
- **Deployment monitoring** - automated post-deployment verification
- **Performance tracking** - monitor build and test performance trends
- **Release automation** - streamlined release and rollback procedures

CircleCI provides valuable CI/CD triggers for development teams, offering rich pipeline context and deployment tracking that enables effective automated DevOps responses and build failure resolution.
