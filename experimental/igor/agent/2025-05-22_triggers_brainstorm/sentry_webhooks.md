# Sentry Webhooks in Augment

This document explains how Sentry webhook registration and management works, and how it compares to other platform webhooks for the triggers feature.

## Sentry Webhook Architecture Overview

### Organization-Level Webhook Model

Sentry uses an **organization-level webhook** approach that provides comprehensive error and performance monitoring events:

- **Organization Webhooks**: Configure webhooks at the organization level
- **Project-Specific Filtering**: Can filter events by specific projects
- **Event Type Selection**: Choose which types of events to receive
- **Custom Payload Support**: Flexible webhook payload configuration
- **Integration Platform**: Part of Sentry's broader integration ecosystem

### Sentry Integration Characteristics

**Sentry webhook model**:
- **Organization-scoped**: Webhooks configured per Sentry organization
- **Event-driven**: Real-time notifications for errors, issues, and performance events
- **Rich metadata**: Comprehensive error context and stack traces
- **Issue grouping**: Intelligent error grouping and deduplication
- **Performance monitoring**: Application performance and transaction events

## Sentry Webhook Registration Process

### How Sentry Webhooks Work

Sentry webhooks are registered **per organization** with flexible event filtering:

#### Manual Registration (Sentry Admin UI)
1. **Organization Settings**: Navigate to Settings > Developer Settings > Webhooks
2. **Webhook Creation**: Create webhook with URL and event subscriptions
3. **Event Selection**: Choose specific event types to receive
4. **Project Filtering**: Optionally filter to specific projects
5. **Secret Configuration**: Set webhook secret for payload verification

#### API Registration (Programmatic Approach)

**Sentry Webhook API**:
```json
POST /api/0/organizations/{organization_slug}/webhooks/
{
    "url": "https://sentry-webhook.augmentcode.com/webhook/{tenant-id}",
    "events": [
        "error.created",
        "issue.created",
        "issue.resolved",
        "issue.assigned"
    ],
    "projects": ["project-1", "project-2"]
}
```

### Sentry Event Types

Sentry supports comprehensive event types for triggers:

#### Error Events
- **error.created** - New error captured
- **issue.created** - New issue created (grouped errors)
- **issue.resolved** - Issue marked as resolved
- **issue.assigned** - Issue assigned to user/team
- **issue.ignored** - Issue marked as ignored

#### Performance Events
- **transaction.created** - Performance transaction captured
- **alert.triggered** - Performance alert triggered
- **alert.resolved** - Performance alert resolved

#### Release Events
- **release.created** - New release deployed
- **release.deployed** - Release deployed to environment

### Sentry Webhook Payload Structure

Sentry webhook payloads are rich with debugging context:

```json
{
    "action": "created",
    "installation": {
        "uuid": "abc123-def456-ghi789"
    },
    "data": {
        "error": {
            "id": "123456789",
            "title": "TypeError: Cannot read property 'foo' of undefined",
            "culprit": "app/components/UserProfile.js in handleClick",
            "level": "error",
            "platform": "javascript",
            "environment": "production",
            "release": "1.2.3",
            "tags": [
                ["browser", "Chrome 96.0"],
                ["user.id", "user123"],
                ["url", "https://app.example.com/profile"]
            ],
            "exception": {
                "values": [
                    {
                        "type": "TypeError",
                        "value": "Cannot read property 'foo' of undefined",
                        "stacktrace": {
                            "frames": [
                                {
                                    "filename": "app/components/UserProfile.js",
                                    "function": "handleClick",
                                    "lineno": 42,
                                    "colno": 15,
                                    "context_line": "const value = user.profile.foo.bar;",
                                    "pre_context": ["function handleClick() {", "  const user = getUser();"],
                                    "post_context": ["  updateUI(value);", "}"]
                                }
                            ]
                        }
                    }
                ]
            },
            "user": {
                "id": "user123",
                "email": "<EMAIL>",
                "username": "john_doe"
            },
            "request": {
                "url": "https://app.example.com/profile",
                "method": "GET",
                "headers": {
                    "User-Agent": "Mozilla/5.0 (Chrome/96.0)"
                }
            }
        }
    },
    "actor": {
        "type": "application",
        "id": "sentry",
        "name": "Sentry"
    }
}
```

## Comparison: Sentry vs Other Platform Webhooks

| Aspect | GitHub Webhooks | Slack Webhooks | JIRA Webhooks | Sentry Webhooks |
|--------|-----------------|----------------|---------------|-----------------|
| **Registration Model** | App-level | App-level | Instance-level | Organization-level |
| **Event Configuration** | Static at app level | Static at app level | Dynamic per webhook | Dynamic per webhook |
| **User Setup Required** | Install GitHub App | Install Slack App | Configure webhook + JQL | Configure webhook + events |
| **Management** | Centralized by Augment | Centralized by Augment | Distributed per instance | Distributed per organization |
| **API Registration** | Not supported | Not needed | Fully supported | Fully supported |
| **Event Filtering** | All-or-nothing | OAuth scope-based | Advanced JQL filtering | Project + event type filtering |
| **Authentication** | GitHub App installation | OAuth + workspace permissions | OAuth + admin permissions | API token + webhook secret |
| **Webhook Lifecycle** | Static (manual app updates) | Static (manual app updates) | Dynamic (create/update/delete) | Dynamic (create/update/delete) |
| **Current Implementation** | ✅ Implemented | ✅ Implemented | ❌ Not implemented | ❌ **Not implemented** |

## Sentry Webhook Advantages for Triggers

### 1. Rich Error Context
- **Complete stack traces** with source code context
- **User information** and session data
- **Environment details** (browser, OS, device)
- **Request context** (URL, headers, parameters)
- **Release tracking** for deployment correlation

### 2. Intelligent Issue Grouping
- **Automatic error grouping** reduces noise
- **Issue lifecycle events** (created, resolved, assigned)
- **Fingerprinting** for consistent grouping
- **Regression detection** when resolved issues reoccur

### 3. Performance Monitoring
- **Transaction events** for performance issues
- **Alert integration** for threshold breaches
- **Release performance** tracking
- **User experience** metrics

### 4. Multi-Project Support
- **Organization-wide** webhook coverage
- **Project-specific** filtering capabilities
- **Team-based** access control
- **Environment separation** (dev, staging, prod)

## Sentry Triggers Use Cases

### Error Response Triggers
- **"When critical error occurs, create debugging agent"**
- **"When error rate spikes, create investigation agent"**
- **"When new error type appears, create analysis agent"**
- **"When user reports error, create support agent"**

### Performance Triggers
- **"When transaction time exceeds threshold, create optimization agent"**
- **"When error rate increases after deployment, create rollback agent"**
- **"When memory usage spikes, create performance agent"**

### Release Management Triggers
- **"When release deployed, create monitoring agent"**
- **"When release has high error rate, create hotfix agent"**
- **"When regression detected, create investigation agent"**

### Team Workflow Triggers
- **"When issue assigned to team, create triage agent"**
- **"When critical issue unresolved for 1 hour, create escalation agent"**
- **"When issue resolved, create post-mortem agent"**

## Implementation Strategy

### Implementation Effort: ⚠️ **Medium**

**Advantages**:
- **Well-documented API** for webhook management
- **Rich event payload** with comprehensive error context
- **Flexible filtering** by project and event type
- **Strong authentication** with webhook secrets
- **High developer adoption** (~70% of teams use error tracking)

**Implementation Requirements**:

#### 1. Sentry Webhook Service
```go
// services/integrations/sentry/webhook_listener/main.go
type sentryWebhookListener struct {
    publishClient pubsub.PublishClient
    tenantLookup  TenantLookup
}

func (sl *sentryWebhookListener) handle(w http.ResponseWriter, r *http.Request) {
    // Verify webhook signature using Sentry secret
    // Parse Sentry webhook payload
    // Extract tenant ID from URL path or organization mapping
    // Look up tenant from organization mapping
    // Publish to tenant-specific pubsub topic
}
```

#### 2. Webhook Management Service
```go
// Sentry webhook registration
func (s *SentryServer) CreateWebhook(tenantID string, orgSlug string, accessToken string) error {
    webhookURL := fmt.Sprintf("https://sentry-webhook.%s/webhook/%s",
        s.config.Domain, tenantID)

    payload := map[string]interface{}{
        "url": webhookURL,
        "events": []string{
            "error.created",
            "issue.created",
            "issue.resolved",
            "issue.assigned",
            "alert.triggered",
            "release.created",
        },
        "projects": []string{}, // All projects by default
    }

    // POST to /api/0/organizations/{org_slug}/webhooks/
    // Store webhook ID in tenant settings
}
```

#### 3. Sentry OAuth Integration
```go
// Enhanced OAuth flow for Sentry integration
func (s *SentryServer) HandleOAuth(tenantID string, code string) error {
    // Exchange code for access token
    // Get organization information
    // Create webhook for organization
    // Store organization mapping for tenant
}
```

### Required Sentry Permissions

For Augment-managed webhooks, users would need to grant:
- **Organization admin** permissions
- **Webhook management** access
- **Project read** access for filtering
- **API access** for webhook registration

### Webhook Lifecycle Management

#### Webhook Creation
- Triggered when user connects Sentry with admin permissions
- Create webhook with comprehensive event subscriptions
- Store webhook ID and organization slug in tenant settings
- Handle webhook secret generation and storage

#### Webhook Updates
- Update event subscriptions when user modifies trigger configurations
- Update project filters based on trigger conditions
- Handle webhook URL changes if infrastructure changes

#### Webhook Deletion
- Delete webhook when user disconnects Sentry integration
- Clean up webhook mappings and configurations
- Handle webhook deletion via Sentry API

#### Error Handling
- Handle webhook delivery failures
- Retry failed webhook registrations
- Alert on webhook configuration errors
- Graceful fallback to API polling if webhooks fail

## Security Considerations

### Webhook Validation

Sentry provides robust webhook validation:

```go
// Webhook signature verification
func (h *SentryWebhookHandler) VerifySignature(r *http.Request, secret string) ([]byte, error) {
    signature := r.Header.Get("Sentry-Hook-Signature")
    timestamp := r.Header.Get("Sentry-Hook-Timestamp")

    // Verify HMAC signature with webhook secret
    // Check timestamp to prevent replay attacks
    return body, nil
}
```

### Tenant Isolation

- **Organization mapping**: Events routed to correct tenant based on Sentry organization
- **Project filtering**: Only process events for authorized projects
- **API token scoping**: Limit access to specific organizations
- **Webhook secret validation**: Ensure webhook authenticity

### Privacy and Data Handling

- **Error data sensitivity**: Handle PII in error messages carefully
- **Source code exposure**: Respect source code privacy in stack traces
- **User data protection**: Handle user information according to privacy policies
- **Retention policies**: Align with Sentry's data retention settings

## Monitoring and Reliability

### Webhook Health Monitoring
```go
var sentryWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
    Name: "sentry_webhook_request_total",
    Help: "Counter of handled Sentry webhook requests",
}, []string{"status_code", "event_type", "organization"})
```

### Error Recovery
- Automatic webhook re-registration on failures
- Retry mechanisms for failed deliveries
- Handle Sentry organization changes/renames
- Graceful degradation to API polling if webhooks fail

### Rate Limiting
- **Sentry rate limits**: Respect Sentry's API rate limits
- **Webhook frequency**: Handle high-frequency error events
- **Burst protection**: Manage error spikes gracefully

## Advanced Features

### Error Fingerprinting
- **Custom grouping rules** for better issue organization
- **Fingerprint-based triggers** for specific error patterns
- **Release correlation** for deployment-related issues

### Performance Integration
- **Transaction monitoring** for performance triggers
- **Alert correlation** with error events
- **Release performance** tracking

### Multi-Environment Support
- **Environment-specific triggers** (prod vs staging)
- **Release environment** correlation
- **Environment-based routing** for different teams

## Migration Path

### Phase 1: Basic Webhook Infrastructure
1. Create Sentry webhook listener service
2. Implement basic event routing to pubsub
3. Support core error events (error.created, issue.created)

### Phase 2: Webhook Management
1. Add webhook registration via Sentry API
2. Implement webhook lifecycle management
3. Update Sentry OAuth flow for webhook permissions
4. Handle organization-to-tenant mapping

### Phase 3: Triggers Integration
1. Connect webhook events to triggers system
2. Implement Sentry-specific trigger conditions
3. Add project and environment filtering
4. Support performance and release events

### Phase 4: Advanced Features
1. Error fingerprinting and custom grouping
2. Performance monitoring integration
3. Advanced filtering and transformation
4. Multi-environment and team routing

## Implementation Effort Comparison (Updated)

| Approach | GitHub | Slack | Linear | JIRA Cloud | Sentry |
|----------|---------|-------|--------|------------|--------|
| **User Setup** | ✅ Simple | ✅ Simple | ⚠️ Medium | ⚠️ Medium | ⚠️ **Medium** |
| **Event Richness** | ⚠️ Medium | ⚠️ Medium | ⚠️ Medium | ⚠️ Medium | ✅ **Very High** |
| **API Quality** | ❌ Limited | ✅ Good | ✅ Excellent | ✅ Good | ✅ **Excellent** |
| **Auth Complexity** | ✅ Simple | ✅ Simple | ⚠️ OAuth | ⚠️ OAuth | ⚠️ **OAuth + API** |
| **Infrastructure** | ✅ Existing | ✅ Existing | ❌ New needed | ❌ New needed | ❌ **New needed** |
| **Implementation Effort** | ✅ Low | ✅ Low | ⚠️ Medium | ⚠️ Medium | ⚠️ **Medium** |
| **Market Coverage** | ✅ ~90% | ✅ ~95% | ⚠️ ~60% | ⚠️ ~70% | ⚠️ **~70%** |
| **Trigger Value** | ⚠️ Medium | ⚠️ Medium | ⚠️ Medium | ⚠️ Medium | ✅ **Very High** |

## Summary

**Key Advantages of Sentry for Triggers**:
- **Highest trigger value** - errors and performance issues are highly actionable
- **Rich error context** - comprehensive debugging information
- **Intelligent grouping** - reduces noise through smart issue aggregation
- **Performance monitoring** - covers both errors and performance
- **Strong API** - well-documented webhook and management APIs
- **High adoption** - widely used by development teams

**Recommended Approach**:
- **High priority** for triggers implementation after Slack and GitHub
- **Error-focused triggers** provide immediate value for debugging
- **Performance triggers** help with optimization and monitoring
- **Organization-level webhooks** provide comprehensive coverage

**Sentry Implementation Requirements**:
- New Sentry webhook listener service
- Webhook management via Sentry API
- Enhanced Sentry OAuth flow with webhook permissions
- Organization-to-tenant mapping and routing
- Error and performance event processing

**Benefits of Sentry Integration**:
- **Immediate debugging value** - errors trigger automated investigation
- **Proactive issue resolution** - catch problems before users report them
- **Performance optimization** - automated response to performance issues
- **Release monitoring** - track deployment impact on error rates

Sentry provides the highest value triggers for development teams, offering rich error context and performance data that enables highly effective automated debugging and optimization agents.
