# Linear Webhooks in Augment

This document explains how Linear webhook registration and management works, and how it differs from GitHub webhooks for the triggers feature.

## Linear Webhook Architecture Overview

### Per-Workspace Webhook Model

Linear uses a **per-workspace webhook** approach that differs significantly from GitHub's app-based model:

- **Individual Webhook URLs**: Each Linear workspace/team can have its own webhook configuration
- **Dynamic Event Configuration**: Can choose which events to subscribe to per webhook
- **Direct Registration**: Webhooks can be registered via Linear's UI or GraphQL API
- **Admin Permissions Required**: Creating webhooks requires admin permissions in the Linear workspace

### Current Linear Integration

**Important**: Your current Linear integration does NOT use webhooks. Instead, it uses a pull-based API approach:

```python
# services/integrations/linear/server/linear_handler.py
# Setup Linear client with appropriate Auth
transport = RequestsHTTPTransport(
    url="https://api.linear.app/graphql",
    headers={"Authorization": credentials.get_secret_value()},
)
client = Client(transport=transport)
result = client.execute(gql(gql_query))
```

**Current architecture**:
- **GraphQL API calls** for querying Linear data on-demand
- **OAuth authentication** for user authorization
- **No webhook listener** for real-time Linear events
- **Pull-based** rather than push-based event handling

## Linear Webhook Registration Process

### How Linear Webhooks Work

Unlike GitHub Apps, Linear webhooks are registered **per workspace**:

#### Manual Registration (Current Linear Approach)
1. **Admin Access**: User must have admin permissions in Linear workspace
2. **Settings Configuration**: Navigate to Settings > Account > API in Linear
3. **Webhook Creation**: Create webhook with specific URL and event selections
4. **Event Selection**: Choose which events to subscribe to (issues, comments, etc.)

#### API Registration (Programmatic Approach)
```graphql
# Linear GraphQL mutation to create webhook
mutation WebhookCreate($input: WebhookCreateInput!) {
  webhookCreate(input: $input) {
    success
    webhook {
      id
      url
      enabled
      resourceTypes
    }
  }
}
```

### Linear Webhook Events

Linear supports these webhook events:

#### Core Entity Events
- **Issues**: `Issue` (created, updated, deleted)
- **Comments**: `Comment` (created, updated, deleted)
- **Issue attachments**: `Attachment` (created, deleted)
- **Documents**: `Document` (created, updated, deleted)
- **Emoji reactions**: `Reaction` (created, deleted)

#### Project Management Events
- **Projects**: `Project` (created, updated, deleted)
- **Project updates**: `ProjectUpdate` (created, updated, deleted)
- **Cycles**: `Cycle` (created, updated, deleted)
- **Labels**: `IssueLabel` (created, updated, deleted)

#### User and SLA Events
- **Users**: `User` (created, updated)
- **Issue SLAs**: `IssueSla` (breached, high risk)

### Webhook Payload Structure

Linear webhook payloads include:
```json
{
  "action": "create|update|remove",
  "data": {
    // Full entity object
  },
  "updatedFrom": {
    // Previous values for updated fields (on updates)
  },
  "url": "https://linear.app/workspace/issue/ABC-123",
  "type": "Issue",
  "organizationId": "org-id",
  "webhookTimestamp": 1640995200000,
  "webhookId": "webhook-id"
}
```

## Comparison: Linear vs GitHub Webhooks

| Aspect | GitHub Webhooks | Linear Webhooks |
|--------|-----------------|-----------------|
| **Registration Model** | App-level (one webhook per GitHub App) | Workspace-level (multiple webhooks possible) |
| **Event Configuration** | Static at GitHub App level | Dynamic per webhook |
| **User Setup Required** | Install GitHub App only | Configure webhook URL + events |
| **Management** | Centralized by Augment | Distributed per workspace |
| **API Registration** | Not supported for event subscriptions | Fully supported via GraphQL |
| **Webhook Lifecycle** | Static (manual GitHub App updates) | Dynamic (create/update/delete via API) |
| **Authentication** | GitHub App installation | OAuth + admin permissions |
| **Event Granularity** | All-or-nothing per event type | Configurable per webhook |

## Implications for Triggers Feature

### Current State: No Linear Webhooks

Since your current Linear integration doesn't use webhooks, implementing Linear triggers would require building webhook infrastructure from scratch.

### Implementation Options

#### Option 1: User-Configured Webhooks

**Users register webhooks themselves:**

**Pros**:
- Simple implementation
- Users control their webhook configuration
- No need for Augment to manage Linear admin permissions

**Cons**:
- Complex user setup process
- Users need admin permissions in Linear
- Inconsistent with GitHub App experience
- Manual webhook management

**User flow**:
1. User connects Linear OAuth in Augment
2. Augment provides webhook URL for their tenant
3. User manually configures webhook in Linear settings
4. User selects which events to subscribe to
5. Triggers work for subscribed events

#### Option 2: Augment-Managed Webhooks (Recommended)

**Augment registers webhooks programmatically:**

**Pros**:
- Consistent with GitHub App experience
- Automatic setup for users
- Centralized webhook management
- Better user experience

**Cons**:
- Requires Linear admin permissions from users
- More complex implementation
- Need to handle webhook lifecycle management

**User flow**:
1. User connects Linear OAuth with admin permissions
2. Augment automatically creates webhook via Linear API
3. Augment manages webhook configuration
4. Triggers work automatically
5. Webhook deleted when user disconnects Linear

### Recommended Architecture

For consistency with the GitHub integration, implement **Option 2**:

```
Linear.app → Per-Tenant Webhook → Linear Webhook Service → Event Router → Triggers
```

#### Required Components

1. **Linear Webhook Service**
   - Similar to `services/integrations/github/webhook_listener`
   - Handle incoming Linear webhook events
   - Validate webhook signatures
   - Route events to tenant-specific pubsub topics

2. **Webhook Management Service**
   - Create webhooks when users connect Linear
   - Update webhook configurations as needed
   - Delete webhooks when users disconnect
   - Handle webhook failures and retries

3. **Linear OAuth Enhancement**
   - Request admin permissions during OAuth flow
   - Store webhook IDs for management
   - Handle permission errors gracefully

#### Webhook Service Architecture

```go
// services/integrations/linear/webhook_listener/main.go
type linearWebhookListener struct {
    webhookSecret secretstring.SecretString
    publishClient pubsub.PublishClient
    tenantLookup  TenantLookup
}

func (ll *linearWebhookListener) handle(w http.ResponseWriter, r *http.Request) {
    // Validate Linear webhook signature
    // Parse Linear webhook payload
    // Look up tenant from webhook ID or organization ID
    // Publish to tenant-specific pubsub topic
}
```

#### Webhook Registration Flow

```go
// When user connects Linear with admin permissions
func (s *LinearServer) CreateWebhook(tenantID string, accessToken string) error {
    webhookURL := fmt.Sprintf("https://linear-webhook.%s/webhook/%s",
        s.config.Domain, tenantID)

    // Create webhook via Linear GraphQL API
    mutation := `
        mutation WebhookCreate($input: WebhookCreateInput!) {
            webhookCreate(input: $input) {
                success
                webhook { id }
            }
        }
    `

    variables := map[string]interface{}{
        "input": map[string]interface{}{
            "url": webhookURL,
            "resourceTypes": []string{"Issue", "Comment", "Project", "Cycle"},
        },
    }

    // Execute mutation and store webhook ID
}
```

### Event Processing Differences

#### GitHub Event Processing
```go
// Single global webhook, route by installation ID
switch event.GetInstallation().GetId() {
case installationID:
    // Route to tenant
}
```

#### Linear Event Processing (Proposed)
```go
// Per-tenant webhook, route by webhook ID or organization ID
switch payload.OrganizationId {
case orgID:
    // Look up tenant from organization mapping
}
```

### Required Linear Permissions

For Augment-managed webhooks, users would need to grant these Linear permissions:
- **Admin permissions** in the Linear workspace
- **OAuth scopes**: `read`, `write`, `admin` (for webhook creation)

### Webhook Lifecycle Management

#### Webhook Creation
- Triggered when user connects Linear with admin permissions
- Create webhook with all relevant event types
- Store webhook ID in tenant settings

#### Webhook Updates
- Update event subscriptions when user modifies trigger configurations
- Handle webhook URL changes if infrastructure changes

#### Webhook Deletion
- Delete webhook when user disconnects Linear integration
- Clean up webhook mappings and configurations

#### Error Handling
- Handle webhook delivery failures
- Retry failed webhook registrations
- Alert on webhook configuration errors

## Security Considerations

### Webhook Validation

Linear webhooks should be validated using signature verification:

```go
// Validate Linear webhook signature
func validateLinearWebhook(payload []byte, signature string, secret string) bool {
    mac := hmac.New(sha256.New, []byte(secret))
    mac.Write(payload)
    expectedSignature := hex.EncodeToString(mac.Sum(nil))
    return hmac.Equal([]byte(signature), []byte(expectedSignature))
}
```

### Tenant Isolation

- **Webhook ID Mapping**: Map webhook IDs to tenant IDs securely
- **Organization Validation**: Verify organization ID matches tenant permissions
- **Event Filtering**: Only process events for authorized users

## Migration Path

### Phase 1: Basic Webhook Infrastructure
1. Create Linear webhook listener service
2. Implement webhook signature validation
3. Set up basic event routing to pubsub

### Phase 2: Webhook Management
1. Add webhook registration via Linear API
2. Implement webhook lifecycle management
3. Update Linear OAuth flow for admin permissions

### Phase 3: Triggers Integration
1. Connect webhook events to triggers system
2. Implement Linear-specific trigger conditions
3. Add Linear event filtering and routing

### Phase 4: Advanced Features
1. Webhook health monitoring
2. Automatic webhook recovery
3. Advanced event filtering and transformation

## Monitoring and Reliability

### Webhook Health Monitoring
- Track webhook delivery success rates
- Monitor webhook response times
- Alert on webhook failures

### Error Recovery
- Automatic webhook re-registration on failures
- Retry mechanisms for failed deliveries
- Graceful degradation to API polling if webhooks fail

### Metrics and Logging
```go
var linearWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
    Name: "linear_webhook_request_total",
    Help: "Counter of handled Linear webhook requests",
}, []string{"status_code", "event_type"})
```

## Summary

**Key Differences from GitHub**:
- **Per-workspace registration** vs app-level registration
- **Dynamic event configuration** vs static configuration
- **API-based webhook management** vs manual GitHub App updates
- **Admin permissions required** vs simple app installation

**Recommended Approach**:
- **Augment-managed webhooks** for consistent user experience
- **Automatic webhook registration** when users connect Linear
- **Centralized webhook lifecycle management**
- **Similar architecture to GitHub webhook service**

**Implementation Requirements**:
- New Linear webhook listener service
- Webhook management via Linear GraphQL API
- Enhanced Linear OAuth flow with admin permissions
- Webhook-to-tenant mapping and routing

This approach would provide Linear triggers with real-time event processing while maintaining consistency with the existing GitHub integration architecture.
