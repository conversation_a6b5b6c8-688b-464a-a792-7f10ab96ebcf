# JIRA Webhooks in Augment

This document explains how JIRA webhook registration and management works, and how it compares to GitHub and Linear webhooks for the triggers feature. It covers both JIRA Cloud and Data Center environments, with specific implementation recommendations for each.

## JIRA Webhook Architecture Overview

### Per-Instance Webhook Model

JIRA uses a **per-instance webhook** approach that differs from both GitHub and Linear:

- **Individual Webhook URLs**: Each JIRA instance (Cloud or Data Center) can have multiple webhooks
- **Flexible Event Configuration**: Can choose specific events and JQL filters per webhook
- **Admin Registration**: Webhooks can be registered via JIRA admin UI or REST API
- **JQL Filtering**: Advanced filtering using JQL queries to scope webhook events
- **Admin Permissions Required**: Creating webhooks requires JIRA administrator permissions

### Current JIRA Integration

**Important**: Your current JIRA integration does NOT use webhooks. Instead, it uses a pull-based API approach:

```python
# services/integrations/atlassian/server/jira_client_wrapper.py
return JIRA(
    server=f"{JIRA_API_BASE_URL}{credentials.settings.cloud_id}",
    token_auth=credentials.settings.access_token,
)
```

**Current architecture**:
- **REST API calls** for querying JIRA data on-demand
- **OAuth authentication** for user authorization
- **No webhook listener** for real-time JIRA events
- **Pull-based** rather than push-based event handling

## JIRA Webhook Registration Process

### How JIRA Webhooks Work

JIRA webhooks are registered **per JIRA instance** with flexible configuration:

#### Manual Registration (JIRA Admin UI)
1. **Admin Access**: User must have JIRA administrator permissions
2. **Admin Console**: Navigate to Settings > System > WebHooks
3. **Webhook Creation**: Create webhook with URL, events, and JQL filters
4. **Event Selection**: Choose specific events (issues, projects, sprints, etc.)
5. **JQL Filtering**: Optional JQL query to filter which issues trigger the webhook

#### API Registration (Programmatic Approach)

**JIRA Cloud (REST API)**:
```json
POST /rest/webhooks/1.0/webhook
{
    "name": "Augment Triggers Webhook",
    "url": "https://jira-webhook.augmentcode.com/webhook/{tenant-id}",
    "events": [
        "jira:issue_created",
        "jira:issue_updated",
        "jira:issue_deleted",
        "comment_created",
        "comment_updated"
    ],
    "filters": {
        "issue-related-events-section": "project = PROJ"
    },
    "excludeBody": false
}
```

**JIRA Data Center (REST API)**:
```json
POST /rest/jira-webhook/1.0/webhooks
{
    "name": "Augment Triggers Webhook",
    "url": "https://jira-webhook.augmentcode.com/webhook/{tenant-id}",
    "events": [
        "jira:issue_created",
        "jira:issue_updated"
    ],
    "configuration": {
        "FILTERS": "project = PROJ AND assignee = currentUser()",
        "EXCLUDE_BODY": "false"
    },
    "active": "true"
}
```

### JIRA Webhook Events

JIRA supports extensive webhook events:

#### Issue-Related Events
- **Issues**: `jira:issue_created`, `jira:issue_updated`, `jira:issue_deleted`
- **Comments**: `comment_created`, `comment_updated`, `comment_deleted`
- **Worklogs**: `worklog_created`, `worklog_updated`, `worklog_deleted`, `jira:worklog_updated`
- **Issue Links**: `issuelink_created`, `issuelink_deleted`

#### Project-Related Events
- **Projects**: `project_created`, `project_updated`, `project_deleted`
- **Versions**: `jira:version_released`, `jira:version_unreleased`, `jira:version_created`, `jira:version_moved`, `jira:version_updated`, `jira:version_deleted`

#### User-Related Events
- **Users**: `user_created`, `user_updated`, `user_deleted`

#### JIRA Software Events (Agile)
- **Sprints**: `sprint_created`, `sprint_deleted`, `sprint_updated`, `sprint_started`, `sprint_closed`
- **Boards**: `board_created`, `board_updated`, `board_deleted`, `board_configuration_changed`

#### Configuration Events
- **Feature toggles**: `option_voting_changed`, `option_watching_changed`, `option_unassigned_issues_changed`, `option_subtasks_changed`, `option_attachments_changed`, `option_issuelinks_changed`, `option_timetracking_changed`

### JIRA Webhook Payload Structure

JIRA webhook payloads are comprehensive:

```json
{
    "timestamp": 1525698237764,
    "webhookEvent": "jira:issue_updated",
    "user": {
        "self": "https://jira.example.com/rest/api/2/user?username=admin",
        "name": "admin",
        "key": "admin",
        "emailAddress": "<EMAIL>",
        "displayName": "Administrator"
    },
    "issue": {
        "id": "99291",
        "self": "https://jira.example.com/rest/api/2/issue/99291",
        "key": "PROJ-123",
        "fields": {
            "summary": "Issue summary",
            "description": "Issue description",
            "priority": {"name": "High"},
            "status": {"name": "In Progress"},
            "assignee": {"displayName": "John Doe"},
            "created": "2023-01-01T10:00:00.000+0000",
            "updated": "2023-01-02T15:30:00.000+0000"
        }
    },
    "changelog": {
        "id": 10124,
        "items": [
            {
                "field": "status",
                "fieldtype": "jira",
                "from": "1",
                "fromString": "To Do",
                "to": "3",
                "toString": "In Progress"
            }
        ]
    },
    "comment": {
        "id": "252789",
        "body": "Work started on this issue",
        "author": {"displayName": "John Doe"},
        "created": "2023-01-02T15:30:00.000+0000"
    }
}
```

## Comparison: JIRA vs GitHub vs Linear Webhooks

| Aspect | GitHub Webhooks | Linear Webhooks | JIRA Webhooks |
|--------|-----------------|-----------------|---------------|
| **Registration Model** | App-level (one per GitHub App) | Workspace-level | Instance-level (multiple per instance) |
| **Event Configuration** | Static at app level | Dynamic per webhook | Dynamic per webhook |
| **User Setup Required** | Install GitHub App only | Configure webhook URL + events | Configure webhook URL + events + JQL |
| **Management** | Centralized by Augment | Distributed per workspace | Distributed per JIRA instance |
| **API Registration** | Not supported for events | Fully supported via GraphQL | Fully supported via REST API |
| **Event Filtering** | All-or-nothing per event type | Configurable per webhook | Advanced JQL filtering |
| **Authentication** | GitHub App installation | OAuth + admin permissions | OAuth + admin permissions |
| **Webhook Lifecycle** | Static (manual app updates) | Dynamic (create/update/delete) | Dynamic (create/update/delete) |
| **Variable Substitution** | Not supported | Not supported | Supported (${issue.key}, ${project.id}, etc.) |
| **JQL Filtering** | Not applicable | Not applicable | Advanced JQL queries |
| **Workflow Integration** | Not applicable | Not applicable | Can be attached to workflow transitions |

## Implications for Triggers Feature

### Current State: No JIRA Webhooks

Since your current JIRA integration doesn't use webhooks, implementing JIRA triggers would require building webhook infrastructure from scratch.

### Implementation Options

#### Option 1: User-Configured Webhooks

**Users register webhooks themselves:**

**Pros**:
- Simple implementation for Augment
- Users control their webhook configuration
- Leverages existing JIRA admin permissions

**Cons**:
- Complex user setup process
- Users need JIRA admin permissions
- Inconsistent with GitHub App experience
- Manual webhook management per JIRA instance

**User flow**:
1. User connects JIRA OAuth in Augment
2. Augment provides webhook URL for their tenant
3. User manually configures webhook in JIRA admin console
4. User selects events and configures JQL filters
5. Triggers work for configured events

#### Option 2: Augment-Managed Webhooks (Recommended)

**Augment registers webhooks programmatically:**

**Pros**:
- Consistent with GitHub App experience
- Automatic setup for users
- Centralized webhook management
- Better user experience

**Cons**:
- Requires JIRA admin permissions from users
- More complex implementation
- Need to handle webhook lifecycle management
- Different APIs for Cloud vs Data Center

**User flow**:
1. User connects JIRA OAuth with admin permissions
2. Augment automatically creates webhook via JIRA REST API
3. Augment manages webhook configuration and JQL filters
4. Triggers work automatically
5. Webhook deleted when user disconnects JIRA

### Recommended Architecture

For consistency with GitHub integration, implement **Option 2**:

```
JIRA Instance → Per-Tenant Webhook → JIRA Webhook Service → Event Router → Triggers
```

#### Required Components

1. **JIRA Webhook Service**
   - Similar to `services/integrations/github/webhook_listener`
   - Handle incoming JIRA webhook events
   - Validate webhook signatures (if supported)
   - Route events to tenant-specific pubsub topics

2. **Webhook Management Service**
   - Create webhooks when users connect JIRA
   - Handle both Cloud and Data Center APIs
   - Update webhook configurations as needed
   - Delete webhooks when users disconnect
   - Handle webhook failures and retries

3. **JIRA OAuth Enhancement**
   - Request admin permissions during OAuth flow
   - Store webhook IDs for management
   - Handle permission errors gracefully
   - Support both Cloud and Data Center instances

#### Webhook Service Architecture

```go
// services/integrations/jira/webhook_listener/main.go
type jiraWebhookListener struct {
    publishClient pubsub.PublishClient
    tenantLookup  TenantLookup
}

func (jl *jiraWebhookListener) handle(w http.ResponseWriter, r *http.Request) {
    // Parse JIRA webhook payload
    // Extract tenant ID from URL path or payload
    // Look up tenant from webhook mapping
    // Publish to tenant-specific pubsub topic
}
```

#### Webhook Registration Flow

```go
// When user connects JIRA with admin permissions
func (s *JiraServer) CreateWebhook(tenantID string, jiraBaseURL string, accessToken string) error {
    webhookURL := fmt.Sprintf("https://jira-webhook.%s/webhook/%s",
        s.config.Domain, tenantID)

    // Determine if Cloud or Data Center
    if isJiraCloud(jiraBaseURL) {
        return s.createCloudWebhook(webhookURL, accessToken)
    } else {
        return s.createDataCenterWebhook(webhookURL, accessToken)
    }
}

func (s *JiraServer) createCloudWebhook(webhookURL string, accessToken string) error {
    payload := map[string]interface{}{
        "name": "Augment Triggers Webhook",
        "url": webhookURL,
        "events": []string{
            "jira:issue_created",
            "jira:issue_updated",
            "jira:issue_deleted",
            "comment_created",
            "comment_updated",
            "comment_deleted",
        },
        "excludeBody": false,
    }

    // POST to /rest/webhooks/1.0/webhook
    // Store webhook ID in tenant settings
}
```

### Advanced JIRA Webhook Features

#### JQL Filtering for Triggers

JIRA's JQL filtering capability could enable sophisticated trigger conditions:

```json
{
    "filters": {
        "issue-related-events-section": "project = MYPROJ AND priority = High AND assignee = currentUser()"
    }
}
```

This allows triggers to fire only for:
- Specific projects
- High priority issues
- Issues assigned to the current user
- Complex combinations of conditions

#### Variable Substitution

JIRA supports variable substitution in webhook URLs:

```
https://jira-webhook.augmentcode.com/webhook/{tenant-id}/${issue.key}/${project.key}
```

Variables available:
- `${issue.id}`, `${issue.key}`
- `${project.id}`, `${project.key}`
- `${sprint.id}`, `${board.id}`
- `${modifiedUser.key}`, `${modifiedUser.name}`

#### Workflow Post Functions

JIRA webhooks can be attached to workflow transitions as post functions, enabling triggers on specific workflow events beyond standard issue events.

### Event Processing Differences

#### GitHub Event Processing
```go
// Single global webhook, route by installation ID
switch event.GetInstallation().GetId() {
case installationID:
    // Route to tenant
}
```

#### Linear Event Processing
```go
// Per-tenant webhook, route by organization ID
switch payload.OrganizationId {
case orgID:
    // Look up tenant from organization mapping
}
```

#### JIRA Event Processing (Proposed)
```go
// Per-tenant webhook, route by tenant ID in URL path
tenantID := extractTenantFromPath(r.URL.Path)
// Or extract from webhook payload if configured
```

### Required JIRA Permissions

For Augment-managed webhooks, users would need to grant:
- **JIRA Administrator** global permission
- **OAuth scopes**: Full access to JIRA instance
- **API access**: REST API permissions

### Webhook Lifecycle Management

#### Webhook Creation
- Triggered when user connects JIRA with admin permissions
- Create webhook with comprehensive event subscriptions
- Store webhook ID and instance URL in tenant settings
- Handle both Cloud and Data Center APIs

#### Webhook Updates
- Update event subscriptions when user modifies trigger configurations
- Update JQL filters based on trigger conditions
- Handle webhook URL changes if infrastructure changes

#### Webhook Deletion
- Delete webhook when user disconnects JIRA integration
- Clean up webhook mappings and configurations
- Handle both Cloud and Data Center deletion APIs

#### Error Handling
- Handle webhook delivery failures
- Retry failed webhook registrations
- Alert on webhook configuration errors
- Graceful fallback to API polling if webhooks fail

## Security Considerations

### Webhook Validation

JIRA webhooks may not support signature validation (unlike GitHub), so security relies on:
- **HTTPS endpoints** for webhook URLs
- **Tenant ID validation** in URL path
- **Source IP validation** if possible
- **Payload validation** against expected JIRA format

### Tenant Isolation

- **URL-based routing**: Include tenant ID in webhook URL path
- **Instance validation**: Verify JIRA instance matches tenant permissions
- **Event filtering**: Only process events for authorized users
- **JQL security**: Ensure JQL filters don't expose unauthorized data

## Migration Path

### Phase 1: Basic Webhook Infrastructure
1. Create JIRA webhook listener service
2. Implement basic event routing to pubsub
3. Support both Cloud and Data Center payloads

### Phase 2: Webhook Management
1. Add webhook registration via JIRA REST APIs
2. Implement webhook lifecycle management
3. Update JIRA OAuth flow for admin permissions
4. Handle Cloud vs Data Center differences

### Phase 3: Triggers Integration
1. Connect webhook events to triggers system
2. Implement JIRA-specific trigger conditions
3. Add JQL-based event filtering
4. Support workflow transition triggers

### Phase 4: Advanced Features
1. Webhook health monitoring
2. Automatic webhook recovery
3. Advanced JQL filtering and transformation
4. Variable substitution support

## Monitoring and Reliability

### Webhook Health Monitoring
- Track webhook delivery success rates per JIRA instance
- Monitor webhook response times
- Alert on webhook failures
- Track JQL filter performance

### Error Recovery
- Automatic webhook re-registration on failures
- Retry mechanisms for failed deliveries
- Graceful degradation to API polling if webhooks fail
- Handle JIRA instance migrations/URL changes

### Metrics and Logging
```go
var jiraWebhookRequestCounter = promauto.NewCounterVec(prometheus.CounterOpts{
    Name: "jira_webhook_request_total",
    Help: "Counter of handled JIRA webhook requests",
}, []string{"status_code", "event_type", "jira_type"})
```

## JIRA Cloud vs Data Center Environments

### JIRA Cloud

**API Characteristics**:
- **Endpoint**: `/rest/webhooks/1.0/webhook`
- **Authentication**: OAuth 2.0 with Atlassian Connect
- **Configuration**: Uses `filters` object for JQL
- **Hosting**: Managed by Atlassian
- **Updates**: Automatic, always latest version
- **Webhooks**: Always asynchronous

**Advantages for Implementation**:
- **Single API version** to support
- **Consistent endpoint** across all instances
- **Standardized OAuth** flow
- **No version compatibility** issues
- **Reliable uptime** and performance

**Example Cloud Webhook Registration**:
```json
POST https://your-domain.atlassian.net/rest/webhooks/1.0/webhook
{
    "name": "Augment Triggers Webhook",
    "url": "https://jira-webhook.augmentcode.com/webhook/{tenant-id}",
    "events": ["jira:issue_created", "jira:issue_updated"],
    "filters": {
        "issue-related-events-section": "project = MYPROJ"
    },
    "excludeBody": false
}
```

### JIRA Data Center (Self-Hosted)

**API Characteristics**:
- **Endpoint**: `/rest/jira-webhook/1.0/webhooks` (JIRA 10.x+)
- **Authentication**: Basic auth, OAuth, or Personal Access Tokens
- **Configuration**: Uses `configuration` object with uppercase keys
- **Hosting**: Customer-managed
- **Updates**: Manual, version varies by customer
- **Webhooks**: Asynchronous by default (10.0+), synchronous in older versions

**Challenges for Implementation**:
- **Multiple API versions** to support
- **Varying endpoints** across versions
- **Different authentication** methods
- **Version compatibility** matrix
- **Customer-specific configurations**
- **Network accessibility** issues

**Example Data Center Webhook Registration**:
```json
POST https://customer-jira.company.com/rest/jira-webhook/1.0/webhooks
{
    "name": "Augment Triggers Webhook",
    "url": "https://jira-webhook.augmentcode.com/webhook/{tenant-id}",
    "events": ["jira:issue_created", "jira:issue_updated"],
    "configuration": {
        "FILTERS": "project = MYPROJ",
        "EXCLUDE_BODY": "false"
    },
    "active": "true"
}
```

### Version Compatibility Matrix

| JIRA Version | Webhook Endpoint | Config Format | Auth Methods | Async Webhooks |
|--------------|------------------|---------------|--------------|----------------|
| **Cloud** | `/rest/webhooks/1.0/webhook` | `filters` | OAuth 2.0 | ✅ Always |
| **DC 10.x+** | `/rest/jira-webhook/1.0/webhooks` | `configuration` | Basic/OAuth/PAT | ✅ Default |
| **DC 9.x** | `/rest/webhooks/1.0/webhook` | `filters` | Basic/OAuth | ❌ Sync only |
| **DC 8.x** | `/rest/webhooks/1.0/webhook` | `filters` | Basic/OAuth | ❌ Sync only |

## Implementation Strategy Options

### Option A: JIRA Cloud Only (Recommended for MVP)

**Implementation Effort**: ⚠️ **Medium** (similar to Linear)

**Advantages**:
- **Single API** to implement and maintain
- **Consistent OAuth** flow across all customers
- **No version compatibility** issues
- **Faster time to market**
- **Easier testing** and debugging
- **Covers majority** of JIRA users (Cloud adoption is growing)

**Implementation Requirements**:
```go
// Single webhook management service
type CloudWebhookManager struct {
    client *http.Client
}

func (c *CloudWebhookManager) CreateWebhook(domain string, token string) error {
    url := fmt.Sprintf("https://%s.atlassian.net/rest/webhooks/1.0/webhook", domain)
    // Single API implementation
}
```

**Limitations**:
- **Excludes Data Center customers** (estimated 20-30% of enterprise JIRA users)
- **May limit enterprise adoption** of triggers feature

### Option B: Both Cloud and Data Center (Full Support)

**Implementation Effort**: ❌ **High** (due to complexity)

**Advantages**:
- **Complete JIRA coverage**
- **Enterprise-friendly**
- **Future-proof** solution

**Implementation Requirements**:
```go
// Dual webhook management with version detection
type JiraWebhookManager struct {
    cloudManager      *CloudWebhookManager
    dataCenterManager *DataCenterWebhookManager
}

func (j *JiraWebhookManager) CreateWebhook(baseURL string, token string) error {
    if j.isCloud(baseURL) {
        return j.cloudManager.CreateWebhook(baseURL, token)
    }

    version := j.detectVersion(baseURL)
    return j.dataCenterManager.CreateWebhook(baseURL, token, version)
}

func (j *JiraWebhookManager) detectVersion(baseURL string) string {
    // Version detection logic
    // Handle 8.x, 9.x, 10.x+ differences
}
```

**Complexity Factors**:
- **Version detection** and compatibility handling
- **Multiple authentication** methods
- **Different API endpoints** and formats
- **Network accessibility** for self-hosted instances
- **Customer-specific configurations**

### Recommended Phased Approach

#### Phase 1: JIRA Cloud Only
- **Quick wins** with majority of users
- **Validate triggers concept** with JIRA integration
- **Gather user feedback** and usage patterns
- **Establish webhook infrastructure**

#### Phase 2: Add Data Center Support
- **Based on customer demand**
- **Leverage learnings** from Cloud implementation
- **Focus on latest Data Center versions** first
- **Gradual rollback** to older versions if needed

## Implementation Effort Comparison (Updated)

| Approach | GitHub | Linear | JIRA Cloud Only | JIRA Cloud + DC |
|----------|---------|---------|-----------------|------------------|
| **User Setup** | ✅ Simple | ⚠️ Medium | ⚠️ Medium | ⚠️ Medium |
| **Event Filtering** | ❌ Basic | ⚠️ Medium | ✅ Advanced | ✅ Advanced |
| **API Complexity** | ❌ Limited | ✅ Single GraphQL | ✅ Single REST | ❌ Multiple REST |
| **Auth Complexity** | ✅ Simple | ⚠️ OAuth | ⚠️ OAuth | ❌ Multiple methods |
| **Version Support** | ✅ Single | ✅ Single | ✅ Single | ❌ Multiple |
| **Implementation Effort** | ✅ Low | ⚠️ Medium | ⚠️ **Medium** | ❌ **High** |
| **Market Coverage** | ✅ ~90% | ⚠️ ~60% | ⚠️ ~70% | ✅ ~95% |

## Summary

**Key Differences from GitHub and Linear**:
- **Per-instance registration** with multiple webhooks possible
- **Advanced JQL filtering** for sophisticated event conditions
- **Variable substitution** in webhook URLs
- **Workflow integration** via post functions
- **Admin permissions required** for webhook management
- **Cloud vs Data Center** complexity

**Recommended Approach**:
- **Start with JIRA Cloud only** for MVP (Medium effort)
- **Augment-managed webhooks** for consistent user experience
- **Automatic webhook registration** when users connect JIRA Cloud
- **Comprehensive event subscriptions** with JQL filtering
- **Add Data Center support** in Phase 2 based on demand

**JIRA Cloud Implementation Requirements**:
- New JIRA webhook listener service
- Webhook management via JIRA Cloud REST API
- Enhanced JIRA OAuth flow with admin permissions
- Webhook-to-tenant mapping and routing
- JQL filter management for advanced trigger conditions

**Benefits of Cloud-First Approach**:
- **Faster time to market** with triggers feature
- **Lower implementation risk**
- **Easier maintenance** and debugging
- **Covers majority** of JIRA users
- **Validates market demand** before investing in Data Center complexity

This approach would provide JIRA triggers with real-time event processing, advanced filtering capabilities, and comprehensive event coverage while maintaining a reasonable implementation timeline and complexity.
