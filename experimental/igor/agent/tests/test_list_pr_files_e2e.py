#!/usr/bin/env python3.11
"""
End-to-end tests for the ListPullRequestFiles public API.

Covers:
- include_patch=false excludes patch field
- max_patch_bytes truncates patch
- pagination has_more/next_page mechanics
- include_total_changed_files only on page 1

Run:
  python3.11 experimental/igor/agent/tests/test_list_pr_files_e2e.py [--api-url URL] [--token-file FILE]
"""

import argparse
import json
import logging
import os
import sys
from typing import Any, Dict

# Repo root on path
sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
)
# Add the agent-tools directory to access CLI modules
sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../agent-tools"))
)

from base.augment_client.client import AugmentClient
from trigger_cli import get_api_url, get_api_token


def get_username_from_config() -> str:
    user_config_path = os.path.expanduser("~/.augment/user.json")
    if not os.path.exists(user_config_path):
        raise ValueError(
            "User config file not found. Create ~/.augment/user.json with your username."
        )
    with open(user_config_path, "r") as f:
        user_config = json.load(f)
    if "name" not in user_config:
        raise ValueError("Username not found in config file.")
    return user_config["name"]


logging.basicConfig(level=logging.INFO, format="%(asctime)s %(levelname)s %(message)s")
log = logging.getLogger(__name__)


def call_list_pr_files(
    client: AugmentClient, payload: Dict[str, Any]
) -> Dict[str, Any]:
    resp, _ = client._post("remote-agent-actions/list-pr-files", json=payload)
    if not resp.ok:
        raise RuntimeError(f"HTTP {resp.status_code}: {resp.text}")
    return resp.json()


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument("--api-url")
    parser.add_argument("--token-file")
    parser.add_argument(
        "--repository",
        default=os.environ.get("AUGMENT_TEST_REPO"),
        help="owner/repo to test against",
    )
    parser.add_argument(
        "--pr",
        type=int,
        default=int(os.environ.get("AUGMENT_TEST_PR"))
        if os.environ.get("AUGMENT_TEST_PR")
        else None,
        help="PR number with at least 1 file change",
    )
    args = parser.parse_args()

    api_url = args.api_url or get_api_url()
    token = get_api_token(args.token_file)
    client = AugmentClient(url=api_url, token=token)

    repository = args.repository or "octocat/Hello-World"
    pr_number = args.pr or 1
    if args.repository is None or args.pr is None:
        log.warning(
            f"Using default test target repository={repository}, pr={pr_number}. Provide --repository/--pr or set AUGMENT_TEST_REPO/AUGMENT_TEST_PR to override."
        )

    # 1) include_patch=false excludes patch field
    out1 = call_list_pr_files(
        client,
        {
            "repository": repository,
            "pr_number": pr_number,
            "include_patch": False,
            "page": 1,
            "per_page": 1,
        },
    )
    assert "files" in out1 and isinstance(
        out1["files"], list
    ), f"bad response: {json.dumps(out1, indent=2)}"
    if out1["files"]:
        assert "patch" not in out1["files"][0] or out1["files"][0]["patch"] is None

    # 2) max_patch_bytes truncates patch
    out2 = call_list_pr_files(
        client,
        {
            "repository": repository,
            "pr_number": pr_number,
            "include_patch": True,
            "max_patch_bytes": 5,
            "page": 1,
            "per_page": 1,
        },
    )
    if out2["files"] and out2["files"][0].get("patch") is not None:
        assert len(out2["files"][0]["patch"]) <= 5

    # 3) pagination mechanics (best-effort; if only one file, may not have has_more)
    out3 = call_list_pr_files(
        client,
        {
            "repository": repository,
            "pr_number": pr_number,
            "page": 1,
            "per_page": 1,
        },
    )
    has_more = out3.get("has_more", False)
    if has_more:
        assert out3.get("next_page") == 2
        out4 = call_list_pr_files(
            client,
            {
                "repository": repository,
                "pr_number": pr_number,
                "page": 2,
                "per_page": 1,
            },
        )
        assert isinstance(out4.get("files", []), list)

    # 4) include_total_changed_files only on page 1
    out5 = call_list_pr_files(
        client,
        {
            "repository": repository,
            "pr_number": pr_number,
            "include_total_changed_files": True,
            "page": 1,
        },
    )
    assert "total_changed_files" in out5 or out5.get("total_changed_files") is None

    log.info("All checks finished. Output samples:")
    log.info("no-patch: %s", json.dumps(out1, indent=2)[:500])
    log.info("truncated: %s", json.dumps(out2, indent=2)[:500])
    log.info("page1: %s", json.dumps(out3, indent=2)[:500])
    log.info("page1-with-total: %s", json.dumps(out5, indent=2)[:500])


if __name__ == "__main__":
    main()
