#!/usr/bin/env python3.11
"""
End-to-end test for remote agent triggers.

This test exercises the complete trigger workflow:
1. Create a sample trigger
2. List matching entities
3. Execute trigger manually
4. Poll agent history until execution starts
5. Delete the remote agent
6. Delete the trigger

Usage:
    python3.11 test_trigger_e2e.py [--api-url URL] [--token-file FILE] [--trigger-type TYPE]
"""

import argparse
import json
import logging
import os
import sys
import time
import traceback
from typing import Dict, Any, Optional, List

# Add the repository root to the Python path
sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../../../.."))
)
# Add the agent-tools directory to access CLI modules
sys.path.insert(
    0, os.path.abspath(os.path.join(os.path.dirname(__file__), "../agent-tools"))
)

from base.augment_client.client import AugmentClient
from trigger_cli import (
    get_api_url,
    get_api_token,
    create_sample_pr_trigger,
    create_sample_schedule_2min_trigger,
    create_sample_linear_issue_trigger,
    create_trigger,
    list_triggers,
    get_matching_entities,
    execute_trigger_manually,
    delete_trigger,
)
from remote_agent_cli import (
    delete_remote_agent,
    enum_value_to_string,
    AgentStatus,
)

# Set up logging
logging.basicConfig(
    level=logging.INFO,
    format="%(asctime)s - %(name)s - %(levelname)s - %(message)s",
)
logger = logging.getLogger(__name__)


class TriggerE2ETest:
    """End-to-end test class for remote agent triggers."""

    def __init__(self, client: AugmentClient, trigger_type: str = "schedule-2min"):
        self.client = client
        self.trigger_type = trigger_type
        self.trigger_id: Optional[str] = None
        self.agent_id: Optional[str] = None
        self.execution_id: Optional[str] = None

    def cleanup(self):
        """Clean up any resources created during the test."""
        logger.info("Starting cleanup...")

        # Delete the remote agent if it exists
        if self.agent_id:
            try:
                logger.info(f"Deleting remote agent: {self.agent_id}")
                delete_remote_agent(self.client, self.agent_id)
                self.agent_id = None
            except Exception as e:
                logger.warning(f"Failed to delete remote agent {self.agent_id}: {e}")

        # Delete the trigger if it exists
        if self.trigger_id:
            try:
                logger.info(f"Deleting trigger: {self.trigger_id}")
                delete_trigger(self.client, self.trigger_id)
                self.trigger_id = None
            except Exception as e:
                logger.warning(f"Failed to delete trigger {self.trigger_id}: {e}")

        logger.info("Cleanup completed")

    def create_sample_trigger(self) -> Dict[str, Any]:
        """Create a sample trigger configuration based on the trigger type."""
        logger.info(f"Creating sample trigger of type: {self.trigger_type}")

        if self.trigger_type == "pr-review":
            return create_sample_pr_trigger()
        elif self.trigger_type == "schedule-2min":
            return create_sample_schedule_2min_trigger()
        elif self.trigger_type == "linear-issue":
            return create_sample_linear_issue_trigger()
        else:
            raise ValueError(f"Unknown trigger type: {self.trigger_type}")

    def step_1_create_trigger(self) -> str:
        """Step 1: Create a sample trigger."""
        logger.info("=== Step 1: Creating trigger ===")

        trigger_config = self.create_sample_trigger()

        # Create the trigger
        try:
            # Use the client's _post method to make a direct API call
            response, _ = self.client._post(
                "remote-agent-actions/triggers/create",
                json={"configuration": trigger_config},
            )

            if not response.ok:
                raise Exception(
                    f"HTTP error creating trigger: {response.status_code} {response.text}"
                )

            result = response.json()
            trigger_id = result.get("trigger", {}).get("trigger_id")
            if not trigger_id:
                raise Exception("No trigger ID returned from create API")

            self.trigger_id = trigger_id
            logger.info(f"✓ Trigger created successfully with ID: {trigger_id}")
            return trigger_id

        except Exception as e:
            logger.error(f"Failed to create trigger: {e}")
            raise

    def step_2_list_matching_entities(self) -> List[Dict[str, Any]]:
        """Step 2: List matching entities for the trigger."""
        logger.info("=== Step 2: Listing matching entities ===")

        if not self.trigger_id:
            raise Exception("No trigger ID available")

        try:
            # Use the client's _post method to get matching entities
            # Schedule triggers also have matching entities (upcoming scheduled times)
            request_data = {
                "trigger_id": self.trigger_id,
                "event_source": 1,  # GitHub
                "limit": 10,
                "offset": 0,
                "show_dismissed": False,
            }

            response, _ = self.client._post(
                "remote-agent-actions/triggers/matching-entities", json=request_data
            )

            if not response.ok:
                raise Exception(
                    f"HTTP error getting matching entities: {response.status_code} {response.text}"
                )

            result = response.json()
            github_entities = result.get("github_entities", [])
            linear_entities = result.get("linear_entities", [])
            schedule_entities = result.get("schedule_entities", [])
            entities = github_entities + linear_entities + schedule_entities

            logger.info(f"✓ Found {len(entities)} matching entities")
            for i, entity in enumerate(entities[:3]):  # Show first 3
                entity_id = entity.get("id", "unknown")
                entity_type = entity.get("type", "unknown")
                logger.info(f"  Entity {i+1}: {entity_id} (type: {entity_type})")

            return entities

        except Exception as e:
            logger.error(f"Failed to list matching entities: {e}")
            raise

    def step_3_execute_manually(self, entities: List[Dict[str, Any]]) -> str:
        """Step 3: Execute trigger manually."""
        logger.info("=== Step 3: Executing trigger manually ===")

        if not self.trigger_id:
            raise Exception("No trigger ID available")

        try:
            # All triggers (including schedule) need an entity for manual execution
            if not entities:
                raise Exception("No entities available for manual execution")

            entity = entities[0]
            entity_id = entity.get("id")
            if not entity_id:
                raise Exception("Entity has no ID")

            logger.info(f"Executing trigger manually for entity: {entity_id}")
            request_data = {
                "trigger_id": self.trigger_id,
                "entity_id": entity_id,
            }

            # Execute the trigger manually
            response, _ = self.client._post(
                "remote-agent-actions/triggers/execute-manually",
                json=request_data,
            )

            if not response.ok:
                raise Exception(
                    f"HTTP error executing trigger: {response.status_code} {response.text}"
                )

            result = response.json()
            agent_id = result.get("remote_agent_id")
            execution_id = result.get("execution_id")

            if not agent_id:
                raise Exception("No agent ID returned from manual execution")

            self.agent_id = agent_id
            self.execution_id = execution_id

            logger.info("✓ Trigger executed successfully")
            logger.info(f"  Agent ID: {agent_id}")
            if execution_id:
                logger.info(f"  Execution ID: {execution_id}")

            return agent_id

        except Exception as e:
            logger.error(f"Failed to execute trigger manually: {e}")
            raise

    def step_4_poll_agent_history(self, timeout_seconds: int = 300) -> bool:
        """Step 4: Poll agent history until execution starts."""
        logger.info("=== Step 4: Polling agent history ===")

        if not self.agent_id:
            raise Exception("No agent ID available")

        logger.info(f"Polling agent history for agent: {self.agent_id}")
        logger.info(f"Timeout: {timeout_seconds} seconds")

        start_time = time.time()
        last_status = None

        try:
            while time.time() - start_time < timeout_seconds:
                # Get agent history using the proper client method
                try:
                    history_response = self.client.get_remote_agent_history(
                        self.agent_id, last_processed_sequence_id=0
                    )

                    # Extract status from the remote agent information
                    status = None
                    if history_response.remote_agent:
                        status = history_response.remote_agent.status

                    if status != last_status:
                        status_str = (
                            enum_value_to_string(AgentStatus, status)
                            if status
                            else "unknown"
                        )
                        logger.info(f"Agent status: {status_str} ({status})")
                        last_status = status

                    # Check if agent has started executing (not just created)
                    if status in [2, 3, 4, 5]:  # RUNNING, IDLE, COMPLETED, FAILED
                        logger.info("✓ Agent has successfully started executing")
                        return True

                    # If agent failed to start, that's also a completion condition
                    if status == 5:  # FAILED
                        logger.warning("Agent failed to start properly")
                        return False

                except Exception as e:
                    logger.warning(f"Error polling agent history: {e}")
                    logger.debug(f"Exception details: {type(e).__name__}: {str(e)}")

                time.sleep(5)  # Poll every 5 seconds

            logger.warning(f"Timeout reached after {timeout_seconds} seconds")
            return False

        except Exception as e:
            logger.error(f"Failed to poll agent history: {e}")
            raise

    def run_test(self) -> bool:
        """Run the complete end-to-end test."""
        logger.info("Starting end-to-end trigger test")
        logger.info(f"Trigger type: {self.trigger_type}")

        try:
            # Step 1: Create trigger
            self.step_1_create_trigger()

            # Step 2: List matching entities
            entities = self.step_2_list_matching_entities()

            # Step 3: Execute manually
            self.step_3_execute_manually(entities)

            # Step 4: Poll agent history
            success = self.step_4_poll_agent_history()

            if success:
                logger.info("✓ End-to-end test completed successfully!")
                return True
            else:
                logger.error("✗ End-to-end test failed - agent did not start executing")
                return False

        except Exception as e:
            logger.error(f"✗ End-to-end test failed with error: {e}")
            logger.error(traceback.format_exc())
            return False
        finally:
            # Always cleanup
            self.cleanup()


def main():
    """Main function to run the end-to-end test."""
    parser = argparse.ArgumentParser(
        description="End-to-end test for remote agent triggers",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run test with schedule trigger (default)
  python3.11 test_trigger_e2e.py

  # Run test with PR trigger
  python3.11 test_trigger_e2e.py --trigger-type pr-review

  # Run test with custom API URL
  python3.11 test_trigger_e2e.py --api-url https://api.example.com

  # Run test with custom token file
  python3.11 test_trigger_e2e.py --token-file /path/to/token.txt

Available trigger types:
  - schedule-2min: Schedule trigger that runs every 2 minutes (default)
  - pr-review: Pull request review trigger
  - linear-issue: Linear issue trigger
        """,
    )

    parser.add_argument(
        "--api-url",
        help="API URL to use (default: from environment or config)",
    )

    parser.add_argument(
        "--token-file",
        help="Path to file containing API token (default: from environment or config)",
    )

    parser.add_argument(
        "--trigger-type",
        choices=["schedule-2min", "pr-review", "linear-issue"],
        default="schedule-2min",
        help="Type of trigger to test (default: schedule-2min)",
    )

    parser.add_argument(
        "--timeout",
        type=int,
        default=300,
        help="Timeout in seconds for waiting for agent to start (default: 300)",
    )

    parser.add_argument(
        "--verbose",
        "-v",
        action="store_true",
        help="Enable verbose logging",
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Test imports and configuration without running the actual test",
    )

    args = parser.parse_args()

    # Set logging level
    if args.verbose:
        logging.getLogger().setLevel(logging.DEBUG)

    try:
        # Get API configuration
        api_url = args.api_url or get_api_url()
        api_token = get_api_token(args.token_file)

        logger.info(f"Using API URL: {api_url}")
        logger.info(f"Using trigger type: {args.trigger_type}")

        # Create client
        client = AugmentClient(url=api_url, token=api_token)

        # Handle dry run
        if args.dry_run:
            logger.info("🧪 Dry run mode - testing configuration and imports")
            test = TriggerE2ETest(client, args.trigger_type)
            trigger_config = test.create_sample_trigger()
            logger.info(
                f"✓ Sample trigger configuration created: {trigger_config['name']}"
            )
            logger.info("✓ All imports and configuration successful!")
            logger.info("🎉 Dry run completed successfully!")
            sys.exit(0)

        # Run the test
        test = TriggerE2ETest(client, args.trigger_type)
        success = test.run_test()

        if success:
            logger.info("🎉 All tests passed!")
            sys.exit(0)
        else:
            logger.error("❌ Test failed!")
            sys.exit(1)

    except KeyboardInterrupt:
        logger.info("Test interrupted by user")
        sys.exit(1)
    except Exception as e:
        logger.error(f"Test failed with error: {e}")
        logger.error(traceback.format_exc())
        sys.exit(1)


if __name__ == "__main__":
    main()
