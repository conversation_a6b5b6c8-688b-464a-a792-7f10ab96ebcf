## Testing in Dev Deploy

1. **Deploy in dev environment**
   ```bash
   bazel run //services/deploy:dev_deploy -- --services chat agents default remote_agents_all --operation Apply
   ```

2. **Interact with remote_agent_cli.py**
   - Use it directly from the command line:
     ```bash
     python3.11 ./remote_agent_cli.py list
     ```

3. **Access Kubernetes**
    - Use `kubectl` to access your Kubernetes cluster:
      ```bash
      kubectl -n dev-igor get pods
      ```
    - DO NOT MODIFY ANYTHING IN KUBERNETES! NO CHANGES, other than dev deploys as explained above.(augment-research)
