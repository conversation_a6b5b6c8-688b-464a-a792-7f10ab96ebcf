# User Secrets CLI Tool

A command-line tool for managing user secrets using the User Secrets service.

## Installation

The tool is located at `experimental/marcmac/user_secrets_cli.py` and requires the Augment Python environment.

## Usage

The tool supports the following subcommands that correspond to the User Secrets API methods:

### Basic Commands

```bash
# Create a secret interactively (will prompt for value)
./experimental/marcmac/user_secrets_cli.py create my-api-key --description "My API key"

# Create a secret with value and tags
./experimental/marcmac/user_secrets_cli.py create github-token \
  --value "ghp_xxx" \
  --tag "service=github" \
  --tag "env=prod" \
  --description "GitHub personal access token"

# Create a secret from file
./experimental/marcmac/user_secrets_cli.py create db-password --value-file /path/to/password.txt

# Get a specific secret
./experimental/marcmac/user_secrets_cli.py get my-api-key

# Get secret as JSON
./experimental/marcmac/user_secrets_cli.py get my-api-key --json

# List all secrets (metadata only, no values)
./experimental/marcmac/user_secrets_cli.py list

# List secrets with values (use with caution)
./experimental/marcmac/user_secrets_cli.py list --show-values

# Delete a secret
./experimental/marcmac/user_secrets_cli.py delete my-api-key
```

### Advanced Filtering

```bash
# List secrets matching a name pattern (regex)
./experimental/marcmac/user_secrets_cli.py list --name-pattern "github.*"

# List secrets with specific tags
./experimental/marcmac/user_secrets_cli.py list --tag-filter "service=github"

# List secrets with multiple tag filters (AND logic)
./experimental/marcmac/user_secrets_cli.py list \
  --tag-filter "service=github" \
  --tag-filter "env=prod"

# List secrets with inverted tag filter (NOT matching)
./experimental/marcmac/user_secrets_cli.py list --tag-filter "!env=dev"

# List secrets with OR logic for tag values
./experimental/marcmac/user_secrets_cli.py list --tag-filter "env=prod,staging"
```

## Authentication and Connection

The tool requires:
1. **`AUGMENT_API_TOKEN`** environment variable to be set
2. **`kubectl`** configured to access the target cluster

```bash
# Set your API token
export AUGMENT_API_TOKEN="your-token-here"

# Use default namespace (dev-{username from augi whoami})
./experimental/marcmac/user_secrets_cli.py list

# Specify namespace explicitly
./experimental/marcmac/user_secrets_cli.py --namespace dev-marcmac list
```

### How it Works

The tool uses **kubectl port-forward** to create a secure tunnel to the User Secrets service running in the Kubernetes cluster:

1. Finds a free local port
2. Runs `kubectl port-forward --context gke_system-services-dev_us-central1_us-central1-dev --namespace {namespace} service/user-secrets-server-svc {local_port}:50051`
3. Connects to `localhost:{local_port}` using the gRPC client
4. Automatically cleans up the port-forward when done

This approach works with dev clusters that don't have external IPs, just like the integration tests.

## Tag Format

Tags are key-value pairs that help organize and filter secrets:

- **Creating tags**: Use `--tag "key=value"` format
- **Filtering by tags**: Use `--tag-filter "key=value1,value2"` format
- **Inverted filters**: Use `--tag-filter "!key=value"` to exclude
- **Any value**: Use `--tag-filter "key"` to match any secret with that key

## JSON Output

Most commands support `--json` flag for machine-readable output:

```bash
./experimental/marcmac/user_secrets_cli.py get my-secret --json
./experimental/marcmac/user_secrets_cli.py list --json
```

## Security Notes

- Secret values are never displayed by default in list operations
- Use `--show-values` flag explicitly to include values in list output
- Values are prompted interactively if not provided via `--value` or `--value-file`
- The tool uses the same authentication and authorization as other Augment services

## Examples

### Managing API Keys

```bash
# Store an OpenAI API key
./experimental/marcmac/user_secrets_cli.py create openai-api-key \
  --tag "service=openai" \
  --tag "env=prod" \
  --description "OpenAI API key for production"

# Store a GitHub token
./experimental/marcmac/user_secrets_cli.py create github-token \
  --value "ghp_xxxxxxxxxxxx" \
  --tag "service=github" \
  --tag "scope=repo" \
  --description "GitHub personal access token"

# List all API keys
./experimental/marcmac/user_secrets_cli.py list --tag-filter "service"
```

### Managing Database Credentials

```bash
# Store database password
./experimental/marcmac/user_secrets_cli.py create postgres-password \
  --tag "type=database" \
  --tag "service=postgres" \
  --tag "env=prod" \
  --description "Production PostgreSQL password"

# List all database credentials
./experimental/marcmac/user_secrets_cli.py list --tag-filter "type=database"
```

### Configuration Management

```bash
# Store JWT signing key
./experimental/marcmac/user_secrets_cli.py create jwt-signing-key \
  --tag "type=config" \
  --tag "component=auth" \
  --tag "env=prod" \
  --description "JWT signing key for authentication"

# List all production configuration
./experimental/marcmac/user_secrets_cli.py list --tag-filter "env=prod"
```
