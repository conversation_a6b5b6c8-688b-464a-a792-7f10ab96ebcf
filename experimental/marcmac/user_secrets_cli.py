#!/usr/bin/env python3
"""Command-line tool for managing user secrets using the User Secrets service."""

import argparse
import getpass
import json
import logging
import os
import subprocess
import sys
import uuid
from typing import Dict, List, Optional, Union

import pydantic
import requests

from base.logging.console_logging import setup_console_logging


def get_default_namespace() -> str:
    """Get default namespace as dev-{username from augi whoami}."""
    try:
        result = subprocess.run(
            ["augi", "whoami"], capture_output=True, text=True, check=True
        )
        username = result.stdout.strip()
        return f"dev-{username}"
    except (subprocess.CalledProcessError, FileNotFoundError) as e:
        # Fallback to $USER if augi whoami fails
        user = os.getenv("USER", "unknown")
        logging.warning(
            f"Failed to run 'augi whoami': {e}. Using $USER={user} as fallback."
        )
        return f"dev-{user}"


def get_authentication() -> pydantic.SecretStr:
    """Get authentication token from environment variable."""
    token = os.getenv("AUGMENT_API_TOKEN")
    if not token:
        raise ValueError(
            "AUGMENT_API_TOKEN environment variable is required. "
            "Please set this environment variable with your API token."
        )
    logging.debug("Using API token authentication")
    return pydantic.SecretStr(token)


def get_auth_token() -> pydantic.SecretStr:
    """Get authentication token from environment variable (legacy function)."""
    token = os.getenv("AUGMENT_API_TOKEN")
    if not token:
        raise ValueError("AUGMENT_API_TOKEN environment variable is required")
    return pydantic.SecretStr(token)


def get_api_proxy_endpoint(namespace: str) -> str:
    """Get the API Proxy endpoint for the given namespace."""
    # Use the external API Proxy endpoint
    return f"https://{namespace}.us-central.api.augmentcode.com"


def make_api_request(
    endpoint: str, path: str, data: dict, auth: pydantic.SecretStr
) -> dict:
    """Make an authenticated HTTP request to the API Proxy."""
    url = f"{endpoint}/{path.lstrip('/')}"
    request_id = str(uuid.uuid4())
    session_id = str(uuid.uuid4())

    # Get API token
    token = auth.get_secret_value()

    headers = {
        "Content-Type": "application/json",
        "Authorization": f"Bearer {token}",
        "x-request-id": request_id,
        "x-request-session-id": session_id,
        "x-api-version": "1",
        "User-Agent": "user-secrets-cli/1.0",
    }

    logging.debug(f"Making request to {url} with request_id {request_id}")

    try:
        response = requests.post(
            url,
            json=data,
            headers=headers,
            timeout=30,
            verify=False,  # Skip SSL verification for now
        )

        if response.status_code == 200:
            return response.json()
        elif response.status_code == 401:
            raise ValueError(
                "Authentication failed with API token. Please check your AUGMENT_API_TOKEN."
            )
        elif response.status_code == 404:
            raise ValueError(f"Endpoint not found: {path}")
        else:
            raise ValueError(
                f"API request failed with status {response.status_code}: {response.text}"
            )

    except requests.exceptions.RequestException as e:
        raise ValueError(f"Network error: {e}")


def parse_tags(tag_strings: List[str]) -> Dict[str, str]:
    """Parse tag strings in format 'key=value' into a dictionary."""
    tags = {}
    for tag_str in tag_strings:
        if "=" not in tag_str:
            raise ValueError(f"Invalid tag format '{tag_str}'. Expected 'key=value'")
        key, value = tag_str.split("=", 1)
        tags[key.strip()] = value.strip()
    return tags


def parse_tag_filters(filter_strings: List[str]) -> List[dict]:
    """Parse tag filter strings into dictionaries for HTTP requests.

    Format: 'key=value1,value2' or 'key' (matches any value) or '!key=value' (inverted)
    """
    filters = []
    for filter_str in filter_strings:
        invert = filter_str.startswith("!")
        if invert:
            filter_str = filter_str[1:]

        if "=" in filter_str:
            key, values_str = filter_str.split("=", 1)
            values = [v.strip() for v in values_str.split(",")]
        else:
            key = filter_str
            values = []

        filters.append(
            {
                "key": key.strip(),
                "values": values,
                "match_any": True,  # Default to OR logic
                "invert": invert,
            }
        )

    return filters


def cmd_create(args, namespace: str):
    """Create or update a secret."""
    # Get secret value
    if args.value:
        value = args.value
    elif args.value_file:
        with open(args.value_file, "r") as f:
            value = f.read().strip()
    else:
        value = getpass.getpass("Enter secret value: ")

    # Parse tags
    tags = parse_tags(args.tags) if args.tags else {}

    # Get authentication and endpoint
    auth = get_authentication()
    endpoint = get_api_proxy_endpoint(namespace)

    # Prepare request data
    data = {
        "name": args.name,
        "value": value,
        "tags": tags,
        "description": args.description or "",
        "expected_version": args.expected_version or "",
        "max_value_size_bytes": 0,  # Use default
    }

    try:
        response = make_api_request(endpoint, "user-secrets/upsert", data, auth)

        print(f"Secret '{args.name}' created/updated successfully")
        print(f"Version: {response.get('version', 'unknown')}")
        if "updated_at" in response:
            print(f"Updated at: {response['updated_at']}")

    except Exception as e:
        logging.error(f"Failed to create/update secret: {e}")
        raise


def cmd_get(args, namespace: str):
    """Get a secret by name."""
    # Get authentication and endpoint
    auth = get_authentication()
    endpoint = get_api_proxy_endpoint(namespace)

    # Prepare request data
    data = {
        "name": args.name,
    }

    try:
        response = make_api_request(endpoint, "user-secrets/get", data, auth)
        secret = response.get("secret")

        if not secret:
            print(f"Secret '{args.name}' not found", file=sys.stderr)
            sys.exit(1)

        if args.json:
            print(json.dumps(secret, indent=2))
        else:
            print(f"Name: {secret['name']}")
            print(f"Value: {secret['value']}")
            if secret.get("description"):
                print(f"Description: {secret['description']}")
            if secret.get("tags"):
                print(f"Tags: {secret['tags']}")
            print(f"Version: {secret['version']}")
            if secret.get("created_at"):
                print(f"Created: {secret['created_at']}")
            if secret.get("updated_at"):
                print(f"Updated: {secret['updated_at']}")

    except Exception as e:
        if "not found" in str(e).lower():
            print(f"Secret '{args.name}' not found", file=sys.stderr)
        else:
            logging.error(f"Failed to get secret: {e}")
        sys.exit(1)


def cmd_list(args, namespace: str):
    """List secrets with optional filtering."""
    # Get authentication and endpoint
    auth = get_authentication()
    endpoint = get_api_proxy_endpoint(namespace)

    # Parse tag filters
    tag_filters = parse_tag_filters(args.tag_filters) if args.tag_filters else []

    # Prepare request data
    data = {
        "name_pattern": args.name_pattern or "",
        "tag_filters": tag_filters,
        "include_values": args.show_values,
        "page_size": 100,  # Get up to 100 secrets
        "page_token": "",
    }

    try:
        response = make_api_request(endpoint, "user-secrets/list", data, auth)
        secrets = response.get("secrets", [])

        if args.json:
            print(json.dumps(secrets, indent=2))
        else:
            if not secrets:
                print("No secrets found")
                return

            print(f"Found {len(secrets)} secret(s):")
            print()

            for secret in secrets:
                print(f"Name: {secret['name']}")
                if args.show_values and "value" in secret:
                    print(f"Value: {secret['value']}")
                if secret.get("description"):
                    print(f"Description: {secret['description']}")
                if secret.get("tags"):
                    print(f"Tags: {secret['tags']}")
                print(f"Version: {secret['version']}")
                if secret.get("updated_at"):
                    print(f"Updated: {secret['updated_at']}")
                print()

    except Exception as e:
        logging.error(f"Failed to list secrets: {e}")
        sys.exit(1)


def cmd_delete(args, namespace: str):
    """Delete a secret."""
    # Get authentication and endpoint
    auth = get_authentication()
    endpoint = get_api_proxy_endpoint(namespace)

    # Prepare request data
    data = {
        "name": args.name,
        "expected_version": args.expected_version or "",
    }

    try:
        response = make_api_request(endpoint, "user-secrets/delete", data, auth)

        if response.get("deleted", False):
            print(f"Secret '{args.name}' deleted successfully")
        else:
            print(f"Secret '{args.name}' was not found or already deleted")

    except Exception as e:
        logging.error(f"Failed to delete secret: {e}")
        sys.exit(1)


def main():
    """Main entry point."""
    setup_console_logging()

    parser = argparse.ArgumentParser(
        description="Command-line tool for managing user secrets",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Create a secret interactively
  %(prog)s create my-api-key --description "My API key"

  # Create a secret with value and tags
  %(prog)s create github-token --value "ghp_xxx" --tag "service=github" --tag "env=prod"

  # Create a secret from file
  %(prog)s create db-password --value-file /path/to/password.txt

  # List all secrets (metadata only)
  %(prog)s list

  # List secrets with values
  %(prog)s list --show-values

  # List secrets matching pattern
  %(prog)s list --name-pattern "github.*"

  # List secrets with tag filters
  %(prog)s list --tag-filter "service=github" --tag-filter "env=prod"

  # Get a specific secret
  %(prog)s get my-api-key

  # Get secret as JSON
  %(prog)s get my-api-key --json

  # Delete a secret
  %(prog)s delete my-api-key

Authentication:
  Set the AUGMENT_API_TOKEN environment variable with your API token.
        """,
    )

    # Add namespace argument
    parser.add_argument(
        "--namespace",
        default=get_default_namespace(),
        help=f"Kubernetes namespace to use (default: {get_default_namespace()})",
    )

    # Add subcommands
    subparsers = parser.add_subparsers(dest="command", help="Available commands")

    # Create command
    create_parser = subparsers.add_parser("create", help="Create or update a secret")
    create_parser.add_argument("name", help="Secret name")
    create_parser.add_argument(
        "--value", help="Secret value (if not provided, will prompt)"
    )
    create_parser.add_argument("--value-file", help="Read secret value from file")
    create_parser.add_argument("--description", help="Secret description")
    create_parser.add_argument(
        "--tag",
        dest="tags",
        action="append",
        help="Add tag in format 'key=value' (can be used multiple times)",
    )
    create_parser.add_argument(
        "--expected-version", help="Expected version for optimistic concurrency"
    )
    create_parser.set_defaults(func=cmd_create)

    # Get command
    get_parser = subparsers.add_parser("get", help="Get a secret by name")
    get_parser.add_argument("name", help="Secret name")
    get_parser.add_argument("--json", action="store_true", help="Output as JSON")
    get_parser.set_defaults(func=cmd_get)

    # List command
    list_parser = subparsers.add_parser("list", help="List secrets")
    list_parser.add_argument(
        "--name-pattern", help="Regex pattern to filter secret names"
    )
    list_parser.add_argument(
        "--tag-filter",
        dest="tag_filters",
        action="append",
        help="Filter by tags (format: 'key=value1,value2' or 'key' or '!key=value')",
    )
    list_parser.add_argument(
        "--show-values",
        action="store_true",
        help="Include secret values in output (use with caution)",
    )
    list_parser.add_argument("--json", action="store_true", help="Output as JSON")
    list_parser.set_defaults(func=cmd_list)

    # Delete command
    delete_parser = subparsers.add_parser("delete", help="Delete a secret")
    delete_parser.add_argument("name", help="Secret name")
    delete_parser.add_argument(
        "--expected-version", help="Expected version for optimistic concurrency"
    )
    delete_parser.set_defaults(func=cmd_delete)

    args = parser.parse_args()

    if not args.command:
        parser.print_help()
        sys.exit(1)

    try:
        # Execute the command with namespace
        args.func(args, args.namespace)

    except KeyboardInterrupt:
        sys.exit(1)
    except ValueError as e:
        logging.error("%s", e)
        sys.exit(1)
    except Exception as e:
        logging.error("Unexpected error: %s", e)
        sys.exit(1)


if __name__ == "__main__":
    main()
