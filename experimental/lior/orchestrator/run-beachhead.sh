#!/bin/bash

# Helper script to run beachhead CLI with custom instructions
# Usage: ./run-beachhead.sh "your instruction here"

if [ $# -eq 0 ]; then
    echo "Usage: $0 \"instruction\""
    echo "Example: $0 \"create a sub agent\""
    exit 1
fi

INSTRUCTION="$1"

echo "Building beachhead CLI..."
pnpm -C clients/beachhead run build > /dev/null 2>&1

if [ $? -ne 0 ]; then
    echo "Build failed!"
    exit 1
fi

echo "Cleanup..."
for i in /tmp/subagent-isolation-XXXXXX*; do umount $i/isolated_view; done
rm -rf /tmp/subagent-isolation-XXXXXX*
rm -rf /tmp/beachhead-agent-*

# Create temporary file with instruction
TEMP_FILE=$(mktemp)
echo "$INSTRUCTION" > "$TEMP_FILE"

AUGMENT_API_URL=https://staging-shard-0.api.augmentcode.com \
AUGMENT_API_TOKEN=$(cat ~/.augment/token) \
./clients/beachhead/out/augment.mjs \
--workspace-root /home/<USER>/augment/ \
--enable-sub-agent-tool \
--instruction-file "$TEMP_FILE"

# Clean up
rm "$TEMP_FILE"
