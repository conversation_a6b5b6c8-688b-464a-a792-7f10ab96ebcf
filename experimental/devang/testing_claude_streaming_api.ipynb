{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2025-03-10 16:36:46\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAnthropic Direct Client initialized\u001b[0m \u001b[36mmodel_name\u001b[0m=\u001b[35mclaude-3-7-sonnet@20250219\u001b[0m\n", "RawMessageStartEvent(message=Message(id='msg_vrtx_01PA4JbnRWpgYuQGXSxbCp6H', content=[], model='claude-3-7-sonnet-20250219', role='assistant', stop_reason=None, stop_sequence=None, type='message', usage=Usage(input_tokens=444, output_tokens=5, cache_creation_input_tokens=0, cache_read_input_tokens=0)), type='message_start')\n", "RawContentBlockStartEvent(content_block=TextBlock(text='', type='text'), index=0, type='content_block_start')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=\"I'll write a program\", type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=\"I'll write a program\", snapshot=\"I'll write a program\")\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=' that gets the current date using the available tool, calculates the', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=' that gets the current date using the available tool, calculates the', snapshot=\"I'll write a program that gets the current date using the available tool, calculates the\")\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=' factorial of 5, and prints both results.\\n\\nFirst', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=' factorial of 5, and prints both results.\\n\\nFirst', snapshot=\"I'll write a program that gets the current date using the available tool, calculates the factorial of 5, and prints both results.\\n\\nFirst\")\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=', let me get the current date using the get_date tool:', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=', let me get the current date using the get_date tool:', snapshot=\"I'll write a program that gets the current date using the available tool, calculates the factorial of 5, and prints both results.\\n\\nFirst, let me get the current date using the get_date tool:\")\n", "ContentBlockStopEvent(index=0, type='content_block_stop', content_block=TextBlock(text=\"I'll write a program that gets the current date using the available tool, calculates the factorial of 5, and prints both results.\\n\\nFirst, let me get the current date using the get_date tool:\", type='text'))\n", "RawContentBlockStartEvent(content_block=ToolUseBlock(id='toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx', input={}, name='get_date', type='tool_use'), index=1, type='content_block_start')\n", "RawContentBlockDeltaEvent(delta=InputJSONDelta(partial_json='', type='input_json_delta'), index=1, type='content_block_delta')\n", "InputJsonEvent(type='input_json', partial_json='', snapshot={})\n", "ContentBlockStopEvent(index=1, type='content_block_stop', content_block=ToolUseBlock(id='toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx', input={}, name='get_date', type='tool_use'))\n", "RawMessageDeltaEvent(delta=Delta(stop_reason='tool_use', stop_sequence=None), type='message_delta', usage=MessageDeltaUsage(output_tokens=81))\n", "MessageStopEvent(type='message_stop', message=Message(id='msg_vrtx_01PA4JbnRWpgYuQGXSxbCp6H', content=[TextBlock(text=\"I'll write a program that gets the current date using the available tool, calculates the factorial of 5, and prints both results.\\n\\nFirst, let me get the current date using the get_date tool:\", type='text'), ToolUseBlock(id='toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx', input={}, name='get_date', type='tool_use')], model='claude-3-7-sonnet-20250219', role='assistant', stop_reason='tool_use', stop_sequence=None, type='message', usage=Usage(input_tokens=444, output_tokens=81, cache_creation_input_tokens=0, cache_read_input_tokens=0)))\n", "\n", "Tool calls detected: [ToolUseBlock(id='toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx', input={}, name='get_date', type='tool_use')]\n", "\n", "Tool call detected: ToolUseBlock(id='toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx', input={}, name='get_date', type='tool_use')\n", "Tool result: {'tool_use_id': 'toolu_vrtx_013RCPYRWrVMT8yGVh8D9PYx', 'content': '2025-03-10', 'is_error': False, 'type': 'tool_result'}\n", "\n", "Continued response after tool call:\n", "RawMessageStartEvent(message=Message(id='msg_vrtx_01Vefjeo2zCcdkZgRYfuyqHH', content=[], model='claude-3-7-sonnet-20250219', role='assistant', stop_reason=None, stop_sequence=None, type='message', usage=Usage(input_tokens=498, output_tokens=2, cache_creation_input_tokens=0, cache_read_input_tokens=0)), type='message_start')\n", "RawContentBlockStartEvent(content_block=TextBlock(text='', type='text'), index=0, type='content_block_start')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='Here', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='Here', snapshot='Here')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=\"'s a program that gets the current date using the tool\", type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=\"'s a program that gets the current date using the tool\", snapshot=\"Here's a program that gets the current date using the tool\")\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=', calculates the factorial of 5, and prints both results:', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=', calculates the factorial of 5, and prints both results:', snapshot=\"Here's a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\")\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='\\n\\n```python\\n# Get the current date using the tool', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='\\n\\n```python\\n# Get the current date using the tool', snapshot=\"Here's a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\")\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='\\ncurrent_date = \"2025-03-', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='\\ncurrent_date = \"2025-03-', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='10\"  # This value comes from the get_date', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='10\"  # This value comes from the get_date', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=' tool\\n\\n# Calculate factorial of 5\\ndef', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=' tool\\n\\n# Calculate factorial of 5\\ndef', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=' factorial(n):\\n    if n == ', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=' factorial(n):\\n    if n == ', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == ')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='0 or n == 1:\\n        return 1\\n    ', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='0 or n == 1:\\n        return 1\\n    ', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    ')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='else:\\n        return n * factorial(n-1)\\n\\nfactorial', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='else:\\n        return n * factorial(n-1)\\n\\nfactorial', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='_5 = factorial(5)\\n\\n# Print both', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='_5 = factorial(5)\\n\\n# Print both', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=' results\\nprint(f\"Current date: {current_date}\")', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=' results\\nprint(f\"Current date: {current_date}\")', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='\\nprint(f\"Factorial of 5: {factorial_5}\")', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='\\nprint(f\"Factorial of 5: {factorial_5}\")', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")\\nprint(f\"Factorial of 5: {factorial_5}\")')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")\\nprint(f\"Factorial of 5: {factorial_5}\")\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=' date: 2025-03-10\\nFactorial of 5', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=' date: 2025-03-10\\nFactorial of 5', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")\\nprint(f\"Factorial of 5: {factorial_5}\")\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent date: 2025-03-10\\nFactorial of 5')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=': 120\\n```\\n\\nThe program successfully:\\n1. Gets', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=': 120\\n```\\n\\nThe program successfully:\\n1. Gets', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")\\nprint(f\"Factorial of 5: {factorial_5}\")\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent date: 2025-03-10\\nFactorial of 5: 120\\n```\\n\\nThe program successfully:\\n1. Gets')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=' the current date using the get_date tool', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=' the current date using the get_date tool', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")\\nprint(f\"Factorial of 5: {factorial_5}\")\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent date: 2025-03-10\\nFactorial of 5: 120\\n```\\n\\nThe program successfully:\\n1. Gets the current date using the get_date tool')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='\\n2. Calculates the factorial of 5 (', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='\\n2. Calculates the factorial of 5 (', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")\\nprint(f\"Factorial of 5: {factorial_5}\")\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent date: 2025-03-10\\nFactorial of 5: 120\\n```\\n\\nThe program successfully:\\n1. Gets the current date using the get_date tool\\n2. Calculates the factorial of 5 (')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='which is 5 × 4 × 3 × ', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='which is 5 × 4 × 3 × ', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")\\nprint(f\"Factorial of 5: {factorial_5}\")\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent date: 2025-03-10\\nFactorial of 5: 120\\n```\\n\\nThe program successfully:\\n1. Gets the current date using the get_date tool\\n2. Calculates the factorial of 5 (which is 5 × 4 × 3 × ')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text='2 × 1 = 120)\\n3. Prints', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text='2 × 1 = 120)\\n3. Prints', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")\\nprint(f\"Factorial of 5: {factorial_5}\")\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent date: 2025-03-10\\nFactorial of 5: 120\\n```\\n\\nThe program successfully:\\n1. Gets the current date using the get_date tool\\n2. Calculates the factorial of 5 (which is 5 × 4 × 3 × 2 × 1 = 120)\\n3. Prints')\n", "RawContentBlockDeltaEvent(delta=TextDelta(text=' both results', type='text_delta'), index=0, type='content_block_delta')\n", "TextEvent(type='text', text=' both results', snapshot='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")\\nprint(f\"Factorial of 5: {factorial_5}\")\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent date: 2025-03-10\\nFactorial of 5: 120\\n```\\n\\nThe program successfully:\\n1. Gets the current date using the get_date tool\\n2. Calculates the factorial of 5 (which is 5 × 4 × 3 × 2 × 1 = 120)\\n3. Prints both results')\n", "ContentBlockStopEvent(index=0, type='content_block_stop', content_block=TextBlock(text='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")\\nprint(f\"Factorial of 5: {factorial_5}\")\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent date: 2025-03-10\\nFactorial of 5: 120\\n```\\n\\nThe program successfully:\\n1. Gets the current date using the get_date tool\\n2. Calculates the factorial of 5 (which is 5 × 4 × 3 × 2 × 1 = 120)\\n3. Prints both results', type='text'))\n", "RawMessageDeltaEvent(delta=Delta(stop_reason='end_turn', stop_sequence=None), type='message_delta', usage=MessageDeltaUsage(output_tokens=255))\n", "MessageStopEvent(type='message_stop', message=Message(id='msg_vrtx_01Vefjeo2zCcdkZgRYfuyqHH', content=[TextBlock(text='Here\\'s a program that gets the current date using the tool, calculates the factorial of 5, and prints both results:\\n\\n```python\\n# Get the current date using the tool\\ncurrent_date = \"2025-03-10\"  # This value comes from the get_date tool\\n\\n# Calculate factorial of 5\\ndef factorial(n):\\n    if n == 0 or n == 1:\\n        return 1\\n    else:\\n        return n * factorial(n-1)\\n\\nfactorial_5 = factorial(5)\\n\\n# Print both results\\nprint(f\"Current date: {current_date}\")\\nprint(f\"Factorial of 5: {factorial_5}\")\\n```\\n\\nWhen executed, this program will output:\\n```\\nCurrent date: 2025-03-10\\nFactorial of 5: 120\\n```\\n\\nThe program successfully:\\n1. Gets the current date using the get_date tool\\n2. Calculates the factorial of 5 (which is 5 × 4 × 3 × 2 × 1 = 120)\\n3. Prints both results', type='text')], model='claude-3-7-sonnet-20250219', role='assistant', stop_reason='end_turn', stop_sequence=None, type='message', usage=Usage(input_tokens=498, output_tokens=255, cache_creation_input_tokens=0, cache_read_input_tokens=0)))\n"]}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "import json\n", "from datetime import datetime\n", "from anthropic.types import ToolParam, ToolUseBlock, MessageParam, ToolResultBlockParam\n", "from typing import Iterable\n", "\n", "# Configuration\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-7-sonnet@20250219\"  # or \"claude-3-5-sonnet-v2@20241022\"\n", "TEMPERATURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "\n", "\n", "# Define a tool for getting the current date\n", "def get_date_tool(args, tool_call_id: str) -> ToolResultBlockParam:\n", "    debug = args.get(\"debug\", False)\n", "    current_date = datetime.now().strftime(\"%Y-%m-%d\")\n", "    if debug:\n", "        print(f\"Debug info: Called get_date_tool\\nCurrent date: {current_date}\")\n", "    return ToolResultBlockParam(\n", "        tool_use_id=tool_call_id,\n", "        content=current_date,\n", "        is_error=False,\n", "        type=\"tool_result\",\n", "    )\n", "\n", "\n", "# Tool definitions\n", "tool_definitions: Iterable[ToolParam] = [\n", "    # {\n", "    #     \"name\": \"get_date\",\n", "    #     \"description\": \"Get the current date in YYYY-MM-DD format\",\n", "    #     \"input_schema\": {\n", "    #         \"type\": \"object\",\n", "    #         \"properties\": {\n", "    #             \"debug\": {\"type\": \"boolean\", \"description\": \"Whether to include debug information\"}\n", "    #         },\n", "    #         \"required\": []\n", "    #     }\n", "    # }\n", "    ToolParam(\n", "        name=\"get_date\",\n", "        description=\"Get the current date in YYYY-MM-DD format\",\n", "        input_schema={\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"debug\": {\n", "                    \"type\": \"boolean\",\n", "                    \"description\": \"Whether to include debug information\",\n", "                }\n", "            },\n", "            \"required\": [],\n", "        },\n", "    )\n", "]\n", "\n", "# Create client\n", "client = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERATURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "\n", "# Function to handle tool calls\n", "def handle_tool_call(tool_call: ToolUseBlock):\n", "    tool_name = tool_call.name\n", "    tool_args = tool_call.input\n", "    tool_call_id = tool_call.id\n", "\n", "    if tool_name == \"get_date\":\n", "        return get_date_tool(tool_args, tool_call_id)\n", "    else:\n", "        return f\"Unknown tool: {tool_name}\"\n", "\n", "\n", "# Example prompt that might trigger a tool call\n", "prompt = \"\"\"\n", "Write a program that:\n", "1. Gets the current date\n", "2. Calculates factorial of 5\n", "3. Prints both results\n", "\n", "Use the get_date tool to retrieve the current date.\n", "\"\"\"\n", "\n", "with client.client.messages.stream(\n", "    model=MODEL_NAME,\n", "    max_tokens=MAX_OUTPUT_TOKENS,\n", "    messages=[{\"role\": \"user\", \"content\": prompt}],\n", "    system=\"You have access to tools. Use them when appropriate.\",\n", "    temperature=TEMPERATURE,\n", "    tools=tool_definitions,\n", ") as stream:\n", "    # Process the streaming response\n", "    for response in stream:\n", "        print(response)\n", "\n", "        # Check for tool calls\n", "        if (\n", "            hasattr(response, \"message\")\n", "            and hasattr(response.message, \"content\")\n", "            and any(\n", "                isinstance(content, ToolUseBlock)\n", "                for content in response.message.content\n", "            )\n", "        ):\n", "            tool_calls = [\n", "                content\n", "                for content in response.message.content\n", "                if isinstance(content, ToolUseBlock)\n", "            ]\n", "            print(f\"\\nTool calls detected: {tool_calls}\")\n", "            for tool_call in tool_calls:\n", "                print(f\"\\nTool call detected: {tool_call}\")\n", "\n", "                # Handle the tool call\n", "                tool_result = handle_tool_call(tool_call)\n", "                print(f\"Tool result: {tool_result}\")\n", "\n", "                fake_messages: Iterable[MessageParam] = [\n", "                    MessageParam(role=\"user\", content=prompt),\n", "                    MessageParam(role=\"assistant\", content=[tool_call]),\n", "                    MessageParam(role=\"user\", content=[tool_result]),\n", "                ]\n", "\n", "                # Continue the conversation with the tool result\n", "                with client.client.messages.stream(\n", "                    model=MODEL_NAME,\n", "                    max_tokens=MAX_OUTPUT_TOKENS,\n", "                    messages=fake_messages,\n", "                    system=\"You have access to tools. Use them when appropriate.\",\n", "                    temperature=TEMPERATURE,\n", "                    tools=tool_definitions,\n", "                ) as continued_stream:\n", "                    print(\"\\nContinued response after tool call:\")\n", "                    for continued_response in continued_stream:\n", "                        print(continued_response)"]}, {"cell_type": "code", "execution_count": 15, "metadata": {}, "outputs": [{"name": "stdout", "output_type": "stream", "text": ["\u001b[2m2025-03-14 23:46:56\u001b[0m [\u001b[32m\u001b[1minfo     \u001b[0m] \u001b[1mAnthropic Direct Client initialized\u001b[0m \u001b[36mmodel_name\u001b[0m=\u001b[35mclaude-3-7-sonnet@20250219\u001b[0m\n"]}, {"name": "stdout", "output_type": "stream", "text": ["\n", "=== TOKEN COUNTING EXPERIMENT ===\n", "\n", "=== TRIAL 1/10 ===\n", "API reported token count: 127\n", "Text content tokens: 99\n", "Tool call tokens: 47\n", "Calculated total: 146\n", "API reported total: 127\n", "Difference: -19\n", "\n", "=== TRIAL 2/10 ===\n", "API reported token count: 103\n", "Text content tokens: 73\n", "Tool call tokens: 45\n", "Calculated total: 118\n", "API reported total: 103\n", "Difference: -15\n", "\n", "=== TRIAL 3/10 ===\n", "API reported token count: 143\n", "Text content tokens: 119\n", "Tool call tokens: 44\n", "Calculated total: 163\n", "API reported total: 143\n", "Difference: -20\n", "\n", "=== TRIAL 4/10 ===\n", "API reported token count: 103\n", "Text content tokens: 73\n", "Tool call tokens: 44\n", "Calculated total: 117\n", "API reported total: 103\n", "Difference: -14\n", "\n", "=== TRIAL 5/10 ===\n", "API reported token count: 143\n", "Text content tokens: 119\n", "Tool call tokens: 43\n", "Calculated total: 162\n", "API reported total: 143\n", "Difference: -19\n", "\n", "=== TRIAL 6/10 ===\n", "API reported token count: 103\n", "Text content tokens: 73\n", "Tool call tokens: 43\n", "Calculated total: 116\n", "API reported total: 103\n", "Difference: -13\n", "\n", "=== TRIAL 7/10 ===\n", "API reported token count: 143\n", "Text content tokens: 119\n", "Tool call tokens: 44\n", "Calculated total: 163\n", "API reported total: 143\n", "Difference: -20\n", "\n", "=== TRIAL 8/10 ===\n", "API reported token count: 103\n", "Text content tokens: 73\n", "Tool call tokens: 44\n", "Calculated total: 117\n", "API reported total: 103\n", "Difference: -14\n", "\n", "=== TRIAL 9/10 ===\n", "API reported token count: 143\n", "Text content tokens: 119\n", "Tool call tokens: 45\n", "Calculated total: 164\n", "API reported total: 143\n", "Difference: -21\n", "\n", "=== TRIAL 10/10 ===\n", "API reported token count: 103\n", "Text content tokens: 73\n", "Tool call tokens: 42\n", "Calculated total: 115\n", "API reported total: 103\n", "Difference: -12\n", "\n", "=== EXPERIMENT SUMMARY ===\n", "Number of trials: 10\n", "\n", "--- Token Differences (Reported - Calculated) ---\n", "Mean difference: -16.70 tokens\n", "Standard deviation: 3.40 tokens\n", "Min difference: -21 tokens\n", "Max difference: -12 tokens\n", "\n", "--- Text Content Tokens ---\n", "Mean: 94.00 tokens\n", "Standard deviation: 22.92 tokens\n", "\n", "--- <PERSON><PERSON> Call <PERSON>s ---\n", "Mean: 44.10 tokens\n", "Standard deviation: 1.37 tokens\n", "\n", "--- Individual Trial Results ---\n", "Trial 1: Text=99, Tool=47, Calculated=146, Reported=127, Diff=-19\n", "Trial 2: Text=73, Tool=45, Calculated=118, Reported=103, Diff=-15\n", "Trial 3: Text=119, Tool=44, Calculated=163, Reported=143, Diff=-20\n", "Trial 4: Text=73, Tool=44, Calculated=117, Reported=103, Diff=-14\n", "Trial 5: Text=119, Tool=43, Calculated=162, Reported=143, Diff=-19\n", "Trial 6: Text=73, Tool=43, Calculated=116, Reported=103, Diff=-13\n", "Trial 7: Text=119, Tool=44, Calculated=163, Reported=143, Diff=-20\n", "Trial 8: Text=73, Tool=44, Calculated=117, Reported=103, Diff=-14\n", "Trial 9: Text=119, Tool=45, Calculated=164, Reported=143, Diff=-21\n", "Trial 10: Text=73, Tool=42, Calculated=115, Reported=103, Diff=-12\n"]}], "source": ["from base.third_party_clients.anthropic_vertexai_client import AnthropicVertexAiClient\n", "from anthropic.types import ToolParam, ToolUseBlock, MessageParam, ToolResultBlockParam\n", "from typing import Iterable, List, Dict, Any, Optional\n", "import statistics\n", "import time\n", "\n", "# Configuration\n", "REGION = \"us-east5\"\n", "PROJECT_ID = \"augment-387916\"\n", "MODEL_NAME = \"claude-3-7-sonnet@20250219\"\n", "TEMPERATURE = 0\n", "MAX_OUTPUT_TOKENS = 1024 * 8\n", "NUM_TRIALS = 10  # Number of trials to run\n", "\n", "# Create client\n", "client = AnthropicVertexAiClient(\n", "    project_id=PROJECT_ID,\n", "    region=REGION,\n", "    model_name=MODEL_NAME,\n", "    temperature=TEMPERATURE,\n", "    max_output_tokens=MAX_OUTPUT_TOKENS,\n", ")\n", "\n", "# Tool definitions\n", "tool_definitions: List[ToolParam] = [\n", "    ToolParam(\n", "        name=\"get_date\",\n", "        description=\"Get the current date in YYYY-MM-DD format\",\n", "        input_schema={\n", "            \"type\": \"object\",\n", "            \"properties\": {\n", "                \"debug\": {\n", "                    \"type\": \"boolean\",\n", "                    \"description\": \"Whether to include debug information\",\n", "                }\n", "            },\n", "            \"required\": [],\n", "        },\n", "    )\n", "]\n", "\n", "\n", "def run_single_trial(trial_num):\n", "    \"\"\"Run a single token counting trial.\"\"\"\n", "    print(f\"\\n=== TRIAL {trial_num}/{NUM_TRIALS} ===\")\n", "\n", "    # Vary the prompt slightly to avoid cached responses\n", "    prompt = f\"\"\"\n", "    Write a program that gets the current date using the get_date tool and prints it.\n", "    Include a comment with trial number {trial_num}.\n", "    \"\"\"\n", "\n", "    # Track tokens\n", "    text_content_tokens = 0\n", "    tool_call_tokens = 0\n", "    reported_total_tokens = 0\n", "\n", "    # Track content for token counting\n", "    text_content = \"\"\n", "    tool_call_json = None\n", "    tool_call_detected = False\n", "\n", "    with client.client.messages.stream(\n", "        model=MODEL_NAME,\n", "        max_tokens=MAX_OUTPUT_TOKENS,\n", "        messages=[{\"role\": \"user\", \"content\": prompt}],\n", "        system=\"You have access to tools. Use them when appropriate.\",\n", "        temperature=TEMPERATURE,\n", "        tools=tool_definitions,\n", "    ) as stream:\n", "        for response in stream:\n", "            # Check for message_stop to get reported token count\n", "            if hasattr(response, \"type\") and response.type == \"message_stop\":\n", "                reported_total_tokens = response.message.usage.output_tokens\n", "                print(f\"API reported token count: {reported_total_tokens}\")\n", "\n", "            # Check for text content and tool calls\n", "            if (\n", "                hasattr(response, \"message\")\n", "                and hasattr(response.message, \"content\")\n", "                and response.message.content\n", "            ):\n", "                for content in response.message.content:\n", "                    # Handle text content\n", "                    if hasattr(content, \"text\") and content.text:\n", "                        text_content += content.text\n", "\n", "                    # Handle tool calls\n", "                    if isinstance(content, ToolUseBlock) and not tool_call_detected:\n", "                        tool_call_detected = True\n", "                        tool_call_json = {\n", "                            \"name\": content.name,\n", "                            \"input\": content.input,\n", "                            \"id\": content.id,\n", "                        }\n", "\n", "                        # Handle the tool call\n", "                        tool_result = get_date_tool(content.input, content.id)\n", "\n", "                        # Continue conversation with tool result\n", "                        fake_messages = [\n", "                            MessageParam(role=\"user\", content=prompt),\n", "                            MessageParam(role=\"assistant\", content=[content]),\n", "                            MessageParam(role=\"user\", content=[tool_result]),\n", "                        ]\n", "\n", "                        # Break out of current stream to handle tool result\n", "                        break\n", "\n", "        # Count tokens for text content and tool call\n", "        if text_content:\n", "            text_content_tokens = client.count_tokens(text_content)\n", "            print(f\"Text content tokens: {text_content_tokens}\")\n", "\n", "        if tool_call_json:\n", "            tool_call_str = json.dumps(tool_call_json)\n", "            tool_call_tokens = client.count_tokens(tool_call_str)\n", "            print(f\"Tool call tokens: {tool_call_tokens}\")\n", "\n", "            # Handle tool result and continue conversation\n", "            with client.client.messages.stream(\n", "                model=MODEL_NAME,\n", "                max_tokens=MAX_OUTPUT_TOKENS,\n", "                messages=fake_messages,\n", "                system=\"You have access to tools. Use them when appropriate.\",\n", "                temperature=TEMPERATURE,\n", "                tools=tool_definitions,\n", "            ) as _:\n", "                # Process continued response if needed\n", "                pass\n", "\n", "    # Analysis for this trial\n", "    calculated_total = text_content_tokens + tool_call_tokens\n", "    difference = reported_total_tokens - calculated_total\n", "    print(f\"Calculated total: {calculated_total}\")\n", "    print(f\"API reported total: {reported_total_tokens}\")\n", "    print(f\"Difference: {difference}\")\n", "\n", "    return {\n", "        \"text_tokens\": text_content_tokens,\n", "        \"tool_tokens\": tool_call_tokens,\n", "        \"calculated_total\": calculated_total,\n", "        \"reported_total\": reported_total_tokens,\n", "        \"difference\": difference,\n", "    }\n", "\n", "\n", "def run_token_counting_experiment():\n", "    \"\"\"Run multiple trials and analyze the results.\"\"\"\n", "    print(\"\\n=== TOKEN COUNTING EXPERIMENT ===\")\n", "\n", "    results = []\n", "\n", "    for i in range(1, NUM_TRIALS + 1):\n", "        trial_result = run_single_trial(i)\n", "        results.append(trial_result)\n", "        # Add a small delay between trials\n", "        if i < NUM_TRIALS:\n", "            time.sleep(1)\n", "\n", "    # Calculate statistics\n", "    differences = [r[\"difference\"] for r in results]\n", "    text_tokens = [r[\"text_tokens\"] for r in results]\n", "    tool_tokens = [r[\"tool_tokens\"] for r in results]\n", "\n", "    # Print summary statistics\n", "    print(\"\\n=== EXPERIMENT SUMMARY ===\")\n", "    print(f\"Number of trials: {NUM_TRIALS}\")\n", "\n", "    print(\"\\n--- Token Differences (Reported - Calculated) ---\")\n", "    print(f\"Mean difference: {statistics.mean(differences):.2f} tokens\")\n", "    if len(differences) > 1:  # Need at least 2 samples for std dev\n", "        print(f\"Standard deviation: {statistics.stdev(differences):.2f} tokens\")\n", "    print(f\"Min difference: {min(differences)} tokens\")\n", "    print(f\"Max difference: {max(differences)} tokens\")\n", "\n", "    print(\"\\n--- Text Content Tokens ---\")\n", "    print(f\"Mean: {statistics.mean(text_tokens):.2f} tokens\")\n", "    if len(text_tokens) > 1:\n", "        print(f\"Standard deviation: {statistics.stdev(text_tokens):.2f} tokens\")\n", "\n", "    print(\"\\n--- <PERSON><PERSON> Call Tokens ---\")\n", "    print(f\"Mean: {statistics.mean(tool_tokens):.2f} tokens\")\n", "    if len(tool_tokens) > 1:\n", "        print(f\"Standard deviation: {statistics.stdev(tool_tokens):.2f} tokens\")\n", "\n", "    # Print individual trial results\n", "    print(\"\\n--- Individual Trial Results ---\")\n", "    for i, result in enumerate(results, 1):\n", "        print(\n", "            f\"Trial {i}: Text={result['text_tokens']}, Tool={result['tool_tokens']}, \"\n", "            f\"Calculated={result['calculated_total']}, Reported={result['reported_total']}, \"\n", "            f\"Diff={result['difference']}\"\n", "        )\n", "\n", "\n", "# Run experiment\n", "run_token_counting_experiment()"]}], "metadata": {"kernelspec": {"display_name": "Python 3 (ipykernel)", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.9"}}, "nbformat": 4, "nbformat_minor": 2}