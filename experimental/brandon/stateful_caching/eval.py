import csv
from typing import Iterable
from dataclasses import dataclass, replace
import argparse
import logging
import zstandard as zstd
from base.datasets.completion import CompletionDatum
from base.prompt_format_completion import (
    EnderPromptFormatter,
    EnderPromptFormatterConfig,
    TokenApportionmentConfig,
    get_completion_prompt_formatter_by_name,
)
from base.prompt_format_completion.stateful_caching import StatefulCachingConfig
from base.prompt_format_completion.stateless_caching import StatelessCachingConfig
from base.prompt_format_completion.prompt_formatter import (
    PromptChunk,
    PromptInput,
    PromptCache,
)
from base.tokenizers import create_tokenizer_by_name, Tokenizer

# These are the values from eldev4_3_15b_deploy.jsonnet
TOKENIZER = "starcoder2"
FORMATTER = "ender"
APPORTIONMENT = TokenApportionmentConfig(
    max_content_len=6144,
    input_fraction=4 / 12,
    prefix_fraction=3 / 4,
    max_path_tokens=50,
    per_retriever_max_tokens={
        "dense_signature": 1024,
        "recency_retriever": 1024,
    },
)
PROMPT_CONFIG = EnderPromptFormatterConfig(
    stateless_caching_config=StatelessCachingConfig(
        nearby_prefix_token_len=512,
        quantize_token_len=64,
        quantize_char_len=250,
    ),
    component_order=[
        "path",
        "prefix",
        "retrieval",
        "signature",
        "nearby_prefix",
        "suffix",
    ],
    signature_chunk_origin="dense_signature",
)


@dataclass
class EvalConfig:
    name: str
    stateful_caching_config: StatefulCachingConfig
    use_cache: bool


OPTIONS = [
    EvalConfig("elden_default", StatefulCachingConfig(enabled=False), False),
    EvalConfig("elden_reverse", StatefulCachingConfig(enabled=True), False),
    EvalConfig("elden_cache", StatefulCachingConfig(enabled=True), True),
]


class GeneratedPrompt:
    special: list[int] = []
    pre = 0
    ret = 0
    sig = 0

    def __init__(self, tokens: Iterable[int]):
        self.tokens = list(tokens)

        self.path_begin = 0
        self.far_prefix_begin = 0
        self.retrieval_begin = 0
        self.signature_begin = 0

        pre, ret, sig = self.special
        for i, t in enumerate(tokens):
            if t not in self.special:
                continue
            if t == pre:
                self.far_prefix_begin = i
            if t == ret:
                self.retrieval_begin = i
                self.far_prefix_begin = self.far_prefix_begin or i
            if t == sig:
                self.signature_begin = i
                self.far_prefix_begin = self.far_prefix_begin or i
                self.retrieval_begin = self.retrieval_begin or i
            if self.far_prefix_begin and self.retrieval_begin and self.signature_begin:
                break

    def first_diff(self, other: list[int]):
        for i, (a, b) in enumerate(zip(self.tokens, other)):
            if a != b:
                return i
        return min(len(self.tokens), len(other))


class Eval:
    def __init__(self, config: EvalConfig, tokenizer: Tokenizer):
        self.config = config
        self.formatter = EnderPromptFormatter(
            APPORTIONMENT,
            replace(
                PROMPT_CONFIG, stateful_caching_config=config.stateful_caching_config
            ),
            tokenizer,
        )
        fields = [
            "user",
            "prompt_length",
            "path_pos",
            "prefix_pos",
            "retrieval_pos",
            "signature_pos",
            "prev_prompt_exists",
            "diff_pos",
        ]
        self.file = open(f"eval_{config.name}.csv", "w")
        self.writer = csv.DictWriter(self.file, fieldnames=fields)
        self.writer.writeheader()

    def process_prompt(self, user: str, curr: GeneratedPrompt, prev: GeneratedPrompt):
        dat = {}
        dat["user"] = user
        dat["prompt_length"] = len(curr.tokens)
        dat["path_pos"] = curr.path_begin
        dat["prefix_pos"] = curr.far_prefix_begin
        dat["retrieval_pos"] = curr.retrieval_begin
        dat["signature_pos"] = curr.signature_begin
        dat["prev_prompt_exists"] = bool(prev is not None and prev.tokens)
        dat["diff_pos"] = curr.first_diff(prev.tokens)
        self.writer.writerow(dat)


class Session:
    """A sequence of requests from a single user"""

    def __init__(self, user_id: str, n_evals: int):
        self.user_id = user_id
        self.prev_request = None
        self.prompts: tuple[GeneratedPrompt, ...] = ()
        self.caches: tuple[PromptCache, ...] = ()
        self.reset_caches(n_evals)

    def reset_caches(self, n_evals: int):
        self.prompts = tuple(GeneratedPrompt([]) for _ in range(n_evals))
        self.caches = tuple(PromptCache() for _ in range(n_evals))


class SessionCache:
    def __init__(self, n_evals: int):
        self.n_evals = n_evals
        self.all_sessions = {}
        self.prev = (None, None)

    def get(self, user: str) -> Session:
        if self.prev[0] != user:
            if user not in self.all_sessions:
                self.all_sessions[user] = Session(user, self.n_evals)
            self.prev = (user, self.all_sessions[user])
        assert isinstance(self.prev[1], Session)
        return self.prev[1]


def process(completions: Iterable[CompletionDatum], max_gap: float):
    tokenizer = create_tokenizer_by_name(TOKENIZER)
    GeneratedPrompt.special = [
        tokenizer.special_tokens.far_prefix,
        tokenizer.special_tokens.retrieval_section,
        tokenizer.special_tokens.sig_begin,
    ]
    evals = [Eval(eval_config, tokenizer) for eval_config in OPTIONS]

    sessions = SessionCache(len(OPTIONS))
    for comp in completions:
        user = comp.user_id
        session = sessions.get(user)
        if session.prev_request is not None:
            gap = (
                comp.request.timestamp - session.prev_request.timestamp
            ).total_seconds()
            if gap > max_gap:
                session.reset_caches(len(OPTIONS))

        if comp.request.position is None:
            logging.warning(f"Skipping {user}:{comp.request_id} due to no position")
            continue

        input = PromptInput(
            prefix=comp.request.prefix,
            suffix=comp.request.suffix,
            prefix_begin=comp.request.position.prefix_begin,
            path=comp.request.path,
            retrieved_chunks=[
                PromptChunk(
                    text=chunk.text,
                    path=chunk.path,
                    unique_id=None,
                    origin=chunk.origin,
                    char_start=chunk.crange.start,
                    char_end=chunk.crange.stop,
                    blob_name=chunk.blob_name,
                )
                for chunk in comp.response.retrieved_chunks
            ],
        )

        new_prompts = []
        new_caches = []
        for i, eval in enumerate(evals):
            out, cache = eval.formatter.format_prompt_with_cache(
                input,
                max_output_token_count=comp.request.output_len,  # TODO I think?
                prompt_cache=session.caches[i],
                invalid_blobs=[],
            )
            out = GeneratedPrompt(out.tokens())
            eval.process_prompt(user, out, session.prompts[i])
            new_prompts.append(out)
            new_caches.append(cache if eval.config.use_cache else PromptCache())
        session.prev_request = comp.request
        session.prompts = tuple(new_prompts)
        session.caches = tuple(new_caches)

    for eval in evals:
        eval.file.close()


def request_gen(dataset_path: str):
    schema = CompletionDatum.schema()
    with zstd.open(dataset_path, "r") as f:
        for line in f:
            yield schema.loads(line)


def main():
    parser = argparse.ArgumentParser()
    parser.add_argument(
        "--dataset",
        type=str,
        required=True,
        help="Path to dataset (zstd-compressed, json-lines)",
    )
    parser.add_argument(
        "--max-gap",
        type=float,
        default=15.0,
        help="If a gap of this many seconds is encountered, cache is reset",
    )
    args = parser.parse_args()
    process(request_gen(args.dataset), args.max_gap)


if __name__ == "__main__":
    main()
