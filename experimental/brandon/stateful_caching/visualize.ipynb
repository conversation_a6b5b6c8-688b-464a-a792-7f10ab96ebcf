{"cells": [{"cell_type": "code", "execution_count": 1, "metadata": {}, "outputs": [], "source": ["import pandas as pd\n", "import numpy as np\n", "import matplotlib.pyplot as plt\n", "\n", "\"\"\"\n", "Provide data generated by stateful_caching/eval.py\n", "data = {\n", "    \"recency_first\": pd.read_csv(\"/home/<USER>/elden_default.csv\"),\n", "    \"dense_first\": pd.read_csv(\"/home/<USER>/elden_reverse.csv\"),\n", "    \"cache_0_0\": pd.read_csv(\"/home/<USER>/elden_cache.csv\"),\n", "}\n", "\"\"\"\n", "data = {}\n", "\n", "\n", "def process_df(df):\n", "    df[\"cache_pct\"] = df.diff_pos / df.prompt_length\n", "\n", "    after_sig = df.diff_pos >= df.signature_pos\n", "    after_ret = df.diff_pos >= df.retrieval_pos\n", "    after_pre = df.diff_pos >= df.prefix_pos\n", "\n", "    df[\"after_sig\"] = after_sig\n", "    df[\"within_ret\"] = after_ret & ~after_sig\n", "    df[\"within_pre\"] = after_pre & ~after_ret\n", "    df[\"within_path\"] = ~after_pre\n", "    return df\n", "\n", "\n", "for name, df in data.items():\n", "    data[name] = process_df(df)\n", "\n", "if data:\n", "    fig = plt.figure(figsize=(10, 6))\n", "    ax = fig.subplots(1, 1)\n", "\n", "    for name, df in data.items():\n", "        df = df[df.prev_prompt_exists]\n", "        ax.ecdf(df[\"cache_pct\"], label=name)\n", "\n", "    ax.legend()\n", "    ax.set_title(\"Empirical CDF of prompt prefix shared with previous prompt\")\n", "    ax.set_xlabel(\"Percent of previous prompt shared\")\n", "    ax.set_ylabel(\"Percentile (Requests)\")\n", "    ax.grid(linewidth=0.5)\n", "    plt.show()"]}, {"cell_type": "code", "execution_count": 2, "metadata": {}, "outputs": [], "source": ["# Plotting occurrence of first difference, scaled to position within the component\n", "# Components are not to scale (e.g. path is scaled up to be visible)\n", "path_range = (0, 0.05)\n", "pre_range = (0.05, 0.15)\n", "ret_range = (0.15, 0.85)\n", "sig_range = (0.85, 1)\n", "\n", "if data:\n", "    fig = plt.figure(figsize=(10, 2.5 * len(data)))\n", "    axs = fig.subplots(len(data), 1)\n", "\n", "for i, (name, df) in enumerate(data.items()):\n", "    ax = axs[i]\n", "    df = df[df.prev_prompt_exists]\n", "    dat = []\n", "    df_path = df[df.within_path]\n", "    df_pre = df[df.within_pre]\n", "    df_ret = df[df.within_ret]\n", "    df_sig = df[df.after_sig]\n", "    dat.extend(\n", "        (df_path.diff_pos / df_path.prefix_pos) * (path_range[1] - path_range[0])\n", "        + path_range[0]\n", "    )\n", "    dat.extend(\n", "        (\n", "            (df_pre.diff_pos - df_pre.prefix_pos)\n", "            / (df_pre.retrieval_pos - df_pre.prefix_pos)\n", "        )\n", "        * (pre_range[1] - pre_range[0])\n", "        + pre_range[0]\n", "    )\n", "    dat.extend(\n", "        (\n", "            (df_ret.diff_pos - df_ret.retrieval_pos)\n", "            / (df_ret.signature_pos - df_ret.retrieval_pos)\n", "        )\n", "        * (ret_range[1] - ret_range[0])\n", "        + ret_range[0]\n", "    )\n", "    dat.extend(\n", "        (\n", "            (df_sig.diff_pos - df_sig.signature_pos)\n", "            / (df_sig.prompt_length - df_sig.signature_pos)\n", "        )\n", "        * (sig_range[1] - sig_range[0])\n", "        + sig_range[0]\n", "    )\n", "    ax.hist(dat, weights=np.ones(len(dat)) / len(dat), bins=100, label=name)\n", "    med = np.median(dat)\n", "    ax.vlines(med, 0, 0.10, color=\"red\")\n", "    ticks = [pre_range[0], ret_range[0], sig_range[0]]\n", "    texts = [\"prefix\", \"retrieval\", \"signature\"]\n", "    ax.vlines(\n", "        ticks,\n", "        0,\n", "        0.10,\n", "        linestyle=\"dashed\",\n", "        color=\"black\",\n", "    )\n", "    ax.set_ylabel(name)\n", "    ax.set_xticks(ticks + [med], labels=texts + [\"median\"])\n", "    ax.set_yticks([0, 0.02, 0.04, 0.06, 0.08, 0.10])\n", "    ax.margins(x=0)\n", "    ax.grid(linestyle=\"dotted\")\n", "\n", "if data:\n", "    axs[0].set_title(\n", "        \"Occurrence of first difference (Components scaled for visibility)\"\n", "    )\n", "    axs[-1].set_xlabel(\"Not-to-scale position within prompt\")\n", "    plt.show()"]}], "metadata": {"kernelspec": {"display_name": "base", "language": "python", "name": "python3"}, "language_info": {"codemirror_mode": {"name": "ipython", "version": 3}, "file_extension": ".py", "mimetype": "text/x-python", "name": "python", "nbconvert_exporter": "python", "pygments_lexer": "ipython3", "version": "3.11.7"}}, "nbformat": 4, "nbformat_minor": 2}