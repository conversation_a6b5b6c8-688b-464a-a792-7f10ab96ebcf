package main

import (
	"fmt"
	"github.com/tliron/glsp"
	protocol "github.com/tliron/glsp/protocol_3_16"
	"github.com/tliron/glsp/server"
)

const serverName = "Augment Language Server"

var version string = "0.0.1"
var handler protocol.Handler

func main() {
	handler = protocol.Handler{
		Initialize:                         Initialize,
		TextDocumentCompletion:             TextDocumentCompletion,
		WorkspaceDidChangeWorkspaceFolders: WorkspaceDidChangeWorkspaceFolders,
		WorkspaceDidChangeWatchedFiles:     WorkspaceDidChangeWatchedFiles,
		WorkspaceDidCreateFiles:            WorkspaceDidCreateFiles,
		WorkspaceDidRenameFiles:            WorkspaceDidRenameFiles,
		WorkspaceDidDeleteFiles:            WorkspaceDidDeleteFiles,
		TextDocumentDidOpen:                TextDocumentDidOpen,
		Shutdown:                           Shutdown,
	}

	server := server.NewServer(&handler, serverName, true)

	server.RunStdio()
}

func Initialize(_ *glsp.Context, params *protocol.InitializeParams) (any, error) {
	fmt.Printf("Initializing at %s...\n", params.WorkspaceFolders)
	capabilities := handler.CreateServerCapabilities()

	capabilities.CompletionProvider = &protocol.CompletionOptions{}

	return protocol.InitializeResult{
		Capabilities: capabilities,
		ServerInfo: &protocol.InitializeResultServerInfo{
			Name:    serverName,
			Version: &version,
		},
	}, nil
}

func Shutdown(context *glsp.Context) error {
	println("Shutting down...")
	return nil
}

func WorkspaceDidChangeWorkspaceFolders(context *glsp.Context, params *protocol.DidChangeWorkspaceFoldersParams) error {
	println("Roots has changed...")
	// need to start/stop watching changed roots
	return nil
}

// one of the most important pieces because the IDE will take responsibility of file watching for changes
// https://microsoft.github.io/language-server-protocol/specifications/lsp/3.17/specification/#workspace_didChangeWatchedFiles
func WorkspaceDidChangeWatchedFiles(context *glsp.Context, params *protocol.DidChangeWatchedFilesParams) error {
	println("Files got changed...")
	// need to sync the changed files
	return nil
}

func WorkspaceDidCreateFiles(context *glsp.Context, params *protocol.CreateFilesParams) error {
	println("Created files...")
	// can sync/upload them here
	return nil
}

func WorkspaceDidRenameFiles(context *glsp.Context, params *protocol.RenameFilesParams) error {
	println("Renamed files...")
	// can sync/upload them here
	return nil
}

func WorkspaceDidDeleteFiles(context *glsp.Context, params *protocol.DeleteFilesParams) error {
	println("Deleted files...")
	// can sync them here
	return nil
}

func TextDocumentDidOpen(context *glsp.Context, params *protocol.DidOpenTextDocumentParams) error {
	println("Opened file...")
	// updated recency information
	return nil
}

func TextDocumentCompletion(context *glsp.Context, params *protocol.CompletionParams) (interface{}, error) {
	fmt.Printf("Completing at %d:%d...\n", params.Position.Line, params.Position.Character)
	completionText := fmt.Sprintf(
		"Hello from Augment!\nCompleting multiple lines at %d:%d",
		params.Position.Line,
		params.Position.Character,
	)
	return &protocol.CompletionList{
		IsIncomplete: true, // typing will recompute us
		Items: []protocol.CompletionItem{
			{
				Label: completionText,
			},
		},
	}, nil
}
