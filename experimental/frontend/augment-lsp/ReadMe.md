# Augment Language Server PoC

A proof of concept of a [language server](https://microsoft.github.io/language-server-protocol/) for Augment.
Just proving that we can share code and complete in multiple IDEs.

First, build the CLI:

```bash
go build -o bin/augment-lsp cmd/augment/main.go
```

Follow instructions below to configured different IDEs.

## neovim

```bash
brwew install neovim
```

Install [LSP support](https://github.com/neovim/nvim-lspconfig):

```bash
git clone https://github.com/neovim/nvim-lspconfig ~/.config/nvim/pack/nvim/start/nvim-lspconfig
```

Create `lsp.lua` in your repository:

```lua
vim.lsp.start({
	name = "augment-lsp",
	cmd = { "/path/to/augment-lsp" }, -- todo: change this
    filetypes = { 'md', 'txt' },
    root_dir = vim.fn.getcwd(),
    single_file_support = true,
})
```

or check out [lsp.lua](lsp.lua) in this project.

Now run neovim and open a file in your repository.

```bash
nvim test.txt
```

Type the following command:

```
:source lsp.lua
```

To manually trigger omni completion, press `Ctrl-X` followed by `Ctrl-O` while in Insert mode (`:i`).

## VS Code

Will require a [little wiring](https://code.visualstudio.com/api/language-extensions/language-server-extension-guide).

## JetBrains

Will require [very simple plugin implementation](https://plugins.jetbrains.com/docs/intellij/language-server-protocol.html#plugin-configuration).
LSP support is only a part of paid JetBrains products so no Android Studio support.
