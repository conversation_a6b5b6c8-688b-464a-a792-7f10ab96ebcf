name: pre-commit
# Run the pre-commit checks defined in .pre-commit-config.yaml
on:
  pull_request:
  push:
    branches: ["main"]

jobs:
  pre-commit:
    name: Pre-commit checks
    runs-on:
      - self-hosted
      - gcp-us1
    continue-on-error: true # currently required
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha }}

      - name: Get changed files
        id: pr-changes
        uses: ./.github/actions/changed-files

      - name: List all changed files
        run: |
          if [ -f "${{ steps.pr-changes.outputs.all_changed_files_path }}" ]; then
            echo "Changed files:"
            cat "${{ steps.pr-changes.outputs.all_changed_files_path }}"
          else
            echo "No changed files found"
          fi

      - name: "Install pylint and augment dependencies"
        run: |
          pip install pylint pylint-pytest
          chgrp -R augment /home/<USER>
          chmod -R g+rw /home/<USER>
          ./research/research-init.sh --cpu --exclude=tensordict --no-pre-commit

      - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a
        with:
          node-version: ">= 20.15.1"

      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda
        with:
          version: 9
          run_install: true

      - uses: actions/setup-java@7a6d8a8234af8eb26422e24e3006232cccaa061b
        with:
          distribution: "zulu"
          java-version: "21"

      - uses: cloudposse/github-action-pre-commit@828247764461bc41b2bd267e24d76e91a279b093 # v4.0.0
        with:
          extra_args: --files $(cat "${{ steps.pr-changes.outputs.all_changed_files_path }}")

      #TODO: remove this temporary check once lint and format are supported in Bazel
      - name: Run Customer UI Static Checks
        if: contains(steps.pr-changes.outputs.all_changed_files, 'services/customer/frontend')
        working-directory: services/customer/frontend
        run: |
          pnpm install
          pnpm test:lint
          pnpm test:format
