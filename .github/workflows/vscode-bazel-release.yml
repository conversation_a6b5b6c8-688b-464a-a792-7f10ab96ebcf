name: Release VSCode Extension (Bazel)

on:
  workflow_dispatch:
    inputs:
      releaseChannel:
        description: "Release Channel"
        required: true
        default: "prerelease"
        type: choice
        options:
          - prerelease
          - stable
      stable_release_ref:
        description: "(Optional) The pre-release version to promote to stable. Defaults to the second to last prerelease version."
        required: false
      version_override:
        description: "(Optional) Force a version, i.e. 0.2.1"
        required: false
  schedule:
    # Pre-Release Schedule - Please update Set release channel if you change this value.
    # Scheduled at 16:55 UTC - 9:55am on Monday, Tuesday, Wednesday, Thursday and Friday
    # NOTE: The time is not on the hour to try and avoid high load times.
    # See note here: https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#schedule
    - cron: "55 16 * * 1,2,3,4,5"
    # Stable Schedule - Please update Set release channel if you change this value.
    # Scheduled at 17:25 UTC - 10:35am on Monday
    # NOTE: The time is not on the hour to try and avoid high load times.
    # See note here: https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#schedule
    - cron: "25 17 * * 1"

jobs:
  build_release:
    name: build_release
    runs-on:
     - self-hosted
     - gcp-us1
    steps:
      # Set release channel
      - name: Set release channel to prerelease
        if: github.event_name == 'schedule' && github.event.schedule == '55 16 * * 1,2,3,4,5' || inputs.releaseChannel == 'prerelease'
        run: |
          echo "RELEASE_CHANNEL=prerelease" >> $GITHUB_ENV
      - name: Set release channel to stable
        if: github.event_name == 'schedule' && github.event.schedule == '25 17 * * 1' || inputs.releaseChannel == 'stable'
        run: |
          echo "RELEASE_CHANNEL=stable" >> $GITHUB_ENV

      # Checkout the main branch for build scripts
      - name: checkout default ref
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0
      - name: Version number
        id: version
        uses: ./.github/actions/vscode-version
        with:
          release_channel: ${{ env.RELEASE_CHANNEL }}
          stable_release_ref: ${{ github.event.inputs.stable_release_ref }}
          version_override: ${{ github.event.inputs.version_override }}

      - name: Print new version
        run: echo "VSCode Version ${{ steps.version.outputs.version }}"

      - name: Build vsix
        id: build
        uses: ./.github/actions/vscode-build
        with:
          release_channel: ${{ env.RELEASE_CHANNEL }}
          version: "${{ steps.version.outputs.version }}"
          release_commitish: ${{ steps.version.outputs.stable_release_ref }}
          use_bazel: true
          release_notes: ${{ steps.generate_release_notes.outputs.release_notes }}

      - name: Print vsix file
        run: |
          echo "Vsix file ${{ steps.build.outputs.extension_filename }}"

      - name: Generate release notes
        id: generate_release_notes
        uses: ./.github/actions/internal-release-notes
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          labels: "vscode,clients"
          release_commit: ${{ env.COMMIT_SHA }}
          release_type: ${{ env.RELEASE_CHANNEL }}
          tag_prefix: "vscode"

      - name: Print release notes
        # cat is used since the variable can contain any characters
        run: |
          cat <<EOF
          ${{ steps.generate_release_notes.outputs.release_notes }}
          EOF

      # Publish the build to GitHub release
      - name: Create GitHub release
        uses: ncipollo/release-action@2c591bcc8ecdcd2db72b97d6147f871fcd833ba5 # v1.14.0
        id: create_release
        if: github.event_name == 'workflow_dispatch' || github.event_name == 'schedule'
        with:
          artifacts: to_release/clients/vscode/${{ steps.build.outputs.extension_filename }}
          artifactContentType: application/zip
          prerelease: ${{ env.RELEASE_CHANNEL == 'prerelease' }}
          makeLatest: ${{ env.RELEASE_CHANNEL == 'stable' }}
          name: "vscode@${{ env.EXTENSION_VERSION }}"
          tag: "vscode@${{ env.TAG_NAME }}"
          body: ${{ steps.generate_release_notes.outputs.release_notes }}

          # By default this action uses the context of the action to determine
          # the commit for this release (i.e. the latest commit in main).
          # Passing in the ref is useful for stable releases where the git
          # checkout will be from the tag/ref used to initiate the action.
          commit: ${{ env.COMMIT_SHA }}

      - name: Print GitHub release
        run: |
          echo "GitHub URL ${{ steps.create_release.outputs.release_url }}"

      - name: Publish extension to VSCode Marketplace
        if: github.event_name == 'workflow_dispatch' || github.event_name == 'schedule'
        id: publish
        env:
          VSCODE_PAT: ${{ secrets.VSCODE_PAT }}
        working-directory: to_release/clients/vscode
        run: |
          if [ "${RELEASE_CHANNEL}" = "stable" ]; then
            ./node_modules/.bin/vsce publish \
              --no-dependencies \
              --pat="${VSCODE_PAT}" \
              --packagePath "${{ steps.build.outputs.extension_filename }}"
          else
            ./node_modules/.bin/vsce publish \
              --no-dependencies \
              --pat="${VSCODE_PAT}" \
              --packagePath "${{ steps.build.outputs.extension_filename }}" \
              --pre-release
          fi

      - name: Post release to slack
        id: slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        if: steps.publish.outcome == 'success'
        with:
          # For posting a rich message using Block Kit
          payload: |
            {
              "text": ":tada: New VSCode Release: v${{ env.EXTENSION_VERSION }} (${{ env.RELEASE_CHANNEL }})",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":tada: New VSCode Release: v${{ env.EXTENSION_VERSION }} (${{ env.RELEASE_CHANNEL }})",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "${{ steps.create_release.outputs.html_url }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Post error to slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        if: ${{ failure() && (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') }}
        with:
          # For posting a rich message using Block Kit
          payload: |
            {
              "text": ":rotating_light: VSCode Release Failed (${{ env.RELEASE_CHANNEL }})",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rotating_light: VSCode Release Failed (${{ env.RELEASE_CHANNEL }})",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "See run for details: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
