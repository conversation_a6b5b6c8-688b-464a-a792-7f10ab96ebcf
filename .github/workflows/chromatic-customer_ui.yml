name: Chromatic - Customer UI
on:
  push:
    paths:
      - services/customer/frontend/README.md
      - services/customer/frontend/app/**
      - services/customer/frontend/.storybook/**
      - services/customer/frontend/vite.config.ts
      - .github/workflows/chromatic-customer_ui.yml
    branches:
      - main
  pull_request:
    paths:
      - services/customer/frontend/README.md
      - services/customer/frontend/app/**
      - services/customer/frontend/.storybook/**
      - services/customer/frontend/vite.config.ts
      - .github/workflows/chromatic-customer_ui.yml

concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  chromatic:
    runs-on:
      - self-hosted
      - gcp-us1
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0
      - uses: ./.github/actions/changed-files
        id: changes
        with:
          files: |
            services/customer/frontend/README.md
            services/customer/frontend/app/**
            services/customer/frontend/.storybook/**
            services/customer/frontend/vite.config.ts
            .github/workflows/chromatic-customer_ui.yml
      - name: List all changed files
        if: steps.changes.outputs.all_changed_files_count != 0
        run: |
          echo "Changed files:"
          cat "${{ steps.changes.outputs.all_changed_files_path }}"
      - name: Noop
        if: steps.changes.outputs.all_changed_files_count == 0
        run: echo "No changes to storybook, skipping upload"
      - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a
        if: steps.changes.outputs.all_changed_files_count != 0
        with:
          node-version: ">= 20.15.1"
      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda
        if: steps.changes.outputs.all_changed_files_count != 0
        with:
          version: 9
      - run: pnpm install
        if: steps.changes.outputs.all_changed_files_count != 0
      - name: Run Chromatic
        id: chroma
        if: steps.changes.outputs.all_changed_files_count != 0
        uses: chromaui/action@latest
        with:
          projectToken: ${{ secrets.CHROMATIC_PROJECT_CUSTOMER_UI_TOKEN }}
          forceRebuild: true
          workingDir: services/customer/frontend
          storybookBaseDir: services/customer/frontend
          exitOnceUploaded: true
        env:
          CI_OBJECT_STORE_CONFIG: ${{ secrets.CI_OBJECT_STORE_CONFIG }}
      - name: update pr
        uses: actions/github-script@60a0d83039c74a4aee543508d2ffcb1c3799cdea # v7
        with:
          script: |
            // Use PR head SHA for deployments
            const deploymentUrl = "${{steps.chroma.outputs.storybookUrl}}";
            //  `🎨 Storybook deployed to: ${{ steps.chroma.outputs.storybookUrl }} with changes ${{ steps.chroma.outputs.changeCount }} and errors ${{ steps.chroma.outputs.errorCount }}`
            const deploymentRef = context.payload.pull_request?.head?.sha || context.sha
            // Create deployment
            const deployment = await github.rest.repos.createDeployment({
              owner: context.repo.owner,
              repo: context.repo.repo,
              ref: deploymentRef,
              environment: 'storybook-preview',
              description: 'Storybook Chromatic Deployment',
              auto_merge: false,
              required_contexts: [],
              production_environment: false
            });

            // Create deployment status
            await github.rest.repos.createDeploymentStatus({
              owner: context.repo.owner,
              repo: context.repo.repo,
              deployment_id: deployment.data.id,
              state: 'success',
              environment: 'storybook-preview ',
              environment_url: deploymentUrl,
              log_url: "${{steps.chroma.outputs.buildUrl}}",
              description: 'Storybook Chromatic Deployment',
            });
