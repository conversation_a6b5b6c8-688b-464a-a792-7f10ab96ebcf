name: Release IntelliJ Plugin

on:
  workflow_dispatch:
    inputs:
      release_channel:
        description: "Release Channel"
        required: true
        default: "beta"
        type: choice
        options:
          - beta
          - stable
      stable_release_ref:
        description: "(Required for stable) The commit SHA or Git reference to promote to stable."
        required: false
      version_override:
        description: "Force a version, i.e. 0.2.1"
        required: false
      change_notes:
        description: "(Optional) Custom change notes for this release in markdown format. If not provided, will use the CHANGELOG.md file."
        required: false

  schedule:
    # Beta Schedule - Please update Set release channel if you change this value.
    # Scheduled at 16:55 UTC - 9:55am on Monday to Thursday (No Friday releases)
    # NOTE: The time is not on the hour to try and avoid high load times.
    # See note here: https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#schedule
    - cron: "55 16 * * 1,2,3,4"
    # Stable Schedule - Please update Set release channel if you change this value.
    # Scheduled at 16:25 UTC - 9:35am on Monday
    # NOTE: The time is not on the hour to try and avoid high load times.
    # See note here: https://docs.github.com/en/actions/using-workflows/events-that-trigger-workflows#schedule
    # - cron: "25 16 * * 1"

jobs:
  build_release:
    name: build_release
    runs-on:
     - self-hosted
     - gcp-us1
    steps:
      # Checkout the main branch for build scripts
      - name: checkout default ref
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0

      # Set release channel
      - name: Set release channel to beta
        if: github.event_name == 'schedule' && github.event.schedule == '55 16 * * 1,2,3,4' || inputs.release_channel == 'beta'
        run: |
          echo "RELEASE_CHANNEL=beta" >> $GITHUB_ENV
      - name: Set release channel to stable
        if: github.event_name == 'schedule' && github.event.schedule == '25 16 * * 1' || inputs.release_channel == 'stable'
        run: |
          echo "RELEASE_CHANNEL=stable" >> $GITHUB_ENV

      # Java isn't available in the self-hosted runner, so we need to install it
      - uses: actions/setup-java@7a6d8a8234af8eb26422e24e3006232cccaa061b
        with:
          distribution: 'corretto' # See 'Supported distributions' for available options
          java-version: '21'

      - name: Install python dependencies
        run: |
          python3 -m pip install --upgrade pip --break-system-packages
          python3 -m pip install -r clients/intellij/requirements.txt

      # For stable releases, figure out the version from beta we wish to
      # promote to stable
      - name: Set stable_release_ref
        id: stable_release_ref
        working-directory: clients/intellij
        if: ${{ env.RELEASE_CHANNEL == 'stable' }}
        env:
          RELEASE_CHANNEL: ${{ env.RELEASE_CHANNEL }}
          STABLE_RELEASE_REF: ${{ inputs.stable_release_ref }}
        run: |
          new_version=$(./update_versions.py stable_release_ref)
          echo stable_release_ref="$new_version" >> $GITHUB_OUTPUT

      - name: Set version
        id: version
        working-directory: clients/intellij
        env:
          RELEASE_CHANNEL: ${{ env.RELEASE_CHANNEL }}
          STABLE_RELEASE_REF: ${{ steps.stable_release_ref.outputs.stable_release_ref }}
          VERSION_OVERRIDE: ${{ inputs.version_override }}
        run: |
          new_version=$(./update_versions.py new_release)
          echo version="$new_version" >> $GITHUB_OUTPUT

      - name: Print new version
        run: echo "IntelliJ Version ${{ steps.version.outputs.version }}"

      - name: Build zip
        id: build
        uses: ./.github/actions/intellij-build
        with:
          version: ${{ steps.version.outputs.version }}
          release_commitish: ${{ steps.stable_release_ref.outputs.stable_release_ref }}
          change_notes: ${{ inputs.change_notes }}

      - name: Print zip file
        run: |
          echo "Zip file ${{ steps.build.outputs.plugin_filename }}"

      - name: Generate release notes
        id: generate_release_notes
        uses: ./.github/actions/internal-release-notes
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          labels: "intellij,clients"
          release_commit: ${{ env.COMMIT_SHA }}
          release_type: ${{ env.RELEASE_CHANNEL }}
          tag_prefix: "intellij"

      - name: Print release notes
        # cat is used since the variable can contain any characters
        run: |
          cat <<EOF
          ${{ steps.generate_release_notes.outputs.release_notes }}
          EOF

      # Publish the build to GitHub release
      - name: Create GitHub release
        uses: ncipollo/release-action@2c591bcc8ecdcd2db72b97d6147f871fcd833ba5 # v1.14.0
        id: create_release
        with:
          artifacts: to_release/clients/intellij/build/distributions/${{ steps.build.outputs.plugin_filename }}
          artifactContentType: application/zip
          prerelease: ${{ env.RELEASE_CHANNEL == 'beta' }}
          makeLatest: ${{ env.RELEASE_CHANNEL == 'stable' }}
          name: "intellij@${{ steps.version.outputs.version }} (${{ env.RELEASE_CHANNEL }})"
          tag: "intellij@${{ env.TAG_NAME }}-${{ env.RELEASE_CHANNEL }}"
          body: ${{ steps.generate_release_notes.outputs.release_notes }}

          # By default this action uses the context of the action to determine
          # the commit for this release (i.e. the latest commit in main).
          # Passing in the ref is useful for stable releases where the git
          # checkout will be from the tag/ref used to initiate the action.
          commit: ${{ env.COMMIT_SHA }}

      - name: Print GitHub release
        run: |
          echo "GitHub URL ${{ steps.create_release.outputs.release_url }}"

      # Publish the plugin to JetBrains Marketplace
      - name: Publish Plugin
        id: publish
        working-directory: to_release/clients/intellij
        env:
          PUBLISH_TOKEN: ${{ secrets.INTELLIJ_PAT }}
          PUBLISH_CHANNEL: ${{ env.RELEASE_CHANNEL }}
          PUBLISH_VERSION: ${{ steps.version.outputs.version }}
          CHANGE_NOTES: ${{ inputs.change_notes }}
        run: ./gradlew publishPlugin

      # Publish the plugin to JetBrains Marketplace under release-candidate channel
      - name: Publish Plugin
        id: publishRC
        working-directory: to_release/clients/intellij
        if: ${{ env.RELEASE_CHANNEL == 'stable' }}
        env:
          PUBLISH_TOKEN: ${{ secrets.INTELLIJ_PAT }}
          PUBLISH_CHANNEL: "release-candidate"
          PUBLISH_VERSION: ${{ steps.version.outputs.version }}
          CHANGE_NOTES: ${{ inputs.change_notes }}
        run: ./gradlew publishPlugin

      - name: Post release to slack
        id: slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        if: steps.publish.outcome == 'success' && (steps.publishRC.outcome == 'success' || steps.publishRC.outcome == 'skipped')
        with:
          # For posting a rich message using Block Kit
          payload: |
            {
              "text": ":tada: New IntelliJ Release: v${{ steps.version.outputs.version }} (${{ env.RELEASE_CHANNEL }})",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":tada: New IntelliJ Release: v${{ steps.version.outputs.version }} (${{ env.RELEASE_CHANNEL }})",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "${{ steps.create_release.outputs.html_url }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Post error to slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        if: ${{ failure() && (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') }}
        with:
          # For posting a rich message using Block Kit
          payload: |
            {
              "text": ":rotating_light: IntelliJ Release Failed (${{ env.RELEASE_CHANNEL }})",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rotating_light: IntelliJ Release Failed (${{ env.RELEASE_CHANNEL }})",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "See run for details: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
