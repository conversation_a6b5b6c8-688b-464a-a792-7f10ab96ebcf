# This workflow will add/remove labels to pull requests based on the contents
# of the `.github/labeler.yml` file.
name: "Pull Request Labeler"
on:
  - pull_request_target

jobs:
  labeler:
    permissions:
      contents: read
      pull-requests: write
    runs-on: ubuntu-latest
    steps:
      - uses: actions/labeler@8558fd74291d67161a8a78ce36a881fa63b766a9  #v5
        with:
          # If a file is added / removed, the labels will be added / removed too.
          sync-labels: true
