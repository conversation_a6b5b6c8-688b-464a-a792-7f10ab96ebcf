name: experimental-approval

# DO NOT restrict this workflow to a the paths under experimental.
# If that happens, there is a scenario where a change in experimental approves
# the PR, but a subsequent change that removes the modification to
# experimental and adds other changes fails to remove the approval.
on:
  workflow_dispatch:
  pull_request:

jobs:
  automate-pullrequest-review:
    runs-on:
      - self-hosted # magic constant
      - gcp-us1 # machine with GPU for now, until we get non-gpu self-hosted runners up.
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0
      - id: files
        name: Get changed files
        run: |
          changed_files=$(git diff --name-only origin/main...HEAD)
          echo changes=$changed_files >> $GITHUB_OUTPUT
      - id: verify
        name: Verify paths
        run: |
          eligible=false
          for changed_file in ${{ steps.files.outputs.changes }}; do
            if [[ ${changed_file} =~ ^experimental/ ]]; then
              echo "${changed_file} in experimental/..."
              eligible=true
            else
              echo "${changed_file} is ineligible for auto-review"
              echo "eligible=false" >> $GITHUB_OUTPUT
              exit 0
            fi
          done
          echo "eligible=$eligible" >> $GITHUB_OUTPUT
          exit 0
      - name: Approve pull request
        if: ${{ steps.verify.outputs.eligible == 'true' }}
        uses: augmentcode/automatic-pull-request-review@b1f0962288b8026858057e2173abff58c0eb906e
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          event: APPROVE
          # body: 'Experimental changes approved'
      - name: Dismiss pull request approval
        if: ${{ steps.verify.outputs.eligible == 'false' }}
        uses: augmentcode/automatic-pull-request-review@b1f0962288b8026858057e2173abff58c0eb906e
        with:
          repo-token: ${{ secrets.GITHUB_TOKEN }}
          event: DISMISS
          body: 'Review dismissed by bot'
