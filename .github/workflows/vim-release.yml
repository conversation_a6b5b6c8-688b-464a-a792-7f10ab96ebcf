name: Release Vim Plugin

on:
  workflow_dispatch:
    inputs:
      release_channel:
        description: "Release Branch"
        required: true
        default: "prerelease"
        type: choice
        options:
          - prerelease
          - main
      promote_version:
        description: "Prerelease version to promote to main (e.g. 1.2.0). Required if releasing to main and ignored otherwise"
        required: false
        type: string
  schedule:
    # Prerelease schedule. Note that main does not release on a schedule so
    # that we have more control over what is released
    - cron: "55 16 * * 1,2,3,4,5"

concurrency:
  group: ${{ github.workflow }}
  cancel-in-progress: false

jobs:
  release:
    runs-on: ubuntu-22.04
    steps:
      # Set release channel
      - name: Set release channel to prerelease
        if: inputs.release_channel == 'prerelease' || github.event_name == 'schedule'
        run: |
          echo "RELEASE_CHANNEL=prerelease" >> $GITHUB_ENV
      - name: Set release channel to main
        if: inputs.release_channel == 'main'
        run: |
          echo "RELEASE_CHANNEL=main" >> $GITHUB_ENV

      # Checkout the augment and augment.vim repos
      - name: Checkout augment development repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          path: augment
      - name: Generate token for release app (used to get access to augment.vim plugin repo)
        uses: actions/create-github-app-token@21cfef2b496dd8ef5b904c159339626a10ad380e # v1.11.6
        id: app-token
        with:
          app-id: ${{ vars.AUGMENT_VIM_APP_ID }}
          private-key: ${{ secrets.AUGMENT_VIM_APP_CERT }}
          repositories: |
            augment.vim
      - name: Checkout augment.vim repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          repository: augmentcode/augment.vim
          path: augment.vim
          token: ${{ steps.app-token.outputs.token }}
          fetch-depth: 0
          ref: ${{ env.RELEASE_CHANNEL }}
      - name: Create augment.vim.staging directory and copy .git
        run: |
          mkdir augment.vim.staging
          rsync -a augment.vim/.git augment.vim.staging/

      # Prepare release files
      # For prerelease
      - name: Copy plugin files from augment to augment.vim.staging
        if: env.RELEASE_CHANNEL == 'prerelease'
        run: |
          rsync -a augment/clients/vim/plugin/ augment.vim.staging/
      - name: Setup Node
        if: env.RELEASE_CHANNEL == 'prerelease'
        uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a
        with:
          # TODO(AU-6054): what specific node version should we use?
          node-version: '>=20.15.1'
      - name: Build LSP server as single minified file
        if: env.RELEASE_CHANNEL == 'prerelease'
        working-directory: augment/clients/vim/sidecar
        run: |
          npm install
          npm run build:bundle
      - name: Copy LSP server file to augment.vim.staging
        if: env.RELEASE_CHANNEL == 'prerelease'
        run: |
          mkdir augment.vim.staging/dist
          rsync -a augment/clients/vim/sidecar/dist/server.bundle.js augment.vim.staging/dist/server.js
      # For main
      - name: Copy prerelease files
        if: env.RELEASE_CHANNEL == 'main'
        run: |
          git -C augment.vim checkout tags/v${{ inputs.promote_version }}
          rsync -a --exclude='.git' augment.vim/ augment.vim.staging/

      # Compute the new version and generate the version file
      - name: Install Python dependencies
        working-directory: augment/clients/vim/version
        run: |
          python3 -m pip install -r requirements.txt
      - name: Compute the new plugin version
        id: version
        working-directory: augment/clients/vim/version
        run: |
          if [ "${{ env.RELEASE_CHANNEL }}" = "main" ]; then
            new_version=$(./update_version.py promote "${{ inputs.promote_version }}")
            echo "Computed promoted version: $new_version"
            # Verify the new version is greater than the previous main
            git -C $GITHUB_WORKSPACE/augment.vim checkout main
            current_main_version=$(git -C $GITHUB_WORKSPACE/augment.vim describe --tags --abbrev=0 | sed 's/^v//')
            echo "Found current main version: $current_main_version"
            ./update_version.py check $new_version $current_main_version
            echo "version=$new_version" >> $GITHUB_OUTPUT
          else
            # Get current version from augment.vim's tags and remove the 'v' prefix
            current_version=$(git -C $GITHUB_WORKSPACE/augment.vim describe --tags --abbrev=0 | sed 's/^v//')
            echo "Found current version: $current_version"
            new_version=$(./update_version.py increment_minor "$current_version")
            echo "Computed new version: $new_version"
            echo "version=$new_version" >> $GITHUB_OUTPUT
          fi
      - name: Generate version file
        working-directory: augment/clients/vim/version
        run: |
          ./generate_version_file.sh ${{ steps.version.outputs.version }}
      - name: Copy version file to augment.vim.staging
        run: |
          rsync -a augment/clients/vim/version/version.vim augment.vim.staging/autoload/augment/version.vim

      # Validate the release
      - name: Verify augment.vim files are only of allowed types (vim, lua, js, md, txt, dotfiles)
        working-directory: augment.vim.staging
        run: |
          DISALLOWED=$(find . -type f -not -path "./.git/*" -not -name "*.vim" -not -name "*.lua" -not -name "*.js" -not -name "*.txt" -not -name "*.md" -not -name ".*")
          if [ ! -z "$DISALLOWED" ]; then
            echo "Found files with disallowed types:"
            echo "$DISALLOWED"
            exit 1
          fi
      - name: Verify all code files have a license header
        working-directory: augment.vim.staging
        run: |
          EXIT_CODE=0
          # Must contain "Copyright (c) 2025 Augment" in first or second line
          find . -type f \( -name "*.vim" -o -name "*.lua" -o -name "*.js" \) -not -path "./.git/*" | while read -r file; do
            if ! head -n 2 "$file" | grep -q "Copyright (c) 2025 Augment"; then
              echo "Error: Missing or incorrect license header in $file"
              echo "Expected 'Copyright (c) 2025 Augment' in first or second line"
              EXIT_CODE=1
            fi
          done
          exit $EXIT_CODE
      - name: Verify changes are not just a version bump
        id: check_changes
        working-directory: augment.vim.staging
        run: |
          git add .
          git branch
          git diff --staged --name-only
          if [ "$(git diff --staged --name-only)" = "autoload/augment/version.vim" ]; then
            echo "Only version file changed, skipping release"
            echo "skip_release=true" >> $GITHUB_OUTPUT
          else
            echo "skip_release=false" >> $GITHUB_OUTPUT
          fi

      - name: Perform release
        id: perform_release
        uses: ./augment/.github/actions/vim-release
        if: steps.check_changes.outputs.skip_release != 'true'
        with:
          version: ${{ steps.version.outputs.version }}
          release_channel: ${{ env.RELEASE_CHANNEL }}

      - name: Post release to slack
        id: slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        if: steps.perform_release.outcome == 'success'
        with:
          # For posting a rich message using Block Kit
          payload: |
            {
              "text": ":tada: New Vim Release: v${{ steps.version.outputs.version }} (${{ env.RELEASE_CHANNEL }})",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":tada: New Vim Release: v${{ steps.version.outputs.version }} (${{ env.RELEASE_CHANNEL }})",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "https://github.com/augmentcode/augment.vim/commits/${{ env.RELEASE_CHANNEL }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Post error to slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        if: ${{ failure() && (github.event_name == 'workflow_dispatch' || github.event_name == 'schedule') }}
        with:
          # For posting a rich message using Block Kit
          payload: |
            {
              "text": ":rotating_light: Vim Release Failed",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rotating_light: Vim Release Failed",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "See run for details: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
