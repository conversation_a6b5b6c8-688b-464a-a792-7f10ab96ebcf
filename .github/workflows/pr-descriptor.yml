name: Pull Request Descriptor

on:
  pull_request:
    types: [opened, ready_for_review, labeled]

permissions:
  contents: read
  pull-requests: write

jobs:
  check-conditions:
    name: Check Trigger Conditions
    runs-on:
      - self-hosted
    outputs:
      should_run: ${{ steps.check.outputs.should_run }}
    steps:
      - name: Checkout repository to access features.json
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683 # v4.2.2
        with:
          repository: augmentcode/augment
          token: ${{ secrets.GITHUB_TOKEN }}
          fetch-depth: 1
          sparse-checkout: .github

      - name: Check if workflow should run
        id: check
        env:
          PR_AUTHOR: ${{ github.event.pull_request.user.login }}
          PR_NUMBER: ${{ github.event.pull_request.number }}
          HEAD_REPO: ${{ github.event.pull_request.head.repo.full_name }}
          BASE_REPO: ${{ github.event.pull_request.base.repo.full_name }}
        shell: bash
        run: |
          if [ "$HEAD_REPO" != "$BASE_REPO" ]; then
            echo "⚠️  Fork PR detected: $HEAD_REPO -> $BASE_REPO"
            echo "Skipping workflow for fork PR due to permission restrictions"
            echo "should_run=false" >> $GITHUB_OUTPUT
            exit 0
          fi
          # Condition 1: augment_describe label added (regardless of other conditions)
          if [ "${{ github.event.action }}" = "labeled" ] && [ "${{ github.event.label.name }}" = "augment_describe" ]; then
            echo "✓ Condition met: augment_describe label added"
            echo "should_run=true" >> $GITHUB_OUTPUT
            exit 0
          fi
          # Check if PR is a draft
          if [ "${{ github.event.pull_request.draft }}" = "true" ]; then
            echo "⚠️  Skipping draft PR"
            echo "should_run=false" >> $GITHUB_OUTPUT
            exit 0
          fi
          # Check if author is in the always_describe_pr whitelist
          if [ -f ".github/actions/augment/features.json" ]; then
            # Check if the author is in the always_describe_pr array
            if jq -e --arg author "$PR_AUTHOR" '.always_describe_pr | index($author)' .github/actions/augment/features.json > /dev/null 2>&1; then
              echo "✓ Author '$PR_AUTHOR' is in always_describe_pr whitelist"
            else
              echo "⚠️  Author is not whitelisted for automatic description"
              echo "should_run=false" >> $GITHUB_OUTPUT
              exit 0
            fi
          else
            echo "⚠️  features.json not found"
            echo "should_run=false" >> $GITHUB_OUTPUT
            exit 0
          fi
          if [ "${{ github.event.action }}" = "opened" ] || [ "${{ github.event.action }}" = "ready_for_review" ]; then
            echo "✓ Condition met: PR opened as non-draft OR draft converted to ready"
            echo "should_run=true" >> $GITHUB_OUTPUT
            exit 0
          else
            echo "⚠️  Skipping PR action: ${{ github.event.action }}"
            echo "should_run=false" >> $GITHUB_OUTPUT
            exit 0
          fi

  pr-descriptor:
    name: Pull Request Descriptor
    runs-on:
      - self-hosted
    needs: check-conditions
    if: needs.check-conditions.outputs.should_run == 'true'
    steps:
      - name: Generate PR Description
        id: augment-agent
        uses: augmentcode/describe-pr@main # Using main to dogfood latest actions
        with:
          augment_session_auth: ${{ secrets.AUGMENT_SESSION_AUTH }}
          github_token: ${{ secrets.GITHUB_TOKEN }}
          pull_number: ${{ github.event.pull_request.number }}
          repo_name: ${{ github.repository }}

      - name: Update labels
        if: steps.augment-agent.outcome == 'success'
        uses: ./.github/actions/augment/label-manager
        with:
          github_token: ${{ secrets.GITHUB_TOKEN }}
          pr_number: ${{ github.event.pull_request.number }}
          workflow_name: "pr_describe"