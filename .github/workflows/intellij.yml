name: IntelliJ Build & Test
# Run the pre-commit checks defined in .pre-commit-config.yaml
on:
  pull_request:
  push:
    branches: ["main"]
    paths:
      - clients/intellij/**
      - clients/sidecar/node-process/protos/**

# Cancel in progress workflows on pull_requests.
# https://docs.github.com/en/actions/using-jobs/using-concurrency#example-using-a-fallback-value
concurrency:
  group: ${{ github.workflow }}-${{ github.head_ref || github.run_id }}
  cancel-in-progress: true

jobs:
  intellij-build-and-test:
    name: IntelliJ Build & Test
    runs-on:
     - self-hosted
     - gcp-us1
    timeout-minutes: 30
    steps:
      - uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0
          ref: ${{ github.event.pull_request.head.sha }}

      # GitHub will run this action on a Rebase + Force Push for PRs that
      # do not include IntelliJ changes, so we use this to reduce run time.
      - uses: dorny/paths-filter@de90cc6fb38fc0963ad72b210f1f284cd68cea36 # v3.0.2
        id: changes
        with:
          filters: |
            intellij:
              - 'clients/intellij/**'
              - 'clients/sidecar/node-process/protos/**'
              - '.github/workflows/intellij.yml'

      # Build Sidecar
      - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a
        if: steps.changes.outputs.intellij == 'true'
        with:
          node-version: ">= 20.15.1"

      - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda
        if: steps.changes.outputs.intellij == 'true'
        with:
          version: 9

      - name: Install dependencies
        if: steps.changes.outputs.intellij == 'true'
        shell: bash
        run: pnpm install

      - name: Build Sidecar
        if: steps.changes.outputs.intellij == 'true'
        working-directory: clients/sidecar/node-process
        run: pnpm run build:intellij

      - uses: actions/setup-java@7a6d8a8234af8eb26422e24e3006232cccaa061b
        if: steps.changes.outputs.intellij == 'true'
        with:
          distribution: 'corretto' # See 'Supported distributions' for available options
          java-version: '21'

      # Validate wrapper
      - name: Gradle Wrapper Validation
        if: steps.changes.outputs.intellij == 'true'
        uses: gradle/wrapper-validation-action@f9c9c575b8b21b6485636a91ffecd10e558c62f6 #v3.5.0

      # Build plugin
      - name: Build plugin
        if: steps.changes.outputs.intellij == 'true'
        working-directory: clients/intellij
        run: ./gradlew buildPlugin

      # Run tests
      - name: Run Tests
        if: steps.changes.outputs.intellij == 'true'
        working-directory: clients/intellij
        run: ./gradlew check --stacktrace

      # Run verification
      - name: Verify Plugin
        if: steps.changes.outputs.intellij == 'true'
        working-directory: clients/intellij
        run: ./gradlew verifyPlugin
