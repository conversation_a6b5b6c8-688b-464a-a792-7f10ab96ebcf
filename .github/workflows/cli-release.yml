name: Release CLI

on:
  workflow_dispatch:
    inputs:
      version_confirmation:
        description: "Confirm the version to release. This value will be compared against the version in the CLI binary and the workflow will fail if they do not match."
        required: true
        type: string
      package_name:
        description: "NPM package name to publish to. This shouldn't be changed from the default unless you want to test the workflow with a non-public package."
        required: false
        type: choice
        default: "@augmentcode/auggie"
        options:
          - "@augmentcode/auggie"
          - "@mpauly11/cli"

jobs:
  release:
    runs-on:
      - self-hosted
      - gcp-us1
    steps:
      # Setup
      - name: Install Node
        uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a
        with:
          node-version: '22.x'
          # We need to provide the registry URL so that the action will
          # configure npm to look at $NODE_AUTH_TOKEN for auth
          registry-url: 'https://registry.npmjs.org'

      # Build the CLI
      - name: Checkout augment repo
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          path: augment
      - name: Build the CLI via bazel
        working-directory: augment
        run: |
          bazel build //clients/beachhead:cli --remote_local_fallback
          mkdir -p clients/beachhead/out
          cp $(bazel info bazel-bin)/clients/beachhead/out/augment.mjs clients/beachhead/out/augment.mjs
      # We'll bring over only the minimum set of files needed to publish to
      # ensure we don't inadvertently publish anything we shouldn't (unminified
      # source code).
      - name: Create package directory and copy CLI binary
        run: |
          mkdir -p package
          cp augment/clients/beachhead/out/augment.mjs package/augment.mjs

      # Create the package.json
      - name: Get the current version from the CLI binary
        working-directory: package
        id: get_version
        run: |
          # Extract only the SemVer prefix (e.g., "0.0.0" from "0.0.0 (commit abc)")
          version=$(node ./augment.mjs --version | sed -E 's/^([0-9]+\.[0-9]+\.[0-9]+).*/\1/')
          echo "version=$version" >> "$GITHUB_OUTPUT"
      - name: Create package.json
        working-directory: package
        run: |
          # NOTE(mpauly): This is using @mpauly11/cli by default so that we can test
          # publishing before we use the real package. The package name can be overridden
          # via the workflow input for ad-hoc test releases to different contexts.
          cat > package.json <<JSON
          {
            "name": "${{ inputs.package_name }}",
            "version": "${{ steps.get_version.outputs.version }}",
            "description": "Auggie CLI Client by Augment Code",
            "homepage": "https://augmentcode.com",
            "bugs": {
              "url": "https://support.augmentcode.com/"
            },
            "license": "SEE LICENSE IN LICENSE.md",
            "main": "augment.mjs",
            "bin": {
              "auggie": "augment.mjs"
            },
            "files": [
              "augment.mjs"
            ],
            "type": "module",
            "engines": {
              "node": ">=22.0.0"
            }
          }
          JSON

      # Include the license
      - name: Copy license
        run: cp augment/clients/beachhead/LICENSE_cli.md package/LICENSE.md

      # Run sanity checks to ensure we're releasing only what we intend to
      - name: Validate matching versions
        working-directory: package
        run: |
          # Get the version from the CLI binary
          cli_version="${{ steps.get_version.outputs.version }}"

          # Get the version from package.json
          package_version=$(node -pe "require('./package.json').version")

          # Get the version from the workflow input
          input_version="${{ inputs.version_confirmation }}"

          echo "CLI binary version: $cli_version"
          echo "package.json version: $package_version"
          echo "Input confirmation version: $input_version"

          # Check if all versions match
          if [ "$cli_version" != "$input_version" ]; then
            echo "Error: CLI version ($cli_version) does not match input confirmation ($input_version)"
            exit 1
          fi

          if [ "$cli_version" != "$package_version" ]; then
            echo "Error: CLI version ($cli_version) does not match package.json version ($package_version)"
            exit 1
          fi

          echo "✓ All versions match: $cli_version"
      - name: Validate no extra files are present
        working-directory: package
        run: |
          # Check for any files (including in subdirectories)
          file_count=$(find . -type f | wc -l)
          if [ "$file_count" -ne 3 ]; then
            echo "Error: Expected exactly 3 files in package directory, found $file_count"
            echo "Files present:"
            find . -type f
            exit 1
          fi
          echo "✓ Only expected files present"
      - name: Validate no sourcemaps in CLI binary
        working-directory: package
        run: |
          # Check if the bundle contains inline sourcemaps (sourcemap data
          # embedded as base64 in the file)
          if grep -q "sourceMappingURL=data:" augment.mjs; then
            echo "Error: CLI binary contains sourcemap references"
            exit 1
          fi

          # Double check by validating the size of the bundle
          # NOTE(mpauly): As of writing the bundle is ~3MB with no sourcemaps
          # and ~20MB with sourcemaps. If we add a large dependency that bloats
          # the bundle even without sourcemaps this check may need to be
          # adjusted.
          file_size=$(stat -c%s augment.mjs)
          max_size=$((10 * 1024 * 1024))  # 10MB threshold
          if [ "$file_size" -gt "$max_size" ]; then
            echo "Error: CLI binary is too large: $file_size bytes ($(($file_size / 1024 / 1024))MB)"
            echo "Expected size should be around 3MB without sourcemaps"
            exit 1
          fi

          echo "✓ CLI binary is under the size threshold and contains no sourcemaps"

      # Publish to the npm registry
      - name: Publish the package to npm (if @augmentcode/auggie)
        id: publish_prod
        if: inputs.package_name == '@augmentcode/auggie'
        working-directory: package
        env:
          NODE_AUTH_TOKEN: ${{ secrets.AUGMENTCODE_NPM_TOKEN }}
        run: npm publish --access public
      - name: Publish the test package to npm (if @mpauly11/cli)
        if: inputs.package_name == '@mpauly11/cli'
        working-directory: package
        env:
          NODE_AUTH_TOKEN: ${{ secrets.MPAULY_DEV_NPM_TOKEN }}
        run: npm publish --access restricted

      # Follow ups
      - name: Create release in the Augment repo associating it with the CLI version
        # Only create a release if we successfully published to the prod package
        if: steps.publish_prod.outcome == 'success' && inputs.package_name == '@augmentcode/auggie'
        uses: ncipollo/release-action@2c591bcc8ecdcd2db72b97d6147f871fcd833ba5 # v1.14.0
        with:
          name: "cli@${{ inputs.version_confirmation }}"
          tag: "cli@${{ inputs.version_confirmation }}"

      - name: Post release to slack
        id: slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        # Only report success when publishing the prod package
        if: steps.publish_prod.outcome == 'success' && inputs.package_name == '@augmentcode/auggie'
        with:
          # For posting a rich message using Block Kit
          payload: |
            {
              "text": ":tada: New CLI Release: v${{ inputs.version_confirmation }} to package ${{ inputs.package_name }}",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":tada: New CLI Release: v${{ inputs.version_confirmation }} to package ${{ inputs.package_name }}",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "https://www.npmjs.com/package/{{ inputs.package_name }}?activeTab=versions"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
      - name: Post error to slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        # Only report failure when publishing the prod package
        if: ${{ failure() && inputs.package_name == '@augmentcode/auggie' }}
        with:
          # For posting a rich message using Block Kit
          payload: |
            {
              "text": ":rotating_light: CLI Release Failed",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rotating_light: CLI Release Failed",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "See run for details: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
