name: research-image-tag

on:
  push:
    branches: ['main']
    paths:    ['research/environments/*_tag.txt']

permissions:
  contents: read

jobs:
  research-image-tagger:
    name: Research Image Tagger
    if: github.repository_owner == 'augmentcode'
    runs-on: [self-hosted, gcp-us1]
    timeout-minutes: 15
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0

      - name: Tag Research Images
        run: |
          research/environments/containers/tag-images.sh
