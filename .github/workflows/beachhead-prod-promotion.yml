name: Promote Beachhead to Production

on:
  workflow_dispatch:
    branches: ['main']
    inputs:
      tag:
        description: 'Source tag to promote to production (default: STAGING)'
        required: false
        type: string
        default: 'STAGING'
      message:
        description: 'Reason for this promotion'
        required: true
        type: string

permissions:
  contents: read

concurrency:
  group: beachhead-prod-promotion
  cancel-in-progress: false

jobs:
  promote-beachhead-prod:
    name: Promote Beachhead to Production
    if: github.repository_owner == 'augmentcode'
    runs-on: [self-hosted, gcp-us1]
    timeout-minutes: 30
    steps:
      - name: Checkout
        uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
        with:
          fetch-depth: 0

      - name: Determine source tag
        id: determine-source-tag
        run: |
          if [ -n "${{ github.event.inputs.tag }}" ]; then
            echo "source_tag=${{ github.event.inputs.tag }}" >> $GITHUB_OUTPUT
          else
            echo "source_tag=STAGING" >> $GITHUB_OUTPUT
          fi

      - name: Promote Beachhead Image to Production
        run: |
          echo "Promoting beachhead image from ${{ steps.determine-source-tag.outputs.source_tag }} to production..."
          /usr/bin/gcloud auth configure-docker us-central1-docker.pkg.dev < /dev/null
          bazel run //clients/beachhead/img:deploy-prod -- copy --source-tag="${{ steps.determine-source-tag.outputs.source_tag }}" --tag=PROD

      - name: Print Image Details
        run: |
          echo "Promotion complete. Image promoted to:"
          bazel run //clients/beachhead/img:deploy-prod -- outie-name --tag=PROD

      - name: Post success to slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        if: success()
        with:
          payload: |
            {
              "text": ":rocket: Beachhead promoted to production",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rocket: Beachhead promoted to production",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Beachhead image successfully promoted to production environment.\n\nReason: `${{ github.event.inputs.message }}`\nSource Tag: `${{ steps.determine-source-tag.outputs.source_tag }}`\nDestination Tag: `PROD`\nTriggered by: ${{ github.actor }}\nRun: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK

      - name: Post failure to slack
        uses: slackapi/slack-github-action@6c661ce58804a1a20f6dc5fbee7f0381b469e001 # v1.25.0
        if: failure()
        with:
          payload: |
            {
              "text": ":rotating_light: Beachhead production promotion failed",
              "blocks": [
                {
                  "type": "header",
                  "text": {
                    "type": "plain_text",
                    "text": ":rotating_light: Beachhead production promotion failed",
                    "emoji": true
                  }
                },
                {
                  "type": "section",
                  "text": {
                    "type": "mrkdwn",
                    "text": "Failed to promote beachhead to production.\n\nReason: `${{ github.event.inputs.message }}`\nSource Tag: `${{ steps.determine-source-tag.outputs.source_tag }}`\nSee run for details: ${{ github.server_url }}/${{ github.repository }}/actions/runs/${{ github.run_id }}"
                  }
                }
              ]
            }
        env:
          SLACK_WEBHOOK_URL: ${{ secrets.SLACK_WEBHOOK_URL }}
          SLACK_WEBHOOK_TYPE: INCOMING_WEBHOOK
