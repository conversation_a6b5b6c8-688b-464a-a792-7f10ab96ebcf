name: "Build VSCode Extension"

inputs:
  use_bazel:
    description: "Use Bazel to build the extension"
    required: false
    default: false
    # GH Actions do not support booleans, they are strings, so lets explicitly
    # restrict the values to true/false
    type: choice
    options:
      - true
      - false
  release_notes:
    description: "Release notes for the extension"
    required: true
  release_channel:
    description: "Release channel"
    required: true
    type: choice
    options:
      - prerelease
      - stable
  version:
    description: "Version of the release"
    required: true
  release_commitish:
    description: "Release commit or branch name"
    required: false

outputs:
  extension_filename:
    description: "The filename of the extension vsix file"
    value: ${{ steps.extension_name.outputs.extension_filename }}

runs:
  using: "composite"
  steps:
    # Checkout the main branch or commit for release
    - name: checkout ref for release
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        fetch-depth: 0
        path: "to_release"
        ref: ${{ inputs.release_commitish }}
    - name: Install dependencies
      working-directory: to_release
      shell: bash
      run: pnpm install

    # Setup environment variables
    - name: Set commit SHA
      working-directory: to_release
      shell: bash
      run: echo COMMIT_SHA=$(git rev-parse HEAD) >> $GITHUB_ENV
    - name: Set version
      working-directory: to_release
      shell: bash
      run: echo EXTENSION_VERSION="${{ inputs.version }}" >> $GITHUB_ENV
    - name: Set tag name
      shell: bash
      run: echo TAG_NAME="${EXTENSION_VERSION}" >> $GITHUB_ENV
    - name: Set extension name
      shell: bash
      id: extension_name
      run: |
        echo extension_filename="vscode-augment-$EXTENSION_VERSION.vsix" >> $GITHUB_OUTPUT

    # Build the extension
    - name: Build extension (NPM Script)
      if: ${{ inputs.use_bazel == 'false' }}
      shell: bash
      working-directory: to_release/clients/vscode
      run: npm run package-extension

    - name: Build extension (Bazel)
      if: ${{ inputs.use_bazel == 'true' }}
      shell: bash
      working-directory: to_release
      env:
        EXTENSION_FILENAME: ${{ steps.extension_name.outputs.extension_filename }}
        STABLE_VSCODE_RELEASE_VERSION: ${{ env.EXTENSION_VERSION }}
        RELEASE_CHANNEL: ${{ inputs.release_channel }}
      run: |
          if [ "${RELEASE_CHANNEL}" = "stable" ]; then
            bazel build //clients/vscode:package-extension-stable --config=local_output --remote_local_fallback --compilation_mode=opt --stamp
            cp bazel-bin/clients/vscode/vscode-augment-stable.vsix clients/vscode/${EXTENSION_FILENAME}
          else
            bazel build //clients/vscode:package-extension-prerelease --config=local_output --remote_local_fallback --compilation_mode=opt --stamp
            cp bazel-bin/clients/vscode/vscode-augment-prerelease.vsix clients/vscode/${EXTENSION_FILENAME}
          fi
