name: "Build IntelliJ Plugin"

inputs:
  version:
    description: "Version of the release"
    required: false
  release_commitish:
    description: "Release commit or branch name"
    required: false
  change_notes:
    description: "Custom change notes for this release in markdown format"
    required: false

outputs:
  plugin_filename:
    description: "The filename of the plugin zip file"
    value: ${{ steps.plugin_filename.outputs.plugin_filename }}

runs:
  using: "composite"
  steps:
    # Checkout the main branch or commit for release
    - name: checkout ref for release
      uses: actions/checkout@11bd71901bbe5b1630ceea73d27597364c9af683
      with:
        fetch-depth: 0
        path: "to_release"
        ref: ${{ inputs.release_commitish }}

    # Build common webviews
    - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a
      with:
        node-version: ">= 20.15.1"

    - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda
      with:
        version: 9

    - name: Install pnpm dependencies
      working-directory: to_release
      shell: bash
      run: pnpm install

    - name: Build webviews
      working-directory: to_release/clients/common/webviews
      shell: bash
      run: pnpm run build:intellij

    - name: Build Sidecar
      working-directory: to_release/clients/sidecar/node-process
      shell: bash
      run: pnpm run build:intellij

    # Setup environment variables
    - name: Set commit SHA
      working-directory: to_release
      shell: bash
      run: echo COMMIT_SHA=$(git rev-parse HEAD) >> $GITHUB_ENV

    - name: Set version
      shell: bash
      run: echo PUBLISH_VERSION="${{ inputs.version }}" >> $GITHUB_ENV

    - name: Set tag name
      shell: bash
      run: echo TAG_NAME="${PUBLISH_VERSION}" >> $GITHUB_ENV

    - name: Set plugin name
      shell: bash
      id: plugin_filename
      run: |
        echo plugin_filename="intellij-augment-$PUBLISH_VERSION.zip" >> $GITHUB_OUTPUT

    # Build the plugin
    - name: Test and Build plugin
      shell: bash
      working-directory: to_release/clients/intellij
      env:
        PUBLISH_VERSION: ${{ inputs.version }}
        CHANGE_NOTES: ${{ inputs.change_notes }}
      run: ./gradlew test buildPlugin
