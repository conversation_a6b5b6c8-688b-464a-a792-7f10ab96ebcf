#!/usr/bin/env python3
"""
Simple GitHub Analytics for Augment Workflows

This script queries GitHub's API to analyze the performance and feedback
for our three automated workflows:
- PR Descriptor (augment_assisted_description)
- PR Review (augment_assisted_review)
- Find Reviewers (augment_assisted_reviewers)

Usage:
    python cli_workflow_automation/scripts/github_workflow_analytics.py --token YOUR_GITHUB_TOKEN

Environment variables:
    GITHUB_TOKEN: GitHub personal access token
"""

import argparse
import os
from datetime import datetime, timedelta
from collections import defaultdict
import sys
from github import Github
from functools import lru_cache


class GithubAPIWrapper:
    """Wrapper around PyGithub with caching for better performance"""

    def __init__(self, token, repo_name):
        self.github = Github(token)
        self.repo = self.github.get_repo(repo_name)

    @lru_cache(maxsize=1000)
    def get_pr_details(self, pr_number):
        """Get PR details with caching"""
        try:
            pr = self.repo.get_pull(pr_number)
            return {
                "number": pr.number,
                "title": pr.title,
                "body": pr.body or "",
                "labels": [{"name": label.name} for label in pr.labels],
                "created_at": pr.created_at.isoformat(),
                "user": {"login": pr.user.login},
            }
        except Exception as e:
            print(f"Error fetching PR {pr_number}: {e}")
            return None

    @lru_cache(maxsize=1000)
    def get_pr_reactions(self, pr_number):
        """Get reactions for PR description with caching"""
        try:
            issue = self.repo.get_issue(pr_number)  # Reactions are on the issue
            reactions = []
            for reaction in issue.get_reactions():
                reactions.append(
                    {
                        "content": reaction.content,
                        "user": {"login": reaction.user.login},
                    }
                )
            return reactions
        except Exception as e:
            print(f"Error fetching PR reactions for {pr_number}: {e}")
            return []

    @lru_cache(maxsize=1000)
    def get_pr_comments(self, pr_number):
        """Get PR comments with reactions data included"""
        try:
            issue = self.repo.get_issue(pr_number)  # Comments are on the issue
            comments = []
            for comment in issue.get_comments():
                # Get reactions for this comment
                reactions = []
                for reaction in comment.get_reactions():
                    reactions.append(
                        {
                            "content": reaction.content,
                            "user": {"login": reaction.user.login},
                        }
                    )

                comments.append(
                    {
                        "id": comment.id,
                        "body": comment.body,
                        "user": {"login": comment.user.login},
                        "created_at": comment.created_at.isoformat(),
                        "reactions": reactions,
                    }
                )
            return comments
        except Exception as e:
            print(f"Error fetching PR comments for {pr_number}: {e}")
            return []

    @lru_cache(maxsize=1000)
    def get_pr_reviews(self, pr_number):
        """Get PR reviews with reactions data included"""
        try:
            pr = self.repo.get_pull(pr_number)
            reviews = []
            for review in pr.get_reviews():
                # Note: PR reviews don't support reactions directly
                reviews.append(
                    {
                        "id": review.id,
                        "body": review.body or "",
                        "user": {"login": review.user.login},
                        "created_at": review.submitted_at.isoformat()
                        if review.submitted_at
                        else "",
                        "reactions": [],  # PR reviews don't have reactions
                    }
                )
            return reviews
        except Exception as e:
            print(f"Error fetching PR reviews for {pr_number}: {e}")
            return []

    @lru_cache(maxsize=1000)
    def get_pr_review_comments(self, pr_number):
        """Get PR review comments (inline comments) with reactions data included"""
        try:
            pr = self.repo.get_pull(pr_number)
            review_comments = []
            for comment in pr.get_review_comments():
                # Get reactions for this review comment
                reactions = []
                for reaction in comment.get_reactions():
                    reactions.append(
                        {
                            "content": reaction.content,
                            "user": {"login": reaction.user.login},
                        }
                    )

                review_comments.append(
                    {
                        "id": comment.id,
                        "body": comment.body,
                        "user": {"login": comment.user.login},
                        "created_at": comment.created_at.isoformat(),
                        "pull_request_review_id": comment.pull_request_review_id,
                        "reactions": reactions,
                    }
                )
            return review_comments
        except Exception as e:
            print(f"Error fetching PR review comments for {pr_number}: {e}")
            return []

    def get_all_prs_since(self, since_date):
        """Get all PRs created since a specific date"""
        try:
            all_prs = []

            print("Fetching PRs...")
            # Use PyGithub's search functionality
            query = f"repo:{self.repo.full_name} is:pr created:>={since_date}"
            issues = self.github.search_issues(query)

            for issue in issues:
                if issue.pull_request:  # Ensure it's actually a PR
                    all_prs.append(
                        {
                            "number": issue.number,
                            "title": issue.title,
                            "body": issue.body or "",
                            "labels": [{"name": label.name} for label in issue.labels],
                            "created_at": issue.created_at.isoformat(),
                            "user": {"login": issue.user.login},
                        }
                    )

            print(f"Found {len(all_prs)} PRs since {since_date}")
            return all_prs
        except Exception as e:
            print(f"Error fetching PRs: {e}")
            return []

    def clear_cache(self):
        """Clear all caches"""
        self.get_pr_details.cache_clear()
        self.get_pr_reactions.cache_clear()
        self.get_pr_comments.cache_clear()
        self.get_pr_reviews.cache_clear()
        self.get_pr_review_comments.cache_clear()


class Workflow:
    """Represents a GitHub workflow with its tracking label, signature, and analytics logic"""

    def __init__(self, name, label, signature):
        self.name = name
        self.run_tracking_label = label
        self.signature = signature

    def has_run_on_pr(self, pr):
        """Check if this workflow has run on the given PR"""
        pr_labels = [label["name"] for label in pr.get("labels", [])]
        return self.run_tracking_label in pr_labels

    def get_reactions_from_pr(self, pr_number, github_api):
        """Get reactions for this workflow's output on a specific PR"""
        reactions = {"thumbs_up": 0, "thumbs_down": 0}

        # Check PR description for this workflow's signature
        if self._check_pr_description(pr_number, github_api):
            description_reactions = self._get_description_reactions(
                pr_number, github_api
            )
            reactions["thumbs_up"] += description_reactions["thumbs_up"]
            reactions["thumbs_down"] += description_reactions["thumbs_down"]

        # Check comments for this workflow's signature
        comment_reactions = self._get_comment_reactions(pr_number, github_api)
        reactions["thumbs_up"] += comment_reactions["thumbs_up"]
        reactions["thumbs_down"] += comment_reactions["thumbs_down"]

        # Check PR reviews for this workflow's signature
        review_reactions = self._get_review_reactions(pr_number, github_api)
        reactions["thumbs_up"] += review_reactions["thumbs_up"]
        reactions["thumbs_down"] += review_reactions["thumbs_down"]

        # Check PR review comments for this workflow's signature
        # Note: GitHub API limitation - PR reviews themselves don't support reactions,
        # only inline review comments do. See _get_review_comment_reactions for details.
        review_comment_reactions = self._get_review_comment_reactions(
            pr_number, github_api
        )
        reactions["thumbs_up"] += review_comment_reactions["thumbs_up"]
        reactions["thumbs_down"] += review_comment_reactions["thumbs_down"]

        return reactions

    def _check_pr_description(self, pr_number, github_api):
        """Check if PR description contains this workflow's signature"""
        pr_data = github_api.get_pr_details(pr_number)
        if pr_data:
            return self.signature in pr_data.get("body", "")
        return False

    def _get_description_reactions(self, pr_number, github_api):
        """Get reactions for PR description"""
        reactions = {"thumbs_up": 0, "thumbs_down": 0}
        reaction_data = github_api.get_pr_reactions(pr_number)
        if reaction_data:
            for reaction in reaction_data:
                if reaction["content"] == "+1":
                    reactions["thumbs_up"] += 1
                elif reaction["content"] == "-1":
                    reactions["thumbs_down"] += 1
        return reactions

    def _get_comment_reactions(self, pr_number, github_api):
        """Get reactions for comments with this workflow's signature"""
        reactions = {"thumbs_up": 0, "thumbs_down": 0}
        comments = github_api.get_pr_comments(pr_number)
        if comments:
            for comment in comments:
                if self.signature in comment["body"]:
                    # Use the reactions data that's already included in the comment
                    for reaction in comment.get("reactions", []):
                        if reaction["content"] == "+1":
                            reactions["thumbs_up"] += 1
                        elif reaction["content"] == "-1":
                            reactions["thumbs_down"] += 1
        return reactions

    def _get_review_reactions(self, pr_number, github_api):
        """Get reactions for PR reviews with this workflow's signature"""
        reactions = {"thumbs_up": 0, "thumbs_down": 0}
        reviews = github_api.get_pr_reviews(pr_number)
        if reviews:
            for review in reviews:
                if self.signature in review["body"]:
                    # Use the reactions data that's already included in the review
                    for reaction in review.get("reactions", []):
                        if reaction["content"] == "+1":
                            reactions["thumbs_up"] += 1
                        elif reaction["content"] == "-1":
                            reactions["thumbs_down"] += 1
        return reactions

    def _get_review_comment_reactions(self, pr_number, github_api):
        """Get reactions for PR review comments (inline comments) from the workflow bot

        Note: GitHub API does not support reactions on PR reviews (top-level review comments).
        Reactions are only supported on PR review comments (inline comments).

        For the Review workflow:
        - The main PR review contains the workflow signature but cannot have reactions
        - Individual inline review comments can have reactions but don't contain the signature
        - We check if the main review has our signature, then count reactions on all
          inline review comments from github-actions[bot]
        """
        reactions = {"thumbs_up": 0, "thumbs_down": 0}

        # For review workflow, we need to check if the main review contains our signature
        # and if so, count reactions on review comments that are part of that specific review
        if self.name == "Review":
            # Find the review with our signature
            reviews = github_api.get_pr_reviews(pr_number)
            target_review = None
            for review in reviews:
                if self.signature in review["body"]:
                    target_review = review
                    break

            if target_review:
                # Count reactions only on review comments that are part of this specific review
                # and from the same author (github-actions[bot])
                review_comments = github_api.get_pr_review_comments(pr_number)
                for comment in review_comments:
                    # Check if this comment is part of the target review and from the same author
                    if (
                        comment.get("pull_request_review_id") == target_review["id"]
                        and comment["user"]["login"] == target_review["user"]["login"]
                    ):
                        # Use the reactions data that's already included in the review comment
                        for reaction in comment.get("reactions", []):
                            if reaction["content"] == "+1":
                                reactions["thumbs_up"] += 1
                            elif reaction["content"] == "-1":
                                reactions["thumbs_down"] += 1
        else:
            # For other workflows, check for signature in review comments
            review_comments = github_api.get_pr_review_comments(pr_number)
            if review_comments:
                for comment in review_comments:
                    if self.signature in comment["body"]:
                        # Use the reactions data that's already included in the review comment
                        for reaction in comment.get("reactions", []):
                            if reaction["content"] == "+1":
                                reactions["thumbs_up"] += 1
                            elif reaction["content"] == "-1":
                                reactions["thumbs_down"] += 1
        return reactions


class GitHubWorkflowAnalytics:
    def __init__(self, token, repo="augmentcode/augment"):
        self.github_api = GithubAPIWrapper(token, repo)

        # Define our workflows
        self.workflows = [
            Workflow(
                name="Description",
                run_tracking_label="augment_assisted_description",
                signature="🤖 This description was generated automatically.",
            ),
            Workflow(
                name="Review",
                run_tracking_label="augment_assisted_review",
                signature="🤖 Automated review complete.",
            ),
            Workflow(
                name="Reviewers",
                run_tracking_label="augment_assisted_reviewers",
                signature="🤖 Reviewers suggested automatically.",
            ),
        ]

        # All workflow labels for easy access
        self.all_labels = [workflow.run_tracking_label for workflow in self.workflows]

    def get_all_prs(self, since_days=30):
        """Get all PRs created in the last N days"""
        since_date = (datetime.now() - timedelta(days=since_days)).strftime("%Y-%m-%d")
        return self.github_api.get_all_prs_since(since_date)

    def filter_prs_by_labels(self, all_prs, labels):
        """Filter PRs that have any of the specified labels"""
        filtered_prs = []
        for pr in all_prs:
            pr_labels = [label["name"] for label in pr.get("labels", [])]
            if any(label in pr_labels for label in labels):
                filtered_prs.append(pr)
        return filtered_prs

    def analyze_workflow_performance(self, since_days=30):
        """Analyze performance of all three workflows"""
        # Get all PRs once and filter by labels
        all_prs = self.get_all_prs(since_days)
        prs = self.filter_prs_by_labels(all_prs, self.all_labels)
        return self.analyze_workflow_performance_with_data(prs, since_days)

    def analyze_workflow_performance_with_data(self, prs, since_days=30):
        """Analyze performance of all three workflows using pre-fetched PR data"""
        print(f"Analyzing workflow performance for the last {since_days} days...")
        print("=" * 60)

        # Group by workflow type
        workflow_stats = defaultdict(
            lambda: {
                "total_runs": 0,
                "total_thumbs_up": 0,
                "total_thumbs_down": 0,
                "prs_with_feedback": 0,
                "satisfaction_rate": 0,
            }
        )

        for pr in prs:
            pr_number = pr["number"]

            # Check each workflow to see if it ran on this PR
            for workflow in self.workflows:
                if workflow.has_run_on_pr(pr):
                    stats = workflow_stats[workflow.name]
                    stats["total_runs"] += 1

                    # Get reactions for this workflow on this PR
                    reactions = workflow.get_reactions_from_pr(
                        pr_number, self.github_api
                    )
                    total_feedback = reactions["thumbs_up"] + reactions["thumbs_down"]

                    if total_feedback > 0:
                        stats["prs_with_feedback"] += 1
                        stats["total_thumbs_up"] += reactions["thumbs_up"]
                        stats["total_thumbs_down"] += reactions["thumbs_down"]

        # Calculate satisfaction rates and display results
        for workflow_name, stats in workflow_stats.items():
            total_feedback = stats["total_thumbs_up"] + stats["total_thumbs_down"]
            if total_feedback > 0:
                stats["satisfaction_rate"] = (
                    stats["total_thumbs_up"] / total_feedback * 100
                )

            print(f"\n{workflow_name}:")
            print(f"  Total runs: {stats['total_runs']}")
            print(f"  PRs with feedback: {stats['prs_with_feedback']}")
            print(
                f"  Feedback rate: {stats['prs_with_feedback']/stats['total_runs']*100:.1f}%"
                if stats["total_runs"] > 0
                else "  Feedback rate: 0%"
            )
            print(f"  👍 reactions: {stats['total_thumbs_up']}")
            print(f"  👎 reactions: {stats['total_thumbs_down']}")
            print(
                f"  Satisfaction rate: {stats['satisfaction_rate']:.1f}%"
                if total_feedback > 0
                else "  Satisfaction rate: No feedback yet"
            )

        return workflow_stats

    def get_adoption_metrics(self, all_prs=None, since_days=30):
        """Get adoption metrics - how often workflows run vs total PRs"""
        # Reuse PRs if already fetched, otherwise fetch them
        if all_prs is None:
            all_prs = self.get_all_prs(since_days)

        total_prs = len(all_prs)

        print(f"\nAdoption Metrics (last {since_days} days):")
        print("=" * 40)
        print(f"Total PRs: {total_prs}")

        for workflow in self.workflows:
            prs_with_label = self.filter_prs_by_labels(all_prs, [workflow.run_tracking_label])
            count = len(prs_with_label)
            percentage = count / total_prs * 100 if total_prs > 0 else 0
            print(f"{workflow.name}: {count} PRs ({percentage:.1f}%)")


def main():
    parser = argparse.ArgumentParser(description="Analyze GitHub workflow performance")
    parser.add_argument("--token", help="GitHub token (or set GITHUB_TOKEN env var)")
    parser.add_argument(
        "--repo", default="augmentcode/augment", help="Repository (owner/name)"
    )
    parser.add_argument(
        "--days", type=int, default=30, help="Days to analyze (default: 30)"
    )

    args = parser.parse_args()

    token = args.token or os.getenv("GITHUB_TOKEN")
    if not token:
        print(
            "Error: GitHub token required. Use --token or set GITHUB_TOKEN environment variable."
        )
        sys.exit(1)

    analytics = GitHubWorkflowAnalytics(token, args.repo)

    try:
        # Fetch all PRs once and reuse for both analyses
        all_prs = analytics.get_all_prs(args.days)

        # Analyze workflow performance using the fetched PRs
        workflow_prs = analytics.filter_prs_by_labels(all_prs, analytics.all_labels)
        analytics.analyze_workflow_performance_with_data(workflow_prs, args.days)

        # Get adoption metrics using the same PRs
        analytics.get_adoption_metrics(all_prs, args.days)

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
