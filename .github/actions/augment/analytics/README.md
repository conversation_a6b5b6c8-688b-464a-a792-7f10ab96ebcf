# GitHub Workflow Analytics

This document describes our simple analytics system for tracking the performance and user satisfaction of our three automated GitHub workflows.

## Overview

We use GitHub's native features to track workflow usage and collect user feedback:

1. **Labels for tracking**: Each workflow adds a label when it runs
2. **Reactions for feedback**: Users provide thumbs up/down on bot outputs  
3. **GitHub API queries**: We analyze the data using GitHub's API

## Tracking Labels

Each workflow automatically adds a label to PRs when it runs:

- `augment_assisted_description` - PR description generation workflow
- `augment_assisted_review` - Automated code review workflow
- `augment_assisted_reviewers` - Reviewer suggestion workflow

These labels allow us to:
- Count how many times each workflow runs
- Calculate adoption rates (workflow runs vs total PRs)
- Track which PRs received automated assistance

## Feedback Collection

Each workflow now includes a feedback request in its output:

### PR Descriptor
Adds to the end of generated descriptions:
```
🤖 This description was generated automatically. Please react with 👍 if it's helpful or 👎 if it needs improvement.
```

### PR Review
Adds to review summary when posting comments:
```
🤖 Automated review complete. Please react with 👍 or 👎 on the individual review comments above to provide feedback on their usefulness.
```

### Find Reviewers
Adds to reviewer suggestion comment:
```
🤖 Reviewers suggested automatically. Please react with 👍 if these suggestions are appropriate or 👎 if they need improvement.
```

## GitHub API Limitations for Review Feedback

**Important**: GitHub's API has limitations that affect how we collect feedback on code reviews:

### PR Reviews vs Review Comments
- **PR Reviews** (top-level review summaries): Cannot have reactions via GitHub API
- **PR Review Comments** (inline code comments): Can have reactions via GitHub API

### How This Affects Our Analytics

For the **Review workflow** specifically:
1. The main review summary contains our workflow signature (`🤖 Automated review complete...`) but **cannot collect reactions**
2. Individual inline review comments can collect reactions but don't contain the signature
3. Our analytics script handles this by:
   - Finding the main review with our signature
   - Counting reactions on all inline review comments from the same review
   - Only counting comments that are part of the specific review with the signature

### What This Means for Users
- **For PR descriptions and reviewer suggestions**: React directly on the comment/description
- **For code reviews**: React on the individual inline review comments (not the main review summary)
- The analytics will correctly attribute inline comment reactions to the review workflow

### Technical Implementation
The analytics script uses `pull_request_review_id` to ensure we only count reactions on inline comments that belong to the specific review containing our workflow signature, making the feedback attribution accurate despite the API limitations.

## Analytics Script

Use `.github/actions/augment/analytics/analyze.py` to analyze the data:

### Basic Usage
```bash
# Set your GitHub token
export GITHUB_TOKEN="your_token_here"

# Run analytics for last 30 days
python .github/actions/augment/analytics/analyze.py

# Analyze specific timeframe
python .github/actions/augment/analytics/analyze.py --days 7

# Use different repo
python .github/actions/augment/analytics/analyze.py --repo "owner/repo"
```

### Sample Output
```
Analyzing workflow performance for the last 30 days...
============================================================

PR Descriptor:
  Total runs: 45
  PRs with feedback: 12
  Feedback rate: 26.7%
  👍 reactions: 18
  👎 reactions: 3
  Satisfaction rate: 85.7%

PR Review:
  Total runs: 23
  PRs with feedback: 8
  Feedback rate: 34.8%
  👍 reactions: 11
  👎 reactions: 2
  Satisfaction rate: 84.6%

Find Reviewers:
  Total runs: 31
  PRs with feedback: 5
  Feedback rate: 16.1%
  👍 reactions: 7
  👎 reactions: 1
  Satisfaction rate: 87.5%

Adoption Metrics (last 30 days):
========================================
Total PRs: 67
Description: 45 PRs (67.2%)
Review: 23 PRs (34.3%)
Reviewers: 31 PRs (46.3%)
```

## Key Metrics

### Performance Metrics
- **Total runs**: How many times each workflow executed
- **Adoption rate**: Percentage of PRs that used each workflow
- **Feedback rate**: Percentage of workflow runs that received user feedback

### Quality Metrics  
- **Satisfaction rate**: Percentage of feedback that was positive (👍 vs 👎)
- **Thumbs up/down counts**: Raw reaction counts for detailed analysis

## Answering Key Questions

This system can answer the original analytical questions:

### "How often do people find the PR descriptions helpful?"
- Look at PR Descriptor satisfaction rate
- Compare thumbs up vs thumbs down on descriptions
- Track feedback rate to see engagement levels

### "Are the code review suggestions actually useful?"
- Check PR Review satisfaction rate
- Monitor feedback trends over time
- Identify patterns in negative feedback

### "Do people use the suggested reviewers?"
- Analyze Find Reviewers satisfaction rate
- Cross-reference with actual reviewer assignments (manual analysis)
- Track adoption rate for reviewer suggestions

### "Which workflow provides the most value?"
- Compare satisfaction rates across workflows
- Look at adoption rates and feedback engagement
- Identify workflows with highest positive feedback

## Manual Analysis

For deeper insights, you can manually analyze:

1. **GitHub Issues/PRs page**: Filter by labels to see workflow usage
2. **Individual PR reactions**: Check specific feedback patterns
3. **Reviewer assignment patterns**: See if suggested reviewers are actually assigned
4. **Workflow logs**: Check GitHub Actions for execution details

## Future Enhancements

This simple system can be extended with:

- **Automated reporting**: Schedule the analytics script to run weekly
- **Trend analysis**: Track metrics over time to identify improvements/regressions  
- **Integration analysis**: Cross-reference with actual reviewer assignments
- **Detailed feedback**: Parse comment text for more nuanced feedback
- **Dashboard**: Create a simple web dashboard for visualizing metrics

## Benefits of This Approach

✅ **Low implementation complexity**: Uses existing GitHub features  
✅ **No new infrastructure**: Leverages GitHub API and labels  
✅ **User-friendly**: Familiar thumbs up/down interface  
✅ **Immediate insights**: Can start collecting data right away  
✅ **Flexible analysis**: GitHub API allows custom queries  
✅ **Cost-effective**: No additional services or storage needed
