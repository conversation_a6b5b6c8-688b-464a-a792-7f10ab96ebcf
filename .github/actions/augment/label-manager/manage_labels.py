#!/usr/bin/env python3
"""
Label Manager Script for Augment Workflows

This script manages GitHub labels for Augment workflows by reading configuration
from a JSON file and making appropriate API calls to add target labels and
remove trigger labels.
"""

import argparse
import json
import os
import sys
import urllib.parse
import urllib.request
from pathlib import Path
from typing import Dict, Optional, Tu<PERSON>


def load_config(config_path: Path) -> Dict:
    """Load the workflow configuration from JSON file."""
    try:
        with open(config_path, 'r') as f:
            return json.load(f)
    except FileNotFoundError:
        print(f"Error: Config file not found at {config_path}")
        sys.exit(1)
    except json.JSONDecodeError as e:
        print(f"Error: Invalid JSON in config file: {e}")
        sys.exit(1)


def get_workflow_labels(config: Dict, workflow_name: str) -> Tuple[Optional[str], Optional[str]]:
    """Extract target and trigger labels for the specified workflow."""
    if workflow_name not in config:
        print(f"Error: No configuration found for workflow '{workflow_name}'")
        print(f"Available workflows: {', '.join(config.keys())}")
        sys.exit(1)
    
    workflow_config = config[workflow_name]
    run_tracking_label = workflow_config.get('run_tracking_label')
    trigger_label = workflow_config.get('trigger_label')
    
    print(f"Configuration loaded for '{workflow_name}':")
    print(f"  Target label: {run_tracking_label}")
    print(f"  Trigger label: {trigger_label}")
    
    return run_tracking_label, trigger_label


def make_github_request(url: str, method: str, token: str, data: Optional[str] = None) -> bool:
    """Make a GitHub API request and return success status."""
    headers = {
        'Accept': 'application/vnd.github+json',
        'Authorization': f'Bearer {token}',
        'X-GitHub-Api-Version': '2022-11-28',
        'User-Agent': 'Augment-Label-Manager/1.0'
    }
    
    try:
        req = urllib.request.Request(url, headers=headers, method=method)
        if data:
            req.data = data.encode('utf-8')
            headers['Content-Type'] = 'application/json'
        
        with urllib.request.urlopen(req) as response:
            if 200 <= response.status < 300:
                return True
            else:
                print(f"Warning: GitHub API returned status {response.status}")
                return False
                
    except urllib.error.HTTPError as e:
        print(f"Warning: HTTP error {e.code}: {e.reason}")
        return False
    except urllib.error.URLError as e:
        print(f"Warning: URL error: {e.reason}")
        return False
    except Exception as e:
        print(f"Warning: Unexpected error: {e}")
        return False


def add_label(repository: str, pr_number: str, label: str, token: str) -> bool:
    """Add a label to the pull request."""
    print(f"Adding target label: {label}")
    
    url = f"https://api.github.com/repos/{repository}/issues/{pr_number}/labels"
    data = json.dumps({"labels": [label]})
    
    success = make_github_request(url, 'POST', token, data)
    if success:
        print("Successfully added target label")
    else:
        print("Warning: Failed to add target label")
    
    return success


def remove_label(repository: str, pr_number: str, label: str, token: str) -> bool:
    """Remove a label from the pull request."""
    print(f"Removing trigger label: {label}")
    
    # URL encode the label to prevent injection
    encoded_label = urllib.parse.quote(label, safe='')
    url = f"https://api.github.com/repos/{repository}/issues/{pr_number}/labels/{encoded_label}"
    
    success = make_github_request(url, 'DELETE', token)
    if success:
        print("Successfully removed trigger label")
    else:
        print("Warning: Failed to remove trigger label")
    
    return success


def main():
    """Main function to manage labels based on workflow configuration."""
    parser = argparse.ArgumentParser(description='Manage GitHub labels for Augment workflows')
    parser.add_argument('--workflow-name', required=True, help='The workflow name')
    parser.add_argument('--repository', required=True, help='The GitHub repository (owner/repo)')
    parser.add_argument('--pr-number', required=True, help='The pull request number')
    parser.add_argument('--github-token', required=True, help='The GitHub token')
    parser.add_argument('--config-path', required=True, help='Path to the config.json file')
    
    args = parser.parse_args()
    
    # Load configuration
    config_path = Path(args.config_path)
    config = load_config(config_path)
    
    # Get labels for the workflow
    run_tracking_label, trigger_label = get_workflow_labels(config, args.workflow_name)
    
    # Track operation results
    operations_performed = 0
    operations_successful = 0
    
    # Add target label if configured
    if run_tracking_label:
        operations_performed += 1
        if add_label(args.repository, args.pr_number, run_tracking_label, args.github_token):
            operations_successful += 1
    else:
        print("No target label configured for this workflow")
    
    # Remove trigger label if configured
    if trigger_label:
        operations_performed += 1
        if remove_label(args.repository, args.pr_number, trigger_label, args.github_token):
            operations_successful += 1
    else:
        print("No trigger label configured for this workflow")
    
    # Summary
    print("\nLabel management completed:")
    print(f"  Operations performed: {operations_performed}")
    print(f"  Operations successful: {operations_successful}")
    
    if operations_performed == 0:
        print("  No label operations were configured for this workflow")
    elif operations_successful == operations_performed:
        print("  All operations completed successfully")
    else:
        print("  Some operations failed (see warnings above)")
    
    # Exit successfully regardless of API failures to avoid failing the workflow
    sys.exit(0)


if __name__ == '__main__':
    main()
