name: "Label Manager"
description: "Manage GitHub labels for Augment workflows"

inputs:
  github_token:
    description: "The GitHub token to use for API requests"
    required: true
  pr_number:
    description: "The pull request number"
    required: true
  workflow_name:
    description: "The workflow name (pr_describe, pr_review, pr_find_reviewers)"
    required: true

runs:
  using: "composite"
  steps:
    - name: Manage labels
      env:
        WORKFLOW_NAME: ${{ inputs.workflow_name }}
        REPOSITORY: ${{ github.repository }}
        PR_NUMBER: ${{ inputs.pr_number }}
        GITHUB_TOKEN: ${{ inputs.github_token }}
        CONFIG_PATH: ${{ github.action_path }}/config.json
      run: |
        python3 "${{ github.action_path }}/manage_labels.py" \
          --workflow-name "$WORKFLOW_NAME" \
          --repository "$REPOSITORY" \
          --pr-number "$PR_NUMBER" \
          --github-token "$GITHUB_TOKEN" \
          --config-path "$CONFIG_PATH"
      shell: bash
