# Label Manager Action

This composite action manages GitHub labels for Augment workflows using a configuration-driven Python script. It reads workflow-specific label mappings from a config file and handles adding target labels and removing trigger labels through the GitHub API.

## Inputs

| Input | Description | Required | Default |
|-------|-------------|----------|---------|
| `github_token` | The GitHub token to use for API requests | Yes | - |
| `pr_number` | The pull request number | Yes | - |
| `workflow_name` | The workflow name (`pr_describe`, `pr_review`, `pr_find_reviewers`) | Yes | - |

## Configuration

The action uses a `config.json` file to map workflow names to their corresponding labels:

```json
{
  "workflow_name": {
    "run_tracking_label": "...",
    "trigger_label": "..."
  },
  ...
}
```

## Usage

### PR Description Workflow

```yaml
- name: Update labels
  if: steps.augment-agent.outcome == 'success'
  uses: ./.github/actions/augment/label-manager
  with:
    github_token: ${{ secrets.GITHUB_TOKEN }}
    pr_number: ${{ github.event.pull_request.number }}
    workflow_name: "pr_describe"
```

### PR Review Workflow

```yaml
- name: Update labels
  if: steps.augment-agent.outcome == 'success'
  uses: ./.github/actions/augment/label-manager
  with:
    github_token: ${{ secrets.GITHUB_TOKEN }}
    pr_number: ${{ github.event.pull_request.number }}
    workflow_name: "pr_review"
```

### PR Find Reviewers Workflow

```yaml
- name: Update labels
  if: steps.augment-agent.outcome == 'success'
  uses: ./.github/actions/augment/label-manager
  with:
    github_token: ${{ secrets.GITHUB_TOKEN }}
    pr_number: ${{ github.event.pull_request.number }}
    workflow_name: "pr_find_reviewers"
```

## Implementation

The action uses a Python script (`manage_labels.py`) that:

1. **Loads configuration**: Reads the `config.json` file and validates the workflow exists
2. **Adds target label**: Makes a POST request to add the target label if configured
3. **Removes trigger label**: Makes a DELETE request to remove the trigger label if configured

## Workflow Integration

This action is designed to be used in Augment workflows after the agent has successfully completed its task. The action runs a single Python script that handles all label management operations.

The typical workflow pattern is:

1. Run the Augment Agent
2. If the agent succeeds, use this action to update labels
3. Add a target label to indicate the workflow completed
4. Remove a trigger label to prevent re-execution

## Features

- **Configuration-driven**: All label mappings centralized in `config.json`
- **Type safety**: Validates workflow names against configuration
- **URL encoding**: Prevents injection attacks from malicious label names
- **Robust error handling**: Logs warnings for API failures but doesn't fail the workflow
- **Comprehensive logging**: Detailed output for debugging and monitoring
- **Pure Python**: No external dependencies beyond standard library

## Error Handling

The Python script includes comprehensive error handling:
- If the config file is missing or invalid JSON, the script exits with error
- If the workflow is not found in configuration, the script exits with error
- If GitHub API calls fail, warnings are logged but the script continues
- The script always exits with code 0 to avoid failing the workflow on API issues
- All operations and their results are clearly logged for debugging
