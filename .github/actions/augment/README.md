# Augment Actions

This directory contains all GitHub Actions related to Augment's AI-powered development tools.

## Actions Overview

### 1. Generate Instructions (`generate-instructions/`)

A composite action that generates dynamic instructions for the Augment Agent using Jinja2 templates. This system provides:

- Modular template composition with inheritance
- Context-aware instruction generation
- Support for workflow types: **PR review** and **PR reviewer finding**
- Automatic PR details integration (branch, changed files, review comments)
- Flexible variable injection via JSON
- Comprehensive testing with output validation
- Security features (environment variables, input sanitization)

> **Note**: PR description generation now uses the standalone [`augmentcode/describe-pr`](https://github.com/augmentcode/describe-pr) action instead of this local action.

**Usage:**
```yaml
- uses: ./.github/actions/augment/generate-instructions
  with:
    config_json: |
      {
        "request_type": "pr_review",  # or pr_find_reviewers
        "context_type": "pr_or_issue",
        "pr_number": ${{ github.event.pull_request.number }},
        "title": ${{ toJSON(github.event.pull_request.title) }},
        "author": "${{ github.event.pull_request.user.login }}",
        "workflow_type": "PR code review",
        "guidelines": true,
        "formatting": true,
        "capabilities": true,
        "limitations": true
      }
    instruction_file_path: /tmp/instruction.txt
```

### 2. Label Manager (`label-manager/`)

A composite action that manages GitHub labels for Augment workflows. This action provides separate steps for adding tracking labels and removing trigger labels, with both operations being optional. Key features:

- **Separate Steps**: Independent add and remove operations
- **Optional Labels**: Both tracking and trigger labels are optional
- **Conditional Execution**: Steps only run when labels are provided
- **Error Handling**: Robust error handling with warning messages
- **Flexible Usage**: Can add labels, remove labels, both, or neither

**Usage:**
```yaml
# Add tracking label and remove trigger label
- uses: ./.github/actions/augment/label-manager
  with:
    github_token: ${{ secrets.GITHUB_TOKEN }}
    pr_number: ${{ github.event.pull_request.number }}
    tracking_label: "augment_assisted_description"
    trigger_label: "augment_describe"

# Only add tracking label
- uses: ./.github/actions/augment/label-manager
  with:
    github_token: ${{ secrets.GITHUB_TOKEN }}
    pr_number: ${{ github.event.pull_request.number }}
    tracking_label: "augment_assisted_review"
```

## Workflows Using These Actions

The following GitHub workflows use these actions:

- **`.github/workflows/pr-descriptor.yml`** - Automatically generates PR descriptions
  - Uses: [`augmentcode/describe-pr`](https://github.com/augmentcode/describe-pr) (standalone action), `label-manager`
  - Labels: Adds `augment_assisted_description`, removes `augment_describe`
- **`.github/workflows/pr-review.yml`** - Provides AI-powered code reviews
  - Uses: `generate-instructions`, `label-manager`
  - Labels: Adds `augment_assisted_review`, removes `augment_review`
- **`.github/workflows/pr-find-reviewers.yml`** - Suggests appropriate reviewers
  - Uses: `generate-instructions`, `label-manager`
  - Labels: Adds `augment_assisted_reviewers`, removes `augment_find_reviewer`

## Development and Testing

Each action includes comprehensive testing mechanisms:

- **Generate Instructions**:
  - Comprehensive test script (`test.sh`) with output validation
  - Expected output files for regression testing
  - Tests PR review and PR reviewer finding workflows with realistic PR details
  - Pass/fail reporting with detailed diff output
