name: "Generate Internal Release Notes"

inputs:
  github_token:
    description: "GitHub token for API access"
    required: true
    type: string
  labels:
    description: "Labels to filter PRs by (comma separated)"
    required: true
    type: string
  release_commit:
    description: "Commit to release"
    required: true
    type: string
  release_type:
    description: "Stable or prerelease"
    required: true
    type: string
  tag_prefix:
    description: "Tag prefix (i.e. intellij for intellij@1.2.3)"
    required: true
    type: string
  repo:
    description: "GitHub repository in format owner/repo"
    required: false
    type: string
    default: "augmentcode/augment"

outputs:
  release_notes:
    description: "The releases notes"
    value: ${{ steps.gen_notes.outputs.gen_notes }}

runs:
  using: "composite"
  steps:
    # Build common webviews
    - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a
      with:
        node-version: ">= 20.15.1"

    - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda
      with:
        version: 9

    - name: Install pnpm dependencies
      working-directory: clients/common/releases/internal-release-notes
      shell: bash
      run: pnpm install

    - name: Build
      working-directory: clients/common/releases/internal-release-notes
      shell: bash
      run: pnpm run build

    - name: Generate release notes
      working-directory: clients/common/releases/internal-release-notes
      shell: bash
      id: gen_notes
      run: |
        gen_notes=$(node ./dist/generate-internal-release-notes.js --token ${{ inputs.github_token }} --labels "${{ inputs.labels }}" --commit "${{ inputs.release_commit }}" --type ${{ inputs.release_type }} --tagPrefix ${{ inputs.tag_prefix }} --repo ${{ inputs.repo }})
        EOF=$(dd if=/dev/urandom bs=15 count=1 status=none | base64)
        echo "gen_notes<<$EOF" >> "$GITHUB_OUTPUT"
        echo "$gen_notes" >> "$GITHUB_OUTPUT"
        echo "$EOF" >> "$GITHUB_OUTPUT"
