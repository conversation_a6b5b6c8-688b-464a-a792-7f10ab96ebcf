# Changed Files Action

A custom GitHub Action to detect changed files in a repository. This action is designed to be robust and handle various edge cases that can occur in different CI environments.

## Usage

```yaml
- uses: ./.github/actions/changed-files
  id: changes
  with:
    files: |
      path/to/file1.txt
      path/to/directory/**
    separator: ","
```

## Inputs

| Name        | Description                                              | Required | Default                  |
| ----------- | -------------------------------------------------------- | -------- | ------------------------ |
| `files`     | One or more files or patterns to filter changed files by | No       |                          |
| `separator` | Separator used to join the changed files output          | No       | " " (space)              |
| `base_sha`  | Base SHA for comparison                                  | No       | Automatically determined |
| `head_sha`  | Head SHA for comparison                                  | No       | Current commit           |

## Outputs

| Name                      | Description                                                                                         |
| ------------------------- | --------------------------------------------------------------------------------------------------- |
| `all_changed_files_path`  | Path to a file containing all changed files (including added, modified, renamed, and deleted files) |
| `all_changed_files_count` | Total count of all changed files                                                                    |
| `any_changed`             | Boolean indicating if any files changed                                                             |

## Examples

### Basic usage

```yaml
- uses: ./.github/actions/changed-files
  id: changes
```

### Filter by file patterns

```yaml
- uses: ./.github/actions/changed-files
  id: changes
  with:
    files: |
      src/**/*.ts
      src/**/*.tsx
```

### Use a custom separator

```yaml
- uses: ./.github/actions/changed-files
  id: changes
  with:
    separator: ","
```

### Use the outputs

```yaml
- name: List all changed files
  if: steps.changes.outputs.all_changed_files_count != 0
  run: |
    echo "Changed files:"
    cat "${{ steps.changes.outputs.all_changed_files_path }}"
```

### Use the file path with other tools

```yaml
- uses: some-action/that-needs-files@v1
  with:
    files: $(cat "${{ steps.changes.outputs.all_changed_files_path }}")
```

## Fallback Mechanisms

This action includes several fallback mechanisms to ensure it works reliably in different CI environments:

1. **Base SHA determination**:

   - For pull requests: Tries to fetch the base branch, falls back to the default branch
   - For pushes: Uses the previous commit, falls back to an empty tree for first commits

2. **Changed files detection**:

   - First tries `git diff` between base and head SHAs (includes added, modified, renamed, and deleted files)
   - If that fails, falls back to `git status` to get modified, added, renamed, and deleted files
   - As a last resort, uses `git ls-files` to get all files in the repository

3. **Pattern matching**:
   - Uses simple bash pattern matching for file filtering
   - Handles glob patterns like `**/*.ts`
