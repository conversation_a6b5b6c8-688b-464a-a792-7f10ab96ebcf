name: "Changed Files"
description: "Get all changed files in a GitHub repository"

inputs:
  files:
    description: "One or more files or patterns to filter changed files by"
    required: false
  separator:
    description: "Separator used to join the changed files output"
    required: false
    default: " "
  base_sha:
    description: "Base SHA for comparison (defaults to the merge base with the target branch for PRs or the previous commit for pushes)"
    required: false
  head_sha:
    description: "Head SHA for comparison (defaults to the current commit)"
    required: false

outputs:
  all_changed_files_path:
    description: "Path to file containing all changed files"
    value: ${{ steps.get-changed-files.outputs.all_changed_files_path }}
  all_changed_files_count:
    description: "Total count of all changed files"
    value: ${{ steps.get-changed-files.outputs.all_changed_files_count }}
  any_changed:
    description: "<PERSON><PERSON><PERSON> indicating if any files changed"
    value: ${{ steps.get-changed-files.outputs.any_changed }}

runs:
  using: "composite"
  steps:
    - name: Get changed files
      id: get-changed-files
      shell: bash
      run: |
        # Set up variables
        BASE_SHA="${{ inputs.base_sha }}"
        HEAD_SHA="${{ inputs.head_sha }}"
        SEPARATOR="${{ inputs.separator }}"
        FILES_FILTER="${{ inputs.files }}"

        # If no HEAD_SHA is provided, use the current commit
        if [ -z "$HEAD_SHA" ]; then
          HEAD_SHA=$(git rev-parse HEAD)
        fi

        # If no BASE_SHA is provided, determine it based on the event type
        if [ -z "$BASE_SHA" ]; then
          if [ "$GITHUB_EVENT_NAME" == "pull_request" ] || [ "$GITHUB_EVENT_NAME" == "pull_request_target" ]; then
            # For pull requests, try to use the base branch
            BASE_REF="${{ github.base_ref || 'main' }}"
            echo "Attempting to fetch base branch: $BASE_REF"
            if git fetch origin $BASE_REF 2>/dev/null; then
              BASE_SHA=$(git rev-parse FETCH_HEAD)
            else
              echo "Could not fetch base branch, using default branch"
              git fetch origin
              BASE_SHA=$(git rev-parse FETCH_HEAD)
            fi
          else
            # For pushes, use the previous commit if it exists, otherwise use an empty tree
            if git rev-parse HEAD~1 >/dev/null 2>&1; then
              BASE_SHA=$(git rev-parse HEAD~1)
            else
              # First commit in the repo - use the empty tree object
              BASE_SHA=$(git hash-object -t tree /dev/null)
              echo "This appears to be the first commit, using empty tree as base"
            fi
          fi
        fi

        echo "Base SHA: $BASE_SHA"
        echo "Head SHA: $HEAD_SHA"

        # Get all changed files
        echo "Running git diff between $BASE_SHA and $HEAD_SHA"
        CHANGED_FILES=$(git diff --name-only --diff-filter=ACMRTD $BASE_SHA...$HEAD_SHA 2>/dev/null || echo "")

        # If git diff failed or returned no files, try using git status as a fallback
        if [ -z "$CHANGED_FILES" ]; then
          echo "Git diff returned no files, trying fallback method"
          # List all tracked files that have been modified or deleted
          CHANGED_FILES=$(git status --porcelain | grep -E '^(M|A|R|C|D)' | awk '{print $2}' || echo "")

          # Add untracked files if they're being tracked by git
          UNTRACKED_FILES=$(git status --porcelain | grep -E '^\?\?' | awk '{print $2}' || echo "")
          if [ -n "$UNTRACKED_FILES" ]; then
            CHANGED_FILES="$CHANGED_FILES $UNTRACKED_FILES"
          fi

          # If still no files, try listing all files in the repo
          if [ -z "$CHANGED_FILES" ]; then
            echo "No changed files detected, using all files in the repository"
            CHANGED_FILES=$(git ls-files || echo "")
          fi
        fi

        # If files filter is provided, apply it
        if [ -n "$FILES_FILTER" ]; then
          # Create a temporary file with the filter patterns
          mkdir -p .github/actions/changed-files
          echo "$FILES_FILTER" > .github/actions/changed-files/filter_patterns.txt

          # Use simple pattern matching for each file
          FILTERED_FILES=""
          for FILE in $CHANGED_FILES; do
            while IFS= read -r PATTERN || [ -n "$PATTERN" ]; do
              if [[ -n "$PATTERN" ]]; then
                # Remove any leading/trailing whitespace
                PATTERN=$(echo "$PATTERN" | xargs)

                # Simple glob pattern matching
                if [[ "$FILE" == $PATTERN ]]; then
                  FILTERED_FILES="$FILTERED_FILES $FILE"
                  break
                fi
              fi
            done < .github/actions/changed-files/filter_patterns.txt
          done

          # Trim leading whitespace
          FILTERED_FILES=$(echo "$FILTERED_FILES" | xargs)

          # Use the filtered files
          CHANGED_FILES="$FILTERED_FILES"
        fi

        # Count the changed files
        if [ -z "$CHANGED_FILES" ]; then
          CHANGED_FILES_COUNT=0
          ANY_CHANGED="false"
        else
          CHANGED_FILES_COUNT=$(echo "$CHANGED_FILES" | wc -w | xargs)
          ANY_CHANGED="true"
        fi

        # Replace newlines with the separator
        if [ -n "$SEPARATOR" ]; then
          CHANGED_FILES=$(echo "$CHANGED_FILES" | tr '\n' "$SEPARATOR" | sed "s/$SEPARATOR$//" | sed "s/^$SEPARATOR//")
        fi

        # Write changed files to a file
        CHANGED_FILES_PATH="${RUNNER_TEMP}/changed_files.txt"
        echo "$CHANGED_FILES" > "$CHANGED_FILES_PATH"

        # Set outputs
        echo "all_changed_files_path=$CHANGED_FILES_PATH" >> $GITHUB_OUTPUT
        echo "all_changed_files_count=$CHANGED_FILES_COUNT" >> $GITHUB_OUTPUT
        echo "any_changed=$ANY_CHANGED" >> $GITHUB_OUTPUT

        # Debug output
        echo "Changed files written to: $CHANGED_FILES_PATH"
        echo "Changed files count: $CHANGED_FILES_COUNT"
        echo "Any changed: $ANY_CHANGED"
