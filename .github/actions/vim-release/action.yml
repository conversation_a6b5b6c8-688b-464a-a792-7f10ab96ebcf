name: 'Release Steps'

description: 'Performs the release steps for the Vim plugin'

inputs:
  version:
    description: 'Version to release'
    required: true
  release_channel:
    description: 'Release channel'
    required: true

runs:
  using: "composite"
  steps:
    - name: Create release commit in augment.vim.staging
      id: create_commit
      shell: bash
      working-directory: augment.vim.staging
      run: |
        git config user.name "github-actions[bot]"
        git config user.email "github-actions[bot]@users.noreply.github.com"
        git add .
        git diff --staged --name-status
        git commit -m "Augment Vim v${{ inputs.version }}"
        git push origin ${{ inputs.release_channel }}

    - name: Create a tag for the release in augment.vim
      shell: bash
      working-directory: augment.vim.staging
      run: |
        git tag v${{ inputs.version }}
        git push origin --tags

    # TODO(mpauly): Create a release for main releases from promoted prerelease's commit (not latest)
    - name: Create release in the Augment repo associating it with the Vim release
      uses: ncipollo/release-action@2c591bcc8ecdcd2db72b97d6147f871fcd833ba5 # v1.14.0
      with:
        name: "vim@${{ inputs.version }}"
        tag: "vim@${{ inputs.version }}"
