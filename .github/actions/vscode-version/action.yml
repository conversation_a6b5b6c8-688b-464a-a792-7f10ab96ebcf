name: "Get VSCode Version"

inputs:
  release_channel:
    description: "Release channel"
    required: false
    type: choice
    options:
      - prerelease
      - stable
  stable_release_ref:
    description: "(Optional) The latest stable release tag or commit SHA"
    required: false
  version_override:
    description: "(Optional) Force a version, i.e. 0.2.1"
    required: false

outputs:
  version:
    description: "The computed version number"
    value: ${{ steps.version.outputs.version || steps.github_version.outputs.version }}
  stable_release_ref:
    description: "The version to publish"
    value: ${{ steps.stable_release_ref.outputs.stable_release_ref }}

runs:
  using: "composite"
  steps:
    # Install dependencies
    - uses: actions/setup-node@1d0ff469b7ec7b3cb9d8673fde0c81c44821de2a
      with:
        node-version: ">= 20.15.1"

    - uses: pnpm/action-setup@a7487c7e89a18df4991f7f222e4898a00d66ddda
      with:
        version: 9
        run_install: true

    - name: Extension metadata
      id: extension_metadata
      shell: bash
      working-directory: clients/vscode
      # Pipe the extension data through jq to limit the size of the
      # EXTENSION_DATA variable
      run: |
        export EXTENSION_DATA=$(npm run --silent marketplace-data | jq '.versions[:50]') || ""
        echo result=$EXTENSION_DATA >> $GITHUB_OUTPUT

    - name: Set stable_release_ref
      id: stable_release_ref
      shell: bash
      working-directory: clients/vscode
      if: ${{ inputs.release_channel == 'stable' }}
      env:
        RELEASE_CHANNEL: ${{ inputs.release_channel }}
        STABLE_RELEASE_REF: ${{ inputs.stable_release_ref }}
        EXTENSION_DATA: ${{ steps.extension_metadata.outputs.result }}
      run: |
        new_version=$(./update_versions.py stable_release_ref)
        echo stable_release_ref="$new_version" >> $GITHUB_OUTPUT

    - name: Set version
      id: version
      shell: bash
      if: ${{ inputs.release_channel }}
      working-directory: clients/vscode
      env:
        RELEASE_CHANNEL: ${{ inputs.release_channel }}
        STABLE_RELEASE_REF: ${{ steps.stable_release_ref.outputs.stable_release_ref }}
        EXTENSION_DATA: ${{ steps.extension_metadata.outputs.result }}
        VERSION_OVERRIDE: ${{ inputs.version_override }}
      run: |
        new_version=$(./update_versions.py new_release)
        echo version="$new_version" >> $GITHUB_OUTPUT

    - name: Set version for GitHub release
      id: github_version
      shell: bash
      if: ${{ !inputs.release_channel }}
      working-directory: clients/vscode
      env:
        RELEASE_CHANNEL: ${{ inputs.release_channel }}
        STABLE_RELEASE_REF: ${{ steps.stable_release_ref.outputs.stable_release_ref }}
        EXTENSION_DATA: ${{ steps.extension_metadata.outputs.result }}
        VERSION_OVERRIDE: ${{ inputs.version_override }}
      run: |
        echo version="$(head -n1 version)-$(git rev-parse --abbrev-ref HEAD).$(git log --format='%ct.%h' -n1)" >> $GITHUB_OUTPUT
