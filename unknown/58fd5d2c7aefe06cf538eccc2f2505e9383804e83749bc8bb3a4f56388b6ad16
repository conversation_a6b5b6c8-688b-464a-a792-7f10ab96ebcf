#!/bin/bash
# Setup Script for services development at Augment

set -e

echo "Setting up development environment for Augment codebase..."

# Update package lists
echo "Updating package lists..."
sudo apt-get update

# Install basic dependencies
echo "Installing basic dependencies..."
sudo apt-get -y install \
	docker.io \
	nfs-common \
	unzip \
	zip \
	gcc \
	g++ \
	libasound2 \
	libatk1.0-0 \
	libatspi2.0-0 \
	libatk-bridge2.0-0 \
	libcairo2 \
	libgtk-3-0 \
	libgbm1 \
	libpango-1.0-0 \
	libxcomposite1 \
	libxdamage1 \
	libxkbcommon0 \
	libxrandr2 \
	libssl-dev \
	libtinfo5 \
	libhwloc-dev \
	libssl-dev \
	libstdc++6-12-dbg \
	pkg-config \
	xvfb \
	tzdata

# Create a .bazelrc.user file to override remote cache settings
cat >~/.bazelrc.user <<EOF
# Disable remote cache for local development
build --remote_cache=
EOF
