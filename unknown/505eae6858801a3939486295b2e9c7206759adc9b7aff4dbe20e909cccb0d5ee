import * as fs from "fs";
import * as os from "os";
import * as path from "path";

import { AugmentSession, FileBasedAuthSessionStore } from "../auth/auth-session-store";

describe("FileBasedAuthSessionStore", () => {
    let tempDir: string;
    let originalEnv: string | undefined;

    beforeEach(() => {
        // Create a temporary directory for each test
        tempDir = fs.mkdtempSync(path.join(os.tmpdir(), "auth-test-"));

        // Save original environment variable
        originalEnv = process.env.AUGMENT_SESSION_AUTH;
        delete process.env.AUGMENT_SESSION_AUTH;
    });

    afterEach(() => {
        // Restore original environment variable
        if (originalEnv !== undefined) {
            process.env.AUGMENT_SESSION_AUTH = originalEnv;
        } else {
            delete process.env.AUGMENT_SESSION_AUTH;
        }

        // Clean up temporary directory
        if (fs.existsSync(tempDir)) {
            fs.rmSync(tempDir, { recursive: true, force: true });
        }
    });

    describe("Environment variable authentication", () => {
        it("should use AUGMENT_SESSION_AUTH environment variable when available", () => {
            const sessionData: AugmentSession = {
                accessToken: "env-token-123",
                tenantURL: "https://env.example.com",
                scopes: ["read", "write"],
            };

            process.env.AUGMENT_SESSION_AUTH = JSON.stringify(sessionData);

            const store = new FileBasedAuthSessionStore(tempDir);
            expect(store.isLoggedIn).toBe(true);
        });

        it("should return session data from environment variable", async () => {
            const sessionData: AugmentSession = {
                accessToken: "env-token-456",
                tenantURL: "https://env2.example.com",
                scopes: ["read", "write"],
            };

            process.env.AUGMENT_SESSION_AUTH = JSON.stringify(sessionData);

            const store = new FileBasedAuthSessionStore(tempDir);
            const session = await store.getSession();

            expect(session).toEqual(sessionData);
        });

        it("should fall back to file when environment variable is invalid JSON", () => {
            process.env.AUGMENT_SESSION_AUTH = "invalid-json";

            // Create a valid session file
            const sessionData: AugmentSession = {
                accessToken: "file-token-123",
                tenantURL: "https://file.example.com",
                scopes: ["read", "write"],
            };
            const sessionPath = path.join(tempDir, "session.json");
            fs.writeFileSync(sessionPath, JSON.stringify(sessionData, null, 2));

            const store = new FileBasedAuthSessionStore(tempDir);
            expect(store.isLoggedIn).toBe(true);
        });

        it("should fall back to file when environment variable has invalid session structure", () => {
            process.env.AUGMENT_SESSION_AUTH = JSON.stringify({ invalid: "structure" });

            // Create a valid session file
            const sessionData: AugmentSession = {
                accessToken: "file-token-456",
                tenantURL: "https://file2.example.com",
                scopes: ["read", "write"],
            };
            const sessionPath = path.join(tempDir, "session.json");
            fs.writeFileSync(sessionPath, JSON.stringify(sessionData, null, 2));

            const store = new FileBasedAuthSessionStore(tempDir);
            expect(store.isLoggedIn).toBe(true);
        });

        it("should prioritize environment variable over file when both exist", async () => {
            const envSessionData: AugmentSession = {
                accessToken: "env-token-priority",
                tenantURL: "https://env-priority.example.com",
                scopes: ["read", "write"],
            };

            const fileSessionData: AugmentSession = {
                accessToken: "file-token-priority",
                tenantURL: "https://file-priority.example.com",
                scopes: ["read", "write"],
            };

            // Set environment variable
            process.env.AUGMENT_SESSION_AUTH = JSON.stringify(envSessionData);

            // Create session file
            const sessionPath = path.join(tempDir, "session.json");
            fs.writeFileSync(sessionPath, JSON.stringify(fileSessionData, null, 2));

            const store = new FileBasedAuthSessionStore(tempDir);
            const session = await store.getSession();

            expect(session).toEqual(envSessionData);
            expect(session?.accessToken).toBe("env-token-priority");
        });
    });

    describe("File-based authentication (fallback)", () => {
        it("should use file when no environment variable is set", () => {
            const sessionData: AugmentSession = {
                accessToken: "file-only-token",
                tenantURL: "https://file-only.example.com",
                scopes: ["read", "write"],
            };
            const sessionPath = path.join(tempDir, "session.json");
            fs.writeFileSync(sessionPath, JSON.stringify(sessionData, null, 2));

            const store = new FileBasedAuthSessionStore(tempDir);
            expect(store.isLoggedIn).toBe(true);
        });

        it("should return null when neither environment variable nor file exist", async () => {
            const store = new FileBasedAuthSessionStore(tempDir);
            expect(store.isLoggedIn).toBe(false);

            const session = await store.getSession();
            expect(session).toBeNull();
        });

        it("should return session data from file when environment variable is not set", async () => {
            const sessionData: AugmentSession = {
                accessToken: "file-token-789",
                tenantURL: "https://file3.example.com",
                scopes: ["read", "write"],
            };
            const sessionPath = path.join(tempDir, "session.json");
            fs.writeFileSync(sessionPath, JSON.stringify(sessionData, null, 2));

            const store = new FileBasedAuthSessionStore(tempDir);
            const session = await store.getSession();

            expect(session).toEqual(sessionData);
        });
    });

    describe("Session validation", () => {
        it("should reject environment variable session missing accessToken", () => {
            const invalidSession = {
                tenantURL: "https://example.com",
                scopes: ["read", "write"],
                // missing accessToken
            };

            process.env.AUGMENT_SESSION_AUTH = JSON.stringify(invalidSession);

            const store = new FileBasedAuthSessionStore(tempDir);
            expect(store.isLoggedIn).toBe(false);
        });

        it("should reject environment variable session missing tenantURL", () => {
            const invalidSession = {
                accessToken: "token-123",
                scopes: ["read", "write"],
                // missing tenantURL
            };

            process.env.AUGMENT_SESSION_AUTH = JSON.stringify(invalidSession);

            const store = new FileBasedAuthSessionStore(tempDir);
            expect(store.isLoggedIn).toBe(false);
        });

        it("should reject environment variable session with invalid scopes", () => {
            const invalidSession = {
                accessToken: "token-123",
                tenantURL: "https://example.com",
                scopes: "not-an-array",
            };

            process.env.AUGMENT_SESSION_AUTH = JSON.stringify(invalidSession);

            const store = new FileBasedAuthSessionStore(tempDir);
            expect(store.isLoggedIn).toBe(false);
        });
    });

    describe("Session string parsing (internal method)", () => {
        it("should parse valid session JSON string", () => {
            const store = new FileBasedAuthSessionStore(tempDir);
            const validSessionString = JSON.stringify({
                accessToken: "test-token",
                tenantURL: "https://test.example.com",
                scopes: ["read", "write"],
            });

            // Access private method for testing
            const result = (store as any)._parseSessionFromString(validSessionString);

            expect(result).toEqual({
                accessToken: "test-token",
                tenantURL: "https://test.example.com",
                scopes: ["read", "write"],
            });
        });

        it("should return null for invalid JSON", () => {
            const store = new FileBasedAuthSessionStore(tempDir);
            const result = (store as any)._parseSessionFromString("invalid-json");
            expect(result).toBeNull();
        });

        it("should return null for session missing required fields", () => {
            const store = new FileBasedAuthSessionStore(tempDir);
            const invalidSessionString = JSON.stringify({
                accessToken: "test-token",
                // missing tenantURL and scopes
            });

            const result = (store as any)._parseSessionFromString(invalidSessionString);
            expect(result).toBeNull();
        });

        it("should return null for session with invalid scopes type", () => {
            const store = new FileBasedAuthSessionStore(tempDir);
            const invalidSessionString = JSON.stringify({
                accessToken: "test-token",
                tenantURL: "https://test.example.com",
                scopes: "not-an-array",
            });

            const result = (store as any)._parseSessionFromString(invalidSessionString);
            expect(result).toBeNull();
        });
    });
});
