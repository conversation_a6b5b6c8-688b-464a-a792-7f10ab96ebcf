<script lang="ts">
  import BaseToolComponent from "../../BaseToolComponent.svelte";
  import ToolUseHeader from "../../ToolUseHeader.svelte";
  import FileIcon from "$common-webviews/src/design-system/icons/fontawesome/svgs/regular/file.svg?component";
  import {
    ToolUsePhase,
    type ToolUseState,
  } from "$common-webviews/src/apps/chat/types/tool-use-state";
  import ShellError from "../../sidecar/ShellError.svelte";
  import StatusBadgeAugment from "$common-webviews/src/design-system/components/StatusBadgeAugment.svelte";
  import TextAugment from "$common-webviews/src/design-system/components/TextAugment.svelte";
  import CardAugment from "$common-webviews/src/design-system/components/CardAugment.svelte";
  import ToolResultFields from "./ToolResultFields.svelte";

  export let toolUseInput: Record<string, unknown> = {};
  export let toolUseState: ToolUseState;
  let collapsed = true;

  // Extract worker agent IDs array from input
  $: workerAgentIds = Array.isArray(toolUseInput.worker_agent_ids)
    ? toolUseInput.worker_agent_ids
    : [];

  // Format tool arguments for display - show count of agents in collapsed state
  $: formattedArgs = (() => {
    const args: string[] = [];
    if (workerAgentIds.length > 0) {
      args.push(`${workerAgentIds.length} agent${workerAgentIds.length === 1 ? "" : "s"}`);
    } else {
      args.push("all agents");
    }
    return args;
  })();

  // Parse the tool result to get edit results
  $: editResults = (() => {
    if (
      toolUseState.phase === ToolUsePhase.completed &&
      toolUseState.result?.text &&
      !toolUseState.result.isError
    ) {
      try {
        const response = JSON.parse(toolUseState.result.text);
        // Combine successful and failed reads into a single array
        const successful = Array.isArray(response.editResults) ? response.editResults : [];
        const failed = Array.isArray(response.failedReads) ? response.failedReads : [];
        return [...successful, ...failed];
      } catch (e) {
        console.error("Failed to parse read worker agent edits tool response:", e);
      }
    }
    return [];
  })();

  // Count successful and failed reads, and total file edits
  $: successCount = editResults.filter((result: any) => result.success).length;
  $: failureCount = editResults.filter((result: any) => !result.success).length;
  $: totalEdits = editResults.reduce(
    (sum: number, result: any) =>
      sum + (result.success && result.fileEdits ? result.fileEdits.length : 0),
    0,
  );
  $: hasResults = editResults.length > 0;

  // Helper function to get change type color
  function getChangeTypeColor(
    changeType: string,
  ): "success" | "info" | "warning" | "error" | "neutral" {
    switch (changeType?.toLowerCase()) {
      case "created":
      case "added":
        return "success";
      case "modified":
      case "updated":
        return "info";
      case "deleted":
      case "removed":
        return "error";
      case "renamed":
      case "moved":
        return "warning";
      default:
        return "neutral";
    }
  }
</script>

<BaseToolComponent bind:collapsed>
  <ToolUseHeader slot="header" toolName="Read Worker Agent Edits" formattedToolArgs={formattedArgs}>
    <FileIcon slot="icon" />
    <svelte:fragment slot="secondary">
      {#if hasResults && collapsed}
        <div class="c-read-edits-tool__counts">
          {#if successCount > 0}
            <StatusBadgeAugment color="success" size={1}>
              {successCount} agent{successCount === 1 ? "" : "s"}
            </StatusBadgeAugment>
          {/if}
          {#if totalEdits > 0}
            <StatusBadgeAugment color="info" size={1}>
              {totalEdits} edit{totalEdits === 1 ? "" : "s"}
            </StatusBadgeAugment>
          {/if}
          {#if failureCount > 0}
            <StatusBadgeAugment color="error" size={1}>
              {failureCount} failed
            </StatusBadgeAugment>
          {/if}
        </div>
      {/if}
    </svelte:fragment>
  </ToolUseHeader>

  <div slot="details" class="c-read-edits-tool__details">
    {#if toolUseState.phase === ToolUsePhase.running}
      <div class="c-read-edits-tool__running">
        <TextAugment size={1} color="neutral">Reading worker agent file edits...</TextAugment>
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && hasResults}
      <div class="c-read-edits-tool__results">
        {#each editResults as result, index}
          <CardAugment class="c-read-edits-tool__agent-card">
            <div class="c-read-edits-tool__agent-header">
              <div class="c-read-edits-tool__agent-title">
                <TextAugment size={1} weight="medium">
                  Agent {result.agentId || `${index + 1}`}
                </TextAugment>
                <StatusBadgeAugment color={result.success ? "success" : "error"} size={1}>
                  {result.success ? "Read" : "Failed"}
                </StatusBadgeAugment>
              </div>
            </div>

            <div class="c-read-edits-tool__agent-details">
              {#if result.success}
                <div class="c-read-edits-tool__success-details">
                  <TextAugment size={1} color="neutral">
                    <strong>Agent ID:</strong>
                    {result.agentId}
                  </TextAugment>

                  {#if result.fileEdits && result.fileEdits.length > 0}
                    <div class="c-read-edits-tool__file-edits">
                      <TextAugment size={1} color="neutral">
                        <strong>File Edits ({result.fileEdits.length}):</strong>
                      </TextAugment>

                      {#each result.fileEdits as edit}
                        <div class="c-read-edits-tool__file-edit">
                          <div class="c-read-edits-tool__file-header">
                            <TextAugment size={1} weight="medium" type="monospace">
                              {edit.filePath}
                            </TextAugment>
                            <StatusBadgeAugment
                              color={getChangeTypeColor(edit.changeType)}
                              size={1}
                            >
                              {edit.changeType}
                            </StatusBadgeAugment>
                          </div>

                          <ToolResultFields
                            data={edit}
                            fields={[
                              {
                                key: "content",
                                label: "Content",
                                type: "monospace",
                                collapsible: true,
                              },
                            ]}
                          />
                        </div>
                      {/each}
                    </div>
                  {:else}
                    <TextAugment size={1} color="neutral">
                      No file edits found for this agent.
                    </TextAugment>
                  {/if}
                </div>
              {:else}
                <div class="c-read-edits-tool__error-details">
                  <TextAugment size={1} color="error">
                    <strong>Error:</strong>
                    {result.error || "Unknown error"}
                  </TextAugment>
                  {#if result.agentId}
                    <TextAugment size={1} color="neutral">
                      <strong>Agent ID:</strong>
                      {result.agentId}
                    </TextAugment>
                  {/if}
                </div>
              {/if}
            </div>
          </CardAugment>
        {/each}
      </div>
    {:else if toolUseState.phase === ToolUsePhase.completed && !hasResults}
      <div class="c-read-edits-tool__no-results">
        <TextAugment size={1} color="neutral">No worker agent edits were found.</TextAugment>
      </div>
    {/if}
  </div>

  {#if toolUseState.phase === ToolUsePhase.error}
    <ShellError slot="error" {...toolUseState.result ?? {}} />
  {/if}
</BaseToolComponent>

<style>
  .c-read-edits-tool__counts {
    display: flex;
    gap: var(--ds-spacing-2);
    align-items: center;
    flex-wrap: wrap;
  }

  .c-read-edits-tool__details {
    padding: var(--ds-spacing-2);
  }

  .c-read-edits-tool__results {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-read-edits-tool__agent-header {
    margin-bottom: var(--ds-spacing-2);
  }

  .c-read-edits-tool__agent-title {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-2);
  }

  .c-read-edits-tool__agent-details {
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-read-edits-tool__file-edits {
    margin-top: var(--ds-spacing-1);
    display: flex;
    flex-direction: column;
    gap: var(--ds-spacing-2);
  }

  .c-read-edits-tool__file-edit {
    border: 1px solid var(--ds-color-neutral-200);
    border-radius: var(--ds-border-radius-1);
    padding: var(--ds-spacing-1);
  }

  .c-read-edits-tool__file-header {
    display: flex;
    justify-content: space-between;
    align-items: center;
    gap: var(--ds-spacing-2);
    margin-bottom: var(--ds-spacing-2);
  }
</style>
