load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_grpc_library")
load("//tools/bzl:python.bzl", "py_grpc_library")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

proto_library(
    name = "notification_proto",
    srcs = ["notification.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/token_exchange:auth_options_proto",
        "@protobuf//:timestamp_proto",
    ],
)

go_grpc_library(
    name = "notification_go_proto",
    importpath = "github.com/augmentcode/augment/services/notification/proto",
    proto = ":notification_proto",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//services/token_exchange:auth_options_go_proto",
    ],
)

py_grpc_library(
    name = "notification_py_proto",
    protos = [":notification_proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/token_exchange:auth_options_py_proto",
    ],
)

ts_proto_library(
    name = "notification_ts_proto",
    copy_files = True,
    node_modules = "//:node_modules",
    proto = ":notification_proto",
    visibility = ["//services:__subpackages__"],
)
