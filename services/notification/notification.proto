syntax = "proto3";

package notification;

import "google/protobuf/timestamp.proto";
import "services/token_exchange/auth_options.proto";

option go_package = "github.com/augmentcode/augment/services/notification/proto";

// NotificationService for creating, reading, and managing user notifications
service NotificationService {
  // Create a notification for a user
  rpc CreateNotification(CreateNotificationRequest) returns (CreateNotificationResponse) {
    option (auth_options.required_token_scopes) = NOTIFICATION_ADMIN;
    option (auth_options.required_token_scopes) = NOTIFICATION_RW;
  }

  // Read notifications for a user without marking them as read
  rpc ReadNotifications(ReadNotificationsRequest) returns (ReadNotificationsResponse) {
    option (auth_options.required_token_scopes) = NOTIFICATION_R;
  }

  // Mark notifications as read by notification names or notification id (move to receipts)
  rpc MarkNotificationsAsRead(MarkNotificationsAsReadRequest) returns (MarkNotificationsAsReadResponse) {
    option (auth_options.required_token_scopes) = NOTIFICATION_RW;
  }

  // Delete notifications for a user
  rpc DeleteNotifications(DeleteNotificationsRequest) returns (DeleteNotificationsResponse) {
    option (auth_options.required_token_scopes) = NOTIFICATION_ADMIN;
    option (auth_options.required_token_scopes) = NOTIFICATION_RW;
  }

  rpc ForgetUser(ForgetUserRequest) returns (ForgetUserResponse) {
    option (auth_options.required_token_scopes) = NOTIFICATION_ADMIN;
    option (auth_options.required_token_scopes) = NOTIFICATION_RW;
  }
}

// The level of the notification
enum NotificationLevel {
  NOTIFICATION_LEVEL_UNSPECIFIED = 0;
  NOTIFICATION_LEVEL_INFO = 1;
  NOTIFICATION_LEVEL_WARNING = 2;
  NOTIFICATION_LEVEL_ERROR = 3;
}

// The display type of the notification
enum NotificationDisplayType {
  DISPLAY_TYPE_UNSPECIFIED = 0;
  DISPLAY_TYPE_TOAST = 1;
  DISPLAY_TYPE_BANNER = 2;
}

// An action item that can be associated with a notification
message ActionItem {
  // The title of the action item (displayed as a button)
  string title = 1;

  // The URL to visit when the action item is clicked
  optional string url = 2;
}

// A notification to be sent to a user
message Notification {
  // The level of the notification
  NotificationLevel level = 1;

  // The message content of the notification
  string message = 2;

  // Optional action items associated with the notification
  repeated ActionItem action_items = 3;

  // The timestamp when the notification was created
  google.protobuf.Timestamp created_at = 4;

  // The timestamp when the notification was received (if applicable)
  google.protobuf.Timestamp received_at = 5;

  // The notification name (unique key for the notification)
  string notification_name = 6;

  // The notification ID
  string notification_id = 7;

  // The display type of the notification
  NotificationDisplayType display_type = 8;
}

// Request to create a notification for a user
message CreateNotificationRequest {
  // The tenant ID to send the notification to
  optional string tenant_id = 1;

  // The opaque user ID to send the notification to
  // If not provided, the notification will be sent to the authenticated user
  optional string opaque_user_id = 2;

  // The level of the notification
  NotificationLevel level = 3;

  // The message content of the notification
  //
  // The message should not contain any PII.
  string message = 4;

  // Optional action items associated with the notification
  repeated ActionItem action_items = 5;

  // Unique key for this notification type (required)
  // Notifications with the same key will be deduplicated
  //
  // Each caller needs to set the notification name. A caller can create a message with a
  // notification name multiple times. This is then treated as an update. If the user already
  // marked the message as seen, the
  // message will not be shown again.
  //
  // The design with a notification name for deduplication was choosen to avoid having to store state in the caller, e.g.
  // the notification name can define the mapping from invoice id to notifiation. We do not
  // manually need
  // to store the mapping.
  //
  // The callers are responsible for choosing a notification name that doesn't create conflicts
  // with other services.
  string notification_name = 6;

  // The display type of the notification
  NotificationDisplayType display_type = 7;
}

// Response to a create notification request
message CreateNotificationResponse {
  // Whether the notification was updated (true) or created (false)
  bool updated = 1;

  // The notification ID
  string notification_id = 2;
}

// Request to read notifications for a user without marking them as read
message ReadNotificationsRequest {
  // The tenant ID to read notifications for
  optional string tenant_id = 1;

  // The opaque user ID to read notifications for
  optional string opaque_user_id = 2;

  // Optional filter by notification name or notification id
  oneof filter {
    string notification_name = 3;

    string notification_id = 6;
  }

  // Whether to read all versions of each notification or just the most recent
  // Default is false (only most recent version)
  bool read_all_versions = 4;

  // Whether to read already-read notifications (receipts) instead of unread notifications
  // Default is false (read unread notifications from notifications family)
  bool read_receipts = 5;
}

// Response containing notifications for a user (without marking as read)
message ReadNotificationsResponse {
  // The list of notifications that were read
  repeated Notification notifications = 1;
}

// A notification name or id with optional action item title for marking as read
message NotificationMarkRequest {
  oneof key {
    // The notification name of the notification to mark as read
    string notification_name = 1;

    // The notification id of the notification to mark as read
    string notification_id = 3;
  }

  // Optional action item title that was taken (if any action was performed)
  optional string action_item_title = 2;
}

// Request to mark notifications as read by notification name or id
message MarkNotificationsAsReadRequest {
  // The tenant ID to mark notifications for
  optional string tenant_id = 1;

  // The opaque user ID to mark notifications for
  optional string opaque_user_id = 2;

  // List of notifications to mark as read, with optional action item titles
  repeated NotificationMarkRequest notifications_to_mark = 3;
}

message NotificationKey {
  // The notification name of the notification
  string notification_name = 1;

  // The notification id of the notification
  string notification_id = 2;
}

// Response containing the notification names that were actually marked as read
message MarkNotificationsAsReadResponse {
  reserved 1;

  // The list of notifications that were actually marked as read
  repeated NotificationKey marked_notifications = 2;
}

// Request to delete notifications for a user
//
// A request should not contain more than 100 notifications names or ids combined.
// the notifiation names should not non-overlapping
message DeleteNotificationsRequest {
  // The tenant ID to read and mark notifications for
  optional string tenant_id = 1;

  // The opaque user ID to read and mark notifications for
  optional string opaque_user_id = 2;

  // List of notification name to delete
  repeated string notification_names = 3;

  // List of notification id to delete
  repeated string notification_ids = 4;
}

// Response to a delete notifications request
message DeleteNotificationsResponse {
  reserved 1;

  // The list of notifications that were actually deleted
  repeated NotificationKey deleted_notifications = 2;
}

message ForgetUserRequest {
  // The tenant ID to forget the user for
  optional string tenant_id = 1;

  // The opaque user ID to forget
  optional string opaque_user_id = 2;
}

message ForgetUserResponse {
  uint32 deleted_count = 1;
}
