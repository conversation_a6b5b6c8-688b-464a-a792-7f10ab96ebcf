package main

import (
	"context"
	"fmt"
	"testing"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/logging/audit"
	fakebigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client/fake_client"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	notificationproto "github.com/augmentcode/augment/services/notification/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	"github.com/stretchr/testify/assert"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

// Helper function to create an authenticated context for testing
func createTestContext(t *testing.T, tenantID, userID, opaqueUserID string) context.Context {
	ctx := context.Background()
	claims := &auth.AugmentClaims{
		TenantID: tenantID,
		UserID:   userID,
		Scope:    []string{"NOTIFICATION_RW", "NOTIFICATION_ADMIN", "NOTIFICATION_R"},
	}

	if opaqueUserID != "" {
		claims.OpaqueUserID = opaqueUserID
		claims.OpaqueUserIDType = "AUGMENT"
	} else {
		claims.ServiceName = "test-service"
	}

	ctx = claims.NewContext(ctx)
	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	return requestcontext.NewIncomingContext(ctx, requestContext)
}

func string_ptr(s string) *string {
	return &s
}

func TestSendNotification_HappyPath(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle, audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock())

	// Create a test request
	req := &notificationproto.CreateNotificationRequest{
		OpaqueUserId:     string_ptr("test-opaque-user-id"),
		Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
		Message:          "Test notification message",
		NotificationName: "test-dedup-key",
		ActionItems: []*notificationproto.ActionItem{
			{
				Title: "Test action",
				Url:   string_ptr("https://example.com"),
			},
		},
		DisplayType: notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
	}

	// Call the CreateNotification method
	resp, err := notificationServer.CreateNotification(ctx, req)

	// Verify results
	assert.NoError(t, err, "CreateNotification should not return an error")
	assert.False(t, resp.Updated, "CreateNotification should return Updated=false for new notification")
}

func TestCreateNotification_ValidationErrors(t *testing.T) {
	// Test cases for validation errors
	testCases := []struct {
		name          string
		ctx           context.Context
		request       *notificationproto.CreateNotificationRequest
		expectedError codes.Code
	}{
		{
			name: "Missing tenant ID",
			ctx:  createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id"),
			request: &notificationproto.CreateNotificationRequest{
				OpaqueUserId:     string_ptr("test-opaque-user-id"),
				Message:          "Test message",
				NotificationName: "test-key",
			},
			expectedError: codes.OK,
		},
		{
			name: "Missing message",
			ctx:  createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id"),
			request: &notificationproto.CreateNotificationRequest{
				OpaqueUserId:     string_ptr("test-opaque-user-id"),
				NotificationName: "test-key",
				// No message
			},
			expectedError: codes.InvalidArgument,
		},
		{
			name: "Missing Notification name",
			ctx:  createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id"),
			request: &notificationproto.CreateNotificationRequest{
				OpaqueUserId: string_ptr("test-opaque-user-id"),
				Message:      "Test message",
				// No Notification name
			},
			expectedError: codes.InvalidArgument,
		},
		{
			name: "Missing opaque user ID in both request and context",
			ctx:  createTestContext(t, "test-tenant-id", "test-user-id", ""), // No opaque user ID in context
			request: &notificationproto.CreateNotificationRequest{
				// No opaque user ID in request
				Message:          "Test message",
				NotificationName: "test-key",
			},
			expectedError: codes.InvalidArgument,
		},
	}

	for _, tc := range testCases {
		t.Run(tc.name, func(t *testing.T) {
			// Create a fake BigTable client
			fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

			// Create a local feature flag handle
			featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

			// Create a test server
			notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle, audit.NewDefaultAuditLogger(),
				ripublisher.NewRequestInsightPublisherMock())

			// Call the CreateNotification method
			_, err := notificationServer.CreateNotification(tc.ctx, tc.request)

			// Verify error
			if err == nil {
				if tc.expectedError == codes.OK {
					return
				}
				t.Errorf("Expected an error, but got none")
			} else {
				st, ok := status.FromError(err)
				assert.True(t, ok, "Expected a gRPC status error")
				assert.Equal(t, tc.expectedError, st.Code(), "Expected error code %v, got %v", tc.expectedError, st.Code())
			}
		})
	}
}

func TestCreateNotification_ImplicitUserID(t *testing.T) {
	// Create context with auth claims but no explicit opaque user ID in request
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "context-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle,
		audit.NewDefaultAuditLogger(), ripublisher.NewRequestInsightPublisherMock())

	// Create a test request with no opaque user ID
	req := &notificationproto.CreateNotificationRequest{
		// No OpaqueUserId field - should use the one from context
		Message:          "Test notification message",
		Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
		DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		NotificationName: "test-implicit-key",
	}

	// Call the CreateNotification method
	resp, err := notificationServer.CreateNotification(ctx, req)

	// Verify results
	assert.NoError(t, err, "CreateNotification should not return an error")
	assert.False(t, resp.Updated, "CreateNotification should return Updated=false for new notification")

	// Now read notifications to verify it was sent to the correct user
	readReq := &notificationproto.ReadNotificationsRequest{}
	readResp, err := notificationServer.ReadNotifications(ctx, readReq)
	assert.NoError(t, err, "ReadNotifications should not return an error")

	// Check that we can read the notification
	assert.Len(t, readResp.Notifications, 1, "Expected 1 notification")
	assert.Equal(t, "Test notification message", readResp.Notifications[0].Message, "Notification message should match")
}

func TestReadNotifications_Basic(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle,
		audit.NewDefaultAuditLogger(), ripublisher.NewRequestInsightPublisherMock())

	// First, create a notification
	createReq := &notificationproto.CreateNotificationRequest{
		OpaqueUserId:     string_ptr("test-opaque-user-id"),
		Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
		DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		Message:          "Test notification message",
		NotificationName: "test-read-key",
	}
	_, err := notificationServer.CreateNotification(ctx, createReq)
	assert.NoError(t, err, "CreateNotification should not return an error")

	// Now read notifications
	readReq := &notificationproto.ReadNotificationsRequest{}
	resp, err := notificationServer.ReadNotifications(ctx, readReq)
	assert.NoError(t, err, "ReadNotifications should not return an error")

	// Check that we read the notification
	assert.Len(t, resp.Notifications, 1, "Expected 1 notification")
	assert.Equal(t, "Test notification message", resp.Notifications[0].Message, "Notification message should match")
	assert.Nil(t, resp.Notifications[0].ReceivedAt, "ReceivedAt should not be set for ReadNotifications")

	// Read again to verify notification is still there (not marked as read)
	resp2, err := notificationServer.ReadNotifications(ctx, readReq)
	assert.NoError(t, err, "ReadNotifications should not return an error on second call")
	assert.Len(t, resp2.Notifications, 1, "Expected 1 notification on second read")
}

func TestCreateNotification_DifferentUsers(t *testing.T) {
	// Create contexts for two different users
	ctx1 := createTestContext(t, "test-tenant-id", "test-user-id-1", "test-opaque-user-id-1")
	ctx2 := createTestContext(t, "test-tenant-id", "test-user-id-2", "test-opaque-user-id-2")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle,
		audit.NewDefaultAuditLogger(), ripublisher.NewRequestInsightPublisherMock())

	// Create notifications for both users
	createReq1 := &notificationproto.CreateNotificationRequest{
		OpaqueUserId:     string_ptr("test-opaque-user-id-1"),
		Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
		DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		Message:          "Notification for user 1",
		NotificationName: "user1-key",
	}
	_, err := notificationServer.CreateNotification(ctx1, createReq1)
	assert.NoError(t, err, "CreateNotification should not return an error for user 1")

	createReq2 := &notificationproto.CreateNotificationRequest{
		OpaqueUserId:     string_ptr("test-opaque-user-id-2"),
		Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
		DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		Message:          "Notification for user 2",
		NotificationName: "user2-key",
	}
	_, err = notificationServer.CreateNotification(ctx2, createReq2)
	assert.NoError(t, err, "CreateNotification should not return an error for user 2")

	// User 1 should only read their own notifications
	readReq := &notificationproto.ReadNotificationsRequest{}
	resp1, err := notificationServer.ReadNotifications(ctx1, readReq)
	assert.NoError(t, err, "ReadNotifications should not return an error for user 1")
	assert.Len(t, resp1.Notifications, 1, "User 1 should read 1 notification")
	assert.Equal(t, "Notification for user 1", resp1.Notifications[0].Message, "User 1 should read the correct notification")

	// User 2 should only read their own notifications
	resp2, err := notificationServer.ReadNotifications(ctx2, readReq)
	assert.NoError(t, err, "ReadNotifications should not return an error for user 2")
	assert.Len(t, resp2.Notifications, 1, "User 2 should read 1 notification")
	assert.Equal(t, "Notification for user 2", resp2.Notifications[0].Message, "User 2 should read the correct notification")
}

func TestCreateNotification_CategoryGrouping_NewCategory(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle,
		audit.NewDefaultAuditLogger(), ripublisher.NewRequestInsightPublisherMock())

	// Create a notification with a Notification name
	req := &notificationproto.CreateNotificationRequest{
		OpaqueUserId:     string_ptr("test-opaque-user-id"),
		Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
		Message:          "Test categorized notification",
		NotificationName: "test-category",
		ActionItems: []*notificationproto.ActionItem{
			{
				Title: "Test action",
				Url:   string_ptr("https://example.com"),
			},
		},
		DisplayType: notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
	}

	// Call the CreateNotification method
	resp, err := notificationServer.CreateNotification(ctx, req)

	// Verify results
	assert.NoError(t, err, "CreateNotification should not return an error")
	assert.False(t, resp.Updated, "CreateNotification should return Updated=false for new notification")

	// Read notifications to verify the category and occurrence count
	readReq := &notificationproto.ReadNotificationsRequest{}
	readResp, err := notificationServer.ReadNotifications(ctx, readReq)
	assert.NoError(t, err, "ReadNotifications should not return an error")

	// Check that we read the notification with correct Notification name and count
	assert.Len(t, readResp.Notifications, 1, "Expected 1 notification")
	notification := readResp.Notifications[0]
	assert.Equal(t, "Test categorized notification", notification.Message, "Notification message should match")
	assert.Equal(t, "test-category", notification.NotificationName, "Notification Notification name should match")
}

func TestCreateNotification_CategoryGrouping_UpdateExisting(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle,
		audit.NewDefaultAuditLogger(), ripublisher.NewRequestInsightPublisherMock())

	// Create first notification with a Notification name
	req1 := &notificationproto.CreateNotificationRequest{
		OpaqueUserId:     string_ptr("test-opaque-user-id"),
		Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
		Message:          "First categorized notification",
		NotificationName: "test-category",
		DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
	}

	resp1, err := notificationServer.CreateNotification(ctx, req1)
	assert.NoError(t, err, "First CreateNotification should not return an error")
	assert.False(t, resp1.Updated, "First CreateNotification should return Updated=false for new notification")

	// Create second notification with the same Notification name
	req2 := &notificationproto.CreateNotificationRequest{
		OpaqueUserId:     string_ptr("test-opaque-user-id"),
		Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_WARNING,
		Message:          "Updated categorized notification",
		NotificationName: "test-category",
		ActionItems: []*notificationproto.ActionItem{
			{
				Title: "Updated action",
				Url:   string_ptr("https://updated.example.com"),
			},
		},
		DisplayType: notificationproto.NotificationDisplayType_DISPLAY_TYPE_TOAST,
	}

	resp2, err := notificationServer.CreateNotification(ctx, req2)
	assert.NoError(t, err, "Second CreateNotification should not return an error")
	assert.NotNil(t, resp2, "Second CreateNotification response should not be nil")

	// The second notification should be an update (existing notification updated)
	assert.True(t, resp2.Updated, "Second CreateNotification should return Updated=true for existing notification")

	// Read notifications to verify the update
	readReq := &notificationproto.ReadNotificationsRequest{}
	readResp, err := notificationServer.ReadNotifications(ctx, readReq)
	assert.NoError(t, err, "ReadNotifications should not return an error")

	// Should still have only 1 notification (updated, not duplicated)
	assert.Len(t, readResp.Notifications, 1, "Expected 1 notification (updated, not duplicated)")
	notification := readResp.Notifications[0]

	// Verify the notification was updated
	assert.Equal(t, "test-category", notification.NotificationName, "Notification name should match")
	assert.Equal(t, "Updated categorized notification", notification.Message, "Message should be updated")
	assert.Equal(t, notificationproto.NotificationLevel_NOTIFICATION_LEVEL_WARNING, notification.Level, "Level should be updated")
	assert.Equal(t, "test-category", notification.NotificationName, "Notification name should match")
	assert.Len(t, notification.ActionItems, 1, "Should have updated action items")
	assert.Equal(t, "Updated action", notification.ActionItems[0].Title, "Action item should be updated")
	assert.Equal(t, notificationproto.NotificationDisplayType_DISPLAY_TYPE_TOAST, notification.DisplayType, "Display type should be updated")
}

func TestCreateNotification_CategoryGrouping_DifferentCategories(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle,
		audit.NewDefaultAuditLogger(), ripublisher.NewRequestInsightPublisherMock())

	// Create notifications with different Notification names
	req1 := &notificationproto.CreateNotificationRequest{
		OpaqueUserId:     string_ptr("test-opaque-user-id"),
		Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
		Message:          "Category A notification",
		NotificationName: "category-a",
		DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
	}

	req2 := &notificationproto.CreateNotificationRequest{
		OpaqueUserId:     string_ptr("test-opaque-user-id"),
		Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
		Message:          "Category B notification",
		NotificationName: "category-b",
		DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
	}

	// Create both notifications
	resp1, err := notificationServer.CreateNotification(ctx, req1)
	assert.NoError(t, err, "First CreateNotification should not return an error")

	resp2, err := notificationServer.CreateNotification(ctx, req2)
	assert.NoError(t, err, "Second CreateNotification should not return an error")

	// Both should be new notifications (not updates)
	assert.False(t, resp1.Updated, "First CreateNotification should return Updated=false for new notification")
	assert.False(t, resp2.Updated, "Second CreateNotification should return Updated=false for new notification")

	// Read notifications to verify both exist
	readReq := &notificationproto.ReadNotificationsRequest{}
	readResp, err := notificationServer.ReadNotifications(ctx, readReq)
	assert.NoError(t, err, "ReadNotifications should not return an error")

	// Should have 2 notifications (different Notification names)
	assert.Len(t, readResp.Notifications, 2, "Expected 2 notifications for different Notification names")

	// Verify both notifications exist with correct Notification names and counts
	categoryAFound := false
	categoryBFound := false
	for _, notification := range readResp.Notifications {
		if notification.NotificationName == "category-a" {
			categoryAFound = true
			assert.Equal(t, "Category A notification", notification.Message)
		} else if notification.NotificationName == "category-b" {
			categoryBFound = true
			assert.Equal(t, "Category B notification", notification.Message)
		}
	}
	assert.True(t, categoryAFound, "Category A notification should be found")
	assert.True(t, categoryBFound, "Category B notification should be found")
}

func TestCreateNotification_CategoryGrouping_MultipleUpdates(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle, audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock())

	// Create multiple notifications with the same Notification name
	dedupKey := "repeated-category"
	for i := 1; i <= 3; i++ {
		req := &notificationproto.CreateNotificationRequest{
			OpaqueUserId:     string_ptr("test-opaque-user-id"),
			Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
			Message:          fmt.Sprintf("Notification %d", i),
			NotificationName: dedupKey,
			DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		}

		_, err := notificationServer.CreateNotification(ctx, req)
		assert.NoError(t, err, fmt.Sprintf("CreateNotification %d should not return an error", i))
	}

	// Read notifications to verify only one exists with correct count
	readReq := &notificationproto.ReadNotificationsRequest{}
	readResp, err := notificationServer.ReadNotifications(ctx, readReq)
	assert.NoError(t, err, "ReadNotifications should not return an error")

	// Should have only 1 notification (updated 3 times)
	assert.Len(t, readResp.Notifications, 1, "Expected 1 notification (updated multiple times)")
	notification := readResp.Notifications[0]

	// Verify the final state
	assert.Equal(t, "Notification 3", notification.Message, "Message should be from the last update")
	assert.Equal(t, dedupKey, notification.NotificationName, "Notification name should match")
}

func TestCreateNotification_NotificationNameRequired(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle, audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock())

	// Try to create notification without Notification name (should fail)
	req := &notificationproto.CreateNotificationRequest{
		OpaqueUserId: string_ptr("test-opaque-user-id"),
		Level:        notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
		Message:      "Notification without Notification name",
		// No notification_name provided
	}

	_, err := notificationServer.CreateNotification(ctx, req)
	assert.Error(t, err, "CreateNotification should return an error when notification_name is missing")
	assert.Contains(t, err.Error(), "notification_name is required", "Error should mention notification name is required")
}

func TestReadNotifications_NotificationNameFilter(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle, audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock())

	// Create notifications with different Notification names
	dedupKeys := []string{"category-a", "category-b", "category-c"}
	for _, dedupKey := range dedupKeys {
		req := &notificationproto.CreateNotificationRequest{
			OpaqueUserId:     string_ptr("test-opaque-user-id"),
			Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
			Message:          fmt.Sprintf("Notification for %s", dedupKey),
			NotificationName: dedupKey,
			DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		}

		_, err := notificationServer.CreateNotification(ctx, req)
		assert.NoError(t, err, fmt.Sprintf("CreateNotification for %s should not return an error", dedupKey))
	}

	// Test reading all notifications (no filter)
	readAllReq := &notificationproto.ReadNotificationsRequest{}
	readAllResp, err := notificationServer.ReadNotifications(ctx, readAllReq)
	assert.NoError(t, err, "ReadNotifications (all) should not return an error")
	assert.Len(t, readAllResp.Notifications, 3, "Should read all 3 notifications")

	// Test reading notifications with Notification name filter
	readFilteredReq := &notificationproto.ReadNotificationsRequest{
		Filter: &notificationproto.ReadNotificationsRequest_NotificationName{
			NotificationName: "category-b",
		},
	}
	readFilteredResp, err := notificationServer.ReadNotifications(ctx, readFilteredReq)
	assert.NoError(t, err, "ReadNotifications (filtered) should not return an error")
	assert.Len(t, readFilteredResp.Notifications, 1, "Should read only 1 notification with Notification name filter")
	assert.Equal(t, "category-b", readFilteredResp.Notifications[0].NotificationName, "Should read notification with correct Notification name")
	assert.Equal(t, "Notification for category-b", readFilteredResp.Notifications[0].Message, "Should read notification with correct message")

	// Test reading notifications with non-existent Notification name
	readNonExistentReq := &notificationproto.ReadNotificationsRequest{
		Filter: &notificationproto.ReadNotificationsRequest_NotificationName{
			NotificationName: "non-existent-category",
		},
	}
	readNonExistentResp, err := notificationServer.ReadNotifications(ctx, readNonExistentReq)
	assert.NoError(t, err, "ReadNotifications (non-existent Notification name) should not return an error")
	assert.Len(t, readNonExistentResp.Notifications, 0, "Should read no notifications for non-existent Notification name")
}

func TestMarkNotificationAsRead_NotificationNameFilter(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle,
		audit.NewDefaultAuditLogger(), ripublisher.NewRequestInsightPublisherMock())

	// Create notifications with different Notification names
	dedupKeys := []string{"category-a", "category-b", "category-c"}
	for _, dedupKey := range dedupKeys {
		req := &notificationproto.CreateNotificationRequest{
			OpaqueUserId:     string_ptr("test-opaque-user-id"),
			Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
			Message:          fmt.Sprintf("Notification for %s", dedupKey),
			NotificationName: dedupKey,
			DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		}

		_, err := notificationServer.CreateNotification(ctx, req)
		assert.NoError(t, err, fmt.Sprintf("CreateNotification for %s should not return an error", dedupKey))
	}

	// Test marking notifications as read with specific Notification name
	markReadReq := &notificationproto.MarkNotificationsAsReadRequest{
		NotificationsToMark: []*notificationproto.NotificationMarkRequest{
			{
				Key: &notificationproto.NotificationMarkRequest_NotificationName{
					NotificationName: "category-b",
				},
			},
		},
	}
	markReadResp, err := notificationServer.MarkNotificationsAsRead(ctx, markReadReq)
	assert.NoError(t, err, "MarkNotificationAsRead (filtered) should not return an error")
	assert.Len(t, markReadResp.MarkedNotifications, 1, "Should mark only 1 notification with Notification name filter")
	assert.Equal(t, "category-b", markReadResp.MarkedNotifications[0].NotificationName, "Should mark notification with correct Notification name")

	// Verify that only the filtered notification was marked as read
	readAllReq := &notificationproto.ReadNotificationsRequest{}
	readAllResp, err := notificationServer.ReadNotifications(ctx, readAllReq)
	assert.NoError(t, err, "ReadNotifications should not return an error")
	assert.Len(t, readAllResp.Notifications, 2, "Should have 2 remaining unread notifications")

	// Verify the remaining notifications are the correct ones
	remainingDedupKeys := make(map[string]bool)
	for _, notification := range readAllResp.Notifications {
		remainingDedupKeys[notification.NotificationName] = true
	}
	assert.True(t, remainingDedupKeys["category-a"], "category-a should still be unread")
	assert.True(t, remainingDedupKeys["category-c"], "category-c should still be unread")
	assert.False(t, remainingDedupKeys["category-b"], "category-b should be marked as read")
}

func TestMarkNotificationAsRead_FilteredByNotificationName(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle, audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock())

	// Create notifications with different Notification names
	for _, dedupKey := range []string{"key-1", "key-2", "key-3"} {
		req := &notificationproto.CreateNotificationRequest{
			OpaqueUserId:     string_ptr("test-opaque-user-id"),
			Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
			Message:          fmt.Sprintf("Notification for %s", dedupKey),
			NotificationName: dedupKey,
			DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		}

		_, err := notificationServer.CreateNotification(ctx, req)
		assert.NoError(t, err, fmt.Sprintf("CreateNotification for %s should not return an error", dedupKey))
	}

	// Create additional notifications with different Notification names
	for _, dedupKey := range []string{"category-a", "category-b"} {
		req := &notificationproto.CreateNotificationRequest{
			OpaqueUserId:     string_ptr("test-opaque-user-id"),
			Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
			Message:          fmt.Sprintf("Categorized notification for %s", dedupKey),
			NotificationName: dedupKey,
			DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		}

		_, err := notificationServer.CreateNotification(ctx, req)
		assert.NoError(t, err, fmt.Sprintf("CreateNotification for %s should not return an error", dedupKey))
	}

	// Test marking notifications as read filtered by specific Notification name
	markReadReq := &notificationproto.MarkNotificationsAsReadRequest{
		NotificationsToMark: []*notificationproto.NotificationMarkRequest{
			{
				Key: &notificationproto.NotificationMarkRequest_NotificationName{
					NotificationName: "key-2", // Only mark key-2
				},
			},
		},
	}
	markReadResp, err := notificationServer.MarkNotificationsAsRead(ctx, markReadReq)
	assert.NoError(t, err, "MarkNotificationAsRead should not return an error")

	// Should return only 1 notification (key-2)
	assert.Len(t, markReadResp.MarkedNotifications, 1, "Should mark only 1 notification with specific Notification name")
	assert.Equal(t, "key-2", markReadResp.MarkedNotifications[0].NotificationName, "Should mark the correct notification")

	// Verify 4 notifications are still unread
	readAllReq := &notificationproto.ReadNotificationsRequest{}
	readAllResp, err := notificationServer.ReadNotifications(ctx, readAllReq)
	assert.NoError(t, err, "ReadNotifications should not return an error")
	assert.Len(t, readAllResp.Notifications, 4, "4 notifications should still be unread")
}

func TestDeleteNotifications_CategorizedAndRegular(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle, audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock())

	// Create notifications with different Notification names
	regularNotificationIDs := []string{}
	for i := 1; i <= 2; i++ {
		req := &notificationproto.CreateNotificationRequest{
			OpaqueUserId:     string_ptr("test-opaque-user-id"),
			Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
			Message:          fmt.Sprintf("Regular notification %d", i),
			NotificationName: fmt.Sprintf("regular-delete-%d", i),
			DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		}

		resp, err := notificationServer.CreateNotification(ctx, req)
		assert.NoError(t, err, fmt.Sprintf("CreateNotification %d should not return an error", i))
		assert.False(t, resp.Updated, fmt.Sprintf("CreateNotification %d should return Updated=true", i))
		regularNotificationIDs = append(regularNotificationIDs, req.NotificationName)
	}

	// Create notifications with different Notification names
	categorizedNotificationIDs := []string{}
	for _, dedupKey := range []string{"category-a", "category-b"} {
		req := &notificationproto.CreateNotificationRequest{
			OpaqueUserId:     string_ptr("test-opaque-user-id"),
			Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
			Message:          fmt.Sprintf("Categorized notification for %s", dedupKey),
			NotificationName: dedupKey,
			DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		}

		resp, err := notificationServer.CreateNotification(ctx, req)
		assert.NoError(t, err, fmt.Sprintf("CreateNotification for %s should not return an error", dedupKey))
		assert.False(t, resp.Updated, fmt.Sprintf("CreateNotification for %s should return Updated=true", dedupKey))
		categorizedNotificationIDs = append(categorizedNotificationIDs, req.NotificationName)
	}

	// Verify we have 4 notifications total
	readAllReq := &notificationproto.ReadNotificationsRequest{}
	readAllResp, err := notificationServer.ReadNotifications(ctx, readAllReq)
	assert.NoError(t, err, "ReadNotifications should not return an error")
	assert.Len(t, readAllResp.Notifications, 4, "Should have 4 notifications initially")

	// Delete all notifications (both regular and categorized)
	allNotificationNames := append(regularNotificationIDs, categorizedNotificationIDs...)
	deleteReq := &notificationproto.DeleteNotificationsRequest{
		NotificationNames: allNotificationNames,
	}

	deleteResp, err := notificationServer.DeleteNotifications(ctx, deleteReq)
	assert.NoError(t, err, "DeleteNotifications should not return an error")
	assert.Len(t, deleteResp.DeletedNotifications, 4, "Should delete all 4 notifications")

	// Verify all expected Notification names were deleted
	deletedKeysSet := make(map[string]bool)
	for _, key := range deleteResp.DeletedNotifications {
		deletedKeysSet[key.NotificationName] = true
	}
	for _, expectedKey := range allNotificationNames {
		assert.True(t, deletedKeysSet[expectedKey], "Expected Notification name %s should be deleted", expectedKey)
	}

	// Verify all notifications are deleted
	readAfterDeleteReq := &notificationproto.ReadNotificationsRequest{}
	readAfterDeleteResp, err := notificationServer.ReadNotifications(ctx, readAfterDeleteReq)
	assert.NoError(t, err, "ReadNotifications after delete should not return an error")
	assert.Len(t, readAfterDeleteResp.Notifications, 0, "Should have no notifications after deletion")
}

func TestReadNotifications_ReadReceipts(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle, audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock())

	// Create a notification
	createReq := &notificationproto.CreateNotificationRequest{
		OpaqueUserId:     string_ptr("test-opaque-user-id"),
		Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
		Message:          "Test notification for receipts",
		NotificationName: "test-receipts-key",
		DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
	}
	_, err := notificationServer.CreateNotification(ctx, createReq)
	assert.NoError(t, err, "CreateNotification should not return an error")

	// Verify notification exists in unread notifications
	readUnreadReq := &notificationproto.ReadNotificationsRequest{
		ReadReceipts: false, // Read unread notifications (default)
	}
	unreadResp, err := notificationServer.ReadNotifications(ctx, readUnreadReq)
	assert.NoError(t, err, "ReadNotifications (unread) should not return an error")
	assert.Len(t, unreadResp.Notifications, 1, "Should have 1 unread notification")

	// Verify no receipts exist yet
	readReceiptsReq := &notificationproto.ReadNotificationsRequest{
		ReadReceipts: true, // Read receipts
	}
	receiptsResp, err := notificationServer.ReadNotifications(ctx, readReceiptsReq)
	assert.NoError(t, err, "ReadNotifications (receipts) should not return an error")
	assert.Len(t, receiptsResp.Notifications, 0, "Should have no receipts initially")

	// Mark notification as read - need to get the Notification name first
	unreadResp2, err := notificationServer.ReadNotifications(ctx, readUnreadReq)
	assert.NoError(t, err, "ReadNotifications should not return an error")
	assert.Len(t, unreadResp2.Notifications, 1, "Should have 1 unread notification")

	markReadReq := &notificationproto.MarkNotificationsAsReadRequest{
		NotificationsToMark: []*notificationproto.NotificationMarkRequest{
			{
				Key: &notificationproto.NotificationMarkRequest_NotificationName{
					NotificationName: unreadResp2.Notifications[0].NotificationName,
				},
			},
		},
	}
	markReadResp, err := notificationServer.MarkNotificationsAsRead(ctx, markReadReq)
	assert.NoError(t, err, "MarkNotificationAsRead should not return an error")
	assert.Len(t, markReadResp.MarkedNotifications, 1, "Should mark 1 notification as read")

	// Verify notification no longer exists in unread notifications
	unreadResp3, err := notificationServer.ReadNotifications(ctx, readUnreadReq)
	assert.NoError(t, err, "ReadNotifications (unread) should not return an error after marking as read")
	assert.Len(t, unreadResp3.Notifications, 0, "Should have no unread notifications after marking as read")

	// Verify notification now exists in receipts
	receiptsResp2, err := notificationServer.ReadNotifications(ctx, readReceiptsReq)
	assert.NoError(t, err, "ReadNotifications (receipts) should not return an error after marking as read")
	assert.Len(t, receiptsResp2.Notifications, 1, "Should have 1 receipt after marking as read")

	receipt := receiptsResp2.Notifications[0]
	assert.Equal(t, "Test notification for receipts", receipt.Message, "Receipt message should match")
	assert.Equal(t, "test-receipts-key", receipt.NotificationName, "Receipt Notification name should match")
	assert.NotNil(t, receipt.ReceivedAt, "Receipt should have ReceivedAt timestamp")
}

func TestReadNotifications_ReadReceiptsWithFilter(t *testing.T) {
	// Create context with auth claims
	ctx := createTestContext(t, "test-tenant-id", "test-user-id", "test-opaque-user-id")

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle, audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock())

	// Create multiple notifications
	dedupKeys := []string{"receipt-key-1", "receipt-key-2", "receipt-key-3"}
	for _, dedupKey := range dedupKeys {
		createReq := &notificationproto.CreateNotificationRequest{
			OpaqueUserId:     string_ptr("test-opaque-user-id"),
			Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
			Message:          fmt.Sprintf("Notification for %s", dedupKey),
			NotificationName: dedupKey,
			DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		}
		_, err := notificationServer.CreateNotification(ctx, createReq)
		assert.NoError(t, err, fmt.Sprintf("CreateNotification for %s should not return an error", dedupKey))
	}

	// Mark all notifications as read - need to get all Notification names first
	readAllReq := &notificationproto.ReadNotificationsRequest{}
	readAllResp, err := notificationServer.ReadNotifications(ctx, readAllReq)
	assert.NoError(t, err, "ReadNotifications should not return an error")
	assert.Len(t, readAllResp.Notifications, 3, "Should have 3 unread notifications")

	// Build the mark request with all Notification names
	notificationsToMark := make([]*notificationproto.NotificationMarkRequest, 0, len(readAllResp.Notifications))
	for _, notification := range readAllResp.Notifications {
		notificationsToMark = append(notificationsToMark, &notificationproto.NotificationMarkRequest{
			Key: &notificationproto.NotificationMarkRequest_NotificationName{
				NotificationName: notification.NotificationName,
			},
		})
	}

	markReadReq := &notificationproto.MarkNotificationsAsReadRequest{
		NotificationsToMark: notificationsToMark,
	}
	markReadResp, err := notificationServer.MarkNotificationsAsRead(ctx, markReadReq)
	assert.NoError(t, err, "MarkNotificationAsRead should not return an error")
	assert.Len(t, markReadResp.MarkedNotifications, 3, "Should mark 3 notifications as read")

	// Read all receipts
	readAllReceiptsReq := &notificationproto.ReadNotificationsRequest{
		ReadReceipts: true,
	}
	allReceiptsResp, err := notificationServer.ReadNotifications(ctx, readAllReceiptsReq)
	assert.NoError(t, err, "ReadNotifications (all receipts) should not return an error")
	assert.Len(t, allReceiptsResp.Notifications, 3, "Should have 3 receipts")

	// Read receipts filtered by Notification name
	readFilteredReceiptsReq := &notificationproto.ReadNotificationsRequest{
		ReadReceipts: true,
		Filter: &notificationproto.ReadNotificationsRequest_NotificationName{
			NotificationName: "receipt-key-2",
		},
	}
	filteredReceiptsResp, err := notificationServer.ReadNotifications(ctx, readFilteredReceiptsReq)
	assert.NoError(t, err, "ReadNotifications (filtered receipts) should not return an error")
	assert.Len(t, filteredReceiptsResp.Notifications, 1, "Should have 1 filtered receipt")
	assert.Equal(t, "receipt-key-2", filteredReceiptsResp.Notifications[0].NotificationName, "Filtered receipt should have correct Notification name")
	assert.Equal(t, "Notification for receipt-key-2", filteredReceiptsResp.Notifications[0].Message, "Filtered receipt should have correct message")

	// Read receipts with non-existent Notification name
	readNonExistentReceiptsReq := &notificationproto.ReadNotificationsRequest{
		ReadReceipts: true,
		Filter: &notificationproto.ReadNotificationsRequest_NotificationName{
			NotificationName: "non-existent-key",
		},
	}
	nonExistentReceiptsResp, err := notificationServer.ReadNotifications(ctx, readNonExistentReceiptsReq)
	assert.NoError(t, err, "ReadNotifications (non-existent receipts) should not return an error")
	assert.Len(t, nonExistentReceiptsResp.Notifications, 0, "Should have no receipts for non-existent Notification name")
}

func TestForgetUser_ComprehensiveDataDeletion(t *testing.T) {
	// Create contexts for two different users
	ctx1 := createTestContext(t, "test-tenant-id", "test-user-id-1", "test-opaque-user-id-1")
	ctx2 := createTestContext(t, "test-tenant-id", "test-user-id-2", "test-opaque-user-id-2")

	// Create admin context with required scopes for ForgetUser
	adminCtx := context.Background()
	adminClaims := &auth.AugmentClaims{
		TenantID:    "test-tenant-id",
		UserID:      "admin-user-id",
		ServiceName: "test-service",
		Scope:       []string{"NOTIFICATION_ADMIN", "PII_ADMIN", "NOTIFICATION_RW"},
	}
	adminCtx = adminClaims.NewContext(adminCtx)
	requestId := requestcontext.NewRandomRequestId()
	requestContext := requestcontext.New(requestId, requestcontext.NewRandomRequestSessionId(), "test-request-source",
		secretstring.New("test-auth-token"))
	adminCtx = requestcontext.NewIncomingContext(adminCtx, requestContext)

	// Create a fake BigTable client
	fakeBigtable := fakebigtableproxy.NewInMemoryBigtable([]string{"NOTIFICATION"})

	// Create a local feature flag handle
	featureFlagHandle := featureflags.NewLocalFeatureFlagHandler()

	// Create a test server
	notificationServer := NewNotificationServer(fakeBigtable, featureFlagHandle, audit.NewDefaultAuditLogger(),
		ripublisher.NewRequestInsightPublisherMock())

	// Step 1: Create notifications for both users
	user1Notifications := []string{"user1-notif-1", "user1-notif-2", "user1-category-a"}
	user2Notifications := []string{"user2-notif-1", "user2-category-b"}

	for _, notifName := range user1Notifications {
		createReq := &notificationproto.CreateNotificationRequest{
			OpaqueUserId:     string_ptr("test-opaque-user-id-1"),
			Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
			Message:          fmt.Sprintf("Notification %s for user 1", notifName),
			NotificationName: notifName,
			DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		}
		_, err := notificationServer.CreateNotification(ctx1, createReq)
		assert.NoError(t, err, "CreateNotification should not return an error for user 1")
	}

	for _, notifName := range user2Notifications {
		createReq := &notificationproto.CreateNotificationRequest{
			OpaqueUserId:     string_ptr("test-opaque-user-id-2"),
			Level:            notificationproto.NotificationLevel_NOTIFICATION_LEVEL_INFO,
			Message:          fmt.Sprintf("Notification %s for user 2", notifName),
			NotificationName: notifName,
			DisplayType:      notificationproto.NotificationDisplayType_DISPLAY_TYPE_BANNER,
		}
		_, err := notificationServer.CreateNotification(ctx2, createReq)
		assert.NoError(t, err, "CreateNotification should not return an error for user 2")
	}

	// Step 2: Mark some notifications as read to create receipts
	markAsReadReq1 := &notificationproto.MarkNotificationsAsReadRequest{
		NotificationsToMark: []*notificationproto.NotificationMarkRequest{
			{
				Key: &notificationproto.NotificationMarkRequest_NotificationName{
					NotificationName: "user1-notif-1",
				},
			},
		},
	}
	_, err := notificationServer.MarkNotificationsAsRead(ctx1, markAsReadReq1)
	assert.NoError(t, err, "MarkNotificationsAsRead should not return an error for user 1")

	markAsReadReq2 := &notificationproto.MarkNotificationsAsReadRequest{
		NotificationsToMark: []*notificationproto.NotificationMarkRequest{
			{
				Key: &notificationproto.NotificationMarkRequest_NotificationName{
					NotificationName: "user2-notif-1",
				},
			},
		},
	}
	_, err = notificationServer.MarkNotificationsAsRead(ctx2, markAsReadReq2)
	assert.NoError(t, err, "MarkNotificationsAsRead should not return an error for user 2")

	// Step 3: Verify initial state - both users have notifications and receipts
	// User 1 should have 2 unread notifications and 1 receipt
	readUser1Notifications := &notificationproto.ReadNotificationsRequest{}
	user1NotifResp, err := notificationServer.ReadNotifications(ctx1, readUser1Notifications)
	assert.NoError(t, err, "ReadNotifications should not return an error for user 1")
	assert.Len(t, user1NotifResp.Notifications, 2, "User 1 should have 2 unread notifications")

	readUser1Receipts := &notificationproto.ReadNotificationsRequest{ReadReceipts: true}
	user1ReceiptResp, err := notificationServer.ReadNotifications(ctx1, readUser1Receipts)
	assert.NoError(t, err, "ReadNotifications (receipts) should not return an error for user 1")
	assert.Len(t, user1ReceiptResp.Notifications, 1, "User 1 should have 1 receipt")

	// User 2 should have 1 unread notification and 1 receipt
	readUser2Notifications := &notificationproto.ReadNotificationsRequest{}
	user2NotifResp, err := notificationServer.ReadNotifications(ctx2, readUser2Notifications)
	assert.NoError(t, err, "ReadNotifications should not return an error for user 2")
	assert.Len(t, user2NotifResp.Notifications, 1, "User 2 should have 1 unread notification")

	readUser2Receipts := &notificationproto.ReadNotificationsRequest{ReadReceipts: true}
	user2ReceiptResp, err := notificationServer.ReadNotifications(ctx2, readUser2Receipts)
	assert.NoError(t, err, "ReadNotifications (receipts) should not return an error for user 2")
	assert.Len(t, user2ReceiptResp.Notifications, 1, "User 2 should have 1 receipt")

	// Step 4: Execute ForgetUser for user 1
	forgetUserReq := &notificationproto.ForgetUserRequest{
		OpaqueUserId: string_ptr("test-opaque-user-id-1"),
	}
	_, err = notificationServer.ForgetUser(adminCtx, forgetUserReq)
	assert.NoError(t, err, "ForgetUser should not return an error")

	// Step 5: Verify user 1's data is completely deleted
	user1NotifRespAfter, err := notificationServer.ReadNotifications(ctx1, readUser1Notifications)
	assert.NoError(t, err, "ReadNotifications should not return an error for user 1 after ForgetUser")
	assert.Len(t, user1NotifRespAfter.Notifications, 0, "User 1 should have no notifications after ForgetUser")

	user1ReceiptRespAfter, err := notificationServer.ReadNotifications(ctx1, readUser1Receipts)
	assert.NoError(t, err, "ReadNotifications (receipts) should not return an error for user 1 after ForgetUser")
	assert.Len(t, user1ReceiptRespAfter.Notifications, 0, "User 1 should have no receipts after ForgetUser")

	// Step 6: Verify user 2's data is unaffected
	user2NotifRespAfter, err := notificationServer.ReadNotifications(ctx2, readUser2Notifications)
	assert.NoError(t, err, "ReadNotifications should not return an error for user 2 after ForgetUser")
	assert.Len(t, user2NotifRespAfter.Notifications, 1, "User 2 should still have 1 unread notification after ForgetUser")

	user2ReceiptRespAfter, err := notificationServer.ReadNotifications(ctx2, readUser2Receipts)
	assert.NoError(t, err, "ReadNotifications (receipts) should not return an error for user 2 after ForgetUser")
	assert.Len(t, user2ReceiptRespAfter.Notifications, 1, "User 2 should still have 1 receipt after ForgetUser")
}
