package main

import (
	"context"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"time"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/types/known/timestamppb"

	bigtableproto "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	"github.com/augmentcode/augment/base/proto/redact"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	proxyproto "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	notificationproto "github.com/augmentcode/augment/services/notification/proto"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
)

const (
	notificationsFamily = "Notifications"
	receiptsFamily      = "Receipts"
	dataColumnQualifier = "data"
)

// maxNotificationsPerRequest limits the number of notifications returned in a single request
// to prevent excessive memory usage and improve response times
var maxNotificationsPerRequest = featureflags.NewIntFlag("notification_max_notifications_per_request", 5)

// NotificationServer implements the NotificationService gRPC service.
// It manages user notifications stored in BigTable with support for deduplication,
// occurrence counting, and read receipts.
type NotificationServer struct {
	notificationproto.UnimplementedNotificationServiceServer
	proxyClient             bigtableproxy.BigtableProxyClient
	featureFlags            featureflags.FeatureFlagHandle
	auditLogger             *audit.AuditLogger
	requestInsightPublisher ripublisher.RequestInsightPublisher
}

// NewNotificationServer creates a new notification server
func NewNotificationServer(proxyClient bigtableproxy.BigtableProxyClient, featureFlags featureflags.FeatureFlagHandle, auditLogger *audit.AuditLogger, requestInsightPublisher ripublisher.RequestInsightPublisher) *NotificationServer {
	return &NotificationServer{
		proxyClient:             proxyClient,
		featureFlags:            featureFlags,
		auditLogger:             auditLogger,
		requestInsightPublisher: requestInsightPublisher,
	}
}

type TenantMessage interface {
	GetTenantId() string
}

func getTenantID(claims *auth.AugmentClaims, req TenantMessage) (string, error) {
	claimTenantID, _ := claims.GetTenantID()
	switch {
	case claims.AllowsAllTenants() && req.GetTenantId() == "":
		return "", status.Error(codes.Internal, "tenant_id must be set in either token or request")
	case claims.AllowsAllTenants():
		return req.GetTenantId(), nil
	case req.GetTenantId() == "":
		return claimTenantID, nil
	case claimTenantID != req.GetTenantId():
		// Note: No context available in this function, so log.Error() remains unchanged
		log.Error().Msgf("Auth claims give permission for tenant %s, but request has tenant %s", claimTenantID, req.GetTenantId())
		return "", status.Error(codes.PermissionDenied, "different tenant ID in token and request")
	default:
		return claimTenantID, nil
	}
}

type UserMessage interface {
	GetOpaqueUserId() string
}

func GetUserID(claims *auth.AugmentClaims, req UserMessage) (*string, error) {
	if claims.ServiceName == "" && claims.OpaqueUserIDType != authentitiesproto.UserId_INTERNAL_IAP.String() {
		if req.GetOpaqueUserId() == "" {
			return &claims.OpaqueUserID, nil
		}
		if req.GetOpaqueUserId() == claims.OpaqueUserID {
			return &claims.OpaqueUserID, nil
		}
		// only IAP tokens and service tokens are allowed to specify a user ID
		return nil, status.Error(codes.PermissionDenied, "Not allowed to specify user_id")
	}
	// background or IAP
	if req.GetOpaqueUserId() == "" {
		// needs to be set
		return nil, status.Error(codes.InvalidArgument, "user_id is required")
	}
	user_id := req.GetOpaqueUserId()
	return &user_id, nil
}

func notificationNameToID(notificationName string) string {
	hash := sha256.Sum256([]byte(notificationName))
	return base64.URLEncoding.EncodeToString(hash[:])
}

func validationNotificationId(notificationId string) error {
	if notificationId == "" {
		return status.Error(codes.InvalidArgument, "notification_id is required")
	}
	// check for valid base64 string (SHA256 hash base64-encoded produces 44 characters)
	if len(notificationId) != 44 {
		return status.Error(codes.InvalidArgument, "notification_id must be 44 characters long")
	}
	_, err := base64.URLEncoding.DecodeString(notificationId)
	if err != nil {
		return status.Error(codes.InvalidArgument, "notification_id is not valid base64")
	}
	return nil
}

// CreateNotification implements the NotificationService.CreateNotification RPC.
// It creates a new notification or updates an existing one if the notification name, matches.
// When updating, it increments the occurrence count to track repeated notifications.
func (s *NotificationServer) CreateNotification(ctx context.Context, req *notificationproto.CreateNotificationRequest) (*notificationproto.CreateNotificationResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get request context")
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)

	if !authInfo.HasScope(tokenscopesproto.Scope_NOTIFICATION_ADMIN) {
		log.Ctx(ctx).Error().Msg("Missing required scope")
		return nil, status.Error(codes.PermissionDenied, "Missing required scope")
	}
	tenantID, err := getTenantID(authInfo, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get tenant ID")
		return nil, err
	}
	targetUserID, err := GetUserID(authInfo, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get user ID")
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("CreateNotification: tenantId=%s, userId=%s, req=%s", tenantID, userIDToString(targetUserID), redact.ToRedactString(req))

	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("CreateNotification request for user %s", *targetUserID),
		audit.NewUser(*targetUserID),
		requestContext,
		audit.NewProtoRequest(req),
		audit.NewTenantID(tenantID),
	)

	if req.Message == "" {
		return nil, status.Error(codes.InvalidArgument, "message is required")
	}
	if req.NotificationName == "" {
		return nil, status.Error(codes.InvalidArgument, "notification_name is required")
	}
	for _, actionItem := range req.ActionItems {
		if actionItem.Title == "" {
			return nil, status.Error(codes.InvalidArgument, "action_item_title is required")
		}
	}

	resp, err := s.createOrUpdateNotification(ctx, req, tenantID, targetUserID, authInfo, requestContext)

	riEvent := ripublisher.NewGenericEvent()
	riEvent.Event = &riproto.GenericEvent_NotificationCreate{
		NotificationCreate: &riproto.NotificationCreate{
			Request:      req,
			Response:     resp,
			OpaqueUserId: userIDToString(targetUserID),
		},
	}
	s.requestInsightPublisher.PublishGenericEvent(ctx, requestContext.RequestSessionId.String(), riEvent)

	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to create notification")
		return nil, err
	}

	return resp, nil
}

// createOrUpdateNotification handles creating or updating notifications with deduplication.
// It uses BigTable's CheckAndMutateRow for atomic operations to prevent race conditions
// when multiple requests try to create/update the same notification simultaneously.
// Row key format: {opaque_user_id}#{notification_id}, which is the SHA256 hash of the notification name
func (s *NotificationServer) createOrUpdateNotification(
	ctx context.Context,
	req *notificationproto.CreateNotificationRequest,
	tenantID string,
	targetUserID *string,
	authInfo *auth.AugmentClaims,
	requestContext *requestcontext.RequestContext,
) (*notificationproto.CreateNotificationResponse, error) {
	notificationID := notificationNameToID(req.NotificationName)
	rowKey := notificationID

	predicateFilter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(notificationsFamily),
		bigtableproxy.LatestNFilter(1),
	).Proto()

	updated := false

	newNotification := &notificationproto.Notification{
		Level:            req.GetLevel(),
		Message:          req.GetMessage(),
		ActionItems:      req.GetActionItems(),
		DisplayType:      req.GetDisplayType(),
		CreatedAt:        timestamppb.Now(),
		NotificationName: req.NotificationName,
		NotificationId:   notificationID,
	}

	newNotificationBytes, err := proto.Marshal(newNotification)
	if err != nil {
		log.Error().Err(err).Msg("Failed to marshal new notification")
		return nil, status.Error(codes.Internal, "Failed to process notification")
	}

	mutations := []*bigtableproto.Mutation{
		{
			Mutation: &bigtableproto.Mutation_SetCell_{
				SetCell: &bigtableproto.Mutation_SetCell{
					FamilyName:      notificationsFamily,
					ColumnQualifier: []byte(dataColumnQualifier),
					TimestampMicros: time.Now().UnixMicro(),
					Value:           newNotificationBytes,
				},
			},
		},
	}

	response, err := s.proxyClient.CheckAndMutateUserRow(
		ctx,
		tenantID,
		targetUserID,
		proxyproto.TableName_NOTIFICATION,
		[]byte(rowKey),
		predicateFilter,
		mutations,
		mutations,
		requestContext,
	)
	log.Ctx(ctx).Info().Msgf("createOrUpdateNotification: CheckAndMutateUserRow response: %v", response)
	if err != nil {
		log.Info().Msgf("createOrUpdateNotification: CheckAndMutateUserRow error: %v", err)
		return nil, err
	}

	if !response.PredicateMatched {
		log.Ctx(ctx).Info().
			Msg("Created new notification")
	} else {
		updated = true
	}

	resp := &notificationproto.CreateNotificationResponse{
		Updated:        updated,
		NotificationId: notificationID,
	}
	log.Ctx(ctx).Info().Msgf("CreateNotification: resp=%s", redact.ToRedactString(resp))
	return resp, nil
}

// ReadNotifications implements the NotificationService.ReadNotifications RPC.
// It retrieves notifications without marking them as read. Supports filtering by
// notification key and reading from either notifications or receipts family.
func (s *NotificationServer) ReadNotifications(ctx context.Context, req *notificationproto.ReadNotificationsRequest) (*notificationproto.ReadNotificationsResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get request context")
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)
	if !authInfo.HasScope(tokenscopesproto.Scope_NOTIFICATION_R) {
		log.Ctx(ctx).Error().Msg("Missing required scope")
		return nil, status.Error(codes.PermissionDenied, "Missing required scope")
	}
	tenantID, err := getTenantID(authInfo, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get tenant ID")
		return nil, err
	}
	targetUserID, err := GetUserID(authInfo, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get user ID")
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("ReadNotifications: tenantId=%s, userId=%s, req=%s", tenantID, userIDToString(targetUserID), redact.ToRedactString(req))

	_, ok = authInfo.GetIapEmail()
	if ok {
		s.auditLogger.WriteAuditLog(
			authInfo,
			fmt.Sprintf("ReadNotifications request for user %s", *targetUserID),
			audit.NewUser(*targetUserID),
			requestContext,
			audit.NewProtoRequest(req),
			audit.NewTenantID(tenantID),
		)
	}

	log.Ctx(ctx).Info().
		Msgf("Reading notifications: req %s", redact.ToRedactString(req))

	var rowSet *bigtableproto.RowSet

	switch filter := req.GetFilter().(type) {
	case *notificationproto.ReadNotificationsRequest_NotificationName:
		dedupRowKey := notificationNameToID(filter.NotificationName)
		rowSet = &bigtableproto.RowSet{
			RowKeys: [][]byte{[]byte(dedupRowKey)},
		}
	case *notificationproto.ReadNotificationsRequest_NotificationId:
		err := validationNotificationId(filter.NotificationId)
		if err != nil {
			return nil, err
		}
		rowSet = &bigtableproto.RowSet{
			RowKeys: [][]byte{[]byte(filter.NotificationId)},
		}
	case nil:
		rowSet = &bigtableproto.RowSet{
			RowRanges: []*bigtableproto.RowRange{
				{
					StartKey: &bigtableproto.RowRange_StartKeyOpen{
						StartKeyOpen: []byte("\x00"),
					},
					EndKey: &bigtableproto.RowRange_EndKeyOpen{
						EndKeyOpen: []byte("\xFF"),
					},
				},
			},
		}
	}

	familyName := notificationsFamily
	if req.ReadReceipts {
		familyName = receiptsFamily
	}

	var filter *bigtableproto.RowFilter
	if req.ReadAllVersions {
		filter = bigtableproxy.FamilyFilter(familyName).Proto()
	} else {
		filter = bigtableproxy.ChainFilters(
			bigtableproxy.FamilyFilter(familyName),
			bigtableproxy.LatestNFilter(1),
		).Proto()
	}

	rows, err := s.proxyClient.ReadUserRows(
		ctx,
		tenantID,
		targetUserID,
		proxyproto.TableName_NOTIFICATION,
		rowSet,
		filter,
		func() int64 {
			limit, err := maxNotificationsPerRequest.Get(s.featureFlags)
			if err != nil {
				log.Ctx(ctx).Warn().Err(err).Msg("Failed to get max notifications limit from feature flags")
			}
			return int64(limit)
		}(),
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read rows")
		return nil, status.Error(codes.Internal, "Failed to retrieve notifications")
	}

	notifications := []*notificationproto.Notification{}

	for _, row := range rows {
		for _, cell := range row.Cells {
			if cell.FamilyName != familyName {
				continue
			}

			notification := &notificationproto.Notification{}
			err := proto.Unmarshal(cell.Value, notification)
			if err != nil {
				log.Ctx(ctx).Error().Err(err).Msg("Failed to unmarshal notification")
				continue
			}

			notifications = append(notifications, notification)
		}
	}

	resp := &notificationproto.ReadNotificationsResponse{
		Notifications: notifications,
	}
	log.Ctx(ctx).Info().Msgf("ReadNotifications: resp=%s", redact.ToRedactString(resp))
	return resp, nil
}

func userIDToString(userID *string) string {
	if userID == nil {
		return ""
	}
	return *userID
}

// MarkNotificationAsRead implements the NotificationService.MarkNotificationAsRead RPC.
// It marks notifications as read by notification keys, atomically moving them from the notifications family
// to the receipts family, setting a received timestamp. This ensures notifications
// are marked as read exactly once.
func (s *NotificationServer) MarkNotificationsAsRead(ctx context.Context, req *notificationproto.MarkNotificationsAsReadRequest) (*notificationproto.MarkNotificationsAsReadResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get request context")
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)
	if !authInfo.HasScope(tokenscopesproto.Scope_NOTIFICATION_RW) {
		log.Ctx(ctx).Error().Msg("Missing required scope")
		return nil, status.Error(codes.PermissionDenied, "Missing required scope")
	}
	tenantID, err := getTenantID(authInfo, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get tenant ID")
		return nil, err
	}
	targetUserID, err := GetUserID(authInfo, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get user ID")
		return nil, err
	}
	log.Ctx(ctx).Info().Msgf("MarkNotificationAsRead: tenantId=%s, userId=%s, req=%s", tenantID, userIDToString(targetUserID), redact.ToRedactString(req))

	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("MarkNotificationAsRead request %s for user %s", redact.ToRedactString(req), *targetUserID),
		audit.NewUser(*targetUserID),
		audit.NewProtoRequest(req),
		requestContext,
		audit.NewTenantID(tenantID),
	)

	if len(req.NotificationsToMark) == 0 {
		return nil, status.Error(codes.InvalidArgument, "notifications_to_mark is required")
	}

	log.Ctx(ctx).Info().
		Int("notifications_count", len(req.NotificationsToMark)).
		Msg("Marking notifications as read")

	// Build row set from the notification keys in the request
	rowKeys := make([][]byte, 0, len(req.NotificationsToMark))
	for _, notifToMark := range req.NotificationsToMark {
		var dedupRowKey string
		switch key := notifToMark.GetKey().(type) {
		case *notificationproto.NotificationMarkRequest_NotificationName:
			dedupRowKey = notificationNameToID(key.NotificationName)
		case *notificationproto.NotificationMarkRequest_NotificationId:
			err := validationNotificationId(key.NotificationId)
			if err != nil {
				return nil, err
			}
			dedupRowKey = key.NotificationId
		default:
			return nil, status.Error(codes.InvalidArgument, "Unknown filter type")
		}
		rowKeys = append(rowKeys, []byte(dedupRowKey))
	}

	rowSet := &bigtableproto.RowSet{
		RowKeys: rowKeys,
	}

	// Only read the latest version of each notification
	filter := bigtableproxy.ChainFilters(
		bigtableproxy.FamilyFilter(notificationsFamily),
		bigtableproxy.LatestNFilter(1),
	).Proto()

	rows, err := s.proxyClient.ReadUserRows(
		ctx,
		tenantID,
		targetUserID,
		proxyproto.TableName_NOTIFICATION,
		rowSet,
		filter,
		int64(len(req.NotificationsToMark)), // Limit to the number of requested notifications
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read rows")
		return nil, status.Error(codes.Internal, "Failed to retrieve notifications")
	}

	// Map to store notifications by their row key for processing
	notificationsByRowKey := make(map[string]*notificationproto.Notification)
	rowKeysToProcess := [][]byte{}

	for _, row := range rows {
		for _, cell := range row.Cells {
			if cell.FamilyName != notificationsFamily {
				continue
			}

			notification := &notificationproto.Notification{}
			err := proto.Unmarshal(cell.Value, notification)
			if err != nil {
				continue
			}

			notification.ReceivedAt = timestamppb.Now()

			notificationsByRowKey[string(row.RowKey)] = notification
			rowKeysToProcess = append(rowKeysToProcess, row.RowKey)
		}
	}

	if len(notificationsByRowKey) == 0 {
		resp := &notificationproto.MarkNotificationsAsReadResponse{
			MarkedNotifications: []*notificationproto.NotificationKey{},
		}
		log.Ctx(ctx).Info().Msgf("MarkNotificationAsRead: resp=%s", redact.ToRedactString(resp))
		return resp, nil
	}

	// Move notifications from pending to receipts atomically
	markedNotifications := []*notificationproto.NotificationKey{}

	for _, rowKey := range rowKeysToProcess {
		rowKeyStr := string(rowKey)
		notification, exists := notificationsByRowKey[rowKeyStr]
		if !exists {
			continue
		}

		predicateFilter := bigtableproxy.FamilyFilter(notificationsFamily).Proto()

		notificationBytes, err := proto.Marshal(notification)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).
				Msg("Failed to marshal notification with received timestamp")
			continue
		}

		trueMutations := []*bigtableproto.Mutation{
			{
				Mutation: &bigtableproto.Mutation_DeleteFromFamily_{
					DeleteFromFamily: &bigtableproto.Mutation_DeleteFromFamily{
						FamilyName: notificationsFamily,
					},
				},
			},
			{
				Mutation: &bigtableproto.Mutation_SetCell_{
					SetCell: &bigtableproto.Mutation_SetCell{
						FamilyName:      receiptsFamily,
						ColumnQualifier: []byte(dataColumnQualifier),
						TimestampMicros: time.Now().UnixMicro(),
						Value:           notificationBytes,
					},
				},
			},
		}

		response, err := s.proxyClient.CheckAndMutateUserRow(
			ctx,
			tenantID,
			targetUserID,
			proxyproto.TableName_NOTIFICATION,
			rowKey,
			predicateFilter,
			trueMutations,
			nil,
			requestContext,
		)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).
				Msg("Failed to execute CheckAndMutateRow")
			continue
		}

		if response.PredicateMatched {
			log.Ctx(ctx).Debug().
				Msg("Successfully moved notification to receipts")
			markedNotifications = append(markedNotifications, &notificationproto.NotificationKey{
				NotificationName: notification.NotificationName,
				NotificationId:   notification.NotificationId,
			})
		} else {
			log.Ctx(ctx).Warn().
				Msg("Notification no longer exists in notifications family")
		}
	}

	resp := &notificationproto.MarkNotificationsAsReadResponse{
		MarkedNotifications: markedNotifications,
	}

	riEvent := ripublisher.NewGenericEvent()
	riEvent.Event = &riproto.GenericEvent_NotificationMarkAsRead{
		NotificationMarkAsRead: &riproto.NotificationsMarkAsRead{
			Request:      req,
			Response:     resp,
			OpaqueUserId: userIDToString(targetUserID),
		},
	}
	s.requestInsightPublisher.PublishGenericEvent(ctx, requestContext.RequestSessionId.String(), riEvent)

	log.Ctx(ctx).Info().Msgf("MarkNotificationAsRead: resp=%s", redact.ToRedactString(resp))
	return resp, nil
}

// DeleteNotifications implements the NotificationService.DeleteNotifications RPC.
// It deletes notifications by their notification keys from both the notifications
// and receipts families. Row key format: {notification_id}
func (s *NotificationServer) DeleteNotifications(ctx context.Context, req *notificationproto.DeleteNotificationsRequest) (*notificationproto.DeleteNotificationsResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get request context")
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)
	if !authInfo.HasScope(tokenscopesproto.Scope_NOTIFICATION_ADMIN) {
		log.Ctx(ctx).Error().Msg("Missing required scope")
		return nil, status.Error(codes.PermissionDenied, "Missing required scope")
	}

	tenantID, err := getTenantID(authInfo, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get tenant ID")
		return nil, err
	}
	targetUserID, err := GetUserID(authInfo, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get user ID")
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("DeleteNotifications: tenantId=%s, userId=%s, req=%s", tenantID, userIDToString(targetUserID), redact.ToRedactString(req))

	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("Delete notification request %s for user %s", redact.ToRedactString(req), *targetUserID),
		audit.NewUser(*targetUserID),
		requestContext,
		audit.NewProtoRequest(req),
		audit.NewTenantID(tenantID),
	)

	if len(req.NotificationNames) == 0 {
		return nil, status.Error(codes.InvalidArgument, "notification_names is required")
	}

	log.Ctx(ctx).Info().
		Msg("Deleting notifications")

	entries := make([]*bigtableproto.MutateRowsRequest_Entry, 0, len(req.NotificationNames)+len(req.NotificationIds))
	// Map to track which key corresponds to which entry index
	indexToNotificationKey := make(map[int]*notificationproto.NotificationKey)

	for i, dedupKey := range req.NotificationNames {
		rowKey := notificationNameToID(dedupKey)

		mutations := []*bigtableproto.Mutation{
			{
				Mutation: &bigtableproto.Mutation_DeleteFromFamily_{
					DeleteFromFamily: &bigtableproto.Mutation_DeleteFromFamily{
						FamilyName: notificationsFamily,
					},
				},
			},
			{
				Mutation: &bigtableproto.Mutation_DeleteFromFamily_{
					DeleteFromFamily: &bigtableproto.Mutation_DeleteFromFamily{
						FamilyName: receiptsFamily,
					},
				},
			},
		}

		entries = append(entries, &bigtableproto.MutateRowsRequest_Entry{
			RowKey:    []byte(rowKey),
			Mutations: mutations,
		})
		indexToNotificationKey[i] = &notificationproto.NotificationKey{
			NotificationName: dedupKey,
			NotificationId:   rowKey,
		}
	}
	for i, notificationId := range req.NotificationIds {
		err := validationNotificationId(notificationId)
		if err != nil {
			return nil, err
		}

		mutations := []*bigtableproto.Mutation{
			{
				Mutation: &bigtableproto.Mutation_DeleteFromFamily_{
					DeleteFromFamily: &bigtableproto.Mutation_DeleteFromFamily{
						FamilyName: notificationsFamily,
					},
				},
			},
			{
				Mutation: &bigtableproto.Mutation_DeleteFromFamily_{
					DeleteFromFamily: &bigtableproto.Mutation_DeleteFromFamily{
						FamilyName: receiptsFamily,
					},
				},
			},
		}

		entries = append(entries, &bigtableproto.MutateRowsRequest_Entry{
			RowKey:    []byte(notificationId),
			Mutations: mutations,
		})
		indexToNotificationKey[len(req.NotificationNames)+i] = &notificationproto.NotificationKey{
			NotificationId: notificationId,
		}
	}

	responses, err := s.proxyClient.MutateUserRows(
		ctx,
		tenantID,
		targetUserID,
		proxyproto.TableName_NOTIFICATION,
		entries,
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to send delete mutation request")
		return nil, status.Error(codes.Internal, "Failed to delete notifications")
	}

	deletedNotifications := []*notificationproto.NotificationKey{}
	entryIndex := 0
	for _, response := range responses {
		for _, entry := range response.Entries {
			if entry.Status.Code == int32(codes.OK) {
				if key, exists := indexToNotificationKey[entryIndex]; exists {
					deletedNotifications = append(deletedNotifications, key)
				}
			} else {
				log.Ctx(ctx).Warn().
					Int32("code", entry.Status.Code).
					Str("message", entry.Status.Message).
					Msg("Delete mutation failed for notification")
			}
			entryIndex++
		}
	}

	resp := &notificationproto.DeleteNotificationsResponse{
		DeletedNotifications: deletedNotifications,
	}
	riEvent := ripublisher.NewGenericEvent()
	riEvent.Event = &riproto.GenericEvent_NotificationDelete{
		NotificationDelete: &riproto.NotificationsDelete{
			Request:      req,
			Response:     resp,
			OpaqueUserId: userIDToString(targetUserID),
		},
	}
	s.requestInsightPublisher.PublishGenericEvent(ctx, requestContext.RequestSessionId.String(), riEvent)

	log.Ctx(ctx).Info().Msgf("DeleteNotifications: resp=%s", redact.ToRedactString(resp))
	return resp, nil
}

// ForgetUser implements the NotificationService.ForgetUser RPC.
// It deletes all notifications and receipts for a user.
func (s *NotificationServer) ForgetUser(ctx context.Context, req *notificationproto.ForgetUserRequest) (*notificationproto.ForgetUserResponse, error) {
	requestContext, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get request context")
		return nil, err
	}
	ctx = requestContext.AnnotateLogContext(ctx)

	authInfo, ok := auth.GetAugmentClaims(ctx)
	if !ok {
		log.Ctx(ctx).Error().Msg("Failed to get auth claims from context")
		return nil, status.Error(codes.Unauthenticated, "Unauthenticated")
	}
	ctx = authInfo.AnnotateLogContext(ctx)
	if !authInfo.HasScope(tokenscopesproto.Scope_NOTIFICATION_ADMIN) {
		log.Ctx(ctx).Error().Msg("Missing required scope")
		return nil, status.Error(codes.PermissionDenied, "Missing required scope")
	}
	if !authInfo.HasScope(tokenscopesproto.Scope_PII_ADMIN) {
		log.Ctx(ctx).Error().Msg("Missing required scope")
		return nil, status.Error(codes.PermissionDenied, "Missing required scope")
	}
	if !authInfo.HasScope(tokenscopesproto.Scope_NOTIFICATION_RW) {
		log.Ctx(ctx).Error().Msg("Missing required scope")
		return nil, status.Error(codes.PermissionDenied, "Missing required scope")
	}

	tenantID, err := getTenantID(authInfo, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get tenant ID")
		return nil, err
	}
	targetUserID, err := GetUserID(authInfo, req)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to get user ID")
		return nil, err
	}

	log.Ctx(ctx).Info().Msgf("ForgetUser: tenantId=%s, userId=%s", tenantID, userIDToString(targetUserID))

	s.auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("ForgetUser request for user %s", *targetUserID),
		audit.NewUser(*targetUserID),
		requestContext,
		audit.NewProtoRequest(req),
		audit.NewTenantID(tenantID),
	)

	// Step 1: Read all rows for the user from both column families
	// Use an empty RowSet to read all rows for this user (bigtable proxy will apply user prefix automatically)
	rowSet := &bigtableproto.RowSet{
		RowRanges: []*bigtableproto.RowRange{
			{
				StartKey: &bigtableproto.RowRange_StartKeyOpen{
					StartKeyOpen: []byte("\x00"),
				},
				EndKey: &bigtableproto.RowRange_EndKeyOpen{
					EndKeyOpen: []byte("\xFF"),
				},
			},
		},
	}

	// Read from both column families to get all user data
	notificationsFilter := bigtableproxy.FamilyFilter(notificationsFamily).Proto()
	receiptsFilter := bigtableproxy.FamilyFilter(receiptsFamily).Proto()

	// Read all notifications
	notificationRows, err := s.proxyClient.ReadUserRows(
		ctx,
		tenantID,
		targetUserID,
		proxyproto.TableName_NOTIFICATION,
		rowSet,
		notificationsFilter,
		0, // No limit - read all rows
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read notification rows")
		return nil, status.Error(codes.Internal, "Failed to read user notifications")
	}

	// Read all receipts
	receiptRows, err := s.proxyClient.ReadUserRows(
		ctx,
		tenantID,
		targetUserID,
		proxyproto.TableName_NOTIFICATION,
		rowSet,
		receiptsFilter,
		0, // No limit - read all rows
		requestContext,
	)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Msg("Failed to read receipt rows")
		return nil, status.Error(codes.Internal, "Failed to read user receipts")
	}

	// Step 2: Collect all unique row keys from both families
	rowKeysToDelete := make(map[string]bool)

	for _, row := range notificationRows {
		rowKeysToDelete[string(row.RowKey)] = true
	}

	for _, row := range receiptRows {
		rowKeysToDelete[string(row.RowKey)] = true
	}

	log.Ctx(ctx).Info().Msgf("ForgetUser: Found %d unique rows to delete for user %s",
		len(rowKeysToDelete), userIDToString(targetUserID))

	// Step 3: Delete all found rows from both column families
	if len(rowKeysToDelete) > 0 {
		entries := make([]*bigtableproto.MutateRowsRequest_Entry, 0, len(rowKeysToDelete))

		for rowKey := range rowKeysToDelete {
			entries = append(entries, &bigtableproto.MutateRowsRequest_Entry{
				RowKey: []byte(rowKey),
				Mutations: []*bigtableproto.Mutation{
					{
						Mutation: &bigtableproto.Mutation_DeleteFromFamily_{
							DeleteFromFamily: &bigtableproto.Mutation_DeleteFromFamily{
								FamilyName: notificationsFamily,
							},
						},
					},
					{
						Mutation: &bigtableproto.Mutation_DeleteFromFamily_{
							DeleteFromFamily: &bigtableproto.Mutation_DeleteFromFamily{
								FamilyName: receiptsFamily,
							},
						},
					},
				},
			})
		}

		resp, err := s.proxyClient.MutateUserRows(
			ctx,
			tenantID,
			targetUserID,
			proxyproto.TableName_NOTIFICATION,
			entries,
			requestContext,
		)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Msg("Failed to send delete mutation request")
			return nil, status.Error(codes.Internal, "Failed to delete notifications")
		}

		for _, response := range resp {
			for _, entry := range response.Entries {
				if entry.Status.Code != int32(codes.OK) {
					log.Ctx(ctx).Warn().
						Int32("code", entry.Status.Code).
						Str("message", entry.Status.Message).
						Msg("Delete mutation failed for notification")
					return nil, status.Error(codes.Internal, "Failed to delete notifications")
				}
			}
		}

		log.Ctx(ctx).Info().Msgf("ForgetUser: Successfully deleted %d rows for user %s",
			len(rowKeysToDelete), userIDToString(targetUserID))
	} else {
		log.Ctx(ctx).Info().Msgf("ForgetUser: No data found to delete for user %s",
			userIDToString(targetUserID))
	}
	return &notificationproto.ForgetUserResponse{
		DeletedCount: uint32(len(rowKeysToDelete)),
	}, nil
}
