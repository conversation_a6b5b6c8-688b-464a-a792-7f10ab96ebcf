use std::{sync::Arc, time::Duration};

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::create_channel;
use tonic::transport::ClientTlsConfig;

use crate::proto::notification::{
    CreateNotificationRequest, CreateNotificationResponse, DeleteNotificationsRequest,
    DeleteNotificationsResponse, MarkNotificationsAsReadRequest, MarkNotificationsAsReadResponse,
    ReadNotificationsRequest, ReadNotificationsResponse,
};
use request_context::RequestContext;
use tracing_tonic::client::TracingService;

pub mod proto {
    pub mod notification {
        tonic::include_proto!("notification");
    }
}

#[async_trait]
pub trait NotificationClient {
    async fn create_notification(
        &self,
        request_context: &RequestContext,
        request: CreateNotificationRequest,
    ) -> Result<CreateNotificationResponse, tonic::Status>;

    async fn read_notifications(
        &self,
        request_context: &RequestContext,
        request: ReadNotificationsRequest,
    ) -> Result<ReadNotificationsResponse, tonic::Status>;

    async fn mark_notifications_as_read(
        &self,
        request_context: &RequestContext,
        request: MarkNotificationsAsReadRequest,
    ) -> Result<MarkNotificationsAsReadResponse, tonic::Status>;

    async fn delete_notifications(
        &self,
        request_context: &RequestContext,
        request: DeleteNotificationsRequest,
    ) -> Result<DeleteNotificationsResponse, tonic::Status>;
}

#[derive(Clone)]
pub struct NotificationClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    client: Arc<
        Mutex<
            Option<
                proto::notification::notification_service_client::NotificationServiceClient<
                    TracingService,
                >,
            >,
        >,
    >,
    timeout: std::time::Duration,
}

#[async_trait]
impl NotificationClient for NotificationClientImpl {
    async fn create_notification(
        &self,
        request_context: &RequestContext,
        request: CreateNotificationRequest,
    ) -> Result<CreateNotificationResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("notification client not ready: {}", e);
            tonic::Status::unavailable("notification service not ready")
        })?;

        let mut tonic_request = tonic::Request::new(request);
        tonic_request.set_timeout(self.timeout);
        request_context.annotate(tonic_request.metadata_mut());

        let response = client.create_notification(tonic_request).await?;
        Ok(response.into_inner())
    }

    async fn read_notifications(
        &self,
        request_context: &RequestContext,
        request: ReadNotificationsRequest,
    ) -> Result<ReadNotificationsResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("notification client not ready: {}", e);
            tonic::Status::unavailable("notification service not ready")
        })?;

        let mut tonic_request = tonic::Request::new(request);
        tonic_request.set_timeout(self.timeout);
        request_context.annotate(tonic_request.metadata_mut());

        let response = client.read_notifications(tonic_request).await?;
        Ok(response.into_inner())
    }

    async fn mark_notifications_as_read(
        &self,
        request_context: &RequestContext,
        request: MarkNotificationsAsReadRequest,
    ) -> Result<MarkNotificationsAsReadResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("notification client not ready: {}", e);
            tonic::Status::unavailable("notification service not ready")
        })?;

        let mut tonic_request = tonic::Request::new(request);
        tonic_request.set_timeout(self.timeout);
        request_context.annotate(tonic_request.metadata_mut());

        let response = client.mark_notifications_as_read(tonic_request).await?;
        Ok(response.into_inner())
    }

    async fn delete_notifications(
        &self,
        request_context: &RequestContext,
        request: DeleteNotificationsRequest,
    ) -> Result<DeleteNotificationsResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("notification client not ready: {}", e);
            tonic::Status::unavailable("notification service not ready")
        })?;

        let mut tonic_request = tonic::Request::new(request);
        tonic_request.set_timeout(self.timeout);
        request_context.annotate(tonic_request.metadata_mut());

        let response = client.delete_notifications(tonic_request).await?;
        Ok(response.into_inner())
    }
}

impl NotificationClientImpl {
    pub fn new(endpoint: &str, tls_config: Option<ClientTlsConfig>, timeout: Duration) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            client: Arc::new(Mutex::new(None)),
            timeout,
        }
    }

    async fn get_client(
        &self,
    ) -> Result<
        proto::notification::notification_service_client::NotificationServiceClient<TracingService>,
        tonic::transport::Error,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client = proto::notification::notification_service_client::NotificationServiceClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
            Some(c) => Ok(c.clone()),
        }
    }
}

pub struct MockNotificationClient {}

impl Default for MockNotificationClient {
    fn default() -> Self {
        Self::new()
    }
}

impl MockNotificationClient {
    pub fn new() -> Self {
        Self {}
    }
}

#[async_trait]
impl NotificationClient for MockNotificationClient {
    async fn create_notification(
        &self,
        _request_context: &RequestContext,
        _request: CreateNotificationRequest,
    ) -> Result<CreateNotificationResponse, tonic::Status> {
        Ok(CreateNotificationResponse {
            ..Default::default()
        })
    }

    async fn read_notifications(
        &self,
        _request_context: &RequestContext,
        _request: ReadNotificationsRequest,
    ) -> Result<ReadNotificationsResponse, tonic::Status> {
        Ok(ReadNotificationsResponse {
            ..Default::default()
        })
    }

    async fn mark_notifications_as_read(
        &self,
        _request_context: &RequestContext,
        _request: MarkNotificationsAsReadRequest,
    ) -> Result<MarkNotificationsAsReadResponse, tonic::Status> {
        Ok(MarkNotificationsAsReadResponse {
            ..Default::default()
        })
    }

    async fn delete_notifications(
        &self,
        _request_context: &RequestContext,
        _request: DeleteNotificationsRequest,
    ) -> Result<DeleteNotificationsResponse, tonic::Status> {
        Ok(DeleteNotificationsResponse {
            ..Default::default()
        })
    }
}
