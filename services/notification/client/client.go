package client

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	notificationproto "github.com/augmentcode/augment/services/notification/proto"
)

// NotificationClient is a client for the notification service
type NotificationClient interface {
	// CreateNotification creates a notification for a user
	CreateNotification(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.CreateNotificationRequest) (*notificationproto.CreateNotificationResponse, error)

	// ReadNotifications reads notifications for a user without marking them as read
	ReadNotifications(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.ReadNotificationsRequest) (*notificationproto.ReadNotificationsResponse, error)

	// MarkNotificationAsRead marks notifications as read by notification names
	MarkNotificationsAsRead(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.MarkNotificationsAsReadRequest) (*notificationproto.MarkNotificationsAsReadResponse, error)

	// DeleteNotifications deletes notifications for a user
	DeleteNotifications(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.DeleteNotificationsRequest) (*notificationproto.DeleteNotificationsResponse, error)

	// Close closes the client connection
	Close() error
}

type notificationClient struct {
	client notificationproto.NotificationServiceClient
	conn   *grpc.ClientConn
}

// NewNotificationClient creates a new notification client
func NewNotificationClient(endpoint string, credentials credentials.TransportCredentials) (NotificationClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(credentials),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	conn, err := grpc.Dial(endpoint, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to notification service: %v", err)
	}

	client := notificationproto.NewNotificationServiceClient(conn)
	return &notificationClient{
		client: client,
		conn:   conn,
	}, nil
}

func (c *notificationClient) CreateNotification(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.CreateNotificationRequest) (*notificationproto.CreateNotificationResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.CreateNotification(ctx, req)
}

func (c *notificationClient) ReadNotifications(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.ReadNotificationsRequest) (*notificationproto.ReadNotificationsResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.ReadNotifications(ctx, req)
}

func (c *notificationClient) MarkNotificationsAsRead(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.MarkNotificationsAsReadRequest) (*notificationproto.MarkNotificationsAsReadResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.MarkNotificationsAsRead(ctx, req)
}

func (c *notificationClient) DeleteNotifications(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.DeleteNotificationsRequest) (*notificationproto.DeleteNotificationsResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return c.client.DeleteNotifications(ctx, req)
}

func (c *notificationClient) Close() error {
	return c.conn.Close()
}

// NotificationCentralClient is a client for the notification service that manages multiple gRPC connections
// to different namespaces and routes requests based on auth claims.
//
// This client provides multi-namespace support by:
// 1. Extracting the target namespace from auth claims in the request context
// 2. Maintaining a pool of gRPC connections, one per namespace
// 3. Routing requests to the appropriate namespace-specific backend
// 4. Thread-safe connection management with RWMutex protection
//
// The client automatically creates new connections as needed and reuses existing ones.
// All connections are properly closed when the client is closed.
type NotificationCentralClient interface {
	// CreateNotification creates a notification for a user
	CreateNotification(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.CreateNotificationRequest) (*notificationproto.CreateNotificationResponse, error)

	// ReadNotifications reads notifications for a user without marking them as read
	ReadNotifications(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.ReadNotificationsRequest) (*notificationproto.ReadNotificationsResponse, error)

	// MarkNotificationAsRead marks notifications as read by notification names
	MarkNotificationsAsRead(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.MarkNotificationsAsReadRequest) (*notificationproto.MarkNotificationsAsReadResponse, error)

	// DeleteNotifications deletes notifications for a user
	DeleteNotifications(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.DeleteNotificationsRequest) (*notificationproto.DeleteNotificationsResponse, error)

	// Close closes all client connections
	Close() error

	// CreateNotificationWithNamespace creates a notification for a user using an explicit namespace.
	// This bypasses the auth claims extraction and uses the provided namespace directly.
	CreateNotificationWithNamespace(ctx context.Context, namespace string, requestContext *requestcontext.RequestContext, req *notificationproto.CreateNotificationRequest) (*notificationproto.CreateNotificationResponse, error)

	// ReadNotificationsWithNamespace reads notifications for a user using an explicit namespace.
	ReadNotificationsWithNamespace(ctx context.Context, namespace string, requestContext *requestcontext.RequestContext, req *notificationproto.ReadNotificationsRequest) (*notificationproto.ReadNotificationsResponse, error)

	// MarkNotificationAsReadWithNamespace marks notifications as read by notification names using an explicit namespace.
	MarkNotificationAsReadWithNamespace(ctx context.Context, namespace string, requestContext *requestcontext.RequestContext, req *notificationproto.MarkNotificationsAsReadRequest) (*notificationproto.MarkNotificationsAsReadResponse, error)

	// DeleteNotificationsWithNamespace deletes notifications for a user using an explicit namespace.
	DeleteNotificationsWithNamespace(ctx context.Context, namespace string, requestContext *requestcontext.RequestContext, req *notificationproto.DeleteNotificationsRequest) (*notificationproto.DeleteNotificationsResponse, error)
}

// notificationCentralClient implements NotificationCentralClient with multi-namespace support
type notificationCentralClient struct {
	// endpointTemplate is used to construct gRPC endpoints based on namespace.
	// It should contain exactly one %s placeholder that will be replaced with the namespace.
	//
	// Examples of valid endpoint templates:
	//   - "notification-svc.%s:50051" -> "notification-svc.namespace1:50051"
	//   - "notification-%s-svc:50051" -> "notification-namespace1-svc:50051"
	//   - "%s-notification.example.com:443" -> "namespace1-notification.example.com:443"
	//
	// The template must be a valid format string for fmt.Sprintf with a single string parameter.
	endpointTemplate string

	// credentials for establishing secure gRPC connections
	credentials credentials.TransportCredentials

	// connections maps namespace to its corresponding gRPC connection
	// Protected by mutex for thread-safe access
	connections map[string]*grpc.ClientConn

	// clients maps namespace to its corresponding notification service client
	// Protected by mutex for thread-safe access
	clients map[string]notificationproto.NotificationServiceClient

	// mutex protects concurrent access to connections and clients maps
	// Uses RWMutex to allow multiple concurrent readers when possible
	mutex sync.RWMutex

	// defaultNamespace is used when no namespace can be determined from context
	// If empty, requests without valid auth claims will fail
	defaultNamespace string
}

// NewNotificationCentralClient creates a new notification central client.
//
// Parameters:
//   - endpointTemplate: A format string for constructing gRPC endpoints. Must contain exactly
//     one %s placeholder for the namespace. Examples:
//   - "notification-svc.%s:50051"
//   - "notification-%s-svc:50051"
//   - "%s-notification.example.com:443"
//   - credentials: Transport credentials for secure gRPC connections
//
// The client will extract namespaces from auth claims in request contexts and route
// requests to the appropriate backend services.
func NewNotificationCentralClient(endpointTemplate string, credentials credentials.TransportCredentials) NotificationCentralClient {
	return &notificationCentralClient{
		endpointTemplate: endpointTemplate,
		credentials:      credentials,
		connections:      make(map[string]*grpc.ClientConn),
		clients:          make(map[string]notificationproto.NotificationServiceClient),
	}
}

// NewNotificationCentralClientWithDefault creates a new notification central client with a default namespace.
//
// Parameters:
//   - endpointTemplate: A format string for constructing gRPC endpoints. Must contain exactly
//     one %s placeholder for the namespace.
//   - credentials: Transport credentials for secure gRPC connections
//   - defaultNamespace: Namespace to use when auth claims are not available or don't contain
//     a shard namespace. If empty, requests without valid auth claims will fail.
//
// This constructor is useful when you want to provide a fallback namespace for requests
// that don't have proper auth context (e.g., internal service-to-service calls).
func NewNotificationCentralClientWithDefault(endpointTemplate string, credentials credentials.TransportCredentials, defaultNamespace string) NotificationCentralClient {
	return &notificationCentralClient{
		endpointTemplate: endpointTemplate,
		credentials:      credentials,
		connections:      make(map[string]*grpc.ClientConn),
		clients:          make(map[string]notificationproto.NotificationServiceClient),
		defaultNamespace: defaultNamespace,
	}
}

// getClientForNamespace returns a notification client for the given namespace,
// creating a new connection if necessary.
//
// This method implements connection pooling with thread-safe access:
// 1. First attempts to retrieve an existing client with a read lock (fast path)
// 2. If no client exists, acquires a write lock and creates a new connection
// 3. Uses double-checked locking to avoid race conditions
// 4. Constructs the endpoint using the configured template
// 5. Applies appropriate gRPC dial options including load balancing for headless services
//
// The created connections are stored in the internal maps for reuse.
func (c *notificationCentralClient) getClientForNamespace(namespace string) (notificationproto.NotificationServiceClient, error) {
	// First try to get existing client with read lock
	c.mutex.RLock()
	if client, exists := c.clients[namespace]; exists {
		c.mutex.RUnlock()
		return client, nil
	}
	c.mutex.RUnlock()

	// Need to create new connection, acquire write lock
	c.mutex.Lock()
	defer c.mutex.Unlock()

	// Double-check in case another goroutine created it while we were waiting
	if client, exists := c.clients[namespace]; exists {
		return client, nil
	}

	// Construct endpoint from template
	endpoint := fmt.Sprintf(c.endpointTemplate, namespace)

	// Create gRPC connection options
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(c.credentials),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	// Create connection
	conn, err := grpc.Dial(endpoint, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to notification service for namespace %s: %v", namespace, err)
	}

	// Create client
	client := notificationproto.NewNotificationServiceClient(conn)

	// Store connection and client
	c.connections[namespace] = conn
	c.clients[namespace] = client

	return client, nil
}

// getNamespaceFromContext extracts the namespace from auth claims in the context.
// If auth claims are not available or don't contain a shard namespace, it falls back
// to the default namespace if one was configured.
func (c *notificationCentralClient) getNamespaceFromContext(ctx context.Context) (string, error) {
	claims, ok := auth.GetAugmentClaims(ctx)
	if !ok || claims == nil {
		if c.defaultNamespace != "" {
			return c.defaultNamespace, nil
		}
		return "", fmt.Errorf("no auth claims found in context and no default namespace configured")
	}

	if claims.ShardNamespace == "" {
		if c.defaultNamespace != "" {
			return c.defaultNamespace, nil
		}
		return "", fmt.Errorf("no shard namespace found in auth claims and no default namespace configured")
	}

	return claims.ShardNamespace, nil
}

// CreateNotification creates a notification for a user
func (c *notificationCentralClient) CreateNotification(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.CreateNotificationRequest) (*notificationproto.CreateNotificationResponse, error) {
	namespace, err := c.getNamespaceFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get namespace from context: %v", err)
	}

	client, err := c.getClientForNamespace(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for namespace %s: %v", namespace, err)
	}

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return client.CreateNotification(ctx, req)
}

// ReadNotifications reads notifications for a user without marking them as read
func (c *notificationCentralClient) ReadNotifications(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.ReadNotificationsRequest) (*notificationproto.ReadNotificationsResponse, error) {
	namespace, err := c.getNamespaceFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get namespace from context: %v", err)
	}

	client, err := c.getClientForNamespace(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for namespace %s: %v", namespace, err)
	}

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return client.ReadNotifications(ctx, req)
}

// MarkNotificationAsRead marks notifications as read by notification name or id
func (c *notificationCentralClient) MarkNotificationsAsRead(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.MarkNotificationsAsReadRequest) (*notificationproto.MarkNotificationsAsReadResponse, error) {
	namespace, err := c.getNamespaceFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get namespace from context: %v", err)
	}

	client, err := c.getClientForNamespace(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for namespace %s: %v", namespace, err)
	}

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return client.MarkNotificationsAsRead(ctx, req)
}

// DeleteNotifications deletes notifications for a user
func (c *notificationCentralClient) DeleteNotifications(ctx context.Context, requestContext *requestcontext.RequestContext, req *notificationproto.DeleteNotificationsRequest) (*notificationproto.DeleteNotificationsResponse, error) {
	namespace, err := c.getNamespaceFromContext(ctx)
	if err != nil {
		return nil, fmt.Errorf("failed to get namespace from context: %v", err)
	}

	client, err := c.getClientForNamespace(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for namespace %s: %v", namespace, err)
	}

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return client.DeleteNotifications(ctx, req)
}

// Close closes all client connections
func (c *notificationCentralClient) Close() error {
	c.mutex.Lock()
	defer c.mutex.Unlock()

	var errors []string
	for namespace, conn := range c.connections {
		if err := conn.Close(); err != nil {
			errors = append(errors, fmt.Sprintf("failed to close connection for namespace %s: %v", namespace, err))
		}
	}

	// Clear the maps
	c.connections = make(map[string]*grpc.ClientConn)
	c.clients = make(map[string]notificationproto.NotificationServiceClient)

	if len(errors) > 0 {
		return fmt.Errorf("errors closing connections: %s", strings.Join(errors, "; "))
	}

	return nil
}

// CreateNotificationWithNamespace creates a notification for a user using an explicit namespace
func (c *notificationCentralClient) CreateNotificationWithNamespace(ctx context.Context, namespace string, requestContext *requestcontext.RequestContext, req *notificationproto.CreateNotificationRequest) (*notificationproto.CreateNotificationResponse, error) {
	client, err := c.getClientForNamespace(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for namespace %s: %v", namespace, err)
	}

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return client.CreateNotification(ctx, req)
}

// ReadNotificationsWithNamespace reads notifications for a user using an explicit namespace
func (c *notificationCentralClient) ReadNotificationsWithNamespace(ctx context.Context, namespace string, requestContext *requestcontext.RequestContext, req *notificationproto.ReadNotificationsRequest) (*notificationproto.ReadNotificationsResponse, error) {
	client, err := c.getClientForNamespace(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for namespace %s: %v", namespace, err)
	}

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return client.ReadNotifications(ctx, req)
}

// MarkNotificationAsReadWithNamespace marks notifications as read by notification name or id using an explicit namespace
func (c *notificationCentralClient) MarkNotificationAsReadWithNamespace(ctx context.Context, namespace string, requestContext *requestcontext.RequestContext, req *notificationproto.MarkNotificationsAsReadRequest) (*notificationproto.MarkNotificationsAsReadResponse, error) {
	client, err := c.getClientForNamespace(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for namespace %s: %v", namespace, err)
	}

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return client.MarkNotificationsAsRead(ctx, req)
}

// DeleteNotificationsWithNamespace deletes notifications for a user using an explicit namespace
func (c *notificationCentralClient) DeleteNotificationsWithNamespace(ctx context.Context, namespace string, requestContext *requestcontext.RequestContext, req *notificationproto.DeleteNotificationsRequest) (*notificationproto.DeleteNotificationsResponse, error) {
	client, err := c.getClientForNamespace(namespace)
	if err != nil {
		return nil, fmt.Errorf("failed to get client for namespace %s: %v", namespace, err)
	}

	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	return client.DeleteNotifications(ctx, req)
}
