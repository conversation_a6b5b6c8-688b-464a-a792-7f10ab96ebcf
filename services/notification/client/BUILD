load("@crates//:defs.bzl", "aliases", "all_crate_deps")
load("@python_pip//:requirements.bzl", "requirement")
load("@rules_rust//cargo:defs.bzl", "cargo_build_script")
load("//tools/bzl:go.bzl", "go_library")
load("//tools/bzl:python.bzl", "py_binary", "py_library")
load("//tools/bzl:rust.bzl", "rust_library")

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/notification/client",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/request_context:request_context_go",
        "//services/notification:notification_go_proto",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//credentials:go_default_library",
        "@org_golang_google_grpc//metadata:go_default_library",
    ],
)

py_library(
    name = "client_py",
    srcs = ["client.py"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/python/grpc:client_options",
        "//services/lib/request_context:request_context_py",
        "//services/notification:notification_py_proto",
    ],
)

rust_library(
    name = "client_rs",
    srcs = ["client.rs"],
    aliases = aliases(),
    crate_name = "notification_client",
    edition = "2021",
    proc_macro_deps = all_crate_deps(
        proc_macro = True,
    ),
    visibility = ["//services:__subpackages__"],
    deps = all_crate_deps(
        normal = True,
    ) + [
        ":proto_gen",
        "//base/rust/tracing-tonic",
        "//services/lib/grpc/client:grpc_client_rs",
        "//services/lib/request_context:request_context_rs",
    ],
)

cargo_build_script(
    name = "proto_gen",
    srcs = [
        "build.rs",
    ],
    aliases = aliases(build = True),
    build_script_env = {
        "PROTOC": "$(execpath @protobuf//:protoc)",
    },
    data = [
        "//services/notification:notification_proto",
        "//services/token_exchange:auth_options_proto",
        "@protobuf//:descriptor_proto",
        "@protobuf//:protoc",
        "@protobuf//:timestamp_proto",
    ],
    proc_macro_deps = all_crate_deps(
        build_proc_macro = True,
    ),
    deps = all_crate_deps(
        build = True,
    ),
)
