"""Client for the notification service."""

import logging
from typing import Optional

import grpc

from base.python.grpc import client_options
from services.notification import notification_pb2, notification_pb2_grpc
from services.lib.request_context.request_context import RequestContext


def setup_stub(
    endpoint: str,
    credentials: Optional[grpc.ChannelCredentials],
    options: client_options.OptionsList | None = None,
) -> notification_pb2_grpc.NotificationServiceStub:
    """Setup the client stub for the notification service."""
    logging.info("Creating grpc client to %s with options %s", endpoint, options)
    if not credentials:
        channel = grpc.insecure_channel(
            endpoint, options=client_options.create(options)
        )
    else:
        channel = grpc.secure_channel(
            endpoint, credentials=credentials, options=client_options.create(options)
        )
    return notification_pb2_grpc.NotificationServiceStub(channel)


class NotificationClient:
    """Client for the notification service."""

    def __init__(
        self,
        endpoint: str,
        credentials: Optional[grpc.ChannelCredentials],
        options: client_options.OptionsList | None = None,
    ):
        """Initialize the notification client."""
        self.stub = setup_stub(endpoint, credentials, options)

    def create_notification(
        self,
        request_context: RequestContext,
        request: notification_pb2.CreateNotificationRequest,
        timeout: float = 30,
    ) -> notification_pb2.CreateNotificationResponse:
        """Create a notification for a user.

        Args:
            request_context: The request context.
            request: The CreateNotificationRequest object.
            timeout: The timeout for the request in seconds.

        Returns:
            The response from the notification service.
        """
        return self.stub.CreateNotification(
            request,
            metadata=request_context.to_metadata(),
            timeout=timeout,
        )

    def read_notifications(
        self,
        request_context: RequestContext,
        request: notification_pb2.ReadNotificationsRequest,
        timeout: float = 30,
    ) -> notification_pb2.ReadNotificationsResponse:
        """Read notifications for a user without marking them as read.

        Args:
            request_context: The request context.
            request: The ReadNotificationsRequest object.
            timeout: The timeout for the request in seconds.

        Returns:
            The response from the notification service.
        """
        return self.stub.ReadNotifications(
            request,
            metadata=request_context.to_metadata(),
            timeout=timeout,
        )

    def mark_notifications_as_read(
        self,
        request_context: RequestContext,
        request: notification_pb2.MarkNotificationsAsReadRequest,
        timeout: float = 30,
    ) -> notification_pb2.MarkNotificationsAsReadResponse:
        """Mark a notification as read.

        Args:
            request_context: The request context.
            request: The MarkNotificationAsReadRequest object.
            timeout: The timeout for the request in seconds.

        Returns:
            The response from the notification service.
        """
        return self.stub.MarkNotificationsAsRead(
            request,
            metadata=request_context.to_metadata(),
            timeout=timeout,
        )

    def delete_notifications(
        self,
        request_context: RequestContext,
        request: notification_pb2.DeleteNotificationsRequest,
        timeout: float = 30,
    ) -> notification_pb2.DeleteNotificationsResponse:
        """Delete notifications for a user.

        Args:
            request_context: The request context.
            request: The DeleteNotificationsRequest object.
            timeout: The timeout for the request in seconds.

        Returns:
            The response from the notification service.
        """
        return self.stub.DeleteNotifications(
            request,
            metadata=request_context.to_metadata(),
            timeout=timeout,
        )
