package client

import (
	"context"
	"fmt"
	"time"

	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	githubproto "github.com/augmentcode/augment/services/integrations/github/eventpb"
	githublib "github.com/augmentcode/augment/services/integrations/github/state/lib"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	remoteagentactionsproto "github.com/augmentcode/augment/services/remote_agent_actions/proto"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
)

// RemoteAgentActionsForwarder handles forwarding GitHub events to the remote agent actions service
type RemoteAgentActionsForwarder interface {
	// ForwardToRemoteAgentActions forwards a GitHub event to remote agent actions if enabled
	ForwardToRemoteAgentActions(ctx context.Context, eventData interface{}, eventType string,
		tenantID, tenantName string, requestId requestcontext.RequestId, requestContext *requestcontext.RequestContext) error
}

type remoteAgentActionsForwarderImpl struct {
	tokenExchangeClient        tokenexchange.TokenExchangeClient
	featureFlagHandle          featureflags.FeatureFlagHandle
	remoteAgentActionsEndpoint string
	clientCredentials          credentials.TransportCredentials
	grpcConn                   *grpc.ClientConn
	remoteAgentActionsClient   remoteagentactionsproto.RemoteAgentActionsClient
}

// NewRemoteAgentActionsForwarder creates a new instance of the remote agent actions forwarder
func NewRemoteAgentActionsForwarder(
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	featureFlagHandle featureflags.FeatureFlagHandle,
	remoteAgentActionsEndpoint string,
	clientCredentials credentials.TransportCredentials,
) (RemoteAgentActionsForwarder, error) {
	// Create persistent gRPC connection
	conn, err := grpc.NewClient(remoteAgentActionsEndpoint,
		grpc.WithTransportCredentials(clientCredentials),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to remote agent actions service at %s: %w", remoteAgentActionsEndpoint, err)
	}

	client := remoteagentactionsproto.NewRemoteAgentActionsClient(conn)

	return &remoteAgentActionsForwarderImpl{
		tokenExchangeClient:        tokenExchangeClient,
		featureFlagHandle:          featureFlagHandle,
		remoteAgentActionsEndpoint: remoteAgentActionsEndpoint,
		clientCredentials:          clientCredentials,
		grpcConn:                   conn,
		remoteAgentActionsClient:   client,
	}, nil
}

// ForwardToRemoteAgentActions forwards a GitHub event to remote agent actions if enabled
func (raf *remoteAgentActionsForwarderImpl) ForwardToRemoteAgentActions(ctx context.Context, eventData interface{}, eventType string,
	tenantID, tenantName string, requestId requestcontext.RequestId, requestContext *requestcontext.RequestContext,
) error {
	// Extract repository and event information for logging
	owner, name := getRepositoryInfo(eventData)
	eventInfo := getEventSpecificInfo(eventData)

	// Check if remote agent actions are enabled before forwarding
	// Bind tenant_name to feature flag context for tenant-specific evaluation
	boundFeatureFlags, err := raf.featureFlagHandle.BindContext("tenant_name", tenantName)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to bind tenant_name to feature flags, using default context")
		boundFeatureFlags = raf.featureFlagHandle
	}
	enableRemoteAgentActions, err := boundFeatureFlags.GetBool("remote_agent_actions_enable_endpoints", false)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).Msg("Failed to get remote_agent_actions_enable_endpoints feature flag, defaulting to disabled")
		enableRemoteAgentActions = false
	}

	if !enableRemoteAgentActions {
		log.Ctx(ctx).Debug().Msgf("Remote agent actions disabled, skipping trigger evaluation for %s", eventInfo)
		return nil
	}

	// Create the full GitHub event structure
	githubEvent := &githubproto.GithubEvent{
		Metadata: &githubproto.EventMetadata{
			TenantId:   tenantID,
			TenantName: tenantName,
			RequestId:  string(requestId),
		},
		EventType: eventType,
	}

	// Set the appropriate event field based on the concrete type
	switch v := eventData.(type) {
	case *githubproto.GithubEvent_PullRequest:
		githubEvent.Event = v
	case *githubproto.GithubEvent_PullRequestReviewComment:
		githubEvent.Event = v
	case *githubproto.GithubEvent_CheckSuite:
		githubEvent.Event = v
	case *githubproto.GithubEvent_IssueComment:
		githubEvent.Event = v
	case *githubproto.GithubEvent_Status:
		githubEvent.Event = v
	default:
		return fmt.Errorf("unsupported event data type: %T", eventData)
	}

	// Forward the event to remote agent actions service for trigger evaluation
	err = raf.forwardGenericEventToRemoteAgentActions(ctx, githubEvent, requestContext)
	if err != nil {
		// Log the error but don't fail the entire event processing
		// This ensures GitHub webhook processing continues even if trigger system has issues
		log.Ctx(ctx).Error().Err(err).Msgf("Failed to forward %s event to remote agent actions for %s", eventType, eventInfo)
		// Don't return the error to avoid failing the entire event processing
	}

	log.Ctx(ctx).Info().Msgf("%s event processed successfully for repo %s/%s",
		eventInfo, githublib.RedactString(owner), githublib.RedactString(name))
	return nil
}

// forwardGenericEventToRemoteAgentActions forwards any GitHub event to the remote agent actions service
func (raf *remoteAgentActionsForwarderImpl) forwardGenericEventToRemoteAgentActions(ctx context.Context, githubEvent *githubproto.GithubEvent,
	requestContext *requestcontext.RequestContext,
) error {
	// Create connection with timeout and proper error handling
	ctx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	// Generate a service token for authentication with remote agent actions service
	serviceToken, err := raf.tokenExchangeClient.GetSignedTokenForService(ctx, githubEvent.Metadata.TenantId, []tokenscopesproto.Scope{
		tokenscopesproto.Scope_CONTENT_R,
		tokenscopesproto.Scope_CONTENT_RW,
	})
	if err != nil {
		return fmt.Errorf("failed to get service token: %w", err)
	}

	// Create authenticated request context
	authenticatedRequestContext := requestContext.WithAuthToken(serviceToken)

	// Add authenticated request context to gRPC context
	ctx = metadata.NewOutgoingContext(ctx, authenticatedRequestContext.ToMetadata())

	// Process the GitHub event
	processRequest := &remoteagentactionsproto.ProcessGitHubEventRequest{
		GithubEvent: githubEvent,
	}

	processResponse, err := raf.remoteAgentActionsClient.ProcessGitHubEvent(ctx, processRequest)
	if err != nil {
		return fmt.Errorf("failed to process GitHub event: %w", err)
	}

	log.Ctx(ctx).Info().Msgf("GitHub event processed by remote agent actions: %d triggers evaluated, %d matched, %d agents spawned",
		processResponse.TriggersEvaluated, processResponse.TriggersMatched, processResponse.AgentsSpawned)

	// Log any warnings
	for _, warning := range processResponse.Warnings {
		log.Ctx(ctx).Warn().Msgf("Remote agent actions warning: %s", warning)
	}

	return nil
}

// Helper functions to extract repository information for logging
func getRepositoryInfo(eventData interface{}) (owner, name string) {
	switch event := eventData.(type) {
	case *githubproto.GithubEvent_PullRequest:
		return event.PullRequest.Repository.Owner, event.PullRequest.Repository.Name
	case *githubproto.GithubEvent_PullRequestReviewComment:
		return event.PullRequestReviewComment.Repository.Owner, event.PullRequestReviewComment.Repository.Name
	case *githubproto.GithubEvent_CheckSuite:
		return event.CheckSuite.Repository.Owner, event.CheckSuite.Repository.Name
	case *githubproto.GithubEvent_IssueComment:
		return event.IssueComment.Repository.Owner, event.IssueComment.Repository.Name
	case *githubproto.GithubEvent_Status:
		return event.Status.Repository.Owner, event.Status.Repository.Name
	default:
		return "", ""
	}
}

// Helper functions to extract event-specific information for logging
func getEventSpecificInfo(eventData interface{}) string {
	switch event := eventData.(type) {
	case *githubproto.GithubEvent_PullRequest:
		return fmt.Sprintf("PR #%d (%s)", event.PullRequest.Number, event.PullRequest.Action)
	case *githubproto.GithubEvent_PullRequestReviewComment:
		return fmt.Sprintf("review comment (%s) for PR #%d", event.PullRequestReviewComment.Action, event.PullRequestReviewComment.Number)
	case *githubproto.GithubEvent_CheckSuite:
		return fmt.Sprintf("check suite (%s) with conclusion %s", event.CheckSuite.Action, event.CheckSuite.CheckSuite.Conclusion)
	case *githubproto.GithubEvent_IssueComment:
		return fmt.Sprintf("issue comment (%s) for issue/PR #%d", event.IssueComment.Action, event.IssueComment.Number)
	case *githubproto.GithubEvent_Status:
		return fmt.Sprintf("status event (%s) for commit %s", event.Status.State, githublib.RedactString(event.Status.Sha))
	default:
		return "unknown event"
	}
}
