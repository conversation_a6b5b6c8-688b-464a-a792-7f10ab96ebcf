package main

import (
	"fmt"
	"time"

	"github.com/robfig/cron"
)

// CronEvaluator handles cron expression parsing and evaluation
type CronEvaluator struct{}

// NewCronEvaluator creates a new CronEvaluator instance
func NewCronEvaluator() *CronEvaluator {
	return &CronEvaluator{}
}

// ValidateCronExpression validates a cron expression using the robfig/cron library
func (c *CronEvaluator) ValidateCronExpression(cronExpr string) error {
	if cronExpr == "" {
		return fmt.Errorf("empty cron expression")
	}

	// Try to parse the cron expression using the standard parser
	_, err := cron.ParseStandard(cronExpr)
	if err != nil {
		return fmt.Errorf("invalid cron expression: %w", err)
	}

	return nil
}

// GetNextExecution calculates the next execution time for a cron expression
func (c *CronEvaluator) GetNextExecution(cronExpr string, timezone string, after time.Time) (time.Time, error) {
	// Handle timezone
	var loc *time.Location
	var err error
	if timezone == "" {
		loc = time.UTC
	} else {
		loc, err = time.LoadLocation(timezone)
		if err != nil {
			return time.Time{}, fmt.Errorf("invalid timezone '%s': %w", timezone, err)
		}
	}

	// Convert 'after' time to the specified timezone
	afterInTz := after.In(loc)

	// Parse the cron expression using the robfig/cron library
	schedule, err := cron.ParseStandard(cronExpr)
	if err != nil {
		return time.Time{}, fmt.Errorf("invalid cron expression '%s': %w", cronExpr, err)
	}

	// Calculate the next execution time
	nextTime := schedule.Next(afterInTz)
	if nextTime.IsZero() {
		return time.Time{}, fmt.Errorf("no future execution time found for cron expression '%s'", cronExpr)
	}

	return nextTime, nil
}

// GetNextExecutionWithBounds calculates the next execution time within date boundaries
func (c *CronEvaluator) GetNextExecutionWithBounds(
	cronExpr string,
	timezone string,
	after time.Time,
	startDate *time.Time,
	endDate *time.Time,
) (time.Time, error) {
	// Get the basic next execution time
	nextTime, err := c.GetNextExecution(cronExpr, timezone, after)
	if err != nil {
		return time.Time{}, err
	}

	// Check start date boundary
	if startDate != nil && nextTime.Before(*startDate) {
		// If next time is before start date, calculate from start date
		nextTime, err = c.GetNextExecution(cronExpr, timezone, *startDate)
		if err != nil {
			return time.Time{}, err
		}
	}

	// Check end date boundary
	if endDate != nil && nextTime.After(*endDate) {
		// Next execution is beyond end date, return zero time to indicate no more executions
		return time.Time{}, nil
	}

	return nextTime, nil
}

// IsTimeToExecute checks if a scheduled trigger should execute now
// Includes extended grace period for missed executions
func (c *CronEvaluator) IsTimeToExecute(scheduledTime time.Time, now time.Time, tolerance time.Duration) bool {
	// Default tolerance for normal execution window
	if tolerance == 0 {
		tolerance = 30 * time.Second
	}

	timeDiff := now.Sub(scheduledTime)

	// Must be at or after the scheduled time
	if timeDiff < 0 {
		return false
	}

	// Normal execution window: within tolerance after scheduled time
	if timeDiff <= tolerance {
		return true
	}

	// Extended grace period for missed executions: up to 15 minutes overdue
	// This handles cases where the scheduler was delayed, had outages, etc.
	maxGracePeriod := 15 * time.Minute
	if timeDiff > tolerance && timeDiff <= maxGracePeriod {
		return true
	}

	return false
}

// ValidateTimezone validates a timezone string
func (c *CronEvaluator) ValidateTimezone(timezone string) error {
	if timezone == "" {
		return nil // Empty timezone defaults to UTC
	}

	_, err := time.LoadLocation(timezone)
	if err != nil {
		return fmt.Errorf("invalid timezone '%s': %w", timezone, err)
	}
	return nil
}

// GetUpcomingExecutions returns the next N execution times for a cron expression
func (c *CronEvaluator) GetUpcomingExecutions(
	cronExpr string,
	timezone string,
	after time.Time,
	count int,
	startDate *time.Time,
	endDate *time.Time,
) ([]time.Time, error) {
	if count <= 0 {
		return []time.Time{}, nil
	}

	// Parse the timezone
	var loc *time.Location
	var err error
	if timezone == "" {
		loc = time.UTC
	} else {
		loc, err = time.LoadLocation(timezone)
		if err != nil {
			return nil, fmt.Errorf("invalid timezone '%s': %w", timezone, err)
		}
	}

	// Parse the cron expression
	schedule, err := cron.ParseStandard(cronExpr)
	if err != nil {
		return nil, fmt.Errorf("invalid cron expression '%s': %w", cronExpr, err)
	}

	var executions []time.Time
	currentTime := after.In(loc)

	for len(executions) < count {
		nextTime := schedule.Next(currentTime)
		if nextTime.IsZero() {
			break
		}

		// Check start date boundary
		if startDate != nil && nextTime.Before(*startDate) {
			currentTime = nextTime.Add(time.Second)
			continue
		}

		// Check end date boundary
		if endDate != nil && nextTime.After(*endDate) {
			break
		}

		executions = append(executions, nextTime)
		currentTime = nextTime.Add(time.Second) // Move past this execution time
	}

	return executions, nil
}
