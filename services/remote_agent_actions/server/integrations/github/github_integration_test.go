package github

import (
	"testing"

	githubproto "github.com/augmentcode/augment/services/integrations/github/eventpb"
)

func TestMatchesPrBotActions(t *testing.T) {
	g := &GitHubIntegration{}

	tests := []struct {
		name             string
		triggerActions   []string
		webhookEventType string
		pullRequest      *githubproto.PullRequestEvent
		issueComment     *githubproto.GithubEvent_IssueComment
		checkSuite       *githubproto.CheckSuiteEvent
		expected         bool
	}{
		{
			name:             "comment action matches issue_comment event",
			triggerActions:   []string{"comment"},
			webhookEventType: "issue_comment",
			expected:         true,
		},
		{
			name:             "comment action matches pull_request_review_comment event",
			triggerActions:   []string{"comment"},
			webhookEventType: "pull_request_review_comment",
			expected:         true,
		},
		{
			name:             "comment action does not match pull_request event",
			triggerActions:   []string{"comment"},
			webhookEventType: "pull_request",
			expected:         false,
		},
		{
			name:             "check_suite_failure matches completed check_suite",
			triggerActions:   []string{"check_suite_failure"},
			webhookEventType: "check_suite",
			pullRequest:      &githubproto.PullRequestEvent{Action: "completed"},
			expected:         true,
		},
		{
			name:             "review_my_pr matches opened pull_request",
			triggerActions:   []string{"review_my_pr"},
			webhookEventType: "pull_request",
			pullRequest:      &githubproto.PullRequestEvent{Action: "opened"},
			expected:         true,
		},
		{
			name:             "review_my_pr matches synchronize pull_request",
			triggerActions:   []string{"review_my_pr"},
			webhookEventType: "pull_request",
			pullRequest:      &githubproto.PullRequestEvent{Action: "synchronize"},
			expected:         true,
		},
		{
			name:             "review_my_pr does not match closed pull_request",
			triggerActions:   []string{"review_my_pr"},
			webhookEventType: "pull_request",
			pullRequest:      &githubproto.PullRequestEvent{Action: "closed"},
			expected:         false,
		},
		{
			name:             "help_review matches review_requested pull_request",
			triggerActions:   []string{"help_review"},
			webhookEventType: "pull_request",
			pullRequest:      &githubproto.PullRequestEvent{Action: "review_requested"},
			expected:         true,
		},
		{
			name:             "multiple actions - any match returns true",
			triggerActions:   []string{"comment", "review_my_pr"},
			webhookEventType: "issue_comment",
			expected:         true,
		},
		{
			name:             "no actions provided returns false",
			triggerActions:   []string{},
			webhookEventType: "pull_request",
			expected:         false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := g.matchesPrBotActions(tt.triggerActions, tt.webhookEventType, tt.pullRequest, tt.issueComment)
			if result != tt.expected {
				t.Errorf("matchesPrBotActions() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestEvaluatePrBotConditions(t *testing.T) {
	g := &GitHubIntegration{}

	tests := []struct {
		name           string
		triggerActions []string
		eventType      string
		pullRequest    *githubproto.PullRequestEvent
		triggerOwnerID string
		expected       bool
	}{
		{
			name:           "review_my_pr - author matches trigger owner",
			triggerActions: []string{"review_my_pr"},
			eventType:      "pull_request",
			pullRequest: &githubproto.PullRequestEvent{
				Author: &githubproto.UserInfo{Login: "testuser"},
			},
			triggerOwnerID: "testuser",
			expected:       true,
		},
		{
			name:           "review_my_pr - author does not match trigger owner",
			triggerActions: []string{"review_my_pr"},
			eventType:      "pull_request",
			pullRequest: &githubproto.PullRequestEvent{
				Author: &githubproto.UserInfo{Login: "otheruser"},
			},
			triggerOwnerID: "testuser",
			expected:       false,
		},
		{
			name:           "help_review - user is not in requested reviewers",
			triggerActions: []string{"help_review"},
			eventType:      "pull_request",
			pullRequest: &githubproto.PullRequestEvent{
				Author: &githubproto.UserInfo{Login: "otheruser"},
				RequestedReviewers: []*githubproto.UserInfo{
					{Login: "reviewer1"},
					{Login: "reviewer2"},
				},
			},
			triggerOwnerID: "testuser",
			expected:       false,
		},
		{
			name:           "help_review - user is in requested reviewers",
			triggerActions: []string{"help_review"},
			eventType:      "pull_request",
			pullRequest: &githubproto.PullRequestEvent{
				Author: &githubproto.UserInfo{Login: "otheruser"},
				RequestedReviewers: []*githubproto.UserInfo{
					{Login: "reviewer1"},
					{Login: "testuser"},
					{Login: "reviewer2"},
				},
			},
			triggerOwnerID: "testuser",
			expected:       true,
		},
		{
			name:           "help_review - empty requested reviewers list",
			triggerActions: []string{"help_review"},
			eventType:      "pull_request",
			pullRequest: &githubproto.PullRequestEvent{
				Author:             &githubproto.UserInfo{Login: "otheruser"},
				RequestedReviewers: []*githubproto.UserInfo{},
			},
			triggerOwnerID: "testuser",
			expected:       false,
		},
		{
			name:           "comment action - no additional filtering",
			triggerActions: []string{"comment"},
			eventType:      "issue_comment",
			triggerOwnerID: "testuser",
			expected:       true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := g.evaluatePrBotConditions(tt.triggerActions, tt.eventType, tt.pullRequest, nil, tt.triggerOwnerID)
			if result != tt.expected {
				t.Errorf("evaluatePrBotConditions() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestIsUserInReviewers(t *testing.T) {
	g := &GitHubIntegration{}

	tests := []struct {
		name        string
		pullRequest *githubproto.PullRequestEvent
		userLogin   string
		expected    bool
	}{
		{
			name:        "nil pull request",
			pullRequest: nil,
			userLogin:   "testuser",
			expected:    false,
		},
		{
			name: "user is in requested reviewers",
			pullRequest: &githubproto.PullRequestEvent{
				RequestedReviewers: []*githubproto.UserInfo{
					{Login: "reviewer1"},
					{Login: "testuser"},
					{Login: "reviewer2"},
				},
			},
			userLogin: "testuser",
			expected:  true,
		},
		{
			name: "user is not in requested reviewers",
			pullRequest: &githubproto.PullRequestEvent{
				RequestedReviewers: []*githubproto.UserInfo{
					{Login: "reviewer1"},
					{Login: "reviewer2"},
				},
			},
			userLogin: "testuser",
			expected:  false,
		},
		{
			name: "empty requested reviewers list",
			pullRequest: &githubproto.PullRequestEvent{
				RequestedReviewers: []*githubproto.UserInfo{},
			},
			userLogin: "testuser",
			expected:  false,
		},
		{
			name: "nil requested reviewers list",
			pullRequest: &githubproto.PullRequestEvent{
				RequestedReviewers: nil,
			},
			userLogin: "testuser",
			expected:  false,
		},
		{
			name: "reviewer with nil login",
			pullRequest: &githubproto.PullRequestEvent{
				RequestedReviewers: []*githubproto.UserInfo{
					{Login: "reviewer1"},
					nil, // nil reviewer
					{Login: "reviewer2"},
				},
			},
			userLogin: "testuser",
			expected:  false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := g.isUserInReviewers(tt.pullRequest, tt.userLogin)
			if result != tt.expected {
				t.Errorf("isUserInReviewers() = %v, expected %v", result, tt.expected)
			}
		})
	}
}

func TestIsCheckSuiteFailure(t *testing.T) {
	g := &GitHubIntegration{}

	tests := []struct {
		name             string
		webhookEventType string
		pullRequest      *githubproto.PullRequestEvent
		checkSuite       *githubproto.CheckSuiteEvent
		expected         bool
	}{
		{
			name:             "check_suite with failure conclusion returns true",
			webhookEventType: "check_suite",
			pullRequest:      &githubproto.PullRequestEvent{Action: "completed"},
			checkSuite: &githubproto.CheckSuiteEvent{
				Action: "completed",
				CheckSuite: &githubproto.CheckSuite{
					Conclusion: "failure",
				},
			},
			expected: true,
		},
		{
			name:             "check_suite with timed_out conclusion returns true",
			webhookEventType: "check_suite",
			pullRequest:      &githubproto.PullRequestEvent{Action: "completed"},
			checkSuite: &githubproto.CheckSuiteEvent{
				Action: "completed",
				CheckSuite: &githubproto.CheckSuite{
					Conclusion: "timed_out",
				},
			},
			expected: true,
		},
		{
			name:             "check_suite with cancelled conclusion returns true",
			webhookEventType: "check_suite",
			pullRequest:      &githubproto.PullRequestEvent{Action: "completed"},
			checkSuite: &githubproto.CheckSuiteEvent{
				Action: "completed",
				CheckSuite: &githubproto.CheckSuite{
					Conclusion: "cancelled",
				},
			},
			expected: true,
		},
		{
			name:             "check_suite with success conclusion returns false",
			webhookEventType: "check_suite",
			pullRequest:      &githubproto.PullRequestEvent{Action: "completed"},
			checkSuite: &githubproto.CheckSuiteEvent{
				Action: "completed",
				CheckSuite: &githubproto.CheckSuite{
					Conclusion: "success",
				},
			},
			expected: false,
		},
		{
			name:             "check_suite with neutral conclusion returns false",
			webhookEventType: "check_suite",
			pullRequest:      &githubproto.PullRequestEvent{Action: "completed"},
			checkSuite: &githubproto.CheckSuiteEvent{
				Action: "completed",
				CheckSuite: &githubproto.CheckSuite{
					Conclusion: "neutral",
				},
			},
			expected: false,
		},
		{
			name:             "check_suite with action_required conclusion returns false",
			webhookEventType: "check_suite",
			pullRequest:      &githubproto.PullRequestEvent{Action: "completed"},
			checkSuite: &githubproto.CheckSuiteEvent{
				Action: "completed",
				CheckSuite: &githubproto.CheckSuite{
					Conclusion: "action_required",
				},
			},
			expected: false,
		},
		{
			name:             "non-completed check_suite returns false",
			webhookEventType: "check_suite",
			pullRequest:      &githubproto.PullRequestEvent{Action: "requested"},
			checkSuite: &githubproto.CheckSuiteEvent{
				Action: "requested",
				CheckSuite: &githubproto.CheckSuite{
					Conclusion: "failure",
				},
			},
			expected: false,
		},
		{
			name:             "non-check_suite event returns false",
			webhookEventType: "pull_request",
			pullRequest:      &githubproto.PullRequestEvent{Action: "opened"},
			checkSuite: &githubproto.CheckSuiteEvent{
				Action: "completed",
				CheckSuite: &githubproto.CheckSuite{
					Conclusion: "failure",
				},
			},
			expected: false,
		},
		{
			name:             "nil checkSuite returns false",
			webhookEventType: "check_suite",
			pullRequest:      &githubproto.PullRequestEvent{Action: "completed"},
			checkSuite:       nil,
			expected:         false,
		},
		{
			name:             "nil checkSuite.CheckSuite returns false",
			webhookEventType: "check_suite",
			pullRequest:      &githubproto.PullRequestEvent{Action: "completed"},
			checkSuite: &githubproto.CheckSuiteEvent{
				Action:     "completed",
				CheckSuite: nil,
			},
			expected: false,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := g.isCheckSuiteFailure(tt.webhookEventType, tt.pullRequest, tt.checkSuite)
			if result != tt.expected {
				t.Errorf("isCheckSuiteFailure() = %v, expected %v", result, tt.expected)
			}
		})
	}
}
