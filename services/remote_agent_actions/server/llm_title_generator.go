package main

import (
	"context"
	"fmt"
	"strings"

	"github.com/rs/zerolog/log"

	chathostclient "github.com/augmentcode/augment/services/chat_host/client/go"
	linearclient "github.com/augmentcode/augment/services/integrations/linear/client_go"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	remoteagentactionsproto "github.com/augmentcode/augment/services/remote_agent_actions/proto"
	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
)

// formatValueForPrompt formats any value for use in LLM prompts by replacing newlines
// with \n, truncating to 500 chars, and wrapping with quotes
func formatValueForPrompt(value string) string {
	if value == "" {
		return "\"\""
	}

	// Replace actual newlines with \n
	formatted := strings.ReplaceAll(value, "\n", "\\n")
	formatted = strings.ReplaceAll(formatted, "\r", "\\n")

	// Truncate to 500 characters
	if len(formatted) > 500 {
		formatted = formatted[:500] + "..."
	}

	// Wrap with quotes
	return fmt.Sprintf("\"%s\"", formatted)
}

// generateLLMTitleImpl generates a title for a remote agent using an LLM
func generateLLMTitleImpl(ctx context.Context, requestContext *requestcontext.RequestContext, chatHostClient *chathostclient.ChatHostClient, trigger *remoteagentactionsproto.Trigger, entityType any, entityID string, entityData map[string]interface{}, agentConfig *remoteagentsproto.AgentConfig, extraPrompt string, linearClient *linearclient.LinearClient) *string {
	triggerName := "Unknown Trigger"
	if trigger != nil && trigger.Configuration != nil && trigger.Configuration.Name != "" {
		triggerName = trigger.Configuration.Name
	}

	// Build the prompt with intro, context parts, and outro
	intro := `Generate a concise, descriptive title for a remote agent based on the following context.
The title should be sentence case and 3-7 words that clearly describe what the agent will do.
Make it specific and actionable, avoiding generic terms like 'Agent' or 'Task' or 'Analyze'.
The user will be creating many agents from this trigger, so don't describe the trigger but rather the specific work the agent needs to do and what entity it applies to.
Be as specific as possible, so this title can be identified in a list of agents.
The most important thing to include in the title is info about the entity (if there is one) and the task.`

	var contextParts []string

	// Add trigger information
	if trigger != nil && trigger.Configuration != nil {
		if trigger.Configuration.Name != "" {
			contextParts = append(contextParts, fmt.Sprintf("Trigger: %s", formatValueForPrompt(trigger.Configuration.Name)))
		}
		if trigger.Configuration.Description != nil && *trigger.Configuration.Description != "" {
			contextParts = append(contextParts, fmt.Sprintf("Trigger Description: %s", formatValueForPrompt(*trigger.Configuration.Description)))
		}

		// Add user instructions from trigger configuration agent config (formatted)
		if trigger.Configuration.AgentConfig != nil && trigger.Configuration.AgentConfig.UserGuidelines != nil && *trigger.Configuration.AgentConfig.UserGuidelines != "" {
			formatted := formatValueForPrompt(*trigger.Configuration.AgentConfig.UserGuidelines)
			contextParts = append(contextParts, fmt.Sprintf("Instructions: %s", formatted))
		}
	}

	// Add user instructions from extraPrompt (formatted)
	if extraPrompt != "" {
		formatted := formatValueForPrompt(extraPrompt)
		contextParts = append(contextParts, fmt.Sprintf("User Instructions: %s", formatted))
	}

	// Add repository and branch information
	if agentConfig != nil {
		if agentConfig.WorkspaceSetup != nil && agentConfig.WorkspaceSetup.GetGithubRef() != nil {
			githubRef := agentConfig.WorkspaceSetup.GetGithubRef()
			if githubRef.Url != "" {
				contextParts = append(contextParts, fmt.Sprintf("Repository: %s", formatValueForPrompt(githubRef.Url)))
			}
			if githubRef.Ref != "" {
				contextParts = append(contextParts, fmt.Sprintf("Branch: %s", formatValueForPrompt(githubRef.Ref)))
			}
		}
	}

	// Add entity information (most important part)
	if entityID != "" {
		entityInfo := getEntityInfo(ctx, requestContext, entityType, entityID, entityData, linearClient)
		if entityInfo != "" {
			contextParts = append(contextParts, fmt.Sprintf("Entity (focus on this, always include this info): %s", formatValueForPrompt(entityInfo)))
		}

		// Add entity description if available
		if entityData != nil {
			if description, ok := getEntityDescription(entityData, entityType); ok && description != "" {
				contextParts = append(contextParts, fmt.Sprintf("Entity Description: %s", formatValueForPrompt(description)))
			}
		}
	}

	outro := `IMPORTANT: Wrap your generated title between <augment-agent-title> and </augment-agent-title> tags.
Put only the title inside these tags, nothing else.

Examples of good titles:
- Always load agents on settings page
- PR review: auth flow changes
- Add local dev instructions to README
- Fix login flow race condition
- Make settings page load faster`

	// Combine all parts
	contextSection := ""
	if len(contextParts) > 0 {
		contextSection = "\n\nContext:\n" + strings.Join(contextParts, "\n")
	}

	prompt := intro + contextSection + "\n\n" + outro

	response, err := chatHostClient.GenerateResponse(ctx, requestContext, prompt)
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).
			Str("trigger_name", triggerName).
			Msg("Failed to generate LLM title")
		return nil
	}

	// Extract title from XML tags
	startTag := "<augment-agent-title>"
	endTag := "</augment-agent-title>"

	startIndex := strings.Index(response, startTag)
	if startIndex == -1 {
		log.Ctx(ctx).Warn().
			Msg("LLM response missing start tag")
		return nil
	}

	endIndex := strings.Index(response, endTag)
	if endIndex == -1 {
		log.Ctx(ctx).Warn().
			Msg("LLM response missing end tag")
		return nil
	}

	if endIndex <= startIndex+len(startTag) {
		log.Ctx(ctx).Warn().
			Msg("LLM response has invalid tag structure")
		return nil
	}

	// Extract the title content between the tags
	titleContent := response[startIndex+len(startTag) : endIndex]
	cleanTitle := strings.TrimSpace(titleContent)

	if cleanTitle == "" {
		log.Ctx(ctx).Warn().
			Msg("LLM generated empty title content")
		return nil
	}

	return &cleanTitle
}

// getEntityInfo returns a formatted string describing the entity for LLM title generation
func getEntityInfo(ctx context.Context, requestContext *requestcontext.RequestContext, entityType any, entityID string, entityData map[string]interface{}, linearClient *linearclient.LinearClient) string {
	switch entityType {
	case remoteagentactionsproto.GitHubEntityType_GITHUB_ENTITY_TYPE_PULL_REQUEST:
		if entityData != nil {
			if title, ok := entityData["title"].(string); ok && title != "" {
				return fmt.Sprintf("%s (GITHUB_ENTITY_TYPE_PULL_REQUEST)", title)
			}
		}
		return fmt.Sprintf("GitHub PR %s (GITHUB_ENTITY_TYPE_PULL_REQUEST)", entityID)

	case remoteagentactionsproto.GitHubEntityType_GITHUB_ENTITY_TYPE_WORKFLOW_RUN:
		if entityData != nil {
			if name, ok := entityData["name"].(string); ok && name != "" {
				return fmt.Sprintf("%s workflow (GITHUB_ENTITY_TYPE_WORKFLOW_RUN)", name)
			}
		}
		return fmt.Sprintf("GitHub workflow %s (GITHUB_ENTITY_TYPE_WORKFLOW_RUN)", entityID)

	case remoteagentactionsproto.LinearEntityType_LINEAR_ENTITY_TYPE_ISSUE:
		if entityData != nil {
			if title, ok := entityData["title"].(string); ok && title != "" {
				return fmt.Sprintf("%s (LINEAR_ENTITY_TYPE_ISSUE)", title)
			}
		}
		// Try to fetch entity data if not provided
		if linearClient != nil {
			fetchedData, err := fetchLinearIssueEntity(entityID, ctx, requestContext, linearClient)
			if err == nil && fetchedData != nil {
				if title, ok := fetchedData["title"].(string); ok && title != "" {
					return fmt.Sprintf("%s (LINEAR_ENTITY_TYPE_ISSUE)", title)
				}
			}
		}
		return fmt.Sprintf("Linear issue %s (LINEAR_ENTITY_TYPE_ISSUE)", entityID)

	default:
		return fmt.Sprintf("Entity %s", entityID)
	}
}

// getEntityDescription extracts the description from entity data based on entity type
func getEntityDescription(entityData map[string]interface{}, entityType any) (string, bool) {
	if entityData == nil {
		return "", false
	}

	switch entityType {
	case remoteagentactionsproto.GitHubEntityType_GITHUB_ENTITY_TYPE_PULL_REQUEST:
		if body, ok := entityData["body"].(string); ok {
			return body, true
		}
	case remoteagentactionsproto.GitHubEntityType_GITHUB_ENTITY_TYPE_WORKFLOW_RUN:
		if displayTitle, ok := entityData["display_title"].(string); ok {
			return displayTitle, true
		}
	case remoteagentactionsproto.LinearEntityType_LINEAR_ENTITY_TYPE_ISSUE:
		if description, ok := entityData["description"].(string); ok {
			return description, true
		}
	}

	return "", false
}

// fetchLinearIssueEntity fetches Linear issue data for LLM title generation
func fetchLinearIssueEntity(entityID string, ctx context.Context, requestContext *requestcontext.RequestContext, linearClient *linearclient.LinearClient) (map[string]interface{}, error) {
	query := fmt.Sprintf(`{
		issue(id: "%s") {
			id
			title
			description
			url
			updatedAt
			state { name }
			assignee { name }
			creator { name }
		}
	}`, entityID)

	response, err := linearClient.CallAPI(ctx, requestContext, query, "")
	if err != nil {
		log.Ctx(ctx).Warn().Err(err).
			Str("entity_id", entityID).
			Msg("Failed to fetch Linear issue for LLM title generation")
		return nil, err
	}

	// Parse the response to extract issue data
	// Try the standard GraphQL format first: {"data": {"issue": {...}}}
	if data, ok := response["data"].(map[string]interface{}); ok {
		if issue, ok := data["issue"].(map[string]interface{}); ok {
			return issue, nil
		}
	} else {
		// Try direct access: {"issue": {...}}
		if issue, ok := response["issue"].(map[string]interface{}); ok {
			return issue, nil
		}
	}

	return nil, fmt.Errorf("invalid response format from Linear API")
}
