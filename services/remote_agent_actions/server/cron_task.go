package main

import (
	"context"
	"sync"
	"time"

	"github.com/rs/zerolog/log"
)

// CronTaskFunc defines the signature for functions executed by CronTask.
type CronTaskFunc func(ctx context.Context)

// CronTask is a generic task that executes a given function periodically.
// It implements the Task interface for SingletonTaskManager.
type CronTask struct {
	name      string // Name of the cron task for logging
	mu        sync.Mutex
	isRunning bool
	ticker    *time.Ticker
	cancel    context.CancelFunc
	doneCh    chan struct{}
	taskFunc  CronTaskFunc  // The function to execute periodically
	interval  time.Duration // The interval for this specific task
}

// NewCronTask creates a new CronTask.
func NewCronTask(name string, interval time.Duration, taskFunc CronTaskFunc) *CronTask {
	if interval <= 0 {
		log.Fatal().Str("task_name", name).Dur("interval", interval).Msg("CronTask: Invalid or zero interval")
	}
	if taskFunc == nil {
		log.Fatal().Str("task_name", name).Msg("CronTask: taskFunc is nil")
	}
	return &CronTask{
		name:     name,
		interval: interval,
		taskFunc: taskFunc,
	}
}

// Start is called by SingletonTaskManager when leadership is acquired.
// It starts a goroutine that periodically calls the configured taskFunc.
func (t *CronTask) Start(ctx context.Context) {
	t.mu.Lock()
	if t.isRunning {
		t.mu.Unlock()
		log.Ctx(ctx).Warn().Str("task_name", t.name).Msg("CronTask: Start called but already running")
		return
	}

	runCtx, cancel := context.WithCancel(ctx)
	t.cancel = cancel
	t.doneCh = make(chan struct{})
	t.isRunning = true
	t.mu.Unlock()

	// Log uses t.interval (the task's own configured interval)
	log.Ctx(ctx).Info().Str("task_name", t.name).Dur("configuredInterval", t.interval).Msg("CronTask: Starting with its configured interval")

	go func() {
		defer func() {
			log.Ctx(runCtx).Info().Str("task_name", t.name).Msg("CronTask: Goroutine stopping")
			if t.ticker != nil {
				t.ticker.Stop()
			}
			t.mu.Lock()
			t.isRunning = false
			t.cancel = nil
			t.mu.Unlock()
			close(t.doneCh)
		}()

		t.ticker = time.NewTicker(t.interval) // Use t.interval

		for {
			select {
			case <-runCtx.Done():
				log.Ctx(runCtx).Info().Str("task_name", t.name).Msg("CronTask: Context done, exiting run loop")
				return
			case <-t.ticker.C:

				log.Ctx(runCtx).Debug().Str("task_name", t.name).Msg("CronTask: Ticker fired, executing taskFunc")
				t.taskFunc(runCtx)
			}
		}
	}()
}

// Stop is called by SingletonTaskManager when leadership is lost or the job is stopped.
// It signals the task's goroutine to terminate.
func (t *CronTask) Stop() {
	t.mu.Lock()
	if !t.isRunning {
		t.mu.Unlock()
		return
	}

	log.Info().Str("task_name", t.name).Msg("CronTask: Attempting to stop...")
	t.cancel()
	t.mu.Unlock()

	if t.doneCh != nil {
		<-t.doneCh
		log.Info().Str("task_name", t.name).Msg("CronTask: Goroutine confirmed stopped.")
	}
	log.Info().Str("task_name", t.name).Msg("CronTask: Stopped")
}

func (t *CronTask) Name() string {
	return t.name
}
