"""Integration tests for the /api/signup endpoint."""

import pytest
import requests

from services.auth.central.server import auth_central_test_setup
from services.auth.central.server import auth_pb2_grpc
from services.auth.test.conftest import (
    VALID_CODE_CHALLENGE,
    VALID_ENTERPRISE_TENANT_NAME,
    VALID_ENTERPRISE_TENANT_ID,
    CUSTOMER_UI_CLIENT_ID,
)
from services.auth.test.auth_test_utils import (
    OAuthFlowHelper,
    TestDataGenerator,
    APITestHelper,
    UserTestHelper,
)


@pytest.fixture()
def auth_central_http_server(auth_central_http_server_raw):
    """Set up the auth central HTTP server for testing."""
    # Enable the React frontend feature flag for signup API tests
    auth_central_http_server_raw.setup_test(
        feature_flags={
            "auth_central_react_frontend": True,
            "auth_central_signup_tenant": VALID_ENTERPRISE_TENANT_NAME,
            "auth_central_individual_tenant": VALID_ENTERPRISE_TENANT_NAME,
        }
    )
    yield auth_central_http_server_raw


class TestSignupEndpoint:
    """Test cases for the /api/signup endpoint."""

    @pytest.mark.timeout(30)
    def test_signup_success_authenticated_user(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
        dummy_echo_web_server,
    ):
        """Test successful signup for authenticated user without tenant."""
        # Create unique test data for a new user
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"signup-success-{request.node.name}", "newcompany.com"
        )

        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            state="test-signup-success",
        )

        # Create authenticated session (user not in database)
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "New",
                "family_name": "User",
            },
        )

        # Call signup endpoint
        response = session.post(
            f"{auth_central_http_server.url('')}/api/signup",
            json={"terms_accepted": True},
            timeout=60,
        )

        # Verify response
        assert (
            response.status_code == 201
        ), f"Expected 201, got {response.status_code}: {response.text}"

        data = response.json()
        assert data["success"] is True
        assert "user_id" in data
        assert "tenant_name" in data
        assert data["message"] == "Sign-up successful"

        # Verify user was created in database
        # The signup endpoint should have created the user in the tenant specified by the response
        assert data["tenant_name"] == VALID_ENTERPRISE_TENANT_NAME

        # Query the database to verify the user exists
        created_user = UserTestHelper.assert_user_exists(
            auth_central_grpc_client,
            VALID_ENTERPRISE_TENANT_ID,
            test_email,
            request_context.to_metadata(),
        )

        # Verify user data matches what was provided during signup
        assert created_user.email == test_email
        assert created_user.id == data["user_id"]

        # Verify user is associated with the correct tenant
        assert VALID_ENTERPRISE_TENANT_ID in created_user.tenants

    @pytest.mark.timeout(30)
    def test_signup_with_terms_revision(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
        dummy_echo_web_server,
    ):
        """Test signup with terms_revision field included."""
        # Create unique test data for a new user
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"signup-with-revision-{request.node.name}", "example.com"
        )

        # Prepare OAuth parameters
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            f"{dummy_echo_web_server}/customer-ui",
            VALID_CODE_CHALLENGE,
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Test",
                "family_name": "User",
            },
        )

        # Call signup endpoint with terms_revision
        response = session.post(
            f"{auth_central_http_server.url('')}/api/signup",
            json={
                "terms_accepted": True,
                "terms_revision": "enterprise_tos_295389227_v13",  # Current version
            },
            timeout=60,
        )

        # Verify response
        assert (
            response.status_code == 201
        ), f"Expected 201, got {response.status_code}: {response.text}"

        data = response.json()
        assert data["success"] is True
        assert "user_id" in data
        assert "tenant_name" in data

    @pytest.mark.timeout(30)
    def test_signup_with_outdated_terms_revision(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        request,
        dummy_echo_web_server,
    ):
        """Test signup fails with outdated terms_revision."""
        # Create unique test data for a new user
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"signup-outdated-revision-{request.node.name}", "example.com"
        )

        # Prepare OAuth parameters
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            f"{dummy_echo_web_server}/customer-ui",
            VALID_CODE_CHALLENGE,
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Test",
                "family_name": "User",
            },
        )

        # Call signup endpoint with outdated terms_revision
        response = session.post(
            f"{auth_central_http_server.url('')}/api/signup",
            json={
                "terms_accepted": True,
                "terms_revision": "enterprise_tos_295389227_v12",  # Old version
            },
            timeout=60,
        )

        # Should return conflict error
        assert (
            response.status_code == 409
        ), f"Expected 409, got {response.status_code}: {response.text}"

        data = response.json()
        assert "message" in data
        assert "Terms have been updated" in data["message"]

    @pytest.mark.timeout(30)
    def test_signup_unauthenticated_user(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
    ):
        """Test signup fails for unauthenticated user."""
        # Try to signup without authentication
        response = requests.post(
            f"{auth_central_http_server.url('')}/api/signup",
            json={"terms_accepted": True},
            timeout=60,
        )

        # Should return authentication error
        assert (
            response.status_code == 401
        ), f"Expected 401, got {response.status_code}: {response.text}"

        data = response.json()
        assert "message" in data
        assert data["message"] == "Authentication required"
        assert "support_info" in data
        assert "timestamp" in data["support_info"]

    @pytest.mark.timeout(30)
    def test_signup_terms_not_accepted(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        request,
        dummy_echo_web_server,
    ):
        """Test signup fails when terms are not accepted."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"signup-no-terms-{request.node.name}", "example.com"
        )

        # Prepare OAuth parameters
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            f"{dummy_echo_web_server}/customer-ui",
            VALID_CODE_CHALLENGE,
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Test",
                "family_name": "User",
            },
        )

        # Try to signup without accepting terms
        response = session.post(
            f"{auth_central_http_server.url('')}/api/signup",
            json={"terms_accepted": False},
            timeout=60,
        )

        # Should return bad request error
        assert (
            response.status_code == 400
        ), f"Expected 400, got {response.status_code}: {response.text}"

        data = response.json()
        assert data["message"] == "Terms must be accepted"

    @pytest.mark.timeout(30)
    def test_signup_invalid_request_body(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        request,
        dummy_echo_web_server,
    ):
        """Test signup fails with invalid request body."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"signup-invalid-{request.node.name}", "example.com"
        )

        # Prepare OAuth parameters
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            f"{dummy_echo_web_server}/customer-ui",
            VALID_CODE_CHALLENGE,
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Test",
                "family_name": "User",
            },
        )

        # Try to signup with invalid JSON
        response = session.post(
            f"{auth_central_http_server.url('')}/api/signup",
            data="invalid json",
            headers={"Content-Type": "application/json"},
            timeout=60,
        )

        # Should return bad request error
        assert (
            response.status_code == 400
        ), f"Expected 400, got {response.status_code}: {response.text}"

        data = response.json()
        assert data["message"] == "Invalid request body"

    @pytest.mark.timeout(30)
    def test_signup_missing_request_body(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        request,
        dummy_echo_web_server,
    ):
        """Test signup fails with missing request body."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"signup-no-body-{request.node.name}", "example.com"
        )

        # Prepare OAuth parameters
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            f"{dummy_echo_web_server}/customer-ui",
            VALID_CODE_CHALLENGE,
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Test",
                "family_name": "User",
            },
        )

        # Try to signup without body
        response = session.post(
            f"{auth_central_http_server.url('')}/api/signup",
            timeout=60,
        )

        # Should return bad request error
        assert (
            response.status_code == 400
        ), f"Expected 400, got {response.status_code}: {response.text}"

        data = response.json()
        assert data["message"] == "Invalid request body"

    @pytest.mark.timeout(30)
    def test_signup_session_updated_on_success(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        request,
        dummy_echo_web_server,
    ):
        """Test that successful signup updates the session with user_id and nonce."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"signup-session-{request.node.name}", "sessiontest.com"
        )

        # Prepare OAuth parameters
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            f"{dummy_echo_web_server}/customer-ui",
            VALID_CODE_CHALLENGE,
            state="test-session-update",
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Session",
                "family_name": "Test",
            },
        )

        # Call signup endpoint
        response = session.post(
            f"{auth_central_http_server.url('')}/api/signup",
            json={"terms_accepted": True},
            timeout=60,
        )

        # Verify successful signup
        assert response.status_code == 201
        data = response.json()
        assert "user_id" in data

        # Call bootstrap again to verify session was updated
        bootstrap_response = APITestHelper.call_bootstrap_endpoint(
            session, auth_central_http_server.url(""), oauth_params
        )

        bootstrap_data = APITestHelper.assert_bootstrap_response(
            bootstrap_response,
            expected_authenticated=True,
            expected_terms_approved=True,
        )

        # Should have redirect URL now that terms are approved
        assert "redirect_url" in bootstrap_data
        APITestHelper.assert_redirect_url_has_code(bootstrap_data)

    @pytest.mark.timeout(30)
    def test_signup_with_existing_tenant_user(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        request,
        dummy_echo_web_server,
    ):
        """Test signup fails when user's email domain already has a tenant."""
        # Create a user with email domain that already has a tenant
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"signup-existing-{request.node.name}", "augmentcode.com"
        )

        # Prepare OAuth parameters
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            f"{dummy_echo_web_server}/customer-ui",
            VALID_CODE_CHALLENGE,
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Existing",
                "family_name": "User",
            },
        )

        # Try to signup - should fail because email domain already has a tenant
        response = APITestHelper.signup_user(
            session, auth_central_http_server.url(""), terms_accepted=True
        )

        # Should fail with 400 because augmentcode.com already has a tenant
        assert response.status_code == 400
        data = response.json()
        assert "message" in data
        # The error message for YOUR_ORG_HAS_AUGMENT status
        assert (
            "Your email belongs to an organization that already has an Augment account"
            in data["message"]
        )

    @pytest.mark.timeout(30)
    def test_signup_feature_flag_disabled(
        self,
        auth_central_http_server_raw: auth_central_test_setup.AuthCentralHttpServer,
        request,
        dummy_echo_web_server,
    ):
        """Test signup returns 404 when feature flag is disabled."""
        # Disable the React frontend feature flag
        auth_central_http_server_raw.setup_test(
            feature_flags={
                "auth_central_react_frontend": False,
            }
        )

        try:
            # Create unique test data
            test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
                f"signup-disabled-{request.node.name}", "example.com"
            )

            # Prepare OAuth parameters
            oauth_params = TestDataGenerator.generate_oauth_params(
                CUSTOMER_UI_CLIENT_ID,
                f"{dummy_echo_web_server}/customer-ui",
                VALID_CODE_CHALLENGE,
            )

            # Create authenticated session
            session = OAuthFlowHelper.authenticate_user_without_terms(
                auth_central_http_server_raw,
                oauth_params,
                {
                    "sub": test_idp_user_id,
                    "email": test_email,
                    "given_name": "Test",
                    "family_name": "User",
                },
            )

            # Try to access signup endpoint
            response = APITestHelper.signup_user(
                session, auth_central_http_server_raw.url(""), terms_accepted=True
            )

            # Should return 404
            assert (
                response.status_code == 404
            ), f"Expected 404, got {response.status_code}: {response.text}"

            data = response.json()
            assert data["message"] == "Not found"

        finally:
            # Re-enable the feature flag for other tests
            auth_central_http_server_raw.setup_test(
                feature_flags={
                    "auth_central_react_frontend": True,
                }
            )
