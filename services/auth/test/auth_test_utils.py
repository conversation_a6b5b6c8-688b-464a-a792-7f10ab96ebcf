"""Common utilities for authentication tests to reduce code duplication."""

import requests
from urllib.parse import parse_qs, urlparse
from typing import Dict, List, Tuple, Optional, Any

from services.auth.central.server import (
    auth_central_test_setup,
    auth_pb2,
    auth_pb2_grpc,
    auth_entities_pb2,
)


class OAuthFlowHelper:
    """Helper class for simulating OAuth flows in tests.

    This helper manages the two-stage OAuth flow in auth-central:
    1. Client-to-AuthCentral OAuth flow
    2. AuthCentral-to-Auth0 OAuth flow
    """

    @staticmethod
    def start_oauth_flow(
        session: requests.Session,
        auth_server_url: str,
        oauth_params: Dict[str, str],
        timeout: int = 60,
    ) -> str:
        """
        Start the client-to-AuthCentral OAuth flow and get the Auth0 redirect URI.

        This initiates the OAuth flow between the client (in this case a test) and auth-central.
        Auth-central will then redirect to Auth0 (the identity provider) for actual
        user authentication.

        - <PERSON><PERSON> returns 302 direct redirect to Auth0

        Args:
            session: The requests session to use
            auth_server_url: Base URL of the auth-central server
            oauth_params: OAuth parameters for the client-to-AuthCentral flow
                        (client_id, redirect_uri, state, etc.)
            timeout: Request timeout in seconds

        Returns:
            The Auth0 redirect URI for user authentication
        """
        response = session.get(
            f"{auth_server_url}/login",
            params=oauth_params,
            timeout=timeout,
            allow_redirects=False,
        )

        if response.status_code == 302:
            internal_redirect_uri = response.headers["Location"]
        else:
            assert False, f"Login returned {response.status_code} {response.headers}"

        return internal_redirect_uri

    @staticmethod
    def parse_oauth_callback_params(redirect_uri: str) -> Dict[str, List[str]]:
        """
        Parse OAuth callback parameters from Auth0 redirect URI.

        This extracts the OAuth parameters that Auth0 includes when redirecting
        back to auth-central after user authentication.

        Args:
            redirect_uri: The Auth0 redirect URI containing OAuth parameters

        Returns:
            Parsed query parameters as a dictionary (state, nonce, redirect_uri, etc.)
        """
        parsed_qs = parse_qs(urlparse(redirect_uri).query)
        assert "state" in parsed_qs, f"{redirect_uri} does not contain state"
        assert "nonce" in parsed_qs, f"{redirect_uri} does not contain nonce"
        return parsed_qs

    @staticmethod
    def call_redirection_endpoint(
        session: requests.Session,
        callback_url: str,
        code: str,
        state: str,
        timeout: int = 60,
    ) -> requests.Response:
        """
        Call auth-central's OAuth callback endpoint with authorization code.

        This simulates Auth0 calling back to auth-central with an authorization
        code after successful user authentication. Auth-central will exchange
        this code for user information and establish a session.

        Args:
            session: The requests session to use
            callback_url: Auth-central's OAuth callback URL
            code: The authorization code from Auth0
            state: The state parameter for CSRF protection
            timeout: Request timeout in seconds

        Returns:
            The response from auth-central's callback endpoint
        """
        return session.get(
            callback_url,
            params={
                "code": code,
                "state": state,
            },
            timeout=timeout,
            allow_redirects=False,
        )

    @staticmethod
    def authenticate_user_without_terms(
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        oauth_params: Dict[str, str],
        userinfo: Dict[str, str],
    ) -> requests.Session:
        """
        Authenticate a user through OAuth flow but stop before terms acceptance.

        Returns a session that is authenticated but hasn't accepted terms.
        This is useful for testing terms acceptance flows.

        Args:
            auth_central_http_server: The auth server instance
            oauth_params: OAuth parameters as a dictionary
            userinfo: User information for OIDC (email, sub, etc.)

        Returns:
            An authenticated session without terms accepted
        """
        session = requests.Session()

        # Step 1: Start OAuth flow
        internal_redirect_uri = OAuthFlowHelper.start_oauth_flow(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Step 2: Parse OAuth callback parameters
        parsed_qs = OAuthFlowHelper.parse_oauth_callback_params(internal_redirect_uri)

        # Step 3: Set up fake OIDC userinfo
        auth_central_http_server.fake_oidc_server.set_userinfo(
            {"email_verified": True, "nonce": parsed_qs["nonce"][0], **userinfo}
        )

        # Step 4: Call auth-central's callback endpoint with auth code
        response = OAuthFlowHelper.call_redirection_endpoint(
            session, parsed_qs["redirect_uri"][0], "fake-code", parsed_qs["state"][0]
        )

        # Should redirect to terms-accept page or React app
        if response.status_code == 302:
            location = response.headers["Location"]
            # With React frontend enabled, redirects to / with query params
            # Without React frontend, redirects to /terms-accept
            assert location.startswith("/terms-accept") or location.startswith(
                "/?"
            ), f"Expected redirect to /terms-accept or /, got {location}"
        else:
            assert False, f"Expected 302 redirect, got {response.status_code}"

        return session


class UserTestHelper:
    """Helper class for user-related test operations."""

    @staticmethod
    def find_user_by_email(
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        tenant_id: str,
        email: str,
        request_context_metadata: List[Tuple[str, str]],
    ) -> Optional[auth_entities_pb2.User]:
        """
        Find a user by email in a tenant using ListTenantUsers.

        This is the correct pattern for finding users by email since
        GetUserRequest uses user_id field, not email field.

        Args:
            auth_central_grpc_client: The gRPC client
            tenant_id: The tenant ID to search in
            email: The email address to search for
            request_context_metadata: Request context metadata

        Returns:
            The user if found, None otherwise
        """
        list_users_response = auth_central_grpc_client.ListTenantUsers(
            auth_pb2.ListTenantUsersRequest(tenant_id=tenant_id),
            metadata=request_context_metadata,
        )

        return next((u for u in list_users_response.users if u.email == email), None)

    @staticmethod
    def assert_user_exists(
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        tenant_id: str,
        email: str,
        request_context_metadata: List[Tuple[str, str]],
    ) -> auth_entities_pb2.User:
        """
        Assert that a user exists in a tenant and return the user.

        Args:
            auth_central_grpc_client: The gRPC client
            tenant_id: The tenant ID to search in
            email: The email address to search for
            request_context_metadata: Request context metadata

        Returns:
            The user object

        Raises:
            AssertionError if user not found
        """
        user = UserTestHelper.find_user_by_email(
            auth_central_grpc_client, tenant_id, email, request_context_metadata
        )
        assert user is not None, f"User {email} should exist in tenant {tenant_id}"
        return user

    @staticmethod
    def assert_user_not_exists(
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        tenant_id: str,
        email: str,
        request_context_metadata: List[Tuple[str, str]],
    ) -> None:
        """
        Assert that a user does not exist in a tenant.

        Args:
            auth_central_grpc_client: The gRPC client
            tenant_id: The tenant ID to search in
            email: The email address to search for
            request_context_metadata: Request context metadata

        Raises:
            AssertionError if user exists
        """
        user = UserTestHelper.find_user_by_email(
            auth_central_grpc_client, tenant_id, email, request_context_metadata
        )
        assert user is None, f"User {email} should not exist in tenant {tenant_id}"


class TestDataGenerator:
    """Helper class for generating consistent test data."""

    @staticmethod
    def _generate_test_email(test_name: str, domain: str = "example.com") -> str:
        """Generate a unique test email based on test name."""
        return f"test-{test_name}@{domain}"

    @staticmethod
    def _generate_idp_user_id(test_name: str) -> str:
        """Generate a unique IDP user ID based on test name."""
        return f"idp|{test_name}"

    @staticmethod
    def generate_test_user_data(
        test_name: str, domain: str = "example.com"
    ) -> Tuple[str, str]:
        """
        Generate consistent test user data.

        Args:
            test_name: The name of the test, used to generate unique identifiers
            domain: The email domain to use (default: "example.com")

        Returns:
            A tuple of (email, idp_user_id) based on the same test_name
        """
        email = TestDataGenerator._generate_test_email(test_name, domain)
        idp_user_id = TestDataGenerator._generate_idp_user_id(test_name)
        return email, idp_user_id

    @staticmethod
    def generate_oauth_params(
        client_id: str,
        redirect_uri: str,
        code_challenge: str,
        state: Optional[str] = None,
    ) -> Dict[str, str]:
        """
        Generate standard OAuth parameters for testing.

        Args:
            client_id: OAuth client ID
            redirect_uri: OAuth redirect URI
            code_challenge: PKCE code challenge
            state: Optional state parameter (defaults to test-state)

        Returns:
            Dictionary of OAuth parameters
        """
        return {
            "client_id": client_id,
            "redirect_uri": redirect_uri,
            "state": state or "test-state",
            "code_challenge": code_challenge,
            "code_challenge_method": "S256",
            "response_type": "code",
        }


class APITestHelper:
    """Helper class for common API testing patterns."""

    @staticmethod
    def call_bootstrap_endpoint(
        session: requests.Session, auth_server_url: str, oauth_params: Dict[str, str]
    ) -> requests.Response:
        """
        Call the /api/bootstrap endpoint with OAuth parameters.

        Args:
            session: The authenticated session
            auth_server_url: Base URL of the auth server
            oauth_params: OAuth parameters to include

        Returns:
            The response from the bootstrap endpoint
        """
        bootstrap_url = f"{auth_server_url}/api/bootstrap"
        return session.get(bootstrap_url, params=oauth_params)

    @staticmethod
    def accept_terms(
        session: requests.Session,
        auth_server_url: str,
        oauth_params: Dict[str, str],
        accepted: bool = True,
    ) -> requests.Response:
        """
        Call the /api/terms/accept endpoint.

        Args:
            session: The authenticated session
            auth_server_url: Base URL of the auth server
            oauth_params: OAuth parameters to include
            accepted: Whether to accept or reject terms

        Returns:
            The response from the terms accept endpoint
        """
        return session.post(
            f"{auth_server_url}/api/terms/accept",
            json={
                "accepted": accepted,
                "client_id": oauth_params["client_id"],
                "redirect_uri": oauth_params["redirect_uri"],
                "state": oauth_params["state"],
                "code_challenge": oauth_params["code_challenge"],
                "code_challenge_method": oauth_params["code_challenge_method"],
            },
            timeout=60,
        )

    @staticmethod
    def assert_bootstrap_response(
        response: requests.Response,
        expected_authenticated: bool,
        expected_terms_approved: Optional[bool] = None,
        expected_needs_signup: Optional[bool] = None,
        expected_needs_tenant_join: Optional[bool] = None,
    ) -> Dict[str, Any]:
        """
        Assert common bootstrap endpoint response patterns.

        Args:
            response: The response to check
            expected_authenticated: Expected authenticated status
            expected_terms_approved: Expected terms_approved status (if authenticated)
            expected_needs_signup: Expected needs_signup status
            expected_needs_tenant_join: Expected needs_tenant_join status

        Returns:
            The response data for further assertions
        """
        assert (
            response.status_code == 200
        ), f"Expected 200, got {response.status_code}: {response.text}"

        data = response.json()
        assert data["authenticated"] == expected_authenticated

        if expected_authenticated:
            if expected_terms_approved is not None:
                assert data["terms_approved"] == expected_terms_approved
            if expected_needs_signup is not None:
                assert data.get("needs_signup") == expected_needs_signup
            if expected_needs_tenant_join is not None:
                assert data.get("needs_tenant_join") == expected_needs_tenant_join

        return data

    @staticmethod
    def assert_redirect_url_has_code(data: Dict[str, Any]) -> None:
        """Assert that the response contains a redirect URL with an authorization code."""
        assert "redirect_url" in data
        assert "code=" in data["redirect_url"]

    @staticmethod
    def signup_user(
        session: requests.Session,
        auth_server_url: str,
        terms_accepted: bool = True,
    ) -> requests.Response:
        """
        Call the /api/signup endpoint.

        Args:
            session: The authenticated session
            auth_server_url: Base URL of the auth server
            terms_accepted: Whether to accept terms in the signup

        Returns:
            The response from the signup endpoint
        """
        return session.post(
            f"{auth_server_url}/api/signup",
            json={"terms_accepted": terms_accepted},
            timeout=60,
        )


# Common test constants that can be imported
TEST_OAUTH_CODE = "fake-code"
TEST_OAUTH_STATE_PREFIX = "test-state"
