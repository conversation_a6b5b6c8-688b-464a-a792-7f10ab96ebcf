"""Integration tests for the terms of service API endpoints."""

import pytest
import requests

from services.auth.central.server import (
    auth_central_test_setup,
    auth_pb2,
    auth_pb2_grpc,
)
from services.auth.test.conftest import (
    VALID_CODE_CHALLENGE,
    VALID_ENTERPRISE_TENANT_ID,
    VALID_COMMUNITY_TENANT_ID,
    CUSTOMER_UI_CLIENT_ID,
)
from services.auth.test.auth_test_utils import (
    OAuth<PERSON>lowHelper,
    UserTestHelper,
    TestDataGenerator,
    APITestHelper,
)


@pytest.fixture()
def auth_central_http_server(auth_central_http_server_raw):
    """Set up the auth central HTTP server for testing."""
    # Enable the React frontend feature flag for terms API tests
    auth_central_http_server_raw.setup_test(
        feature_flags={
            "auth_central_react_frontend": True,
        }
    )
    yield auth_central_http_server_raw


class TestTermsStatus:
    """Test cases for terms status via the /api/bootstrap endpoint."""

    @pytest.mark.timeout(30)
    def test_bootstrap_terms_authenticated_user_enterprise_tenant(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
        dummy_echo_web_server,
    ):
        """Test bootstrap endpoint returns terms info for authenticated user with enterprise tenant."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"terms-status-{request.node.name}", "augmentcode.com"
        )

        # Create user in database
        add_user_response = auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=test_email,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
            ),
            metadata=request_context.to_metadata(),
        )
        user_id = add_user_response.user.id
        assert user_id, "User should have been created with an ID"

        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            state="test-state-123",
        )

        # Create authenticated session without accepting terms
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Test",
                "family_name": "User",
            },
        )

        # Call bootstrap endpoint
        response = APITestHelper.call_bootstrap_endpoint(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Verify response
        data = APITestHelper.assert_bootstrap_response(
            response, expected_authenticated=True, expected_terms_approved=False
        )
        assert data["terms_type"] == "enterprise"
        assert (
            data["terms_url"]
            == "https://www.augmentcode.com/terms-of-service/enterprise"
        )
        assert data["tenant_name"] == "augment"
        assert "terms_revision" in data
        assert data["terms_revision"] == "enterprise_tos_295389227_v13"

    @pytest.mark.timeout(30)
    def test_bootstrap_terms_authenticated_user_community_tenant(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context_community,
        request,
        dummy_echo_web_server,
    ):
        """Test bootstrap endpoint returns terms info for authenticated user with community tenant."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"terms-community-{request.node.name}", "community-team.com"
        )

        # Create user in community tenant
        auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=test_email,
                tenant_id=VALID_COMMUNITY_TENANT_ID,
            ),
            metadata=request_context_community.to_metadata(),
        )

        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            state="test-state-456",
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Community",
                "family_name": "User",
            },
        )

        # Call bootstrap endpoint
        response = APITestHelper.call_bootstrap_endpoint(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Verify response
        data = APITestHelper.assert_bootstrap_response(
            response, expected_authenticated=True, expected_terms_approved=False
        )
        assert data["terms_type"] == "community"
        assert (
            data["terms_url"]
            == "https://www.augmentcode.com/terms-of-service/community"
        )
        assert data["tenant_name"] == "community"
        assert "terms_revision" in data
        assert data["terms_revision"] == "vangaurd_tos_306690148_v6"

    @pytest.mark.timeout(30)
    def test_bootstrap_terms_unauthenticated_user(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        dummy_echo_web_server,
    ):
        """Test bootstrap endpoint for unauthenticated user."""
        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            state="test-state-789",
        )

        # Call bootstrap endpoint without authentication
        response = APITestHelper.call_bootstrap_endpoint(
            requests.Session(), auth_central_http_server.url(""), oauth_params
        )

        # Verify response
        data = APITestHelper.assert_bootstrap_response(
            response, expected_authenticated=False
        )
        # For unauthenticated users, we only get the authenticated field
        assert "terms_approved" not in data
        assert "terms_type" not in data
        assert "terms_url" not in data
        assert "tenant_name" not in data


class TestTermsAcceptEndpoint:
    """Test cases for the /api/terms/accept endpoint."""

    @pytest.mark.timeout(30)
    def test_terms_accept_success_enterprise_tenant(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
        dummy_echo_web_server,
    ):
        """Test successful terms acceptance for enterprise tenant."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"terms-accept-{request.node.name}", "augmentcode.com"
        )

        # Create user in database
        add_user_response = auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=test_email,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
            ),
            metadata=request_context.to_metadata(),
        )
        user_id = add_user_response.user.id
        assert user_id, "User should have been created with an ID"

        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            state="test-state-accept",
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Test",
                "family_name": "User",
            },
        )

        # Accept terms
        response = APITestHelper.accept_terms(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Verify response
        assert (
            response.status_code == 200
        ), f"Expected 200, got {response.status_code}: {response.text}"

        data = response.json()
        assert data["success"] is True
        assert "redirect_url" in data
        APITestHelper.assert_redirect_url_has_code(data)
        assert "state=test-state-accept" in data["redirect_url"]

    @pytest.mark.timeout(30)
    def test_terms_accept_unauthenticated_user(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        dummy_echo_web_server,
    ):
        """Test terms acceptance for unauthenticated user."""
        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            state="test-state-unauth",
        )

        # Try to accept terms without authentication
        response = APITestHelper.accept_terms(
            requests.Session(), auth_central_http_server.url(""), oauth_params
        )

        # Should return authentication error
        assert (
            response.status_code == 401
        ), f"Expected 401, got {response.status_code}: {response.text}"

        data = response.json()
        assert "message" in data
        assert "Authentication required" in data["message"]
        assert "support_info" in data

    @pytest.mark.timeout(30)
    def test_terms_accept_not_accepted(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
        dummy_echo_web_server,
    ):
        """Test terms acceptance when accepted=false."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"terms-not-accepted-{request.node.name}", "augmentcode.com"
        )

        # Create user in database
        auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=test_email,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
            ),
            metadata=request_context.to_metadata(),
        )

        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            state="test-state-not-accepted",
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
            },
        )

        # Try to submit with accepted=false
        terms_accept_url = auth_central_http_server.url("/api/terms/accept")
        accept_data = {
            **oauth_params,
            "accepted": False,
        }

        response = session.post(
            terms_accept_url,
            json=accept_data,
            headers={"Content-Type": "application/json"},
        )

        # Should return error
        assert (
            response.status_code == 400
        ), f"Expected 400, got {response.status_code}: {response.text}"

        data = response.json()
        assert "message" in data
        assert "Terms must be accepted to continue" in data["message"]

    @pytest.mark.timeout(30)
    def test_terms_accept_missing_parameters(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
        dummy_echo_web_server,
    ):
        """Test terms acceptance with missing OAuth parameters."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"terms-missing-params-{request.node.name}", "augmentcode.com"
        )

        # Create user in database
        auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=test_email,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
            ),
            metadata=request_context.to_metadata(),
        )

        # Create authenticated session
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            f"{dummy_echo_web_server}/customer-ui",
            VALID_CODE_CHALLENGE,
            state="test-state-missing-params",
        )
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
            },
        )

        # Try to accept terms with missing parameters
        terms_accept_url = auth_central_http_server.url("/api/terms/accept")
        incomplete_data = {
            "client_id": CUSTOMER_UI_CLIENT_ID,
            # Missing redirect_uri, state, code_challenge
            "accepted": True,
        }

        response = session.post(
            terms_accept_url,
            json=incomplete_data,
            headers={"Content-Type": "application/json"},
        )

        # Should return error
        assert (
            response.status_code == 400
        ), f"Expected 400, got {response.status_code}: {response.text}"

        data = response.json()
        assert "message" in data
        assert "Missing required OAuth parameters" in data["message"]

    @pytest.mark.timeout(30)
    def test_terms_accept_version_mismatch(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
        dummy_echo_web_server,
    ):
        """Test terms acceptance with mismatched terms version."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"terms-version-mismatch-{request.node.name}", "augmentcode.com"
        )

        # Create user in database
        auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=test_email,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
            ),
            metadata=request_context.to_metadata(),
        )

        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            state="test-state-version-mismatch",
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Test",
                "family_name": "User",
            },
        )

        # Try to accept terms with an outdated version
        terms_accept_url = auth_central_http_server.url("/api/terms/accept")
        accept_data = {
            **oauth_params,
            "accepted": True,
            "terms_revision": "enterprise_tos_295389227_v12",  # Old version
        }

        response = session.post(
            terms_accept_url,
            json=accept_data,
            headers={"Content-Type": "application/json"},
        )

        # Should return conflict error
        assert (
            response.status_code == 409
        ), f"Expected 409, got {response.status_code}: {response.text}"

        data = response.json()
        assert "message" in data
        assert "Terms have been updated" in data["message"]
        assert "Please refresh the page" in data["message"]
        assert "support_info" in data
        assert "timestamp" in data["support_info"]

    @pytest.mark.timeout(30)
    def test_terms_accept_with_correct_version(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
        dummy_echo_web_server,
    ):
        """Test terms acceptance with correct terms version."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"terms-correct-version-{request.node.name}", "augmentcode.com"
        )

        # Create user in database
        auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=test_email,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
            ),
            metadata=request_context.to_metadata(),
        )

        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            state="test-state-correct-version",
        )

        # Create authenticated session
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Test",
                "family_name": "User",
            },
        )

        # Accept terms with the correct version
        terms_accept_url = auth_central_http_server.url("/api/terms/accept")
        accept_data = {
            **oauth_params,
            "accepted": True,
            "terms_revision": "enterprise_tos_295389227_v13",  # Current version
        }

        response = session.post(
            terms_accept_url,
            json=accept_data,
            headers={"Content-Type": "application/json"},
        )

        # Should succeed
        assert (
            response.status_code == 200
        ), f"Expected 200, got {response.status_code}: {response.text}"

        data = response.json()
        assert data["success"] is True
        assert "redirect_url" in data
        APITestHelper.assert_redirect_url_has_code(data)

    @pytest.mark.timeout(30)
    def test_terms_accept_with_tenant_join(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
        dummy_echo_web_server,
    ):
        """Test that /api/terms/accept can handle users joining an existing tenant."""
        # Create unique test data
        # Use augmentcode.com domain which has an existing tenant
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"join-test-{request.node.name}", "augmentcode.com"
        )

        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            state="test-state-123",
        )

        # Authenticate user without terms acceptance
        session = OAuthFlowHelper.authenticate_user_without_terms(
            auth_central_http_server,
            oauth_params,
            {
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Test",
                "family_name": "User",
            },
        )

        # Verify user doesn't exist yet
        UserTestHelper.assert_user_not_exists(
            auth_central_grpc_client,
            VALID_ENTERPRISE_TENANT_ID,
            test_email,
            request_context.to_metadata(),
        )

        # Call /api/terms/accept to accept terms and join tenant
        response = APITestHelper.accept_terms(
            session, auth_central_http_server.url(""), oauth_params
        )

        assert response.status_code == 200
        data = response.json()
        assert data["success"] is True
        assert "redirect_url" in data

        # Verify user was created and added to tenant
        created_user = UserTestHelper.assert_user_exists(
            auth_central_grpc_client,
            VALID_ENTERPRISE_TENANT_ID,
            test_email,
            request_context.to_metadata(),
        )
        assert len(created_user.tenants) > 0

        # Verify user is in the augmentcode.com tenant
        tenant_ids = created_user.tenants
        # The augmentcode.com tenant (test123) should be in the user's tenants
        assert VALID_ENTERPRISE_TENANT_ID in tenant_ids

        # Verify terms were accepted by checking bootstrap
        bootstrap_response = APITestHelper.call_bootstrap_endpoint(
            session, auth_central_http_server.url(""), oauth_params
        )
        APITestHelper.assert_bootstrap_response(
            bootstrap_response,
            expected_authenticated=True,
            expected_terms_approved=True,
        )
