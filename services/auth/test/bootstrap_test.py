"""Integration tests for the /api/bootstrap endpoint."""

import pytest
import requests
from urllib.parse import parse_qs, urlparse

from services.auth.central.server import (
    auth_central_test_setup,
    auth_pb2,
    auth_pb2_grpc,
)
from services.auth.test.conftest import (
    VALID_CODE_CHALLENGE,
    VALID_ENTERPRISE_TENANT_ID,
    VALID_SELF_SERVE_TENANT_ID,
    CUSTOMER_UI_CLIENT_ID,
    VALID_CLIENT_ID as VSCODE_CLIENT_ID,
    VALID_REDIRECT_URI as VSCODE_REDIRECT_URI,
)
from services.auth.test.auth_test_utils import (
    <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>per,
    TestDataGenerator,
    APITestHelper,
)


@pytest.fixture()
def auth_central_http_server(auth_central_http_server_raw):
    """Set up the auth central HTTP server for testing."""
    # Enable the React frontend feature flag for bootstrap endpoint tests
    auth_central_http_server_raw.setup_test(
        feature_flags={
            "auth_central_react_frontend": True,
        }
    )
    yield auth_central_http_server_raw


class TestBootstrapEndpoint:
    """Test cases for the /api/bootstrap endpoint."""

    @pytest.mark.timeout(30)
    def test_bootstrap_authenticated_user_with_database_record(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
        dummy_echo_web_server,
    ):
        """Test bootstrap endpoint for authenticated user with existing database record."""
        # Create unique test data
        test_name = request.node.name
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            test_name, "augmentcode.com"
        )

        # First, create a user in the database
        add_user_response = auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=test_email,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
            ),
            metadata=request_context.to_metadata(),
        )
        user_id = add_user_response.user.id
        assert user_id, "User should have been created with an ID"

        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            "test-state-123",
        )

        # Create a new session for this test
        session = requests.Session()

        # Step 1: Start OAuth flow
        internal_redirect_uri = OAuthFlowHelper.start_oauth_flow(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Step 2: Parse OAuth callback parameters
        parsed_qs = OAuthFlowHelper.parse_oauth_callback_params(internal_redirect_uri)

        # Step 3: Set up fake OIDC userinfo
        auth_central_http_server.fake_oidc_server.set_userinfo(
            {
                "email_verified": True,
                "nonce": parsed_qs["nonce"][0],
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "Test",
                "family_name": "User",
            }
        )

        # Step 4: Call auth-central's callback endpoint (this creates the session)
        response = OAuthFlowHelper.call_redirection_endpoint(
            session, parsed_qs["redirect_uri"][0], "fake-code", parsed_qs["state"][0]
        )

        # With React frontend enabled, this should redirect to / with OAuth params
        assert response.status_code == 302
        redirect_location = response.headers["Location"]
        assert redirect_location.startswith(
            "/"
        ), f"Expected redirect to /, got {redirect_location}"

        # Step 5: Since user exists but doesn't have terms approved, we need to approve terms
        terms_response = APITestHelper.accept_terms(
            session, auth_central_http_server.url(""), oauth_params
        )
        assert (
            terms_response.status_code == 200
        ), f"Terms accept failed: {terms_response.text}"

        # Step 6: Call the bootstrap endpoint with the authenticated session
        response = APITestHelper.call_bootstrap_endpoint(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Verify response
        data = APITestHelper.assert_bootstrap_response(
            response, expected_authenticated=True, expected_terms_approved=True
        )
        APITestHelper.assert_redirect_url_has_code(data)

    @pytest.mark.timeout(30)
    def test_bootstrap_authenticated_user_without_database_record(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        request,
        dummy_echo_web_server,
    ):
        """Test bootstrap endpoint for authenticated user without database record."""
        # Create unique test data for a user that doesn't exist in DB
        # Use a domain that doesn't have a tenant configured to test signup flow
        test_name = request.node.name
        test_email, _ = TestDataGenerator.generate_test_user_data(
            test_name, "example.com"
        )
        _, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"new-{test_name}"
        )

        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            "test-state-456",
        )

        # Create a new session for this test
        session = requests.Session()

        # Step 1: Start OAuth flow
        internal_redirect_uri = OAuthFlowHelper.start_oauth_flow(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Step 2: Parse OAuth callback parameters
        parsed_qs = OAuthFlowHelper.parse_oauth_callback_params(internal_redirect_uri)

        # Step 3: Set up fake OIDC userinfo
        auth_central_http_server.fake_oidc_server.set_userinfo(
            {
                "email_verified": True,
                "nonce": parsed_qs["nonce"][0],
                "sub": test_idp_user_id,
                "email": test_email,
                "given_name": "New",
                "family_name": "User",
            }
        )

        # Step 4: Call auth-central's callback endpoint (this creates the session)
        response = OAuthFlowHelper.call_redirection_endpoint(
            session, parsed_qs["redirect_uri"][0], "fake-code", parsed_qs["state"][0]
        )

        # With React frontend enabled, this should redirect to / with OAuth params
        assert response.status_code == 302
        redirect_location = response.headers["Location"]
        assert redirect_location.startswith(
            "/"
        ), f"Expected redirect to /, got {redirect_location}"

        # Step 5: Call the bootstrap endpoint with the authenticated session
        response = APITestHelper.call_bootstrap_endpoint(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Verify response
        data = APITestHelper.assert_bootstrap_response(
            response,
            expected_authenticated=True,
            expected_terms_approved=False,
            expected_needs_signup=True,
        )
        assert "redirect_url" not in data  # No redirect for users who need signup

    @pytest.mark.timeout(30)
    def test_bootstrap_unauthenticated_user(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        dummy_echo_web_server,
    ):
        """Test bootstrap endpoint for unauthenticated user."""
        # Prepare OAuth parameters
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            "test-state-789",
        )

        # Create a new session (no authentication)
        session = requests.Session()

        # Call the bootstrap endpoint without authentication
        response = APITestHelper.call_bootstrap_endpoint(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Verify response
        APITestHelper.assert_bootstrap_response(response, expected_authenticated=False)
        data = response.json()
        assert "terms_approved" not in data
        assert "redirect_url" not in data

    @pytest.mark.timeout(30)
    def test_bootstrap_missing_oauth_parameters(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
        dummy_echo_web_server,
    ):
        """Test bootstrap endpoint with missing OAuth parameters."""
        # Create a user for testing
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"bootstrap-params-{request.node.name}", "augmentcode.com"
        )

        # Create user in database
        auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=test_email,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
            ),
            metadata=request_context.to_metadata(),
        )

        # Create authenticated session using direct OAuth flow
        session = requests.Session()
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID, customer_ui_redirect_uri, VALID_CODE_CHALLENGE
        )

        # Perform OAuth flow
        internal_redirect_uri = OAuthFlowHelper.start_oauth_flow(
            session, auth_central_http_server.url(""), oauth_params
        )
        parsed_qs = OAuthFlowHelper.parse_oauth_callback_params(internal_redirect_uri)
        auth_central_http_server.fake_oidc_server.set_userinfo(
            {
                "email_verified": True,
                "nonce": parsed_qs["nonce"][0],
                "sub": test_idp_user_id,
                "email": test_email,
            }
        )
        OAuthFlowHelper.call_redirection_endpoint(
            session, parsed_qs["redirect_uri"][0], "fake-code", parsed_qs["state"][0]
        )

        # Call bootstrap with missing parameters (no client_id)
        incomplete_params = {
            "redirect_uri": customer_ui_redirect_uri,
            "state": "test-state",
            # Missing client_id and code_challenge
        }

        bootstrap_url = auth_central_http_server.url(
            "/api/bootstrap", list(incomplete_params.items())
        )
        response = session.get(bootstrap_url)

        # Should return error
        assert (
            response.status_code == 400
        ), f"Expected 400, got {response.status_code}: {response.text}"

        data = response.json()
        assert "message" in data
        assert "Missing required OAuth parameters" in data["message"]
        assert "support_info" in data
        assert "timestamp" in data["support_info"]

    @pytest.mark.timeout(30)
    def test_bootstrap_with_self_serve_tenant(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context_self_serve,
        request,
        dummy_echo_web_server,
    ):
        """Test bootstrap endpoint with self-serve tenant user."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"self-serve-{request.node.name}", "self-serve-team.com"
        )

        # Create user in self-serve tenant
        auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=test_email,
                tenant_id=VALID_SELF_SERVE_TENANT_ID,
            ),
            metadata=request_context_self_serve.to_metadata(),
        )

        # Create authenticated session using direct OAuth flow
        session = requests.Session()
        customer_ui_redirect_uri = f"{dummy_echo_web_server}/customer-ui"
        oauth_params = TestDataGenerator.generate_oauth_params(
            CUSTOMER_UI_CLIENT_ID,
            customer_ui_redirect_uri,
            VALID_CODE_CHALLENGE,
            "test-state-self-serve",
        )

        # Perform OAuth flow
        internal_redirect_uri = OAuthFlowHelper.start_oauth_flow(
            session, auth_central_http_server.url(""), oauth_params
        )
        parsed_qs = OAuthFlowHelper.parse_oauth_callback_params(internal_redirect_uri)
        auth_central_http_server.fake_oidc_server.set_userinfo(
            {
                "email_verified": True,
                "nonce": parsed_qs["nonce"][0],
                "sub": test_idp_user_id,
                "email": test_email,
            }
        )
        callback_response = OAuthFlowHelper.call_redirection_endpoint(
            session, parsed_qs["redirect_uri"][0], "fake-code", parsed_qs["state"][0]
        )
        # With React frontend, this redirects to / with OAuth params
        assert callback_response.status_code == 302
        assert callback_response.headers["Location"].startswith("/")

        # Accept terms (user is already in tenant but needs terms approval)
        APITestHelper.accept_terms(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Call bootstrap endpoint using the same session
        response = APITestHelper.call_bootstrap_endpoint(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Verify response
        data = APITestHelper.assert_bootstrap_response(
            response, expected_authenticated=True, expected_terms_approved=True
        )
        assert "redirect_url" in data

        # Verify customer UI does NOT get tenant_url (instant_redirect=True)
        redirect_url = data["redirect_url"]
        assert "tenant_url=" not in redirect_url
        # Only code and state should be included
        parsed = urlparse(redirect_url)
        query_params = parse_qs(parsed.query)
        assert "code" in query_params
        assert "state" in query_params
        assert "tenant_url" not in query_params

    @pytest.mark.timeout(30)
    def test_bootstrap_vscode_extension_redirect(
        self,
        auth_central_http_server: auth_central_test_setup.AuthCentralHttpServer,
        auth_central_grpc_client: auth_pb2_grpc.AuthServiceStub,
        request_context,
        request,
    ):
        """Test bootstrap endpoint for VS Code extension with custom protocol redirect."""
        # Create unique test data
        test_email, test_idp_user_id = TestDataGenerator.generate_test_user_data(
            f"bootstrap-vscode-{request.node.name}", "augmentcode.com"
        )

        # Create user in database
        auth_central_grpc_client.AddUserToTenant(
            auth_pb2.AddUserToTenantRequest(
                email=test_email,
                tenant_id=VALID_ENTERPRISE_TENANT_ID,
            ),
            metadata=request_context.to_metadata(),
        )

        # Create authenticated session using direct OAuth flow
        session = requests.Session()
        oauth_params = TestDataGenerator.generate_oauth_params(
            VSCODE_CLIENT_ID, VSCODE_REDIRECT_URI, VALID_CODE_CHALLENGE
        )

        # Perform OAuth flow
        internal_redirect_uri = OAuthFlowHelper.start_oauth_flow(
            session, auth_central_http_server.url(""), oauth_params
        )
        parsed_qs = OAuthFlowHelper.parse_oauth_callback_params(internal_redirect_uri)
        auth_central_http_server.fake_oidc_server.set_userinfo(
            {
                "email_verified": True,
                "nonce": parsed_qs["nonce"][0],
                "sub": test_idp_user_id,
                "email": test_email,
            }
        )
        callback_response = OAuthFlowHelper.call_redirection_endpoint(
            session, parsed_qs["redirect_uri"][0], "fake-code", parsed_qs["state"][0]
        )
        # With React frontend, this redirects to / with OAuth params
        assert callback_response.status_code == 302
        assert callback_response.headers["Location"].startswith("/")

        # Accept terms (user is already in tenant but needs terms approval)
        APITestHelper.accept_terms(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Call bootstrap with VS Code extension parameters
        response = APITestHelper.call_bootstrap_endpoint(
            session, auth_central_http_server.url(""), oauth_params
        )

        # Verify response
        data = APITestHelper.assert_bootstrap_response(
            response, expected_authenticated=True, expected_terms_approved=True
        )
        assert "redirect_url" in data

        # Verify VS Code extension redirect URL format
        redirect_url = data["redirect_url"]
        assert redirect_url.startswith(VSCODE_REDIRECT_URI)
        assert "code=" in redirect_url
        assert "state=test-state" in redirect_url

        # Verify it's using the custom protocol
        assert redirect_url.startswith("vscode://augment.vscode-augment/auth/result")

        # Verify tenant_url is included for VS Code extension
        assert "tenant_url=" in redirect_url
        # Extract and verify the tenant URL
        parsed = urlparse(redirect_url)
        query_params = parse_qs(parsed.query)
        assert "tenant_url" in query_params
        tenant_url = query_params["tenant_url"][0]
        # Should be in format https://{namespace}.{domain}/
        assert tenant_url.startswith("https://")
        assert tenant_url.endswith("/")
        assert "augment" in tenant_url  # Should contain the namespace
