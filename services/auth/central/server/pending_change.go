package main

import (
	"context"
	"fmt"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
)

// ModifyPendingChange modifies a pending change for a user or team based on their billing info
// Uses the billing info to determine whether to modify in user or tenant subscription mapping
// This function is called by other CRUD functions in the file, you should sparingly need to use this function outside this file
func ModifyPendingChange(
	ctx context.Context,
	daoFactory *DAOFactory,
	billingInfo *UserBillingInfo,
	modifier func(current *auth_entities.PendingChange) *auth_entities.PendingChange,
	operationName string,
) error {
	if billingInfo.IsSelfServeTeam {
		// Modify in tenant subscription mapping
		tenantSubscriptionMappingDAO := daoFactory.GetTenantSubscriptionMappingDAO()

		// We need to find the tenant ID - get it from the user's tenants
		userDAO := daoFactory.GetUserDAO()
		user, err := userDAO.Get(ctx, billingInfo.UserID)
		if err != nil {
			return fmt.Errorf("failed to get user: %v", err)
		}
		if user == nil || len(user.Tenants) == 0 {
			return fmt.Errorf("user has no tenants")
		}

		tenantID := user.Tenants[0] // Self-serve teams have one tenant
		_, err = tenantSubscriptionMappingDAO.TryUpdate(ctx, tenantID, func(mapping *auth_entities.TenantSubscriptionMapping) bool {
			mapping.PendingChange = modifier(mapping.PendingChange)
			return true
		}, DefaultRetry)
		if err != nil {
			return fmt.Errorf("failed to %s team pending change: %v", operationName, err)
		}

		log.Ctx(ctx).Info().Str("tenant_id", tenantID).Str("user_id", billingInfo.UserID).Msgf("Successfully %s team pending change", operationName)
	} else {
		// Modify in user
		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.TryUpdate(ctx, billingInfo.UserID, func(user *auth_entities.User) bool {
			user.PendingChange = modifier(user.PendingChange)
			return true
		}, DefaultRetry)
		if err != nil {
			return fmt.Errorf("failed to %s user pending change: %v", operationName, err)
		}

		log.Ctx(ctx).Info().Str("user_id", billingInfo.UserID).Msgf("Successfully %s user pending change", operationName)
	}

	return nil
}

// ClearPendingChange clears a pending change for a user or team based on their billing info
func ClearPendingChange(
	ctx context.Context,
	daoFactory *DAOFactory,
	billingInfo *UserBillingInfo,
) error {
	if billingInfo.PendingChange == nil {
		log.Ctx(ctx).Info().Str("user_id", billingInfo.UserID).Msg("No pending change to clear")
		return nil
	}

	return ModifyPendingChange(ctx, daoFactory, billingInfo, func(current *auth_entities.PendingChange) *auth_entities.PendingChange {
		return nil // Clear the pending change
	}, "clear")
}

// UpdatePendingChange updates a pending change for a user or team based on their billing info
func UpdatePendingChange(
	ctx context.Context,
	daoFactory *DAOFactory,
	billingInfo *UserBillingInfo,
	updatedChange *auth_entities.PendingChange,
) error {
	if billingInfo.PendingChange == nil {
		log.Ctx(ctx).Info().Str("user_id", billingInfo.UserID).Msg("No pending change to update")
		return nil
	}

	return ModifyPendingChange(ctx, daoFactory, billingInfo, func(current *auth_entities.PendingChange) *auth_entities.PendingChange {
		return updatedChange // Replace with the updated change
	}, "update")
}

// PlanChangeOptions contains optional data for creating a plan change
type PlanChangeOptions struct {
	CheckoutSessionUrl *string
	CheckoutSessionId  *string
	TotalCost          *string
	OrbPendingChangeId *string
}

// CreatePendingPlanChange creates a pending plan change for a user or team
// Returns an error if there's already a PENDING change or if the update fails
// The pendingChangeId should be the ID from the published tier change message
func CreatePendingPlanChange(
	ctx context.Context, daoFactory *DAOFactory, user *auth_entities.User, billingInfo *UserBillingInfo, targetPlanId string, pendingChangeId string, options *PlanChangeOptions,
) error {
	// Check if there's already a PENDING change
	if billingInfo.PendingChange != nil && billingInfo.PendingChange.GetPlanChange() != nil && billingInfo.PendingChange.GetPlanChange().Status == auth_entities.PlanChange_PENDING {
		log.Ctx(ctx).Error().Str("user_id", billingInfo.UserID).Msg("Pending change already exists")
		return status.Error(codes.FailedPrecondition, "Pending change already exists")
	}

	changeId := pendingChangeId

	// Create the plan change
	planChange := &auth_entities.PlanChange{
		Status:       auth_entities.PlanChange_PENDING,
		TargetPlanId: targetPlanId,
	}

	// Add optional fields if provided
	if options != nil {
		if options.CheckoutSessionUrl != nil {
			planChange.CheckoutSessionUrl = *options.CheckoutSessionUrl
		}
		if options.CheckoutSessionId != nil {
			planChange.CheckoutSessionId = options.CheckoutSessionId
		}
		if options.TotalCost != nil {
			planChange.TotalCost = *options.TotalCost
		}
		if options.OrbPendingChangeId != nil {
			planChange.OrbPendingChangeId = options.OrbPendingChangeId
		}
	}

	// Create the entire pending change
	newPendingChange := &auth_entities.PendingChange{
		Id:        changeId,
		CreatedAt: timestamppb.Now(),
		UpdatedAt: timestamppb.Now(),
		Change: &auth_entities.PendingChange_PlanChange{
			PlanChange: planChange,
		},
	}

	// Use the unified helper to create the pending change
	return ModifyPendingChange(ctx, daoFactory, billingInfo, func(current *auth_entities.PendingChange) *auth_entities.PendingChange {
		return newPendingChange // Set the new pending change
	}, "create plan change")
}

// AssignMessageIDToPendingChange assigns a message ID to a pending plan change if it's not already set.
func AssignMessageIDToPendingChange(
	ctx context.Context,
	daoFactory *DAOFactory,
	billingInfo *UserBillingInfo,
	messageID string,
) error {
	return ModifyPendingChange(ctx, daoFactory, billingInfo, func(current *auth_entities.PendingChange) *auth_entities.PendingChange {
		if current == nil || current.GetPlanChange() == nil {
			log.Ctx(ctx).Info().Str("user_id", billingInfo.UserID).Msg("No pending change found to assign message ID")
			return current
		}

		// Only assign the message ID if it's not already set
		if current.GetPlanChange().MessageId == nil || *current.GetPlanChange().MessageId == "" {
			current.GetPlanChange().MessageId = &messageID
			current.UpdatedAt = timestamppb.Now()
		}
		return current
	}, "assign message ID")
}
