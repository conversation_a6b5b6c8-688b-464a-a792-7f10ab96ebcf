package main

import (
	"context"
	"fmt"
	"slices"

	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"

	"github.com/augmentcode/augment/base/logging/audit"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
)

// RemoveUserFromTenantInternal removes a user from a tenant. This should be used AFTER any auth checks.
// tenantIdToNameMappings is an optional map of tenant IDs to names for deleted tenants
func RemoveUserFromTenantInternal(
	ctx context.Context,
	userId string,
	tenantId string,
	tenantIdToNameMappings map[string]string,
	daoFactory *DAOFactory,
	tenantMap *TenantMap,
	auditLogger *audit.AuditLogger,
	requestInsightPublisher ripublisher.RequestInsightPublisher,
) error {
	tenant, err := tenantMap.GetTenantByIdDeletedOk(tenantId)
	if err != nil {
		return err
	}

	var tenantName string
	if tenant != nil {
		tenantName = tenant.Name
	} else if tenantIdToNameMappings != nil && tenantIdToNameMappings[tenantId] != "" {
		tenantName = tenantIdToNameMappings[tenantId]
	} else {
		log.Error().Msgf("Tenant ID %s not found in tenant table nor in tenantIdToNameMappings", tenantId)
		return status.Error(codes.NotFound, "Tenant not found")
	}

	authInfo, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	auditLogger.WriteAuditLog(
		authInfo,
		fmt.Sprintf("[Tenant Change] Remove user '%s' from tenant %s", userId, tenantName),
		audit.NewTenantName(tenantName),
		audit.NewUser(userId),
		requestContext,
	)

	userDAO := daoFactory.GetUserDAO()

	removeTenantIDFromUser := func(user *auth_entities.User) bool {
		idx := slices.Index(user.Tenants, tenantId)
		if idx == -1 {
			return false
		}
		user.Tenants = slices.Delete(user.Tenants, idx, idx+1)
		return true
	}

	user, err := userDAO.TryUpdate(ctx, userId, removeTenantIDFromUser, DefaultRetry)
	if err != nil {
		return status.Error(codes.Internal, "Failed to fetch user")
	}

	if user == nil {
		return status.Error(codes.NotFound, "Resource not found")
	}

	// Don't delete the user themselves as we need to keep the record around for fraud purposes

	// Delete the user tenant mapping
	tenantMappingDAO := daoFactory.GetUserTenantMappingDAO(tenantName)
	mapping_to_delete, err := tenantMappingDAO.GetByUser(ctx, userId)
	if err != nil {
		return status.Error(codes.Internal, "Failed to check existing mapping")
	}

	if mapping_to_delete == nil {
		return status.Error(codes.NotFound, "Resource not found")
	}

	err = tenantMappingDAO.Delete(ctx, userId)
	if err != nil {
		return status.Error(codes.Internal, "Failed to delete mapping")
	}

	// Remove any tokens associated with this tenant and user.
	_, err = deleteUserTokensForTenant(ctx, userId, tenantId, daoFactory)
	if err != nil {
		return err
	}

	// Revoke any user cookies used for authentication
	userDAO.UpdateNonce(ctx, user.Id)

	// Publish Request Insight RemoveUserFromTenant event.
	tenantEvent := ripublisher.NewTenantEvent()
	tenantEvent.Event = &riproto.TenantEvent_RemoveUserFromTenant{
		RemoveUserFromTenant: &riproto.RemoveUserFromTenant{
			User: user,
		},
	}
	riErr := requestInsightPublisher.PublishTenantEvent(ctx, &riproto.TenantInfo{
		TenantId:   tenantId,
		TenantName: tenantName,
	}, tenantEvent)
	if riErr != nil {
		log.Warn().Err(riErr).Msg("Failed to publish RemoveUserFromTenant event")
	}

	return nil
}

// deleteUserTokensForTenant is a helper function to delete user tokens for a specific tenant
func deleteUserTokensForTenant(ctx context.Context, userId string, tenantID string, daoFactory *DAOFactory) (int, error) {
	tokenHashDAO := daoFactory.GetTokenHashDAO()

	hashes := make([]string, 0, 16)
	err := tokenHashDAO.FindAll(ctx, func(token *auth_entities.TokenHash) bool {
		if token.AugmentUserId == userId && token.TenantId == tenantID {
			hashes = append(hashes, token.Hash)
		}
		return true
	})
	if err != nil {
		return 0, status.Error(codes.Internal, "Failed to fetch tokens")
	}

	deleted := 0
	for _, hash := range hashes {
		if err := tokenHashDAO.Delete(ctx, hash); err != nil {
			return deleted, status.Error(codes.Internal, "Failed to delete token")
		}
		deleted++
	}
	return deleted, nil
}
