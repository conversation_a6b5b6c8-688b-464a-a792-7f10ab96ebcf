package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"net/url"
	"time"

	"github.com/augmentcode/augment/base/go/secretstring"
	front_end_token_service "github.com/augmentcode/augment/services/auth/central/server/front_end_token_service_proto"
	riproto "github.com/augmentcode/augment/services/request_insight/proto"
	ripublisher "github.com/augmentcode/augment/services/request_insight/publisher"
	"github.com/rs/zerolog"
	"github.com/rs/zerolog/log"
)

type hcaptcha<PERSON>hecker struct {
	secret        secretstring.SecretString
	siteKey       string
	endpoint      string
	httpsPostFunc httpsPostFunc
	timeout       time.Duration

	requestInsightPublisher ripublisher.RequestInsightPublisher
	logger                  zerolog.Logger

	evalEnabled  func() bool
	blockEnabled func() bool
}

func (c *hcaptchaChecker) name() string {
	return "hcaptcha"
}

func (c *hcaptcha<PERSON><PERSON><PERSON>) securityCheck(ctx context.Context, req *front_end_token_service.RunUserSecurityChecksRequest) (bool, error) {
	if !c.evalEnabled() {
		return true, nil
	}

	var body []byte

	if req.HcaptchaResponse != "" {
		params := url.Values{
			"secret":   []string{c.secret.Expose()},
			"response": []string{req.HcaptchaResponse},
			"remoteip": []string{req.IpAddress},
			"sitekey":  []string{c.siteKey},
		}
		resp, err := c.httpsPostFunc(ctx, c.endpoint, c.secret, params.Encode(), c.timeout)
		if err != nil {
			return true, fmt.Errorf("failed to make HTTP request: %w", err)
		}

		if resp.statusCode != http.StatusOK {
			return true, fmt.Errorf("hcaptcha returned non-200 status code: %d details: %s", resp.statusCode, string(resp.body))
		}

		body = resp.body
	} else {
		body = []byte("{ \"success\": false, \"error-codes\": [\"missing-input-response\"] }")
	}

	var hcaptchaResponse struct {
		Success     bool     `json:"success"`
		ChallengeTs string   `json:"challenge_ts"`
		Hostname    string   `json:"hostname"`
		ErrorCodes  []string `json:"error-codes"`
		Score       *float64 `json:"score"`
		ScoreReason []string `json:"score_reason"`
	}
	err := json.Unmarshal(body, &hcaptchaResponse)
	if err != nil {
		return true, fmt.Errorf("failed to unmarshal hcaptcha response: %w", err)
	}

	c.logger.Info().Str("augment_user_id", req.AugmentUserId).Str("idp_user_id", req.IdpUserId).Str("email_address", req.EmailAddress).Msgf("hcaptcha report: %s", string(body))

	event := ripublisher.NewGenericEvent()
	event.Event = &riproto.GenericEvent_Hcaptcha{
		Hcaptcha: &riproto.Hcaptcha{
			Email:     req.EmailAddress,
			UserAgent: req.UserAgent,
			SourceIp:  req.IpAddress,

			Success:     hcaptchaResponse.Success,
			ChallengeTs: hcaptchaResponse.ChallengeTs,
			Hostname:    hcaptchaResponse.Hostname,
			ErrorCodes:  hcaptchaResponse.ErrorCodes,
			Score:       hcaptchaResponse.Score,
			ScoreReason: hcaptchaResponse.ScoreReason,
		},
	}
	err = c.requestInsightPublisher.PublishGenericEvent(ctx, "", event)
	if err != nil {
		c.logger.Err(err).Msg("Failed to publish hcaptcha report to request insight")
	}

	return !c.blockEnabled() || hcaptchaResponse.Success, nil
}

type hcaptchaCheckerParameters struct {
	secret                  secretstring.SecretString
	siteKey                 string
	endpoint                string
	timeout                 time.Duration
	httpsPostFunc           httpsPostFunc
	requestInsightPublisher ripublisher.RequestInsightPublisher
	logger                  *zerolog.Logger
	enabled                 func() bool
	block                   func() bool
}

func newHcaptchaCheckerForTest(params hcaptchaCheckerParameters) *hcaptchaChecker {
	h := &hcaptchaChecker{}

	h.secret = params.secret // pragma: allowlist secret
	h.siteKey = params.siteKey
	h.endpoint = params.endpoint

	if params.timeout != 0 {
		h.timeout = params.timeout
	} else {
		h.timeout = 10 * time.Second
	}

	if params.httpsPostFunc == nil {
		h.httpsPostFunc = func(ctx context.Context, url string, bearerToken secretstring.SecretString, jsonData interface{}, timeout time.Duration) (*httpPostResponse, error) {
			return &httpPostResponse{
				statusCode: http.StatusOK,
				body:       []byte("{}"),
			}, nil
		}
	} else {
		h.httpsPostFunc = params.httpsPostFunc
	}

	if params.requestInsightPublisher == nil {
		h.requestInsightPublisher = ripublisher.NewRequestInsightPublisherMock()
	} else {
		h.requestInsightPublisher = params.requestInsightPublisher
	}

	if params.logger == nil {
		h.logger = log.Logger
	} else {
		h.logger = *params.logger
	}

	if params.enabled == nil {
		h.evalEnabled = func() bool { return true }
	} else {
		h.evalEnabled = params.enabled
	}

	if params.block == nil {
		h.blockEnabled = func() bool { return true }
	} else {
		h.blockEnabled = params.block
	}

	return h
}
