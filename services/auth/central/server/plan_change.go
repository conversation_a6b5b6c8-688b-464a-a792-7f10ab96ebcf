package main

import (
	"context"
	"fmt"
	"os"
	"strings"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging/audit"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	auth_internal "github.com/augmentcode/augment/services/auth/central/server/auth_internal_proto"
	"github.com/augmentcode/augment/services/auth/central/server/test_utils"
	"github.com/augmentcode/augment/services/billing/lib/orb"
	orb_config "github.com/augmentcode/augment/services/billing/lib/orb/config"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tw_pb "github.com/augmentcode/augment/services/tenant_watcher/proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// TestPanicPoints defines panic points that can be enabled for testing
var PlanChangePanicPoints = struct {
	MoveUserToTenantError string
}{
	MoveUserToTenantError: "plan-change-move-user-to-tenant-error",
}

var (
	PlanChangeProcessDeadLetterQueue = featureflags.NewBoolFlag("auth_central_async_ops_process_dead_letter_queue", false)
	PlanChangeLogDeadLetters         = featureflags.NewBoolFlag("auth_central_async_ops_log_dead_letters", false)
	PlanChangeEnabled                = featureflags.NewBoolFlag("team_management_pending_changes_enabled", true)
)

// PlanChangeProcessor is responsible for processing plan change messages from the async-ops pubsub topic
type PlanChangeProcessor struct {
	daoFactory    *DAOFactory
	changeManager *PlanChangeManager
	auditLogger   *audit.AuditLogger
	tenantMap     *TenantMap
}

func NewPlanChangeProcessor(
	config *Config,
	daoFactory *DAOFactory,
	featureFlagHandle featureflags.FeatureFlagHandle,
	auditLogger *audit.AuditLogger,
	tenantMap *TenantMap,
) (*PlanChangeProcessor, error) {
	changeManager, err := NewPlanChangeManager(daoFactory, featureFlagHandle, &config.Orb, auditLogger, tenantMap, &DefaultRandomSelector{})
	if err != nil {
		return nil, fmt.Errorf("failed to create plan change manager: %w", err)
	}

	return &PlanChangeProcessor{
		daoFactory:    daoFactory,
		changeManager: changeManager,
		auditLogger:   auditLogger,
		tenantMap:     tenantMap,
	}, nil
}

type PlanChangeManager struct {
	daoFactory        *DAOFactory
	featureFlagHandle featureflags.FeatureFlagHandle
	orbConfig         *orb_config.OrbConfig
	orbClient         orb.OrbClient
	auditLogger       *audit.AuditLogger
	tenantMap         *TenantMap
	randomSelector    RandomSelector
}

func NewPlanChangeManager(
	daoFactory *DAOFactory,
	featureFlagHandle featureflags.FeatureFlagHandle,
	orbConfig *orb_config.OrbConfig,
	auditLogger *audit.AuditLogger,
	tenantMap *TenantMap,
	randomSelector RandomSelector,
) (*PlanChangeManager, error) {
	if daoFactory == nil {
		return nil, fmt.Errorf("daoFactory cannot be nil")
	}
	if featureFlagHandle == nil {
		return nil, fmt.Errorf("featureFlagHandle cannot be nil")
	}

	// Initialize Orb client if enabled
	var orbClient orb.OrbClient
	if orbConfig != nil && orbConfig.Enabled {
		// Read the Orb API key from the specified file path
		key, err := os.ReadFile(orbConfig.ApiKeyPath)
		if err != nil {
			log.Error().Err(err).Msg("Failed to read Orb secret key")
		} else {
			cleanKey := strings.TrimSpace(string(key))
			orbClient = orb.NewOrbClient(cleanKey, featureFlagHandle)
			log.Info().Msg("Orb client initialized successfully")
		}
	}

	// Use default random selector if none provided
	if randomSelector == nil {
		randomSelector = &DefaultRandomSelector{}
	}

	return &PlanChangeManager{
		daoFactory:        daoFactory,
		featureFlagHandle: featureFlagHandle,
		orbConfig:         orbConfig,
		orbClient:         orbClient,
		auditLogger:       auditLogger,
		tenantMap:         tenantMap,
		randomSelector:    randomSelector,
	}, nil
}

func checkPlanChangeDeadLetterFlag(featureFlagsHandle featureflags.FeatureFlagHandle) bool {
	val, err := PlanChangeProcessDeadLetterQueue.Get(featureFlagsHandle)
	if err != nil {
		log.Error().Err(err).Msg("Error reading plan change dead letter queue feature flag")
		return false
	}
	return val
}

func checkPlanChangeDeadLetterLoggingFlag(featureFlagsHandle featureflags.FeatureFlagHandle) bool {
	val, err := PlanChangeLogDeadLetters.Get(featureFlagsHandle)
	if err != nil {
		log.Error().Err(err).Msg("Error reading plan change dead letter logging feature flag")
		return false
	}
	return val
}

func checkPlanChangeEnabled(featureFlagsHandle featureflags.FeatureFlagHandle) bool {
	val, err := PlanChangeEnabled.Get(featureFlagsHandle)
	if err != nil {
		log.Error().Err(err).Msg("Error reading plan change feature flag")
		return false
	}
	return val
}

func (w *PlanChangeProcessor) Process(ctx context.Context, msg *auth_internal.PlanChangeMessage) error {
	if !checkPlanChangeEnabled(w.changeManager.featureFlagHandle) {
		log.Ctx(ctx).Info().Msg("Plan change is disabled, skipping message")
		return fmt.Errorf("plan change is disabled, nack so we can recover these")
	}

	log.Ctx(ctx).Info().Str("user_id", msg.UserId).Str("plan_change_id", msg.PlanChangeId).Msg("Processing plan change message")
	userDAO := w.daoFactory.GetUserDAO()

	// Get the user
	user, err := userDAO.Get(ctx, msg.UserId)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", msg.UserId).Msg("Failed to fetch user for plan change")
		return fmt.Errorf("failed to fetch user for plan change: user_id=%s: %w", msg.UserId, err)
	}

	if user == nil {
		log.Ctx(ctx).Error().Str("user_id", msg.UserId).Msg("User not found for plan change")
		return fmt.Errorf("user not found for plan change: user_id=%s", msg.UserId)
	}

	log.Ctx(ctx).Info().
		Str("user_id", msg.UserId).
		Str("plan_change_id", msg.PlanChangeId).
		Str("target_plan_id", msg.TargetPlanId).
		Msg("Executing plan change")

	// Get billing info to access the generic pending change
	// Only individual users should ever be in 0 tenants, so this is ok
	//  even from the result of a partial failure of `MoveUserToTenant` which
	//  at the time of writing this comment puts the user in the new tenant before removing them from the old one.
	//  however the code is not written such that this is a requirement which is why we are supporting 0 tenants here.
	var billingInfo *UserBillingInfo
	if len(user.Tenants) > 0 {
		// Team user - use GetUserBillingInfo with first tenant
		billingInfo, err = GetUserBillingInfo(ctx, user.Id, user.Tenants[0], w.daoFactory, w.tenantMap)
	} else {
		// Individual user without tenants - create billing info manually from user entity
		billingInfo = &UserBillingInfo{
			UserID:                   user.Id,
			Email:                    user.Email,
			IsAdmin:                  false,
			IsSelfServeTeam:          false,
			OrbCustomerID:            user.OrbCustomerId,
			OrbSubscriptionID:        user.OrbSubscriptionId,
			StripeCustomerID:         user.StripeCustomerId,
			SubscriptionCreationInfo: user.GetSubscriptionCreationInfo(),
			PendingChange:            user.PendingChange,
		}
	}
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", msg.UserId).Msg("Failed to get billing info for plan change validation")
		return fmt.Errorf("failed to get billing info: %w", err)
	}

	// TODO(cam): what if the pending change is nil here?
	planChange := billingInfo.PendingChange.GetPlanChange()

	// Validate message ID for deduplication if both the pending change and message have message IDs
	if planChange == nil || *planChange.MessageId != msg.PlanChangeId || planChange.Status != auth_entities.PlanChange_PENDING {
		messageAge := time.Since(msg.PublishTime.AsTime())
		if messageAge > 5*time.Minute {
			log.Ctx(ctx).Info().
				Str("user_id", msg.UserId).
				Str("message_id", msg.PlanChangeId).
				Dur("message_age", messageAge).
				Str("plan_change_status", planChange.Status.String()).
				Msg("Failed to find the pending change, acknowledging message after 5 minute timeout")
			return nil // Acknowledge the message
		}

		log.Ctx(ctx).Info().
			Str("user_id", msg.UserId).
			Str("message_id", msg.PlanChangeId).
			Dur("message_age", messageAge).
			Str("plan_change_status", planChange.Status.String()).
			Msg("Failed to find the pending change, retrying")
		return fmt.Errorf("failed to find the pending change: user_id=%s, message_id=%s, message_age=%v, plan_change_status=%s",
			msg.UserId, msg.PlanChangeId, messageAge, planChange.Status.String())
	}

	// Execute the plan change
	err = w.changeManager.executePlanChange(ctx, msg, user, billingInfo)
	if err != nil {
		log.Ctx(ctx).Error().
			Err(err).
			Str("user_id", msg.UserId).
			Str("plan_change_id", msg.PlanChangeId).
			Str("target_plan_id", msg.TargetPlanId).
			Msg("Failed to process plan change")
		return fmt.Errorf("failed to process plan change: user_id=%s, plan_change_id=%s, target_plan_id=%s: %w",
			msg.UserId, msg.PlanChangeId, msg.TargetPlanId, err)
	}

	log.Ctx(ctx).Info().
		Str("user_id", msg.UserId).
		Str("plan_change_id", msg.PlanChangeId).
		Str("target_plan_id", msg.TargetPlanId).
		Msg("Plan change completed successfully")

	authClaims, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	w.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("User %s plan change completed successfully", msg.UserId),
		requestContext,
		audit.NewUser(msg.UserId),
	)

	return nil
}

// Core plan change logic
func (pcm *PlanChangeManager) executePlanChange(
	ctx context.Context,
	msg *auth_internal.PlanChangeMessage,
	user *auth_entities.User,
	billingInfo *UserBillingInfo,
) error {
	// Step 1: Handle subscription changes first
	if err := pcm.processOrbSubscription(ctx, msg, user, billingInfo); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", msg.UserId).Msg("subscription update failed")
		return fmt.Errorf("failed to update subscription: %w", err)
	}

	// Step 2: Determine if we need to move the user to a different tenant
	// For individual users, we need to check if the plan matches the tier of the tenant
	// For team users, the plan should always match the tier of the tenant
	if err := pcm.handleTenantMovement(ctx, msg, user); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", msg.UserId).Msg("Failed to handle tenant movement")
		return fmt.Errorf("failed to handle tenant movement: %w", err)
	}

	// Step 3: Mark the plan change as successful after all processing completes
	if err := pcm.markPlanChangeSuccess(ctx, msg, user, billingInfo); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", msg.UserId).Msg("Failed to mark plan change as successful after plan change")
		return fmt.Errorf("failed to mark plan change as successful: %w", err)
	}

	return nil
}

func (pcm *PlanChangeManager) processOrbSubscription(ctx context.Context, msg *auth_internal.PlanChangeMessage, user *auth_entities.User, billingInfo *UserBillingInfo) error {
	log.Ctx(ctx).Info().Msg("Processing Orb subscription change for plan change")

	// Check if orbClient is nil
	if pcm.orbClient == nil {
		return status.Error(codes.Internal, "Orb client is nil")
	}

	targetPlanId := msg.TargetPlanId

	// Determine target plan details
	targetPlan := pcm.orbConfig.FindPlan(targetPlanId)
	if targetPlan == nil {
		log.Ctx(ctx).Error().Str("user_id", msg.UserId).Str("target_plan_id", targetPlanId).Msg("Failed to find target plan")
		return status.Error(codes.Internal, fmt.Sprintf("Failed to find target plan %s", targetPlanId))
	}

	// Block switching to trial plans (they're only for new users)
	if targetPlan.Features.PlanType == orb_config.PlanTypePaidTrial {
		log.Ctx(ctx).Error().Str("user_id", msg.UserId).Str("target_plan_id", msg.TargetPlanId).Msg("Cannot change to a trial plan")
		return status.Error(codes.FailedPrecondition, "Cannot change to a trial plan")
	}

	// Block teams from switching to plans that don't allow teams
	if billingInfo.IsSelfServeTeam && !targetPlan.Features.TeamsAllowed {
		log.Ctx(ctx).Error().
			Str("user_id", msg.UserId).
			Str("target_plan_id", msg.TargetPlanId).
			Bool("teams_allowed", targetPlan.Features.TeamsAllowed).
			Msg("Teams are not allowed on this plan")
		return status.Error(codes.InvalidArgument, "Teams are not allowed on this plan")
	}

	// Handle community plan case - no staged change in Orb for community
	if targetPlan.Features.PlanType == orb_config.PlanTypeCommunity {
		return pcm.handleCommunityPlanChange(ctx, msg, user, targetPlan)
	}

	// For non-community plans, a staged change must exist
	if msg.OrbPendingChangeId == nil || *msg.OrbPendingChangeId == "" {
		log.Ctx(ctx).Error().Str("user_id", msg.UserId).Str("target_plan_id", msg.TargetPlanId).Msg("No staged change ID provided for non-community plan")
		return fmt.Errorf("staged change ID is required for non-community plans")
	}

	// Apply the staged change
	return pcm.applyPendingSubscriptionChange(ctx, msg, user, targetPlan)
}

func (pcm *PlanChangeManager) handleCommunityPlanChange(ctx context.Context, msg *auth_internal.PlanChangeMessage, user *auth_entities.User, targetPlan *orb_config.PlanConfig) error {
	log.Ctx(ctx).Info().Str("user_id", msg.UserId).Msg("Handling community plan change")

	orbSubscriptionId := user.OrbSubscriptionId
	orbCustomerId := user.OrbCustomerId

	// Check if the user has a valid subscription
	var currentSubscription *orb.OrbSubscriptionInfo
	var err error

	if orbSubscriptionId != "" {
		// Try to get the current subscription
		currentSubscription, err = pcm.orbClient.GetUserSubscription(ctx, orbSubscriptionId, nil)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("subscription_id", orbSubscriptionId).Msg("Failed to get user's current subscription for community change")
			return status.Error(codes.Internal, fmt.Sprintf("Failed to get user's current subscription: %v", err))
		}
	}

	// If no subscription exists or it's not active, create a new community subscription
	if currentSubscription == nil || currentSubscription.OrbStatus != "active" {
		log.Ctx(ctx).Info().Str("user_id", msg.UserId).Str("orb_customer_id", orbCustomerId).Msg("No active subscription found, creating community subscription")

		// Create a new community subscription
		orbSubscription := orb.OrbSubscription{
			CustomerOrbID:  orbCustomerId,
			ExternalPlanID: targetPlan.ID,
		}

		// Use the plan change ID as the idempotency key
		idempotencyKey := msg.PlanChangeId
		newSubscriptionId, err := pcm.orbClient.CreateSubscription(ctx, orbSubscription, &idempotencyKey, false)
		if err != nil {
			return status.Error(codes.Internal, fmt.Sprintf("Failed to create new community Orb subscription: %v", err))
		}

		// Update the user with the new subscription ID
		userDAO := pcm.daoFactory.GetUserDAO()
		_, err = userDAO.TryUpdate(ctx, msg.UserId, func(u *auth_entities.User) bool {
			u.OrbSubscriptionId = newSubscriptionId
			return true
		}, DefaultRetry)
		if err != nil {
			return status.Error(codes.Internal, fmt.Sprintf("Failed to update user with new subscription ID: %v", err))
		}

		log.Ctx(ctx).Info().Str("user_id", msg.UserId).Str("subscription_id", newSubscriptionId).Msg("Created new community subscription")
		return nil
	}

	// If subscription exists, change it to community plan immediately
	planChange := orb.OrbPlanChange{
		CustomerOrbID:         orbCustomerId,
		SubscriptionID:        orbSubscriptionId,
		NewPlanID:             targetPlan.ID,
		PlanChangeType:        orb.PlanChangeImmediate,
		BillingCycleAlignment: orb.BillingCycleAlignmentPlanChangeDate,
	}

	// Use the plan change ID as the idempotency key
	idempotencyKey := msg.PlanChangeId
	_, err = pcm.orbClient.SetCustomerPlanType(ctx, planChange, &idempotencyKey, false)
	if err != nil {
		return status.Error(codes.Internal, fmt.Sprintf("Failed to update Orb subscription to community: %v", err))
	}

	return nil
}

func (pcm *PlanChangeManager) applyPendingSubscriptionChange(ctx context.Context, msg *auth_internal.PlanChangeMessage, user *auth_entities.User, targetPlan *orb_config.PlanConfig) error {
	log.Ctx(ctx).Info().Str("user_id", msg.UserId).Str("orb_pending_change_id", *msg.OrbPendingChangeId).Msg("Applying staged change from Orb")

	// For paid plans, require the collected amount to ensure proper billing reconciliation
	if targetPlan.Features.PlanType == orb_config.PlanTypePaid && msg.CollectedAmountCents == nil {
		log.Ctx(ctx).Error().
			Str("user_id", msg.UserId).
			Str("target_plan_id", msg.TargetPlanId).
			Str("plan_type", string(targetPlan.Features.PlanType)).
			Msg("Collected amount is required for paid plan changes")
		return status.Error(codes.InvalidArgument, "collected amount is required for paid plan changes")
	}

	// Get the old subscription details BEFORE applying the staged change for proration calculations
	var oldSubscription *orb.OrbSubscriptionInfo
	orbSubscriptionId := user.OrbSubscriptionId
	if orbSubscriptionId != "" {
		var err error
		oldSubscription, err = pcm.orbClient.GetUserSubscription(ctx, orbSubscriptionId, nil)
		if err != nil {
			log.Ctx(ctx).Warn().Err(err).Str("subscription_id", orbSubscriptionId).Msg("Failed to get old subscription details before applying staged change")
			// Continue without old subscription details - proration will be skipped
		}
	}

	// Apply the staged change using the Orb pending change ID
	idempotencyKey := msg.PlanChangeId
	// ApplyPendingSubscriptionChange requires: subscriptionChangeID, previouslyCollectedAmountInCents, description, idempotencyKey

	// At this point, we've already validated that paid plans have collected amounts
	var previouslyCollectedAmount string
	if msg.CollectedAmountCents != nil {
		previouslyCollectedAmount = fmt.Sprintf("%d", *msg.CollectedAmountCents)
	} else {
		// This should not happen due to validation above, but be defensive
		log.Ctx(ctx).Error().
			Str("user_id", msg.UserId).
			Str("target_plan_id", msg.TargetPlanId).
			Str("plan_type", string(targetPlan.Features.PlanType)).
			Msg("Missing collected amount for paid plan")
		return status.Error(codes.Internal, "missing collected amount for plan change")
	}

	err := pcm.orbClient.ApplyPendingSubscriptionChange(ctx, *msg.OrbPendingChangeId, previouslyCollectedAmount, "Staged change applied", &idempotencyKey)
	if err != nil {
		return status.Error(codes.Internal, fmt.Sprintf("Failed to apply staged change: %v", err))
	}

	log.Ctx(ctx).Info().Str("user_id", msg.UserId).Str("orb_pending_change_id", *msg.OrbPendingChangeId).Msg("Successfully applied staged change")

	// Handle proration for upgrades if needed, using the old subscription details
	if err := pcm.handleProrationForPlanChange(ctx, msg, user, targetPlan, oldSubscription); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", msg.UserId).Msg("Failed to handle proration for staged change")
		return fmt.Errorf("failed to handle proration for staged change: %w", err)
	}

	return nil
}

// handleProrationForPlanChange handles proration logic for staged changes, similar to user tier changes
func (pcm *PlanChangeManager) handleProrationForPlanChange(ctx context.Context, msg *auth_internal.PlanChangeMessage, user *auth_entities.User, targetPlan *orb_config.PlanConfig, oldSubscription *orb.OrbSubscriptionInfo) error {
	orbSubscriptionId := user.OrbSubscriptionId
	if orbSubscriptionId == "" {
		log.Ctx(ctx).Info().Str("user_id", msg.UserId).Msg("No subscription ID, skipping proration")
		return nil
	}

	// Use the old subscription details passed in (from before the staged change was applied)
	if oldSubscription == nil {
		log.Ctx(ctx).Info().Str("user_id", msg.UserId).Msg("No old subscription details available, skipping proration")
		return nil
	}

	// Get current plan information using the old subscription details
	currentPlan := pcm.orbConfig.FindPlan(oldSubscription.ExternalPlanID)
	if currentPlan == nil {
		log.Ctx(ctx).Warn().Str("current_plan_id", oldSubscription.ExternalPlanID).Msg("Failed to find current plan for proration")
		return nil
	}

	// Only handle proration for paid plan upgrades from non-trial/non-community plans
	if targetPlan.Features.PlanType != orb_config.PlanTypePaid {
		log.Ctx(ctx).Info().Str("target_plan_type", string(targetPlan.Features.PlanType)).Msg("Target plan is not paid, skipping proration")
		return nil
	}

	if currentPlan.Features.PlanType == orb_config.PlanTypePaidTrial || currentPlan.Features.PlanType == orb_config.PlanTypeCommunity {
		log.Ctx(ctx).Info().Str("current_plan_type", string(currentPlan.Features.PlanType)).Msg("Current plan is trial or community, no proration needed")
		return nil
	}

	// Get plan information for both current and target plans to compare
	currentPlanInfo, err := pcm.orbClient.GetPlanInformation(ctx, orb.ItemIds{SeatsID: pcm.orbConfig.SeatsItemID, IncludedMessagesID: pcm.orbConfig.IncludedMessagesItemID}, &orbSubscriptionId, nil)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("orb_subscription_id", orbSubscriptionId).Msg("Failed to get current plan information for proration")
		return err
	}

	targetPlanInfo, err := pcm.orbClient.GetPlanInformation(ctx, orb.ItemIds{SeatsID: pcm.orbConfig.SeatsItemID, IncludedMessagesID: pcm.orbConfig.IncludedMessagesItemID}, nil, &targetPlan.ID)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("target_plan_id", targetPlan.ID).Msg("Failed to get target plan information for proration")
		return err
	}

	// Determine if this is an upgrade to decide on proration
	isUpgrade, err := IsPlanUpgrade(ctx, currentPlanInfo, targetPlanInfo)
	if err != nil {
		log.Ctx(ctx).Error().
			Err(err).
			Str("user_id", msg.UserId).
			Str("orb_subscription_id", orbSubscriptionId).
			Str("target_plan_id", targetPlan.ID).
			Msg("Failed to determine if plan change is an upgrade for proration")
		return err
	}

	// Only apply proration for upgrades
	if !isUpgrade {
		log.Ctx(ctx).Info().Str("user_id", msg.UserId).Msg("Plan change is not an upgrade, skipping proration")
		return nil
	}

	// Get the number of seats for next month calculations using old subscription details
	// Use future fixed quantities if available, otherwise fall back to current quantities
	numberOfSeats := int(oldSubscription.CurrentFixedQuantities.Seats)
	if oldSubscription.FutureFixedQuantities != nil && oldSubscription.FutureFixedQuantities.Seats > 0 {
		numberOfSeats = int(oldSubscription.FutureFixedQuantities.Seats)
	}

	// Update future months to the full amount
	creditQuantity := targetPlanInfo.MessagesPerSeat * float64(numberOfSeats)
	priceID := targetPlanInfo.IncludedMessagesPriceID
	nextMonthCreditsIdempotencyKey := fmt.Sprintf("%s-next-month-credits", msg.PlanChangeId)
	err = pcm.orbClient.UpdateFixedQuantity(ctx, orb.OrbQuantityUpdate{
		OrbSubscriptionID: orbSubscriptionId,
		PriceOverride: orb.OrbPriceOverrides{
			PriceID:  priceID,
			Quantity: creditQuantity,
		},
		UpdateTimeType: orb.PlanChangeEndOfTerm,
	}, &nextMonthCreditsIdempotencyKey)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Float64("credit_quantity", creditQuantity).Str("orb_subscription_id", orbSubscriptionId).Msg("Failed to update future number of credits per month")
		return fmt.Errorf("failed to update future number of credits per month: %w", err)
	}

	log.Ctx(ctx).Info().Float64("credit_quantity", creditQuantity).Str("orb_subscription_id", orbSubscriptionId).Msg("Successfully updated future number of credits per month for staged change")
	return nil
}

func (pcm *PlanChangeManager) handleTenantMovement(ctx context.Context, msg *auth_internal.PlanChangeMessage, user *auth_entities.User) error {
	// Get target plan to determine the tier
	targetPlan := pcm.orbConfig.FindPlan(msg.TargetPlanId)
	if targetPlan == nil {
		return fmt.Errorf("failed to find target plan %s", msg.TargetPlanId)
	}

	// Get the current tenant (0th tenant only)
	currentTenantId := ""
	var currentTenant *tw_pb.Tenant
	var err error

	if len(user.Tenants) > 0 {
		currentTenantId = user.Tenants[0]
		// Get current tenant info to check its tier
		currentTenant, err = pcm.tenantMap.GetTenantByID(ctx, currentTenantId)
		if err != nil {
			return fmt.Errorf("failed to get current tenant %s: %w", currentTenantId, err)
		}
		if currentTenant == nil {
			log.Ctx(ctx).Warn().Str("user_id", msg.UserId).Str("tenant_id", currentTenantId).Msg("Current tenant not found, will assign to new tenant")
			currentTenant = nil // Treat as no current tenant
		}
	} else {
		log.Ctx(ctx).Info().Str("user_id", msg.UserId).Msg("User has no current tenant, will assign to appropriate tenant for plan")
	}

	// Determine the target tier based on the plan
	var targetTenantTier tw_pb.TenantTier
	switch targetPlan.Features.PlanType {
	case orb_config.PlanTypeCommunity:
		targetTenantTier = tw_pb.TenantTier_COMMUNITY
	case orb_config.PlanTypePaid:
		targetTenantTier = tw_pb.TenantTier_PROFESSIONAL
	default:
		log.Ctx(ctx).Warn().Str("plan_type", string(targetPlan.Features.PlanType)).Msg("Unknown plan type, defaulting to PROFESSIONAL")
		targetTenantTier = tw_pb.TenantTier_PROFESSIONAL
	}

	// If the current tenant already has the correct tier, no movement needed
	if currentTenant != nil && currentTenant.Tier == targetTenantTier {
		log.Ctx(ctx).Info().Str("user_id", msg.UserId).Str("tenant_id", currentTenantId).Str("tier", targetTenantTier.String()).Msg("User already in correct tier tenant, no movement needed")
		return nil
	}

	// Log the reason for tenant movement
	if currentTenant == nil {
		log.Ctx(ctx).Info().Str("user_id", msg.UserId).Str("target_tier", targetTenantTier.String()).Msg("User has no current tenant, assigning to appropriate tier")
	} else {
		log.Ctx(ctx).Info().
			Str("user_id", msg.UserId).
			Str("current_tenant_id", currentTenantId).
			Str("current_tier", currentTenant.Tier.String()).
			Str("target_tier", targetTenantTier.String()).
			Msg("User needs to move to different tier")
	}

	// Get tenant names from feature flags based on the target tier
	var tenantNamesStr string
	switch targetTenantTier {
	case tw_pb.TenantTier_COMMUNITY:
		tenantNamesStr, err = pcm.featureFlagHandle.GetString("auth_central_signup_tenant", "")
	case tw_pb.TenantTier_PROFESSIONAL:
		tenantNamesStr, err = pcm.featureFlagHandle.GetString("auth_central_individual_tenant", "")
	default:
		return fmt.Errorf("unsupported target tenant tier: %s", targetTenantTier.String())
	}
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", msg.UserId).Str("target_tier", targetTenantTier.String()).Msg("Failed to get tenant names from feature flag")
		return fmt.Errorf("failed to get tenant names for tier %s: %w", targetTenantTier.String(), err)
	}

	if tenantNamesStr == "" {
		log.Ctx(ctx).Error().Str("user_id", msg.UserId).Str("target_tier", targetTenantTier.String()).Msg("Tenant names not configured for tier")
		return fmt.Errorf("tenant names not configured for tier %s", targetTenantTier.String())
	}

	// Split comma-separated list of tenant names
	tenantNames := strings.Split(tenantNamesStr, ",")

	// Choose a tenant randomly
	var newTenantName string
	if len(tenantNames) > 0 {
		newTenantName = tenantNames[pcm.randomSelector.Intn(len(tenantNames))]
		log.Ctx(ctx).Info().
			Str("user_id", msg.UserId).
			Str("selected_tenant", newTenantName).
			Strs("available_tenants", tenantNames).
			Str("target_tier", targetTenantTier.String()).
			Msg("Selected tenant for user tier change")
	}

	// Get target tenant
	targetTenant, err := pcm.tenantMap.GetTenant(ctx, newTenantName)
	if err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", msg.UserId).Str("tenant_name", newTenantName).Msg("Failed to get target tenant")
		return fmt.Errorf("failed to get target tenant %s: %w", newTenantName, err)
	}
	if targetTenant == nil {
		log.Ctx(ctx).Error().Str("user_id", msg.UserId).Str("tenant_name", newTenantName).Msg("Target tenant not found")
		return fmt.Errorf("target tenant %s not found", newTenantName)
	}

	targetTenantId := targetTenant.Id

	// Move the user to the new tenant
	if err := pcm.tenantMap.MoveUserToTenant(ctx, msg.UserId, targetTenantId); err != nil {
		log.Ctx(ctx).Error().Err(err).Str("user_id", msg.UserId).Str("target_tenant_id", targetTenantId).Msg("Failed to move user to new tenant")
		return fmt.Errorf("failed to move user to new tenant: %w", err)
	}

	test_utils.CheckPanic(PlanChangePanicPoints.MoveUserToTenantError)

	log.Ctx(ctx).Info().Str("user_id", msg.UserId).Str("old_tenant_id", currentTenantId).Str("new_tenant_id", targetTenantId).Msg("Successfully moved user to new tenant")
	return nil
}

// markPlanChangeSuccess marks the plan change as successful after processing completes
func (pcm *PlanChangeManager) markPlanChangeSuccess(ctx context.Context, msg *auth_internal.PlanChangeMessage, user *auth_entities.User, billingInfo *UserBillingInfo) error {
	if billingInfo.IsSelfServeTeam {
		// Update team subscription mapping
		tenantId := user.Tenants[0]
		tenantDAO := pcm.daoFactory.GetTenantSubscriptionMappingDAO()
		_, err := tenantDAO.TryUpdate(ctx, tenantId, func(m *auth_entities.TenantSubscriptionMapping) bool {
			if m.PendingChange != nil {
				if pc := m.PendingChange.GetPlanChange(); pc != nil && pc.MessageId != nil && *pc.MessageId == msg.PlanChangeId {
					pc.Status = auth_entities.PlanChange_SUCCESS
				}
				// Update the pending change timestamp
				m.PendingChange.UpdatedAt = timestamppb.Now()
				return true
			}
			return false
		}, DefaultRetry)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("tenant_id", tenantId).Msg("Failed to mark tenant plan change as successful")
			return err
		}
		log.Ctx(ctx).Info().Str("tenant_id", tenantId).Msg("Successfully marked tenant plan change as successful")
	} else {
		// Update individual user
		userDAO := pcm.daoFactory.GetUserDAO()
		_, err := userDAO.TryUpdate(ctx, user.Id, func(u *auth_entities.User) bool {
			if u.PendingChange != nil {
				if pc := u.PendingChange.GetPlanChange(); pc != nil && pc.MessageId != nil && *pc.MessageId == msg.PlanChangeId {
					pc.Status = auth_entities.PlanChange_SUCCESS
				}
				// Update the pending change timestamp
				u.PendingChange.UpdatedAt = timestamppb.Now()
				return true
			}
			return false
		}, DefaultRetry)
		if err != nil {
			log.Ctx(ctx).Error().Err(err).Str("user_id", user.Id).Msg("Failed to mark user plan change as successful")
			return err
		}
		log.Ctx(ctx).Info().Str("user_id", user.Id).Msg("Successfully marked user plan change as successful")
	}

	return nil
}
