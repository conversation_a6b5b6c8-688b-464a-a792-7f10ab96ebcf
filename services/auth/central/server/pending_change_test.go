package main

import (
	"testing"

	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	"github.com/google/uuid"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func TestModifyPendingChange(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	ctx := bigtableFixture.Ctx
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	t.Run("ModifyPendingChange for individual user", func(t *testing.T) {
		// Create a test user
		userID := uuid.New().String()
		user := &auth_entities.User{
			Id:    userID,
			Email: "<EMAIL>",
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create billing info for individual user (not self-serve team)
		billingInfo := &UserBillingInfo{
			UserID:          userID,
			IsSelfServeTeam: false,
		}

		// Test modifier function that sets a pending change
		testPendingChange := &auth_entities.PendingChange{
			Id:        "test-change-id",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		}

		modifier := func(current *auth_entities.PendingChange) *auth_entities.PendingChange {
			return testPendingChange
		}

		// Execute the function
		err = ModifyPendingChange(ctx, daoFactory, billingInfo, modifier, "test")
		require.NoError(t, err)

		// Verify the change was applied
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err)
		require.NotNil(t, updatedUser.PendingChange)
		assert.Equal(t, "test-change-id", updatedUser.PendingChange.Id)
	})

	t.Run("ModifyPendingChange for self-serve team", func(t *testing.T) {
		// Create a test user with tenant
		userID := uuid.New().String()
		tenantID := uuid.New().String()
		user := &auth_entities.User{
			Id:      userID,
			Email:   "<EMAIL>",
			Tenants: []string{tenantID},
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create tenant subscription mapping
		tenantMapping := &auth_entities.TenantSubscriptionMapping{
			TenantId: tenantID,
		}

		tenantDAO := daoFactory.GetTenantSubscriptionMappingDAO()
		_, err = tenantDAO.Create(ctx, tenantMapping)
		require.NoError(t, err)

		// Create billing info for self-serve team
		billingInfo := &UserBillingInfo{
			UserID:          userID,
			IsSelfServeTeam: true,
		}

		// Test modifier function
		testPendingChange := &auth_entities.PendingChange{
			Id:        "team-change-id",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		}

		modifier := func(current *auth_entities.PendingChange) *auth_entities.PendingChange {
			return testPendingChange
		}

		// Execute the function
		err = ModifyPendingChange(ctx, daoFactory, billingInfo, modifier, "team test")
		require.NoError(t, err)

		// Verify the change was applied to tenant mapping
		updatedMapping, err := tenantDAO.Get(ctx, tenantID)
		require.NoError(t, err)
		require.NotNil(t, updatedMapping.PendingChange)
		assert.Equal(t, "team-change-id", updatedMapping.PendingChange.Id)
	})

	t.Run("ModifyPendingChange fails when user has no tenants for self-serve team", func(t *testing.T) {
		// Create a test user without tenants
		userID := uuid.New().String()
		user := &auth_entities.User{
			Id:      userID,
			Email:   "<EMAIL>",
			Tenants: []string{}, // No tenants
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create billing info for self-serve team
		billingInfo := &UserBillingInfo{
			UserID:          userID,
			IsSelfServeTeam: true,
		}

		modifier := func(current *auth_entities.PendingChange) *auth_entities.PendingChange {
			return &auth_entities.PendingChange{Id: "test"}
		}

		// Execute the function - should fail
		err = ModifyPendingChange(ctx, daoFactory, billingInfo, modifier, "test")
		require.Error(t, err)
		assert.Contains(t, err.Error(), "user has no tenants")
	})
}

// Since all of these methods are implemeneted using ModifyPendingChange
// we don't test both teams and users. If the underlying implementation changes
// then we should probably expand the scope of these tests. I know the tests
// should ideally protect changes to the underlying implementation, but
// considering that I don't see these functions changing independently
// of ModifyPendingChange, I think it's okay to keep it simple for now.

func TestClearPendingChange(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	ctx := bigtableFixture.Ctx
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	t.Run("ClearPendingChange clears existing change", func(t *testing.T) {
		// Create a test user with existing pending change
		userID := uuid.New().String()
		existingChange := &auth_entities.PendingChange{
			Id:        "existing-change",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		}
		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			PendingChange: existingChange,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create billing info with existing pending change
		billingInfo := &UserBillingInfo{
			UserID:          userID,
			IsSelfServeTeam: false,
			PendingChange:   existingChange,
		}

		// Execute clear function
		err = ClearPendingChange(ctx, daoFactory, billingInfo)
		require.NoError(t, err)

		// Verify the change was cleared
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err)
		assert.Nil(t, updatedUser.PendingChange)
	})

	t.Run("ClearPendingChange does nothing when no pending change exists", func(t *testing.T) {
		// Create billing info without pending change
		billingInfo := &UserBillingInfo{
			UserID:          "test-user",
			IsSelfServeTeam: false,
			PendingChange:   nil, // No pending change
		}

		// Execute clear function - should not error
		err := ClearPendingChange(ctx, daoFactory, billingInfo)
		require.NoError(t, err)
	})
}

func TestUpdatePendingChange(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	ctx := bigtableFixture.Ctx
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	t.Run("UpdatePendingChange updates existing change", func(t *testing.T) {
		// Create a test user with existing pending change
		userID := uuid.New().String()
		existingChange := &auth_entities.PendingChange{
			Id:        "existing-change",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		}
		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			PendingChange: existingChange,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create billing info with existing pending change
		billingInfo := &UserBillingInfo{
			UserID:          userID,
			IsSelfServeTeam: false,
			PendingChange:   existingChange,
		}

		// Create updated change
		updatedChange := &auth_entities.PendingChange{
			Id:        "updated-change",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
		}

		// Execute update function
		err = UpdatePendingChange(ctx, daoFactory, billingInfo, updatedChange)
		require.NoError(t, err)

		// Verify the change was updated
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err)
		require.NotNil(t, updatedUser.PendingChange)
		assert.Equal(t, "updated-change", updatedUser.PendingChange.Id)
	})

	t.Run("UpdatePendingChange does nothing when no pending change exists", func(t *testing.T) {
		// Create billing info without pending change
		billingInfo := &UserBillingInfo{
			UserID:          "test-user",
			IsSelfServeTeam: false,
			PendingChange:   nil, // No pending change
		}

		updatedChange := &auth_entities.PendingChange{
			Id: "new-change",
		}

		// Execute update function - should not error
		err := UpdatePendingChange(ctx, daoFactory, billingInfo, updatedChange)
		require.NoError(t, err)
	})
}

func TestCreatePendingPlanChange(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	ctx := bigtableFixture.Ctx
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	t.Run("CreatePendingPlanChange creates new plan change", func(t *testing.T) {
		// Create a test user
		userID := uuid.New().String()
		user := &auth_entities.User{
			Id:    userID,
			Email: "<EMAIL>",
		}

		userDAO := daoFactory.GetUserDAO()
		createdUser, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create billing info without existing pending change
		billingInfo := &UserBillingInfo{
			UserID:          userID,
			IsSelfServeTeam: false,
			PendingChange:   nil,
		}

		// Create plan change options
		checkoutUrl := "https://checkout.stripe.com/test"
		checkoutSessionId := "cs_test_123"
		totalCost := "29.99"
		orbPendingChangeId := "orb_change_123"

		options := &PlanChangeOptions{
			CheckoutSessionUrl: &checkoutUrl,
			CheckoutSessionId:  &checkoutSessionId,
			TotalCost:          &totalCost,
			OrbPendingChangeId: &orbPendingChangeId,
		}

		// Execute create function
		err = CreatePendingPlanChange(ctx, daoFactory, createdUser, billingInfo, "professional", "change-123", options)
		require.NoError(t, err)

		// Verify the plan change was created
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err)
		require.NotNil(t, updatedUser.PendingChange)
		assert.Equal(t, "change-123", updatedUser.PendingChange.Id)

		planChange := updatedUser.PendingChange.GetPlanChange()
		require.NotNil(t, planChange)
		assert.Equal(t, auth_entities.PlanChange_PENDING, planChange.Status)
		assert.Equal(t, "professional", planChange.TargetPlanId)
		assert.Equal(t, checkoutUrl, planChange.CheckoutSessionUrl)
		assert.Equal(t, checkoutSessionId, *planChange.CheckoutSessionId)
		assert.Equal(t, totalCost, planChange.TotalCost)
		assert.Equal(t, orbPendingChangeId, *planChange.OrbPendingChangeId)
	})

	t.Run("CreatePendingPlanChange fails when pending change already exists", func(t *testing.T) {
		// Create a test user with existing pending plan change
		userID := uuid.New().String()
		existingPlanChange := &auth_entities.PlanChange{
			Status:       auth_entities.PlanChange_PENDING,
			TargetPlanId: "existing-plan",
		}
		existingChange := &auth_entities.PendingChange{
			Id:        "existing-change",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
			Change: &auth_entities.PendingChange_PlanChange{
				PlanChange: existingPlanChange,
			},
		}
		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			PendingChange: existingChange,
		}

		userDAO := daoFactory.GetUserDAO()
		createdUser, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create billing info with existing pending change
		billingInfo := &UserBillingInfo{
			UserID:          userID,
			IsSelfServeTeam: false,
			PendingChange:   existingChange,
		}

		// Execute create function - should fail
		err = CreatePendingPlanChange(ctx, daoFactory, createdUser, billingInfo, "professional", "new-change", nil)
		require.Error(t, err)

		// Verify it's a FailedPrecondition error
		st, ok := status.FromError(err)
		require.True(t, ok)
		assert.Equal(t, codes.FailedPrecondition, st.Code())
		assert.Contains(t, st.Message(), "Pending change already exists")
	})

	t.Run("CreatePendingPlanChange works with minimal options", func(t *testing.T) {
		// Create a test user
		userID := uuid.New().String()
		user := &auth_entities.User{
			Id:    userID,
			Email: "<EMAIL>",
		}

		userDAO := daoFactory.GetUserDAO()
		createdUser, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create billing info without existing pending change
		billingInfo := &UserBillingInfo{
			UserID:          userID,
			IsSelfServeTeam: false,
			PendingChange:   nil,
		}

		// Execute create function with no options
		err = CreatePendingPlanChange(ctx, daoFactory, createdUser, billingInfo, "community", "minimal-change", nil)
		require.NoError(t, err)

		// Verify the plan change was created with minimal fields
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err)
		require.NotNil(t, updatedUser.PendingChange)
		assert.Equal(t, "minimal-change", updatedUser.PendingChange.Id)

		planChange := updatedUser.PendingChange.GetPlanChange()
		require.NotNil(t, planChange)
		assert.Equal(t, auth_entities.PlanChange_PENDING, planChange.Status)
		assert.Equal(t, "community", planChange.TargetPlanId)
		assert.Empty(t, planChange.CheckoutSessionUrl)
		assert.Nil(t, planChange.CheckoutSessionId)
		assert.Empty(t, planChange.TotalCost)
		assert.Nil(t, planChange.OrbPendingChangeId)
	})
}

func TestAssignMessageIDToPendingChange(t *testing.T) {
	bigtableFixture := NewBigtableFixture(t)
	defer bigtableFixture.Cleanup()

	ctx := bigtableFixture.Ctx
	daoFactory := NewDAOFactory(bigtableFixture.Table)

	t.Run("AssignMessageIDToPendingChange assigns message ID to existing plan change", func(t *testing.T) {
		// Create a test user with existing pending plan change (no message ID)
		userID := uuid.New().String()
		planChange := &auth_entities.PlanChange{
			Status:       auth_entities.PlanChange_PENDING,
			TargetPlanId: "professional",
			MessageId:    nil, // No message ID initially
		}
		existingChange := &auth_entities.PendingChange{
			Id:        "existing-change",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
			Change: &auth_entities.PendingChange_PlanChange{
				PlanChange: planChange,
			},
		}
		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			PendingChange: existingChange,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create billing info with existing pending change
		billingInfo := &UserBillingInfo{
			UserID:          userID,
			IsSelfServeTeam: false,
			PendingChange:   existingChange,
		}

		messageID := "msg_12345"

		// Execute assign function
		err = AssignMessageIDToPendingChange(ctx, daoFactory, billingInfo, messageID)
		require.NoError(t, err)

		// Verify the message ID was assigned
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err)
		require.NotNil(t, updatedUser.PendingChange)

		updatedPlanChange := updatedUser.PendingChange.GetPlanChange()
		require.NotNil(t, updatedPlanChange)
		require.NotNil(t, updatedPlanChange.MessageId)
		assert.Equal(t, messageID, *updatedPlanChange.MessageId)
	})

	t.Run("AssignMessageIDToPendingChange does not overwrite existing message ID", func(t *testing.T) {
		// Create a test user with existing pending plan change with message ID
		userID := uuid.New().String()
		existingMessageID := "existing_msg_123"
		planChange := &auth_entities.PlanChange{
			Status:       auth_entities.PlanChange_PENDING,
			TargetPlanId: "professional",
			MessageId:    &existingMessageID, // Already has message ID
		}
		existingChange := &auth_entities.PendingChange{
			Id:        "existing-change",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
			Change: &auth_entities.PendingChange_PlanChange{
				PlanChange: planChange,
			},
		}
		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			PendingChange: existingChange,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create billing info with existing pending change
		billingInfo := &UserBillingInfo{
			UserID:          userID,
			IsSelfServeTeam: false,
			PendingChange:   existingChange,
		}

		newMessageID := "new_msg_456"

		// Execute assign function
		err = AssignMessageIDToPendingChange(ctx, daoFactory, billingInfo, newMessageID)
		require.NoError(t, err)

		// Verify the message ID was NOT overwritten
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err)
		require.NotNil(t, updatedUser.PendingChange)

		updatedPlanChange := updatedUser.PendingChange.GetPlanChange()
		require.NotNil(t, updatedPlanChange)
		require.NotNil(t, updatedPlanChange.MessageId)
		assert.Equal(t, existingMessageID, *updatedPlanChange.MessageId) // Should still be the original
	})

	t.Run("AssignMessageIDToPendingChange does nothing when no pending change exists", func(t *testing.T) {
		// Create billing info without pending change
		billingInfo := &UserBillingInfo{
			UserID:          "test-user",
			IsSelfServeTeam: false,
			PendingChange:   nil, // No pending change
		}

		messageID := "msg_12345"

		// Execute assign function - should not error
		err := AssignMessageIDToPendingChange(ctx, daoFactory, billingInfo, messageID)
		require.NoError(t, err)
	})

	t.Run("AssignMessageIDToPendingChange does nothing when pending change is not a plan change", func(t *testing.T) {
		// Create a test user with non-plan-change pending change
		userID := uuid.New().String()
		existingChange := &auth_entities.PendingChange{
			Id:        "existing-change",
			CreatedAt: timestamppb.Now(),
			UpdatedAt: timestamppb.Now(),
			// No plan change - could be some other type of change
		}
		user := &auth_entities.User{
			Id:            userID,
			Email:         "<EMAIL>",
			PendingChange: existingChange,
		}

		userDAO := daoFactory.GetUserDAO()
		_, err := userDAO.Create(ctx, user)
		require.NoError(t, err)

		// Create billing info with existing pending change
		billingInfo := &UserBillingInfo{
			UserID:          userID,
			IsSelfServeTeam: false,
			PendingChange:   existingChange,
		}

		messageID := "msg_12345"

		// Execute assign function - should not error
		err = AssignMessageIDToPendingChange(ctx, daoFactory, billingInfo, messageID)
		require.NoError(t, err)

		// Verify the change was not modified
		updatedUser, err := userDAO.Get(ctx, userID)
		require.NoError(t, err)
		require.NotNil(t, updatedUser.PendingChange)
		assert.Nil(t, updatedUser.PendingChange.GetPlanChange()) // Should still be nil
	})
}
