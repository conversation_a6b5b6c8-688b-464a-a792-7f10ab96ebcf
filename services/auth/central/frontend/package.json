{"name": "auth-frontend", "private": true, "description": "A client-side React frontend for Auth Central.", "type": "module", "scripts": {"dev": "vite", "build": "tsc -b && vite build", "preview": "vite preview", "storybook": "storybook dev -p 6006", "build-storybook": "storybook build"}, "dependencies": {"@tanstack/react-query": "^5.81.2", "framer-motion": "^12.23.3", "react": "^19.1.0", "react-dom": "^19.1.0", "react-router-dom": "^7.6.2", "zod": "^3.25.67"}, "devDependencies": {"@storybook/react": "^9.0.15", "@storybook/react-vite": "^9.0.15", "@tailwindcss/vite": "^4.1.10", "@types/node": "^24.0.4", "@types/react": "^19.1.8", "@types/react-dom": "^19.1.6", "@vitejs/plugin-react": "^4.6.0", "msw": "^2.7.5", "msw-storybook-addon": "^2.0.4", "storybook": "^9.0.15", "tailwindcss": "^4.1.10", "typescript": "^5.8.3", "vite": "6.3.5"}, "msw": {"workerDirectory": ["third_party/msw"]}}