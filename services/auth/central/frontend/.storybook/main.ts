import type { StorybookConfig } from "@storybook/react-vite";
import { createRequire } from "module";
import path from "path";

const fullPath = (packageName: string): any =>
  path
    .dirname(
      createRequire(import.meta.url).resolve(
        path.join(packageName, "package.json"),
      ),
    )
    .replace(/^file:\/\//, "")
    .replace("/package.json", "");

const config: StorybookConfig = {
  stories: ["../src/**/*.mdx", "../src/**/*.stories.@(js|jsx|mjs|ts|tsx)"],
  addons: [],
  framework: {
    name: fullPath("@storybook/react-vite"),
    options: {
      builder: {
        viteConfigPath: ".storybook/vite.config.ts",
      },
    },
  },
  core: {
    disableTelemetry: true,
  },
  staticDirs: ["../third_party/msw"],
};
export default config;
