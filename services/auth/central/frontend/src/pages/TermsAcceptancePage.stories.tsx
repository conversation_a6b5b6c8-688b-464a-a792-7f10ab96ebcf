import React from "react";
import type { Meta, StoryObj } from "@storybook/react";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { within, waitFor, userEvent, expect } from "@storybook/test";
import { http, HttpResponse } from "msw";
import { TermsAcceptancePage } from "./TermsAcceptancePage";

const meta = {
  title: "Pages/TermsAcceptancePage",
  component: TermsAcceptancePage,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "Terms acceptance component that displays terms of service and handles user acceptance.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    oauthParams: {
      control: "object",
      description: "OAuth parameters for the authentication flow",
    },
    termsUrl: {
      control: "text",
      description: "URL to the terms of service document",
    },
  },
  decorators: [
    (Story: React.FC) => {
      const queryClient = new QueryClient({
        defaultOptions: {
          queries: { retry: false },
        },
      });
      return (
        <QueryClientProvider client={queryClient}>
          <Story />
        </QueryClientProvider>
      );
    },
  ],
} satisfies Meta<typeof TermsAcceptancePage>;

export default meta;
type Story = StoryObj<typeof meta>;

const defaultOAuthParams = {
  client_id: "test-client-id",
  redirect_uri: "https://app.example.com/callback",
  state: "test-state-123",
  code_challenge: "test-challenge",
};

/**
 * Default terms acceptance for enterprise
 */
export const Default: Story = {
  args: {
    oauthParams: defaultOAuthParams,
    termsUrl: "https://www.augmentcode.com/terms-of-service/enterprise",
  },
};

/**
 * Interactive story demonstrating the seamless redirect flow
 */
export const WithSuccessfulAcceptance: Story = {
  args: {
    oauthParams: defaultOAuthParams,
    termsUrl: "https://www.augmentcode.com/terms-of-service/enterprise",
    termsRevision: "enterprise_tos_v1",
  },
  parameters: {
    msw: {
      handlers: [
        http.post("/api/terms/accept", async () => {
          await new Promise((resolve) => setTimeout(resolve, 1000));
          return HttpResponse.json({
            success: true,
            redirect_url:
              "https://app.example.com/callback?code=test-code&state=test-state-123",
          });
        }),
      ],
    },
  },
  play: async ({ canvasElement }: { canvasElement: HTMLElement }) => {
    const canvas = within(canvasElement);

    // Wait for component to render
    await waitFor(() => {
      expect(canvas.getByText(/I agree to Augment's/)).toBeInTheDocument();
    });

    // Check the checkbox
    const checkbox = canvas.getByRole("checkbox");
    await userEvent.click(checkbox);

    await new Promise((resolve) => setTimeout(resolve, 1000));

    // Click the continue button
    const continueButton = canvas.getByRole("button", { name: /Continue/i });
    await userEvent.click(continueButton);

    // Verify the loading state appears
    await waitFor(() => {
      expect(
        canvas.getByText("Processing terms acceptance..."),
      ).toBeInTheDocument();
    });

    await waitFor(
      () => {
        expect(canvas.getByText("Redirecting...")).toBeInTheDocument();
      },
      { timeout: 2000 },
    );
  },
};
