import type { <PERSON>a, StoryObj } from "@storybook/react";
import { ErrorPage } from "./ErrorPage";

const meta = {
  title: "Pages/ErrorPage",
  component: ErrorPage,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "A full-screen error state component with customizable message, variant, and support information.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    message: {
      control: "text",
      description: "The error message to display",
    },
    showBackToLogin: {
      control: "boolean",
      description: "Whether to show the 'Back to Login' link",
    },
    variant: {
      control: "radio",
      options: ["error", "warning"],
      description: "The visual variant of the error state",
    },
    supportInfo: {
      control: "object",
      description: "Optional support information including timestamp",
    },
  },
} satisfies Meta<typeof ErrorPage>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default error state
 */
export const Default: Story = {
  args: {
    message: "An error occurred while processing your request.",
  },
};

/**
 * Error with support information
 */
export const WithSupportInfo: Story = {
  args: {
    message: "Authentication failed. Please try again.",
    supportInfo: {
      timestamp: new Date().toISOString(),
    },
  },
};

/**
 * Warning variant
 */
export const Warning: Story = {
  args: {
    message:
      "Your session is about to expire. Please log in again to continue.",
    variant: "warning",
  },
};

/**
 * Long error message
 */
export const LongErrorMessage: Story = {
  args: {
    message:
      "The authentication service encountered an unexpected error while processing your request. This might be due to an invalid configuration or temporary service disruption. Please try again in a few moments. If the problem persists, contact your system administrator.",
    supportInfo: {
      timestamp: new Date().toISOString(),
    },
  },
};

/**
 * Warning with support info and no back link
 */
export const ComplexWarning: Story = {
  args: {
    message: "Your account requires additional verification before proceeding.",
    variant: "warning",
    showBackToLogin: false,
    supportInfo: {
      timestamp: new Date().toISOString(),
    },
  },
};
