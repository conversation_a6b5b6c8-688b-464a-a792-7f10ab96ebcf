import { useState } from "react";
import { AugmentLogo } from "../components/AugmentLogo";
import { Button } from "../components/Button";
import { Card } from "../components/Card";
import { LoadingPage } from "./LoadingPage";
import { Checkbox } from "../components/Checkbox";
import { Layout } from "../components/Layout";
import { useAcceptTerms } from "../hooks/useTerms";
import { OAuthParams } from "../types/api";

interface TermsAcceptancePageProps {
  oauthParams: OAuthParams;
  termsUrl: string;
  termsRevision?: string;
}

export function TermsAcceptancePage({
  oauthParams,
  termsUrl,
  termsRevision,
}: TermsAcceptancePageProps) {
  const [accepted, setAccepted] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const acceptTermsMutation = useAcceptTerms();

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!accepted) {
      return;
    }

    try {
      const result = await acceptTermsMutation.mutateAsync({
        client_id: oauthParams.client_id,
        redirect_uri: oauthParams.redirect_uri,
        state: oauthParams.state,
        code_challenge: oauthParams.code_challenge,
        accepted: true,
        terms_revision: termsRevision,
      });

      if (result.success && result.redirect_url) {
        setIsRedirecting(true);
        window.location.href = result.redirect_url;
      }
    } catch (error) {
      console.error("Failed to accept terms:", error);
    }
  };

  if (acceptTermsMutation.isPending || isRedirecting) {
    return (
      <LoadingPage
        message={
          isRedirecting ? "Redirecting..." : "Processing terms acceptance..."
        }
      />
    );
  }

  return (
    <Layout>
      <div className="w-full max-w-md px-4 sm:px-6 lg:px-8">
        <Card>
          <header className="text-center">
            <AugmentLogo className="h-10 w-auto max-w-full mx-auto text-aug-text-light" />
          </header>

          <form onSubmit={handleSubmit} className="space-y-6">
            <Checkbox
              checked={accepted}
              onChange={setAccepted}
              disabled={acceptTermsMutation.isPending || isRedirecting}
              label={
                <span className="text-sm text-aug-text-light">
                  I agree to Augment's{" "}
                  <a
                    href={termsUrl}
                    target="_blank"
                    rel="noopener noreferrer"
                    className="checkbox-link"
                  >
                    terms of service
                  </a>
                </span>
              }
            />

            {acceptTermsMutation.error && (
              <div className="text-sm text-red-400 bg-red-900/20 border border-red-500/30 p-3">
                {acceptTermsMutation.error.message}
              </div>
            )}

            <Button
              type="submit"
              disabled={
                !accepted || acceptTermsMutation.isPending || isRedirecting
              }
            >
              {acceptTermsMutation.isPending || isRedirecting
                ? "Processing..."
                : "Continue"}
            </Button>
          </form>
        </Card>
      </div>
    </Layout>
  );
}
