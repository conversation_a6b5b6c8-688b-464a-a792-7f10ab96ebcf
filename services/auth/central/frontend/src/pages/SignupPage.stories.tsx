import { fn, within, waitFor, userEvent, expect } from "@storybook/test";
import type { Meta, StoryObj } from "@storybook/react";
import { http, HttpResponse } from "msw";
import { QueryClient, QueryClientProvider } from "@tanstack/react-query";
import { SignupPage } from "./SignupPage";

// Create a wrapper component for React Query
const queryClient = new QueryClient({
  defaultOptions: {
    queries: { retry: false },
    mutations: { retry: false },
  },
});

const meta = {
  title: "Pages/SignupPage",
  component: SignupPage,
  parameters: {
    layout: "fullscreen",
  },
  decorators: [
    (Story) => (
      <QueryClientProvider client={queryClient}>
        <Story />
      </QueryClientProvider>
    ),
  ],
  args: {
    onSignupSuccess: fn(),
  },
} satisfies Meta<typeof SignupPage>;

export default meta;
type Story = StoryObj<typeof meta>;

const defaultOAuthParams = {
  client_id: "customer-ui",
  redirect_uri: "https://app.example.com/callback",
  state: "test-state-123",
  code_challenge: "test-challenge",
  code_challenge_method: "S256",
};

export const Default: Story = {
  args: {
    email: "<EMAIL>",
    oauthParams: defaultOAuthParams,
    termsRevision: "enterprise_tos_295389227_v13",
  },
};

export const Loading: Story = {
  args: {
    email: "<EMAIL>",
    oauthParams: defaultOAuthParams,
    termsRevision: "enterprise_tos_295389227_v13",
  },
  parameters: {
    msw: {
      handlers: [
        // Mock a slow response to show loading state
        http.post("/api/signup", async () => {
          await new Promise((resolve) => setTimeout(resolve, 100000));
          return HttpResponse.json({
            success: true,
            message: "User created successfully",
            user_id: "test-user-id",
            tenant_name: "Example Corp",
            tenant_url: null,
          });
        }),
      ],
    },
  },
  play: async ({ canvasElement }: { canvasElement: HTMLElement }) => {
    const canvas = within(canvasElement);

    // Wait for component to render
    await waitFor(() => {
      expect(canvas.getByText(/Welcome to Augment Code/)).toBeInTheDocument();
    });

    // Check the terms checkbox
    const checkbox = canvas.getByRole("checkbox");
    await userEvent.click(checkbox);

    // Click the signup button
    const signupButton = canvas.getByRole("button", {
      name: /Sign up and start coding/i,
    });
    await userEvent.click(signupButton);
  },
};

export const WithLongEmail: Story = {
  args: {
    email: "<EMAIL>",
    oauthParams: defaultOAuthParams,
    termsRevision: "enterprise_tos_295389227_v13",
  },
};

export const WithError: Story = {
  args: {
    email: "<EMAIL>",
    oauthParams: defaultOAuthParams,
    termsRevision: "enterprise_tos_295389227_v13",
  },
  parameters: {
    msw: {
      handlers: [
        http.post("/api/signup", () => {
          return HttpResponse.json(
            {
              message:
                "We have reached our signup limit. Please try again later.",
              supportInfo: {
                timestamp: new Date().toISOString(),
              },
            },
            { status: 429 },
          );
        }),
      ],
    },
  },
  play: async ({ canvasElement }: { canvasElement: HTMLElement }) => {
    const canvas = within(canvasElement);

    // Wait for component to render
    await waitFor(() => {
      expect(canvas.getByText(/Welcome to Augment Code/)).toBeInTheDocument();
    });

    // Check the terms checkbox
    const checkbox = canvas.getByRole("checkbox");
    await userEvent.click(checkbox);

    // Click the signup button
    const signupButton = canvas.getByRole("button", {
      name: /Sign up and start coding/i,
    });
    await userEvent.click(signupButton);
  },
};

/**
 * Interactive story demonstrating the seamless redirect flow after signup
 */
export const WithSuccessfulSignup: Story = {
  args: {
    email: "<EMAIL>",
    oauthParams: defaultOAuthParams,
    termsRevision: "enterprise_tos_295389227_v13",
    onSignupSuccess: fn(),
  },
  parameters: {
    msw: {
      handlers: [
        http.post("/api/signup", async () => {
          // Simulate a slight delay for realism
          await new Promise((resolve) => setTimeout(resolve, 500));
          return HttpResponse.json({
            success: true,
            message: "User created successfully",
            user_id: "test-user-id",
            tenant_name: "Example Corp",
            tenant_url: null,
          });
        }),
      ],
    },
  },
  play: async ({
    canvasElement,
    args,
  }: {
    canvasElement: HTMLElement;
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    args: any;
  }) => {
    const canvas = within(canvasElement);

    // Wait for component to render
    await waitFor(() => {
      expect(canvas.getByText(/Welcome to Augment Code/)).toBeInTheDocument();
    });

    // Check the terms checkbox
    const checkbox = canvas.getByRole("checkbox");
    await userEvent.click(checkbox);

    // Click the signup button
    const signupButton = canvas.getByRole("button", {
      name: /Sign up and start coding/i,
    });
    await userEvent.click(signupButton);

    // Verify the button shows loading state
    await waitFor(() => {
      expect(
        canvas.getByRole("button", { name: /Signing up.../i }),
      ).toBeInTheDocument();
    });

    // After the API call succeeds, it should show the redirecting state
    await waitFor(
      () => {
        expect(
          canvas.getByText("Setting up your account..."),
        ).toBeInTheDocument();
      },
      { timeout: 2000 },
    );

    // Verify onSignupSuccess was called
    await waitFor(() => {
      expect(args.onSignupSuccess).toHaveBeenCalled();
    });
  },
};
