import { AugmentLogo } from "../components/AugmentLogo";
import { Card } from "../components/Card";
import { Layout } from "../components/Layout";

interface SupportInfo {
  timestamp: string;
}

interface ErrorPageProps {
  message: string;
  showBackToLogin?: boolean;
  variant?: "error" | "warning";
  supportInfo?: SupportInfo;
}

export function ErrorPage({
  message,
  showBackToLogin = true,
  variant = "error",
  supportInfo,
}: ErrorPageProps) {
  const alertClass =
    variant === "error"
      ? "bg-red-900/20 border border-red-500/30 p-4"
      : "bg-yellow-900/20 border border-yellow-500/30 p-4";
  const textClass = variant === "error" ? "text-red-400" : "text-yellow-400";

  return (
    <Layout>
      <div className="w-full max-w-lg px-4 sm:px-6 lg:px-8">
        <Card>
          <div className="text-center mb-8">
            <AugmentLogo className="h-10 w-auto max-w-full mx-auto text-aug-text-light" />
          </div>
          <div className={alertClass}>
            <p className={`text-sm ${textClass} m-0`}>{message}</p>
            {supportInfo && (
              <div className="mt-3 pt-3 border-t border-aug-border-dark">
                <p className="text-xs text-aug-text-light-tertiary m-0 mt-1">
                  Error occurred at: {supportInfo.timestamp}
                </p>
              </div>
            )}
          </div>
          {showBackToLogin && (
            <div className="text-center mt-4">
              <a
                href="/login"
                className="text-sm font-medium transition-colors duration-200 hover:text-aug-primary-dark"
              >
                ← Back to Login
              </a>
            </div>
          )}
        </Card>
      </div>
    </Layout>
  );
}
