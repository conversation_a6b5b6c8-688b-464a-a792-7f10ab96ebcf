import React, { useState, useMemo } from "react";
import { AugmentLogo } from "../components/AugmentLogo";
import { Button } from "../components/Button";
import { Card } from "../components/Card";
import { LoadingPage } from "./LoadingPage";
import { Checkbox } from "../components/Checkbox";
import { Layout } from "../components/Layout";
import { useSignup } from "../hooks/useSignup";
import { OAuthParams } from "../types/api";

interface SignupPageProps {
  email: string;
  oauthParams: OAuthParams;
  termsRevision?: string;
  onSignupSuccess?: () => void;
}

export function SignupPage({
  email,
  oauthParams,
  termsRevision,
  onSignupSuccess,
}: SignupPageProps) {
  const [termsAccepted, setTermsAccepted] = useState(false);
  const [isRedirecting, setIsRedirecting] = useState(false);
  const signupMutation = useSignup();

  // Function to truncate email for display
  const getTruncatedEmail = (email: string, maxLength = 30) => {
    if (email.length <= maxLength) {
      return email;
    }

    const [username, domain] = email.split("@");
    if (!domain) {
      return email;
    }

    // Always preserve the full domain
    const domainWithAt = `@${domain}`;
    const availableUsernameLength = maxLength - domainWithAt.length - 3; // -3 for "..."

    if (availableUsernameLength <= 0) {
      // If domain is too long, just return the full email
      return email;
    }

    if (username.length <= availableUsernameLength) {
      return email;
    }

    // Truncate username with ellipsis
    const truncatedUsername =
      username.substring(0, availableUsernameLength) + "...";
    return `${truncatedUsername}${domainWithAt}`;
  };

  const loginUrl = useMemo(() => {
    const params = new URLSearchParams({
      response_type: "code",
      client_id: oauthParams.client_id || "customer-ui",
      redirect_uri: oauthParams.redirect_uri,
      state: oauthParams.state,
      code_challenge: oauthParams.code_challenge,
      code_challenge_method: oauthParams.code_challenge_method || "S256",
    });
    return `/login?${params.toString()}`;
  }, [oauthParams]);

  const handleSubmit = async (e: React.FormEvent) => {
    e.preventDefault();
    if (termsAccepted && !signupMutation.isPending) {
      await signupMutation.mutateAsync({ termsRevision });
      // Set redirecting state before calling onSignupSuccess
      setIsRedirecting(true);
      onSignupSuccess?.();
    }
  };

  // Show loading state while redirecting
  if (isRedirecting) {
    return <LoadingPage message="Setting up your account..." />;
  }

  return (
    <Layout>
      <div className="w-full max-w-lg px-4 sm:px-6 lg:px-8">
        <Card>
          <div className="text-center">
            <AugmentLogo className="h-10 w-auto max-w-full mx-auto" />
          </div>

          <div>
            <h2 className="text-2xl font-semibold text-aug-text-light">
              Welcome to Augment Code
            </h2>
            <p className="mt-2 text-sm text-aug-text-light-secondary">
              Sign up with{" "}
              <code
                className="px-2 py-1 bg-aug-bg-dark-tertiary text-sm text-aug-lavender-dark-mode inline-block max-w-full"
                title={email}
              >
                {getTruncatedEmail(email)}
              </code>{" "}
              to get started
            </p>
          </div>

          {signupMutation.error && (
            <div className="p-4 bg-red-900/20 border border-red-500/30">
              <p className="text-sm text-red-400">
                {signupMutation.error.message}
              </p>
            </div>
          )}

          <form onSubmit={handleSubmit} className="space-y-6">
            <div className="space-y-4">
              <Checkbox
                checked={termsAccepted}
                onChange={setTermsAccepted}
                disabled={signupMutation.isPending || isRedirecting}
                label={
                  <span className="text-sm text-aug-text-light">
                    I agree to Augment's{" "}
                    <a
                      href="https://www.augmentcode.com/terms-of-service/enterprise"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="checkbox-link"
                    >
                      Terms of Service
                    </a>{" "}
                    and{" "}
                    <a
                      href="https://www.augmentcode.com/privacy-policy"
                      target="_blank"
                      rel="noopener noreferrer"
                      className="checkbox-link"
                    >
                      Privacy Policy
                    </a>
                  </span>
                }
              />
            </div>

            <Button
              type="submit"
              disabled={
                !termsAccepted || signupMutation.isPending || isRedirecting
              }
            >
              {signupMutation.isPending
                ? "Signing up..."
                : "Sign up and start coding"}
            </Button>

            <div className="text-center">
              <a
                href={loginUrl}
                className="text-sm text-aug-text-light-secondary hover:text-aug-text-light transition-colors"
              >
                Different Account
              </a>
            </div>
          </form>
        </Card>
      </div>
    </Layout>
  );
}
