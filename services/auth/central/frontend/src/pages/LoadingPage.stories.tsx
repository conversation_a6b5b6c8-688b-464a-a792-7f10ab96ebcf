import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { LoadingPage } from "./LoadingPage";

const meta = {
  title: "Pages/LoadingPage",
  component: LoadingPage,
  parameters: {
    layout: "fullscreen",
    docs: {
      description: {
        component:
          "A full-screen loading state component with a spinner and customizable message.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    message: {
      control: "text",
      description: "The loading message to display below the spinner",
    },
  },
} satisfies Meta<typeof LoadingPage>;

export default meta;
type Story = StoryObj<typeof meta>;

/**
 * Default loading state with the standard authentication message
 */
export const Default: Story = {
  args: {},
};

/**
 * Loading state with custom message
 */
export const CustomMessage: Story = {
  args: {
    message: "Processing your request...",
  },
};

/**
 * Loading state with empty message
 */
export const NoMessage: Story = {
  args: {
    message: "",
  },
};
