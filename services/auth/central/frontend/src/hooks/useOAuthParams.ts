import { useSearchParams } from "react-router-dom";
import { OAuthParams, OAuthParamsSchema } from "../types/api";

export function useOAuthParams(): OAuthParams | null {
  const [searchParams] = useSearchParams();

  const client_id = searchParams.get("client_id");
  const redirect_uri = searchParams.get("redirect_uri");
  const state = searchParams.get("state");
  const code_challenge = searchParams.get("code_challenge");
  const code_challenge_method =
    searchParams.get("code_challenge_method") || "S256";

  if (!client_id || !redirect_uri || !state || !code_challenge) {
    return null;
  }

  const params = {
    client_id,
    redirect_uri,
    state,
    code_challenge,
    code_challenge_method,
  };

  const result = OAuthParamsSchema.safeParse(params);
  return result.success ? result.data : null;
}
