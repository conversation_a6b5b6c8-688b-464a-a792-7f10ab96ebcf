import { useMutation } from "@tanstack/react-query";
import {
  TermsAcceptRequest,
  TermsAcceptResponse,
  TermsAcceptResponseSchema,
  ErrorResponseSchema,
} from "../types/api";

/**
 * Hook to accept terms of service
 */
export function useAcceptTerms() {
  return useMutation<TermsAcceptResponse, Error, TermsAcceptRequest>({
    mutationFn: async (request: TermsAcceptRequest) => {
      const response = await fetch("/api/terms/accept", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        credentials: "include",
        body: JSON.stringify(request),
      });

      if (!response.ok) {
        const errorData = await response.json();
        const errorResult = ErrorResponseSchema.safeParse(errorData);
        const errorMessage = errorResult.success
          ? errorResult.data.message
          : "Failed to accept terms";
        throw new Error(errorMessage);
      }

      const data = await response.json();
      const result = TermsAcceptResponseSchema.safeParse(data);

      if (!result.success) {
        throw new Error("Invalid response format from server");
      }

      return result.data;
    },
    retry: 1,
  });
}
