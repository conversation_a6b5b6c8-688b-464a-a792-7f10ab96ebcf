import { z } from "zod";
import { useMutation } from "@tanstack/react-query";

// Response schemas
const SignupSuccessSchema = z.object({
  success: z.boolean(),
  message: z.string(),
  user_id: z.string(),
  tenant_name: z.string(),
  tenant_url: z.string().nullable(),
});

const SignupErrorSchema = z.object({
  message: z.string(),
  support_info: z
    .object({
      timestamp: z.string(),
    })
    .optional(),
});

type SignupSuccess = z.infer<typeof SignupSuccessSchema>;

interface SignupParams {
  termsRevision?: string;
}

export function useSignup() {
  return useMutation<SignupSuccess, Error, SignupParams>({
    mutationFn: async ({ termsRevision }) => {
      const response = await fetch("/api/signup", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          terms_accepted: true,
          terms_revision: termsRevision,
        }),
        credentials: "same-origin",
      });

      const data = await response.json();

      if (!response.ok) {
        // Parse error response
        const errorData = SignupErrorSchema.parse(data);
        throw new Error(errorData.message);
      }

      // Parse success response
      return SignupSuccessSchema.parse(data);
    },
  });
}
