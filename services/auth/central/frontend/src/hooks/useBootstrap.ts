import { useEffect } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  OAuthParams,
  BootstrapResponse,
  BootstrapResponseSchema,
} from "../types/api";

export interface SupportInfo {
  timestamp: string;
}

export class BootstrapError extends Error {
  supportInfo?: SupportInfo;

  constructor(message: string, supportInfo?: SupportInfo) {
    super(message);
    this.supportInfo = supportInfo;
  }
}

export function useBootstrap(oauthParams: OAuthParams | null) {
  const query = useQuery<BootstrapResponse>({
    queryKey: ["bootstrap", oauthParams],
    queryFn: async () => {
      if (!oauthParams) {
        throw new Error("Missing required OAuth parameters");
      }

      const queryString = new URLSearchParams(
        Object.entries(oauthParams),
      ).toString();

      const response = await fetch(`/api/bootstrap?${queryString}`, {
        credentials: "same-origin",
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new BootstrapError(
          errorData.message || "Failed to fetch user status",
          errorData.support_info,
        );
      }

      const data = await response.json();
      const result = BootstrapResponseSchema.safeParse(data);

      if (!result.success) {
        throw new Error("Invalid response format from server");
      }

      return result.data;
    },
    enabled: !!oauthParams,
    retry: (failureCount, error) => {
      // Don't retry on 4xx errors
      if (error instanceof BootstrapError && error.message.includes("4")) {
        return false;
      }
      return failureCount < 3;
    },
  });

  // Handle redirects based on the response data
  useEffect(() => {
    if (!query.data || !oauthParams) return;

    const { authenticated, terms_approved, redirect_url } = query.data;

    // If not authenticated, redirect to login
    if (!authenticated) {
      const loginParams = new URLSearchParams({
        ...oauthParams,
        response_type: "code",
      });
      window.location.href = `/login?${loginParams.toString()}`;
      return;
    }

    // If authenticated and terms approved with redirect URL, complete OAuth flow
    if (authenticated && terms_approved && redirect_url) {
      console.log(
        "User authenticated and approved, redirecting to:",
        redirect_url,
      );
      window.location.replace(redirect_url);
      return;
    }

    // If authenticated but terms not approved, do nothing (UI will handle this)
  }, [query.data, oauthParams]);

  return query;
}
