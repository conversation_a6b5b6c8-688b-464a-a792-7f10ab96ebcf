import { z } from "zod";

// Common error response schema
export const ErrorResponseSchema = z.object({
  message: z.string(),
  support_info: z
    .object({
      timestamp: z.string(),
    })
    .optional(),
});

export type ErrorResponse = z.infer<typeof ErrorResponseSchema>;

// Bootstrap API types (consolidated with terms information)
export const BootstrapResponseSchema = z.object({
  authenticated: z.boolean(),
  terms_approved: z.boolean().optional(),
  redirect_url: z.string().optional(),
  terms_type: z.enum(["community", "enterprise"]).optional(),
  terms_url: z.string().optional(),
  tenant_name: z.string().optional(),
  terms_revision: z.string().optional(),
  needs_signup: z.boolean().optional(),
  email: z.string().optional(),
});

export type BootstrapResponse = z.infer<typeof BootstrapResponseSchema>;

// Terms Accept API types
export const TermsAcceptRequestSchema = z.object({
  client_id: z.string(),
  redirect_uri: z.string(),
  state: z.string(),
  code_challenge: z.string(),
  accepted: z.boolean(),
  terms_revision: z.string().optional(),
});

export type TermsAcceptRequest = z.infer<typeof TermsAcceptRequestSchema>;

export const TermsAcceptResponseSchema = z.object({
  success: z.boolean(),
  redirect_url: z.string().optional(),
});

export type TermsAcceptResponse = z.infer<typeof TermsAcceptResponseSchema>;

// OAuth parameters type (shared across endpoints)
export const OAuthParamsSchema = z.object({
  client_id: z.string(),
  redirect_uri: z.string(),
  state: z.string(),
  code_challenge: z.string(),
  code_challenge_method: z.string().optional(),
  response_type: z.string().optional(),
});

export type OAuthParams = z.infer<typeof OAuthParamsSchema>;
