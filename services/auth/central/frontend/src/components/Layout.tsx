import { ReactNode } from "react";
import { motion, AnimatePresence } from "framer-motion";
import { Footer } from "./Footer";

interface LayoutProps {
  children: ReactNode;
  // Optional key for AnimatePresence - if not provided, uses children type
  transitionKey?: string;
}

// Animation variants for page transitions
// Using subtle fade + vertical slide creates a natural flow between auth states
// Initial state slides up from below, exit slides up and out
const pageVariants = {
  initial: {
    opacity: 0,
    transform: "translateX(-20px)",
  },
  animate: {
    opacity: 1,
    transform: "translateX(0px)",
  },
  exit: {
    opacity: 0,
    transform: "translateX(20px)",
  },
};

// Transition timing - 300ms provides smooth movement without feeling sluggish
// easeInOut creates natural acceleration/deceleration
const pageTransition = {
  type: "tween" as const,
  ease: "easeInOut" as const,
  duration: 0.3,
};

export function Layout({ children, transitionKey }: LayoutProps) {
  // Generate a key based on the child component type if no explicit key provided
  const animationKey =
    // eslint-disable-next-line @typescript-eslint/no-explicit-any
    transitionKey || (children as any)?.type?.name || "default";

  return (
    <div className="min-h-screen flex flex-col bg-aug-bg-dark">
      <AnimatePresence mode="wait">
        <motion.main
          key={animationKey}
          className="flex-1 pb-24 flex items-center justify-center"
          style={{ willChange: "transform, opacity" }}
          initial="initial"
          animate="animate"
          exit="exit"
          variants={pageVariants}
          transition={pageTransition}
        >
          {children}
        </motion.main>
      </AnimatePresence>
      <Footer />
    </div>
  );
}
