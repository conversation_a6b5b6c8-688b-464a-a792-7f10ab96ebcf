import React from "react";

interface ButtonProps {
  children: React.ReactNode;
  disabled?: boolean;
  onClick?: () => void;
  type?: "button" | "submit" | "reset";
  className?: string;
}

export function Button({
  children,
  disabled = false,
  onClick,
  type = "button",
  className = "",
}: ButtonProps) {
  const baseStyles =
    "w-full py-3 px-4 font-medium rounded-md transition-all duration-200";

  const stateStyles = disabled
    ? "bg-aug-bg-dark-tertiary text-aug-text-light-tertiary cursor-not-allowed border border-aug-border-muted opacity-50"
    : "button-premium text-aug-text-light focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-aug-primary focus:ring-offset-aug-bg-dark-secondary cursor-pointer";

  return (
    <button
      type={type}
      disabled={disabled}
      onClick={onClick}
      className={`${baseStyles} ${stateStyles} ${className}`}
    >
      {children}
    </button>
  );
}
