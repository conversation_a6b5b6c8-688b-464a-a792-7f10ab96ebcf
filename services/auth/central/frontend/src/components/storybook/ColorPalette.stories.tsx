import type { <PERSON>a, StoryObj } from "@storybook/react";

const meta: Meta = {
  title: "Design System/Color Palette",
  parameters: {
    layout: "padded",
  },
};

export default meta;

type Story = StoryObj;

export const AugmentCodePalette: Story = {
  render: () => (
    <div className="space-y-8 p-8 bg-aug-bg-dark min-h-screen">
      <div>
        <h1 className="text-3xl font-bold text-aug-text-light mb-8">
          AugmentCode.com Color Palette
        </h1>

        {/* Primary Colors - Silver/Metallic */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-aug-text-light mb-4">
            Primary Colors (Silver/Metallic)
          </h2>
          <div className="grid grid-cols-2 gap-4 max-w-2xl">
            <div className="bg-aug-primary p-6 rounded-md">
              <p className="text-aug-black font-mono text-sm">--aug-metallic</p>
              <p className="text-aug-black text-xs mt-1">Primary Silver</p>
            </div>
            <div className="bg-aug-primary-dark p-6 rounded-md">
              <p className="text-aug-white font-mono text-sm">
                --aug-metallic-dark
              </p>
              <p className="text-aug-white text-xs mt-1">Dark Silver</p>
            </div>
          </div>
        </section>

        {/* Neutral Colors */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-aug-text-light mb-4">
            Neutral Colors
          </h2>
          <div className="grid grid-cols-3 gap-4 max-w-3xl">
            <div className="bg-aug-swiss-white p-6 rounded-md border border-aug-border-muted">
              <p className="text-aug-black font-mono text-sm">
                --aug-swiss-white
              </p>
              <p className="text-aug-black text-xs mt-1">Swiss White</p>
            </div>
            <div className="bg-aug-gray-light p-6 rounded-md">
              <p className="text-aug-black font-mono text-sm">
                --aug-gray-light
              </p>
              <p className="text-aug-black text-xs mt-1">Light Gray</p>
            </div>
            <div className="bg-aug-gray p-6 rounded-md">
              <p className="text-aug-black font-mono text-sm">--aug-gray</p>
              <p className="text-aug-black text-xs mt-1">Neutral Gray</p>
            </div>
            <div className="bg-aug-gray-dark p-6 rounded-md">
              <p className="text-aug-white font-mono text-sm">
                --aug-gray-dark
              </p>
              <p className="text-aug-white text-xs mt-1">Dark Gray</p>
            </div>
            <div className="bg-aug-bg-dark-secondary p-6 rounded-md border border-aug-border-muted">
              <p className="text-aug-white font-mono text-sm">--aug-card-bg</p>
              <p className="text-aug-white text-xs mt-1">Card Background</p>
            </div>
            <div className="bg-aug-black p-6 rounded-md border border-aug-border-muted">
              <p className="text-aug-white font-mono text-sm">--aug-black</p>
              <p className="text-aug-white text-xs mt-1">Pure Black</p>
            </div>
          </div>
        </section>

        {/* Accent Colors */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-aug-text-light mb-4">
            Accent Colors
          </h2>
          <div className="grid grid-cols-2 gap-4 max-w-2xl">
            <div className="bg-aug-lavender p-6 rounded-md">
              <p className="text-aug-black font-mono text-sm">--aug-lavender</p>
              <p className="text-aug-black text-xs mt-1">Lavender Accent</p>
            </div>
            <div className="bg-aug-lavender-new p-6 rounded-md">
              <p className="text-aug-black font-mono text-sm">
                --aug-lavender-new
              </p>
              <p className="text-aug-black text-xs mt-1">New Lavender</p>
            </div>
          </div>
        </section>

        {/* Text Examples */}
        <section className="mb-12">
          <h2 className="text-xl font-semibold text-aug-text-light mb-4">
            Typography & Effects
          </h2>
          <div className="space-y-4">
            <div>
              <h3 className="text-lg font-medium text-aug-text-light mb-2">
                Silver Text Effect
              </h3>
              <p className="silver-text text-4xl font-bold">Augment Code</p>
            </div>
            <div className="mt-6">
              <h3 className="text-lg font-medium text-aug-text-light mb-2">
                Text Hierarchy
              </h3>
              <p className="text-aug-text-light">
                Primary Text (--aug-foreground)
              </p>
              <p className="text-aug-text-light-secondary">
                Secondary Text (--aug-foreground-muted)
              </p>
              <p className="text-aug-text-light-tertiary">
                Tertiary Text (--aug-neutral-gray-light)
              </p>
            </div>
          </div>
        </section>

        {/* Component Examples */}
        <section>
          <h2 className="text-xl font-semibold text-aug-text-light mb-4">
            Component Examples
          </h2>
          <div className="space-y-4 max-w-md">
            <button className="w-full py-3 px-4 button-premium text-aug-text-light rounded-md transition-all duration-200">
              Premium Button Style
            </button>
            <button className="w-full py-3 px-4 bg-aug-bg-dark-tertiary hover:bg-aug-bg-dark-secondary text-aug-text-light border border-aug-border hover:border-aug-text-light-tertiary rounded-md transition-all duration-200">
              Secondary Button
            </button>
            <button
              disabled
              className="w-full py-3 px-4 bg-aug-bg-dark-tertiary text-aug-text-light-tertiary cursor-not-allowed border border-aug-border-muted opacity-50 rounded-md"
            >
              Disabled Button
            </button>
            <div className="silver-gradient p-6 rounded-md">
              <p className="text-aug-black font-semibold">
                Silver Gradient Background
              </p>
              <p className="text-aug-black text-sm mt-1">
                Using the .silver-gradient utility class
              </p>
            </div>
            <div className="border-metallic rounded-md">
              <div className="bg-aug-bg-dark p-6">
                <p className="text-aug-text-light font-semibold">
                  Metallic Border
                </p>
                <p className="text-aug-text-light-secondary text-sm mt-1">
                  Using the .border-metallic utility class
                </p>
              </div>
            </div>
          </div>
        </section>
      </div>
    </div>
  ),
};

export const InteractiveElements: Story = {
  render: () => (
    <div className="p-8 bg-aug-bg-dark min-h-screen">
      <div className="max-w-2xl mx-auto space-y-6">
        <h2 className="text-2xl font-bold text-aug-text-light mb-6">
          Interactive Elements
        </h2>

        {/* Links */}
        <div>
          <h3 className="text-lg font-medium text-aug-text-light mb-3">
            Links
          </h3>
          <p className="text-aug-text-light-secondary">
            Check out our <a href="#">documentation</a> and{" "}
            <a href="#">API reference</a>.
          </p>
        </div>

        {/* Form Elements */}
        <div>
          <h3 className="text-lg font-medium text-aug-text-light mb-3">
            Form Elements
          </h3>
          <input
            type="text"
            placeholder="Enter your email"
            className="w-full px-4 py-2 bg-aug-input-bg border border-aug-border-muted rounded-md text-aug-text-light placeholder-aug-text-light-tertiary focus:outline-none focus:ring-2 focus:ring-aug-primary focus:border-transparent mb-4"
          />
          <label className="flex items-center space-x-3 cursor-pointer">
            <div className="relative flex-shrink-0">
              <input type="checkbox" className="sr-only" defaultChecked />
              <div className="w-5 h-5 border-2 rounded-md transition-colors flex items-center justify-center bg-aug-metallic-dark border-aug-metallic-dark">
                <svg
                  className="w-3 h-3 text-aug-white"
                  fill="currentColor"
                  viewBox="0 0 20 20"
                >
                  <path
                    fillRule="evenodd"
                    d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                    clipRule="evenodd"
                  />
                </svg>
              </div>
            </div>
            <span className="text-sm text-aug-text-light">
              I agree to the terms (metallic-dark checkbox)
            </span>
          </label>
        </div>

        {/* Cards */}
        <div>
          <h3 className="text-lg font-medium text-aug-text-light mb-3">
            Cards
          </h3>
          <div className="bg-aug-card-bg border border-aug-border-muted rounded-md p-6">
            <h4 className="text-lg font-semibold text-aug-text-light mb-2">
              Card Title
            </h4>
            <p className="text-aug-text-light-secondary">
              This is a card component using the new silver-based color scheme.
            </p>
          </div>
        </div>

        {/* Code Blocks */}
        <div>
          <h3 className="text-lg font-medium text-aug-text-light mb-3">Code</h3>
          <pre className="bg-aug-bg-dark-secondary border border-aug-border-muted rounded-md p-4 overflow-x-auto">
            <code className="text-aug-text-light font-mono text-sm">
              {`const theme = {
  primary: 'silver',
  accent: 'lavender',
  background: 'black'
};`}
            </code>
          </pre>
        </div>
      </div>
    </div>
  ),
};
