import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";

import { AugmentLogo } from "./AugmentLogo";

const meta = {
  title: "Components/AugmentLogo",
  component: AugmentLogo,
  parameters: {
    layout: "centered",
    docs: {
      description: {
        component:
          "The Augment logo SVG component that can be styled with CSS classes.",
      },
    },
  },
  tags: ["autodocs"],
  argTypes: {
    className: {
      control: "text",
      description: "CSS classes to apply to the SVG element",
    },
  },
} satisfies Meta<typeof AugmentLogo>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    className: "h-8 w-auto text-white",
  },
};

/**
 * Custom styling example
 */
export const CustomStyling: Story = {
  args: {
    className:
      "h-12 w-auto text-aug-primary hover:text-aug-primary-dark transition-colors cursor-pointer",
  },
};
