import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { fn } from "@storybook/test";
import { Button } from "./Button";

const meta = {
  title: "Components/Button",
  component: Button,
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "dark",
      values: [
        { name: "dark", value: "#1e1e1e" },
        { name: "secondary", value: "#2d2d2d" },
      ],
    },
  },
  tags: ["autodocs"],
  argTypes: {
    children: {
      control: "text",
      description: "The content of the button",
    },
    disabled: {
      control: "boolean",
      description: "Whether the button is disabled",
    },
    type: {
      control: "select",
      options: ["button", "submit", "reset"],
      description: "The HTML button type",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
  args: {
    onClick: fn(),
  },
} satisfies Meta<typeof Button>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: "Click me",
    disabled: false,
    type: "button",
  },
};

export const Disabled: Story = {
  args: {
    children: "Disabled button",
    disabled: true,
    type: "button",
  },
};
