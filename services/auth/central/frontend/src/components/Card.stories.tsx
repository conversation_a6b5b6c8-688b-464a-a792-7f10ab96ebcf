import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { Card } from "./Card";
import { Button } from "./Button";

const meta = {
  title: "Components/Card",
  component: Card,
  parameters: {
    layout: "centered",
    backgrounds: {
      default: "dark",
      values: [{ name: "dark", value: "#1e1e1e" }],
    },
  },
  tags: ["autodocs"],
  argTypes: {
    children: {
      control: "text",
      description: "The content of the card",
    },
    className: {
      control: "text",
      description: "Additional CSS classes",
    },
  },
} satisfies Meta<typeof Card>;

export default meta;
type Story = StoryObj<typeof meta>;

export const Default: Story = {
  args: {
    children: (
      <div>
        <h2 className="text-xl font-semibold text-aug-text-light mb-4">
          Card Title
        </h2>
        <p className="text-aug-text-light-secondary">
          This is a card component.
        </p>
      </div>
    ),
  },
};

export const WithForm: Story = {
  args: {
    children: (
      <div className="space-y-6">
        <h2 className="text-xl font-semibold text-aug-text-light">Sign Up</h2>
        <input
          type="email"
          placeholder="Email"
          className="w-full px-3 py-2 bg-aug-bg-dark-tertiary border border-aug-border-dark rounded-md text-aug-text-light placeholder-aug-text-light-tertiary focus:outline-none focus:ring-2 focus:ring-aug-primary"
        />
        <Button type="submit">Continue</Button>
      </div>
    ),
  },
};
