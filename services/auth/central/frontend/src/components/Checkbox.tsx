import React from "react";

interface CheckboxProps {
  checked: boolean;
  onChange: (checked: boolean) => void;
  label: string | React.ReactNode;
  disabled?: boolean;
  className?: string;
}

export function Checkbox({
  checked,
  onChange,
  label,
  disabled = false,
  className = "",
}: CheckboxProps) {
  return (
    <label
      className={`flex items-start space-x-3 ${disabled ? "cursor-not-allowed" : "cursor-pointer"} ${className}`}
    >
      <div className="relative flex-shrink-0 mt-0.5">
        <input
          type="checkbox"
          checked={checked}
          onChange={(e) => onChange(e.target.checked)}
          className="sr-only"
          disabled={disabled}
        />
        <div
          className={`w-5 h-5 border-2 rounded-md transition-colors flex items-center justify-center ${
            checked
              ? "bg-aug-metallic-dark border-aug-metallic-dark"
              : "border-aug-border-dark bg-aug-bg-dark-secondary"
          } ${disabled ? "opacity-50" : ""}`}
        >
          {checked && (
            <svg
              className="w-3 h-3 text-aug-white"
              fill="currentColor"
              viewBox="0 0 20 20"
            >
              <path
                fillRule="evenodd"
                d="M16.707 5.293a1 1 0 010 1.414l-8 8a1 1 0 01-1.414 0l-4-4a1 1 0 011.414-1.414L8 12.586l7.293-7.293a1 1 0 011.414 0z"
                clipRule="evenodd"
              />
            </svg>
          )}
        </div>
      </div>
      <div
        className={`text-sm leading-relaxed ${disabled ? "text-aug-text-light-tertiary" : ""}`}
      >
        {label}
      </div>
    </label>
  );
}
