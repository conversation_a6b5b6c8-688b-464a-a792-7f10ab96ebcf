use std::sync::Arc;

use async_lock::Mutex;
use async_trait::async_trait;
use auth_entities_proto::auth_entities;
use grpc_client::create_channel;
use request_context::RequestContext;
use tonic::transport::ClientTlsConfig;
use tracing_tonic::client::TracingService;

// Include the generated protobuf code
pub mod auth {
    tonic::include_proto!("auth");
}

#[async_trait]
pub trait AuthCentralClient: Send + Sync {
    async fn revoke_user_by_id(
        &self,
        request: auth::RevokeUserByIdRequest,
        request_context: &RequestContext,
    ) -> tonic::Result<auth::RevokeUserByIdResponse>;
}

pub struct AuthCentralClientImpl {
    endpoint: String,
    tls_config: Option<ClientTlsConfig>,
    client: Arc<Mutex<Option<auth::auth_service_client::AuthServiceClient<TracingService>>>>,
}

impl AuthCentralClientImpl {
    pub fn new(endpoint: &str, tls_config: Option<ClientTlsConfig>) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<auth::auth_service_client::AuthServiceClient<TracingService>, tonic::transport::Error>
    {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            Some(c) => Ok(c.clone()),
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client = auth::auth_service_client::AuthServiceClient::new(channel);
                *m = Some(client.clone());
                Ok(client)
            }
        }
    }
}

#[async_trait]
impl AuthCentralClient for AuthCentralClientImpl {
    async fn revoke_user_by_id(
        &self,
        request: auth::RevokeUserByIdRequest,
        request_context: &RequestContext,
    ) -> tonic::Result<auth::RevokeUserByIdResponse> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("auth central client not ready: {}", e);
            tonic::Status::unavailable("auth central not ready")
        })?;

        let mut tonic_request = tonic::Request::new(request);
        request_context.annotate(tonic_request.metadata_mut());

        let response = client.revoke_user_by_id(tonic_request).await?;
        Ok(response.into_inner())
    }
}

pub struct MockAuthCentralClient {
    pub tokens_deleted: i32,
}

impl MockAuthCentralClient {
    pub fn new() -> Self {
        Self { tokens_deleted: 1 }
    }
}

#[async_trait]
impl AuthCentralClient for MockAuthCentralClient {
    async fn revoke_user_by_id(
        &self,
        _request: auth::RevokeUserByIdRequest,
        _request_context: &RequestContext,
    ) -> tonic::Result<auth::RevokeUserByIdResponse> {
        Ok(auth::RevokeUserByIdResponse {
            tokens_deleted: self.tokens_deleted,
        })
    }
}
