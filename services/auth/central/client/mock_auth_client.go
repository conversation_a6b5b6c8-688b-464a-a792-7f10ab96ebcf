package client

import (
	"context"

	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	pb "github.com/augmentcode/augment/services/auth/central/server/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/stretchr/testify/mock"
)

type MockAuthClient struct {
	AuthClient
	mock.Mock
}

func NewMockAuthClient() *MockAuthClient {
	return &MockAuthClient{}
}

func (m *MockAuthClient) ListTenantUsers(
	ctx context.Context, requestContext *requestcontext.RequestContext, tenantID string,
) ([]*auth_entities.User, error) {
	args := m.Called(ctx, requestContext, tenantID)
	return args.Get(0).([]*auth_entities.User), args.Error(1)
}

func (m *MockAuthClient) RemoveUserFromTenant(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string,
) error {
	args := m.Called(ctx, requestContext, userId, tenantID)
	return args.Error(0)
}

func (m *MockAuthClient) CreateUserSuspension(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string, suspensionType auth_entities.UserSuspensionType, evidence string,
) (suspensionID string, tokensDeleted int32, err error) {
	args := m.Called(ctx, requestContext, userId, tenantID, suspensionType, evidence)
	return args.Get(0).(string), args.Get(1).(int32), args.Error(2)
}

func (m *MockAuthClient) DeleteUserSuspensions(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string, suspensionIds []string,
) (int32, error) {
	args := m.Called(ctx, requestContext, userId, tenantID, suspensionIds)
	return args.Get(0).(int32), args.Error(1)
}

func (m *MockAuthClient) UpdateSuspensionExemption(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string, exempt bool,
) (bool, error) {
	args := m.Called(ctx, requestContext, userId, tenantID, exempt)
	return args.Get(0).(bool), args.Error(1)
}

func (m *MockAuthClient) AddUserTags(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string, tags []string,
) error {
	args := m.Called(ctx, requestContext, userId, tenantID, tags)
	return args.Error(0)
}

func (m *MockAuthClient) RemoveUserTags(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string, tags []string,
) error {
	args := m.Called(ctx, requestContext, userId, tenantID, tags)
	return args.Error(0)
}

func (m *MockAuthClient) GetUser(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID *string,
) (*auth_entities.User, error) {
	args := m.Called(ctx, requestContext, userId, tenantID)
	return args.Get(0).(*auth_entities.User), args.Error(1)
}

func (m *MockAuthClient) GetUsers(
	ctx context.Context, requestContext *requestcontext.RequestContext, request *pb.GetUsersRequest,
) (*pb.GetUsersResponse, error) {
	args := m.Called(ctx, requestContext, request)
	return args.Get(0).(*pb.GetUsersResponse), args.Error(1)
}

func (m *MockAuthClient) GetUserBillingInfo(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string,
) (*pb.GetUserBillingInfoResponse, error) {
	args := m.Called(ctx, requestContext, userId, tenantID)
	return args.Get(0).(*pb.GetUserBillingInfoResponse), args.Error(1)
}

func (m *MockAuthClient) UpdateUserBillingInfo(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	userId string,
	tenantID string,
	orbCustomerID *string,
	orbSubscriptionID *string,
	stripeCustomerID *string,
) error {
	args := m.Called(ctx, requestContext, userId, tenantID, orbCustomerID, orbSubscriptionID, stripeCustomerID)
	return args.Error(0)
}

func (m *MockAuthClient) GetUserSubscriptionInfo(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string, tenantID string,
) (*pb.GetUserSubscriptionInfoResponse, error) {
	args := m.Called(ctx, requestContext, userId, tenantID)
	return args.Get(0).(*pb.GetUserSubscriptionInfoResponse), args.Error(1)
}

func (m *MockAuthClient) ForgetUser(
	ctx context.Context, requestContext *requestcontext.RequestContext, userId string,
) error {
	args := m.Called(ctx, requestContext, userId)
	return args.Error(0)
}

func (m *MockAuthClient) AllowNewTrialForUsers(
	ctx context.Context, requestContext *requestcontext.RequestContext, userIds []string,
) ([]*pb.TrialAllowResult, error) {
	args := m.Called(ctx, requestContext, userIds)
	return args.Get(0).([]*pb.TrialAllowResult), args.Error(1)
}

func (m *MockAuthClient) GetTokenInfo(
	ctx context.Context, requestContext *requestcontext.RequestContext, token string, requestor string,
) (*pb.GetTokenInfoResponse, error) {
	args := m.Called(ctx, requestContext, token, requestor)
	return args.Get(0).(*pb.GetTokenInfoResponse), args.Error(1)
}

func (m *MockAuthClient) Close() error {
	args := m.Called()
	return args.Error(0)
}
