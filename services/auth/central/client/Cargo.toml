[package]
name = "auth_central_client"
version = "0.1.0"
edition = "2021"

[dependencies]
async-lock = { workspace = true }
async-trait = { workspace = true }
auth_entities_proto = { path = "../server" }
grpc_client = { path = "../../../lib/grpc/client" }
prost = { workspace = true }
prost-wkt-types = { workspace = true }
request_context = { path = "../../../lib/request_context" }
tonic = { workspace = true }
tracing = { workspace = true }
tracing-tonic = { path = "../../../../base/rust/tracing-tonic" }

[dev-dependencies]
tokio = { workspace = true, features = ["test-util"] }

[build-dependencies]
tonic-build = { workspace = true }
