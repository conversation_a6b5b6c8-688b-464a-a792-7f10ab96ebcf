package main

import (
	"context"
	"sync"
	"time"

	authclient "github.com/augmentcode/augment/services/auth/central/client"
	authproto "github.com/augmentcode/augment/services/auth/central/server/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
)

const (
	// userSubscriptionCacheTTL is the TTL for user subscription info cache (1 hour)
	userSubscriptionCacheTTL = 60 * time.Minute
)

// userSubscriptionCacheEntry represents a cached user subscription info entry
type userSubscriptionCacheEntry struct {
	response  *authproto.GetUserSubscriptionInfoResponse
	expiresAt time.Time
}

// userSubscriptionCache is a simple in-memory cache for user subscription info
type userSubscriptionCache struct {
	lastClearedTime time.Time
	cache           map[string]*userSubscriptionCacheEntry
	mutex           sync.RWMutex
}

// newUserSubscriptionCache creates a new user subscription cache
func newUserSubscriptionCache() *userSubscriptionCache {
	return &userSubscriptionCache{
		lastClearedTime: time.Now(),
		cache:           make(map[string]*userSubscriptionCacheEntry),
	}
}

// GetUserSubscriptionInfo retrieves user subscription info with in-memory caching
func (c *userSubscriptionCache) GetUserSubscriptionInfo(
	ctx context.Context,
	authClient authclient.AuthClient,
	requestContext *requestcontext.RequestContext,
	userId string,
	tenantID string,
) (*authproto.GetUserSubscriptionInfoResponse, error) {
	now := nowFunc()

	// First, try to get from cache with read lock
	c.mutex.RLock()

	// Clear cache entries older than 1 hour (check if needed)
	needsEviction := now.Sub(c.lastClearedTime) > userSubscriptionCacheTTL

	// Try to get from cache first
	if entry, exists := c.cache[userId]; exists && now.Before(entry.expiresAt) && !needsEviction {
		c.mutex.RUnlock()
		return entry.response, nil
	}
	c.mutex.RUnlock()

	// Need to either evict or fetch - acquire write lock
	c.mutex.Lock()
	defer c.mutex.Unlock()
	// Double-check eviction need and perform if necessary
	if now.Sub(c.lastClearedTime) > userSubscriptionCacheTTL {
		for userId, entry := range c.cache {
			if now.After(entry.expiresAt) {
				delete(c.cache, userId)
			}
		}
		c.lastClearedTime = now
	}

	// Double-check cache after eviction
	if entry, exists := c.cache[userId]; exists && now.Before(entry.expiresAt) {
		return entry.response, nil
	}

	log.Ctx(ctx).Info().Str("user_id", userId).Msg("User subscription info cache miss, fetching from auth service")
	resp, err := authClient.GetUserSubscriptionInfo(ctx, requestContext, userId, tenantID)
	if err != nil {
		return nil, err
	}

	c.cache[userId] = &userSubscriptionCacheEntry{
		response:  resp,
		expiresAt: now.Add(userSubscriptionCacheTTL),
	}

	return resp, nil
}
