package main

import (
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	entitiesproto "github.com/augmentcode/augment/services/remote_agents/server/entities_proto"
)

func createTestAgentEntityForHelpers(agentStatus remoteagentsproto.AgentStatus, workspaceStatus remoteagentsproto.WorkspaceStatus) *AgentEntity {
	now := time.Now()
	return &AgentEntity{
		AgentID: "test-agent-id",
		Status: &entitiesproto.AgentStatus{
			Status:                    agentStatus,
			WorkspaceStatus:           workspaceStatus,
			LastUserUpdateReceivedAt:  timestamppb.New(now),
			LastAgentUpdateReceivedAt: timestamppb.New(now),
			LastSshActivityObservedAt: timestamppb.New(now),
			PendingDeletionAt:         timestamppb.New(now),
			WorkspaceUptimeStartedAt:  timestamppb.New(now),
			HasUpdates:                true,
		},
	}
}

func TestAutoPauseConditions_ThresholdCalculations(t *testing.T) {
	now := time.Now()
	conditions := AutoPauseConditions{
		HardTTLMinutes: 60,
		SoftTTLMinutes: 30,
		Now:            now,
	}

	hardThreshold := conditions.HardTTLThreshold()
	softThreshold := conditions.SoftTTLThreshold()

	assert.Equal(t, now.Add(-60*time.Minute), hardThreshold)
	assert.Equal(t, now.Add(-30*time.Minute), softThreshold)
}

func TestAgentEntity_ValidateNotPendingDeletion(t *testing.T) {
	tests := []struct {
		name        string
		agentStatus remoteagentsproto.AgentStatus
		expectError bool
	}{
		{
			name:        "agent not pending deletion - should pass",
			agentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE,
			expectError: false,
		},
		{
			name:        "agent pending deletion - should fail",
			agentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			agent := createTestAgentEntityForHelpers(tt.agentStatus, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING)
			err := agent.ValidateNotPendingDeletion()

			if tt.expectError {
				require.Error(t, err)
				assert.Equal(t, codes.FailedPrecondition, status.Code(err))
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestAgentEntity_ValidateIsPendingDeletion(t *testing.T) {
	tests := []struct {
		name        string
		agentStatus remoteagentsproto.AgentStatus
		expectError bool
	}{
		{
			name:        "agent pending deletion - should pass",
			agentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION,
			expectError: false,
		},
		{
			name:        "agent not pending deletion - should fail",
			agentStatus: remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE,
			expectError: true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			agent := createTestAgentEntityForHelpers(tt.agentStatus, remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING)
			err := agent.ValidateIsPendingDeletion()

			if tt.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestAgentEntity_ValidateWorkspacePaused(t *testing.T) {
	tests := []struct {
		name            string
		workspaceStatus remoteagentsproto.WorkspaceStatus
		expectError     bool
	}{
		{
			name:            "workspace paused - should pass",
			workspaceStatus: remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED,
			expectError:     false,
		},
		{
			name:            "workspace running - should fail",
			workspaceStatus: remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			expectError:     true,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			agent := createTestAgentEntityForHelpers(remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE, tt.workspaceStatus)
			err := agent.ValidateWorkspacePaused()

			if tt.expectError {
				require.Error(t, err)
				assert.Equal(t, codes.FailedPrecondition, status.Code(err))
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestAgentEntity_ValidateCanPause(t *testing.T) {
	tests := []struct {
		name            string
		agentStatus     remoteagentsproto.AgentStatus
		workspaceStatus remoteagentsproto.WorkspaceStatus
		expectError     bool
		errorCount      int
	}{
		{
			name:            "idle agent, running workspace - should pass",
			agentStatus:     remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE,
			workspaceStatus: remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			expectError:     false,
			errorCount:      0,
		},
		{
			name:            "failed agent, pausing workspace - should pass",
			agentStatus:     remoteagentsproto.AgentStatus_AGENT_STATUS_FAILED,
			workspaceStatus: remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING,
			expectError:     false,
			errorCount:      0,
		},
		{
			name:            "starting agent, running workspace - should fail",
			agentStatus:     remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING,
			workspaceStatus: remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			expectError:     true,
			errorCount:      1,
		},
		{
			name:            "idle agent, paused workspace - should fail",
			agentStatus:     remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE,
			workspaceStatus: remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED,
			expectError:     true,
			errorCount:      1,
		},
		{
			name:            "starting agent, paused workspace - should fail with multiple errors",
			agentStatus:     remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING,
			workspaceStatus: remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED,
			expectError:     true,
			errorCount:      2,
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			agent := createTestAgentEntityForHelpers(tt.agentStatus, tt.workspaceStatus)
			err := agent.ValidateCanPause()

			if tt.expectError {
				require.Error(t, err)
				// Check that we have the expected number of errors joined
				errorStr := err.Error()
				if tt.errorCount > 1 {
					// For multiple errors, check that both error messages are present
					assert.Contains(t, errorStr, "Agent is not idle or failed")
					assert.Contains(t, errorStr, "Workspace is not running or pausing")
				}
			} else {
				require.NoError(t, err)
			}
		})
	}
}

func TestAgentEntity_BoolHelpers(t *testing.T) {
	tests := []struct {
		name            string
		agentStatus     remoteagentsproto.AgentStatus
		workspaceStatus remoteagentsproto.WorkspaceStatus
		expectedResults map[string]bool
	}{
		{
			name:            "idle agent, running workspace",
			agentStatus:     remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE,
			workspaceStatus: remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING,
			expectedResults: map[string]bool{
				"IsWorkspaceRunning":          true,
				"IsWorkspacePaused":           false,
				"IsAgentPendingDeletion":      false,
				"IsAgentStarting":             false,
				"IsWorkspaceResuming":         false,
				"IsWorkspacePausing":          false,
				"IsAgentIdleOrFailed":         true,
				"IsWorkspaceRunningOrPausing": true,
			},
		},
		{
			name:            "pending deletion agent, paused workspace",
			agentStatus:     remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION,
			workspaceStatus: remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED,
			expectedResults: map[string]bool{
				"IsWorkspaceRunning":          false,
				"IsWorkspacePaused":           true,
				"IsAgentPendingDeletion":      true,
				"IsAgentStarting":             false,
				"IsWorkspaceResuming":         false,
				"IsWorkspacePausing":          false,
				"IsAgentIdleOrFailed":         false,
				"IsWorkspaceRunningOrPausing": false,
			},
		},
		{
			name:            "failed agent, pausing workspace",
			agentStatus:     remoteagentsproto.AgentStatus_AGENT_STATUS_FAILED,
			workspaceStatus: remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING,
			expectedResults: map[string]bool{
				"IsWorkspaceRunning":          false,
				"IsWorkspacePaused":           false,
				"IsAgentPendingDeletion":      false,
				"IsAgentStarting":             false,
				"IsWorkspaceResuming":         false,
				"IsWorkspacePausing":          true,
				"IsAgentIdleOrFailed":         true,
				"IsWorkspaceRunningOrPausing": true,
			},
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			agent := createTestAgentEntityForHelpers(tt.agentStatus, tt.workspaceStatus)

			assert.Equal(t, tt.expectedResults["IsWorkspaceRunning"], agent.IsWorkspaceRunning())
			assert.Equal(t, tt.expectedResults["IsWorkspacePaused"], agent.IsWorkspacePaused())
			assert.Equal(t, tt.expectedResults["IsAgentPendingDeletion"], agent.IsAgentPendingDeletion())
			assert.Equal(t, tt.expectedResults["IsAgentStarting"], agent.IsAgentStarting())
			assert.Equal(t, tt.expectedResults["IsWorkspaceResuming"], agent.IsWorkspaceResuming())
			assert.Equal(t, tt.expectedResults["IsWorkspacePausing"], agent.IsWorkspacePausing())
			assert.Equal(t, tt.expectedResults["IsAgentIdleOrFailed"], agent.IsAgentIdleOrFailed())
			assert.Equal(t, tt.expectedResults["IsWorkspaceRunningOrPausing"], agent.IsWorkspaceRunningOrPausing())
		})
	}
}
