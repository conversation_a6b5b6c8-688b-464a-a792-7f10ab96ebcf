package main

import (
	"testing"
	"time"

	"github.com/golang/mock/gomock"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"

	authclient "github.com/augmentcode/augment/services/auth/central/client"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	authproto "github.com/augmentcode/augment/services/auth/central/server/proto"
)

// createMockAuthClientForCache creates a mock auth client with the specified subscription type
func createMockAuthClientForCache(subscriptionType string) *authclient.MockAuthClient {
	mockClient := authclient.NewMockAuthClient()

	// Set up the GetUserSubscriptionInfo method based on subscription type
	switch subscriptionType {
	case "suspended":
		mockClient.On("GetUserSubscriptionInfo", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(
			&authproto.GetUserSubscriptionInfoResponse{
				Suspensions: []*authentitiesproto.UserSuspension{
					{
						SuspensionId: "test-suspension-id",
						Evidence:     "test suspension",
					},
				},
			}, nil)
	case "inactive":
		mockClient.On("GetUserSubscriptionInfo", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(
			&authproto.GetUserSubscriptionInfoResponse{
				Subscription: &authproto.GetUserSubscriptionInfoResponse_InactiveSubscription{
					InactiveSubscription: &authproto.InactiveSubscription{},
				},
			}, nil)
	case "trial":
		mockClient.On("GetUserSubscriptionInfo", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(
			&authproto.GetUserSubscriptionInfoResponse{
				Subscription: &authproto.GetUserSubscriptionInfoResponse_Trial{
					Trial: &authproto.Trial{},
				},
			}, nil)
	case "active":
		fallthrough
	default:
		mockClient.On("GetUserSubscriptionInfo", mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return(
			&authproto.GetUserSubscriptionInfoResponse{
				Subscription: &authproto.GetUserSubscriptionInfoResponse_ActiveSubscription{
					ActiveSubscription: &authproto.ActiveSubscription{},
				},
			}, nil)
	}

	return mockClient
}

func TestGetCachedUserSubscriptionInfo(t *testing.T) {
	// Create a mock auth client
	ctrl := gomock.NewController(t)
	defer ctrl.Finish()

	// Setup test context
	testCtx := setupTestParameters()
	defer deleteAllRowsInTable(testCtx.Ctx, testCtx.TenantID, testCtx.RequestCtx)

	// Create a fresh cache for testing
	cache := newUserSubscriptionCache()

	// Create a mock auth client
	mockAuthClient := createMockAuthClientForCache("active")

	t.Run("CacheMiss_FirstCall", func(t *testing.T) {
		// First call should be a cache miss and call the auth service
		resp, err := cache.GetUserSubscriptionInfo(testCtx.Ctx, mockAuthClient, testCtx.RequestCtx, testCtx.UserID, testCtx.TenantID)

		// Should return without error
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.NotNil(t, resp.Subscription)

		// Verify the response is cached
		cache.mutex.RLock()
		entry, exists := cache.cache[testCtx.UserID]
		cache.mutex.RUnlock()

		assert.True(t, exists, "Response should be cached")
		assert.NotNil(t, entry)
		assert.Equal(t, resp, entry.response)
		assert.True(t, time.Now().Before(entry.expiresAt), "Cache entry should not be expired")
	})

	t.Run("CacheHit_SecondCall", func(t *testing.T) {
		// Create a new mock auth client that should NOT be called
		mockAuthClient2 := createMockAuthClientForCache("active")

		// Second call should be a cache hit and NOT call the auth service
		resp, err := cache.GetUserSubscriptionInfo(testCtx.Ctx, mockAuthClient2, testCtx.RequestCtx, testCtx.UserID, testCtx.TenantID)

		// Should return without error
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.NotNil(t, resp.Subscription)
	})

	t.Run("CacheExpiry", func(t *testing.T) {
		// Manually expire the cache entry
		cache.mutex.Lock()
		if entry, exists := cache.cache[testCtx.UserID]; exists {
			entry.expiresAt = time.Now().Add(-1 * time.Minute) // Expire 1 minute ago
		}
		cache.mutex.Unlock()

		// Create a new mock auth client for the expired cache scenario
		mockAuthClient3 := createMockAuthClientForCache("inactive")

		// Call should be a cache miss due to expiry and call the auth service
		resp, err := cache.GetUserSubscriptionInfo(testCtx.Ctx, mockAuthClient3, testCtx.RequestCtx, testCtx.UserID, testCtx.TenantID)

		// Should return without error
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.NotNil(t, resp.Subscription)

		// Verify the cache is updated with new expiry
		cache.mutex.RLock()
		entry, exists := cache.cache[testCtx.UserID]
		cache.mutex.RUnlock()

		assert.True(t, exists, "Response should be cached")
		assert.NotNil(t, entry)
		assert.True(t, time.Now().Before(entry.expiresAt), "Cache entry should have new expiry time")
	})

	t.Run("DifferentUser_SeparateCache", func(t *testing.T) {
		// Test with a different user ID to ensure separate cache entries
		differentUserID := "different-user-id"
		mockAuthClient4 := createMockAuthClientForCache("trial")

		// Call with different user should be a cache miss
		resp, err := cache.GetUserSubscriptionInfo(testCtx.Ctx, mockAuthClient4, testCtx.RequestCtx, differentUserID, testCtx.TenantID)

		// Should return without error
		assert.NoError(t, err)
		assert.NotNil(t, resp)
		assert.NotNil(t, resp.Subscription)

		// Verify both cache entries exist
		cache.mutex.RLock()
		_, originalExists := cache.cache[testCtx.UserID]
		_, differentExists := cache.cache[differentUserID]
		cache.mutex.RUnlock()

		assert.True(t, originalExists, "Original user cache should still exist")
		assert.True(t, differentExists, "Different user cache should exist")
	})

	t.Run("CacheEviction_MockedTime", func(t *testing.T) {
		// Create a fresh cache for this test
		evictionCache := newUserSubscriptionCache()

		// Mock time to control cache eviction behavior
		fixedTime := time.Date(2023, 12, 25, 10, 0, 0, 0, time.UTC)
		originalNowFunc := nowFunc
		nowFunc = func() time.Time { return fixedTime }
		defer func() { nowFunc = originalNowFunc }()

		// Reset lastClearedTime to the mocked time
		evictionCache.lastClearedTime = fixedTime

		// Use a single auth client for simplicity
		mockAuthClient := createMockAuthClientForCache("active")

		// Add first user to cache
		user1ID := "user-1"
		_, err := evictionCache.GetUserSubscriptionInfo(testCtx.Ctx, mockAuthClient, testCtx.RequestCtx, user1ID, testCtx.TenantID)
		assert.NoError(t, err)

		// Advance time by 30 minutes and add second user
		fixedTime = fixedTime.Add(30 * time.Minute)
		nowFunc = func() time.Time { return fixedTime }
		user2ID := "user-2"
		_, err = evictionCache.GetUserSubscriptionInfo(testCtx.Ctx, mockAuthClient, testCtx.RequestCtx, user2ID, testCtx.TenantID)
		assert.NoError(t, err)

		// Verify both entries exist in cache
		evictionCache.mutex.RLock()
		assert.Len(t, evictionCache.cache, 2, "Should have 2 cache entries")
		evictionCache.mutex.RUnlock()

		// Advance time by more than 1 hour since last clear to trigger eviction
		// user1 will be expired (75 minutes old), user2 will not be expired (45 minutes old)
		fixedTime = fixedTime.Add(45 * time.Minute) // Total 75 minutes from start, 75 minutes since last clear
		nowFunc = func() time.Time { return fixedTime }

		// This call should trigger cache eviction since it's been > 1 hour since lastClearedTime
		user3ID := "user-3"
		_, err = evictionCache.GetUserSubscriptionInfo(testCtx.Ctx, mockAuthClient, testCtx.RequestCtx, user3ID, testCtx.TenantID)
		assert.NoError(t, err)

		// Verify cache eviction results
		evictionCache.mutex.RLock()
		_, user1ExistsAfter := evictionCache.cache[user1ID]
		_, user2ExistsAfter := evictionCache.cache[user2ID]
		_, user3ExistsAfter := evictionCache.cache[user3ID]
		evictionCache.mutex.RUnlock()

		// user1 should be evicted (expired), user2 and user3 should remain
		assert.False(t, user1ExistsAfter, "User1 should be evicted from cache (expired)")
		assert.True(t, user2ExistsAfter, "User2 should remain in cache (not expired)")
		assert.True(t, user3ExistsAfter, "User3 should be added to cache")

		// Verify final cache size
		evictionCache.mutex.RLock()
		finalCacheSize := len(evictionCache.cache)
		evictionCache.mutex.RUnlock()
		assert.Equal(t, 2, finalCacheSize, "Cache should have 2 entries after eviction")
	})
}
