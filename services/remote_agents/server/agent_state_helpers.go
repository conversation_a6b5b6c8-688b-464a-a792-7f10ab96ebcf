package main

import (
	"errors"
	"fmt"
	"time"

	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	remoteagentsproto "github.com/augmentcode/augment/services/remote_agents/proto"
	entitiesproto "github.com/augmentcode/augment/services/remote_agents/server/entities_proto"
)

// AgentEntity represents a remote agent entity with its configuration and status
type AgentEntity struct {
	// The original config and status value right after reading from bigtable
	// These non-exported values are used to verify the state transition and
	// can not be modified by the caller.
	configInStorage *entitiesproto.AgentConfig
	statusInStorage *entitiesproto.AgentStatus
	// Transaction predicate to compare against
	statusCell []byte

	Config  *entitiesproto.AgentConfig
	Status  *entitiesproto.AgentStatus
	AgentID string
}

// AutoPauseConditions contains the conditions for auto-pause logic
type AutoPauseConditions struct {
	HardTTLMinutes int
	SoftTTLMinutes int
	Now            time.Time
}

// HardTTLThreshold calculates the hard TTL threshold time
func (c AutoPauseConditions) HardTTLThreshold() time.Time {
	return c.Now.Add(-time.Duration(c.HardTTLMinutes) * time.Minute)
}

// SoftTTLThreshold calculates the soft TTL threshold time
func (c AutoPauseConditions) SoftTTLThreshold() time.Time {
	return c.Now.Add(-time.Duration(c.SoftTTLMinutes) * time.Minute)
}

// ===== AgentEntity Methods =====
// The following methods provide a cleaner API by being directly on AgentEntity

// IsWorkspaceRunning checks if workspace is running
func (a *AgentEntity) IsWorkspaceRunning() bool {
	return a.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING
}

// IsWorkspacePaused checks if workspace is paused
func (a *AgentEntity) IsWorkspacePaused() bool {
	return a.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
}

// IsAgentPendingDeletion checks if agent is pending deletion
func (a *AgentEntity) IsAgentPendingDeletion() bool {
	return a.Status.Status == remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION
}

// IsAgentStarting checks if agent is starting
func (a *AgentEntity) IsAgentStarting() bool {
	return a.Status.Status == remoteagentsproto.AgentStatus_AGENT_STATUS_STARTING
}

// IsWorkspaceResuming checks if workspace is resuming
func (a *AgentEntity) IsWorkspaceResuming() bool {
	return a.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING
}

// IsWorkspacePausing checks if workspace is pausing
func (a *AgentEntity) IsWorkspacePausing() bool {
	return a.Status.WorkspaceStatus == remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
}

// IsAgentIdle checks if agent is idle
func (a *AgentEntity) IsAgentIdle() bool {
	return a.Status.Status == remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
}

// IsAgentFailed checks if agent is failed
func (a *AgentEntity) IsAgentFailed() bool {
	return a.Status.Status == remoteagentsproto.AgentStatus_AGENT_STATUS_FAILED
}

// IsAgentIdleOrFailed checks if agent is idle or failed
func (a *AgentEntity) IsAgentIdleOrFailed() bool {
	return a.IsAgentIdle() || a.IsAgentFailed()
}

// IsWorkspaceRunningOrPausing checks if workspace is running or pausing
func (a *AgentEntity) IsWorkspaceRunningOrPausing() bool {
	return a.IsWorkspaceRunning() || a.IsWorkspacePausing()
}

// ValidateNotPendingDeletion checks if agent is not in pending deletion state
func (a *AgentEntity) ValidateNotPendingDeletion() error {
	if a.IsAgentPendingDeletion() {
		return status.Errorf(codes.FailedPrecondition, "Agent is in pending deletion")
	}
	return nil
}

// ValidateIsPendingDeletion checks if agent is in pending deletion state
func (a *AgentEntity) ValidateIsPendingDeletion() error {
	if !a.IsAgentPendingDeletion() {
		return fmt.Errorf("Agent %s is not in pending deletion status, but in %s",
			a.AgentID, a.Status.Status.String())
	}
	return nil
}

// ValidateWorkspacePaused checks if workspace is in paused state
func (a *AgentEntity) ValidateWorkspacePaused() error {
	if !a.IsWorkspacePaused() {
		return status.Errorf(codes.FailedPrecondition, "Workspace is not paused")
	}
	return nil
}

// ValidateCanPause checks if agent can be paused (idle or failed, workspace running or pausing)
func (a *AgentEntity) ValidateCanPause() error {
	var errs []error

	// Check agent status
	if !a.IsAgentIdleOrFailed() {
		errs = append(errs, status.Errorf(codes.FailedPrecondition, "Agent is not idle or failed"))
	}

	// Check workspace status
	if !a.IsWorkspaceRunningOrPausing() {
		errs = append(errs, status.Errorf(codes.FailedPrecondition, "Workspace is not running or pausing"))
	}

	return errors.Join(errs...)
}

// ShouldAutoPauseHard checks if agent should be auto-paused with hard TTL
func (a *AgentEntity) ShouldAutoPauseHard(conditions AutoPauseConditions) bool {
	hardTTLThreshold := conditions.HardTTLThreshold()

	return a.Status.LastUserUpdateReceivedAt.AsTime().Before(hardTTLThreshold) &&
		a.Status.LastAgentUpdateReceivedAt.AsTime().Before(hardTTLThreshold)
}

// ShouldAutoPauseSoft checks if agent should be auto-paused with soft TTL
func (a *AgentEntity) ShouldAutoPauseSoft(conditions AutoPauseConditions) bool {
	softTTLThreshold := conditions.SoftTTLThreshold()

	return a.Status.LastUserUpdateReceivedAt.AsTime().Before(softTTLThreshold) &&
		a.Status.LastAgentUpdateReceivedAt.AsTime().Before(softTTLThreshold) &&
		(a.Status.LastSshActivityObservedAt == nil ||
			a.Status.LastSshActivityObservedAt.AsTime().Before(softTTLThreshold)) &&
		a.IsAgentIdleOrFailed()
}

// ShouldAutoDelete checks if agent should be auto-deleted
func (a *AgentEntity) ShouldAutoDelete(expiresAt time.Time) bool {
	now := nowFunc()
	return now.After(expiresAt) && a.IsAgentIdle()
}

// ShouldFinalDelete checks if agent should be finally deleted (after pending deletion period)
func (a *AgentEntity) ShouldFinalDelete(deletionPendingHours int, now time.Time) bool {
	return a.IsAgentPendingDeletion() &&
		now.After(a.Status.PendingDeletionAt.AsTime().Add(time.Duration(deletionPendingHours)*time.Hour))
}

// MarkPendingDeletion marks an agent for pending deletion
func (a *AgentEntity) MarkPendingDeletion() {
	now := nowFunc()
	a.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_PENDING_DELETION
	a.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
	a.Status.PendingDeletionAt = timestamppb.New(now.In(time.UTC))
	a.Status.WorkspaceUptimeStartedAt = nil
}

// MarkPaused marks an agent as paused
func (a *AgentEntity) MarkPaused() {
	a.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED
	a.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_IDLE
	a.Status.WorkspaceUptimeStartedAt = nil
}

// MarkPausing marks an agent as pausing
func (a *AgentEntity) MarkPausing() {
	a.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
}

// MarkResuming marks an agent as resuming
func (a *AgentEntity) MarkResuming() {
	now := nowFunc()
	a.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RESUMING
	a.Status.WorkspaceUptimeStartedAt = timestamppb.New(now.In(time.UTC))
}

// MarkRunning marks an agent as running
func (a *AgentEntity) MarkRunning() {
	now := nowFunc()
	a.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_RUNNING
	if a.Status.WorkspaceUptimeStartedAt == nil {
		a.Status.WorkspaceUptimeStartedAt = timestamppb.New(now.In(time.UTC))
	}
}

// MarkFailed marks an agent as failed
func (a *AgentEntity) MarkFailed() {
	a.Status.Status = remoteagentsproto.AgentStatus_AGENT_STATUS_FAILED
}

// MarkDeleting marks an agent as deleting (workspace status)
func (a *AgentEntity) MarkDeleting() {
	a.Status.WorkspaceStatus = remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_DELETING
}

// UpdateLastUserActivity updates the last user activity timestamp
func (a *AgentEntity) UpdateLastUserActivity() {
	now := nowFunc()
	a.Status.LastUserUpdateReceivedAt = timestamppb.New(now.In(time.UTC))
}

// ClearHasUpdates clears the has updates flag
func (a *AgentEntity) ClearHasUpdates() {
	a.Status.HasUpdates = false
}

// IsPublicAgentWorkspaceActive checks if a public agent's workspace is active (not paused or pausing)
func IsPublicAgentWorkspaceActive(agent *remoteagentsproto.Agent) bool {
	return agent.WorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSED &&
		agent.WorkspaceStatus != remoteagentsproto.WorkspaceStatus_WORKSPACE_STATUS_PAUSING
}
