package client

import (
	"context"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/remote_agents/proto"
	"github.com/stretchr/testify/mock"
)

type MockRemoteAgentsClient struct {
	RemoteAgentsClient
	mock.Mock
}

func NewMockRemoteAgentsClient() *MockRemoteAgentsClient {
	return new(MockRemoteAgentsClient)
}

func (c *MockRemoteAgentsClient) CreateAgent(ctx context.Context, req *pb.CreateAgentRequest) (*pb.CreateAgentResponse, error) {
	args := c.Called(ctx, req)
	return args.Get(0).(*pb.CreateAgentResponse), args.Error(1)
}

func (c *MockRemoteAgentsClient) ListAgents(ctx context.Context, req *pb.ListAgentsRequest) (*pb.ListAgentsResponse, error) {
	args := c.Called(ctx, req)
	return args.Get(0).(*pb.ListAgentsResponse), args.Error(1)
}

func (c *MockRemoteAgentsClient) DeleteAgent(ctx context.Context, req *pb.DeleteAgentRequest) (*pb.DeleteAgentResponse, error) {
	args := c.Called(ctx, req)
	return args.Get(0).(*pb.DeleteAgentResponse), args.Error(1)
}

func (c *MockRemoteAgentsClient) InterruptAgent(ctx context.Context, req *pb.InterruptAgentRequest) (*pb.InterruptAgentResponse, error) {
	args := c.Called(ctx, req)
	return args.Get(0).(*pb.InterruptAgentResponse), args.Error(1)
}

func (c *MockRemoteAgentsClient) Chat(ctx context.Context, req *pb.ChatRequest) (*pb.ChatResponse, error) {
	args := c.Called(ctx, req)
	return args.Get(0).(*pb.ChatResponse), args.Error(1)
}

func (c *MockRemoteAgentsClient) ChatHistory(ctx context.Context, req *pb.ChatHistoryRequest) (*pb.ChatHistoryResponse, error) {
	args := c.Called(ctx, req)
	return args.Get(0).(*pb.ChatHistoryResponse), args.Error(1)
}

func (c *MockRemoteAgentsClient) WorkspaceReportStatus(ctx context.Context, req *pb.WorkspaceReportStatusRequest) (*pb.WorkspaceReportStatusResponse, error) {
	args := c.Called(ctx, req)
	return args.Get(0).(*pb.WorkspaceReportStatusResponse), args.Error(1)
}

func (c *MockRemoteAgentsClient) WorkspaceReportChatHistory(ctx context.Context, req *pb.WorkspaceReportChatHistoryRequest) (*pb.WorkspaceReportChatHistoryResponse, error) {
	args := c.Called(ctx, req)
	return args.Get(0).(*pb.WorkspaceReportChatHistoryResponse), args.Error(1)
}

func (c *MockRemoteAgentsClient) WorkspacePollUpdate(ctx context.Context, req *pb.WorkspacePollUpdateRequest) (*pb.WorkspacePollUpdateResponse, error) {
	args := c.Called(ctx, req)
	return args.Get(0).(*pb.WorkspacePollUpdateResponse), args.Error(1)
}

func (c *MockRemoteAgentsClient) DeleteAllAgentsForUser(ctx context.Context, requestContext *requestcontext.RequestContext, userID string) (int32, error) {
	args := c.Called(ctx, requestContext, userID)
	return args.Get(0).(int32), args.Error(1)
}

func (c *MockRemoteAgentsClient) Close() error {
	args := c.Called()
	return args.Error(0)
}
