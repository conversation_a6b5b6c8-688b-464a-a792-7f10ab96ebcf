package main

import (
	"context"
	"encoding/binary"
	"fmt"
	"strings"
	"time"

	googlepb "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	pb "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	"github.com/rs/zerolog/log"
)

const (
	stateFamilyName            = "State"
	timestampColumnQualifier   = "Timestamp"
	signatureColumnQualifier   = "Signature"
	blobCountColumnQualifier   = "BlobCount"
	lastRenewedColumnQualifier = "LastRenewedTime"
)

type BigtableHelper interface {
	DeleteActiveCheckpoint(tenantID string, checkpointID string) error
	GetAllActiveCheckpoints(tenantID string) ([]struct {
		CheckpointID    string
		Timestamp       time.Time
		LastRenewedTime time.Time
	}, error)
	WriteActiveCheckpoint(tenantID string, checkpointID string, timestamp time.Time, lastRenewedTime time.Time) error

	DeletePendingIndex(tenantID string, checkpointID string, transformationKey string) error
	GetAllPendingIndexes(tenantID string) ([]struct {
		CheckpointID      string
		TransformationKey string
		Timestamp         time.Time
	}, error)
	WritePendingIndex(tenantID string, checkpointID string, transformationKey string, timestamp time.Time) error

	DeleteActiveTransformationKey(tenantID string, transformationKey string) error
	GetAllActiveTransformationKeys(tenantID string) ([]struct {
		TransformationKey string
		Timestamp         time.Time
	}, error)
	WriteActiveTransformationKey(tenantID string, transformationKey string, timestamp time.Time) error

	WriteActiveIndex(versionID string, tenantID string, indexID string, transformationKey string, timestamp time.Time, signature []int, blobCount int) error
	DeleteActiveIndex(versionID string, tenantID string, indexID string, transformationKey string) error
	GetAllActiveIndexes(versionID string, tenantID string) ([]struct {
		CheckpointID      string
		TransformationKey string
		Timestamp         time.Time
		Signature         []int
		BlobCount         int
	}, error)
}

// PersistToBigtableFn is a function wrapper that returns whether to persist to Bigtable
type PersistToBigtableFn func() bool

type bigtableHelper struct {
	ctx                  context.Context
	client               bigtableproxy.BigtableProxyClient
	requestContextHelper RequestContextHelper
	persistToBigtable    PersistToBigtableFn
}

func NewBigtableHelper(ctx context.Context, requestContextHelper RequestContextHelper, client bigtableproxy.BigtableProxyClient, persistToBigtable PersistToBigtableFn) BigtableHelper {
	log.Info().Msgf("Configured with PersistToBigtable: %v", persistToBigtable())

	return &bigtableHelper{
		ctx:                  ctx,
		client:               client,
		requestContextHelper: requestContextHelper,
		persistToBigtable:    persistToBigtable,
	}
}

// makeTimestampSetCellMutation creates a SetCell mutation for a timestamp value
func makeTimestampSetCellMutation(timestamp time.Time) *googlepb.Mutation {
	timestampBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(timestampBytes, uint64(timestamp.UnixNano()))

	return &googlepb.Mutation{
		Mutation: &googlepb.Mutation_SetCell_{
			SetCell: &googlepb.Mutation_SetCell{
				FamilyName:      stateFamilyName,
				ColumnQualifier: []byte(timestampColumnQualifier),
				TimestampMicros: -1, // Use server timestamp
				Value:           timestampBytes,
			},
		},
	}
}

func makeLastRenewedTimeSetCellMutation(timestamp time.Time) *googlepb.Mutation {
	timestampBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(timestampBytes, uint64(timestamp.UnixNano()))

	return &googlepb.Mutation{
		Mutation: &googlepb.Mutation_SetCell_{
			SetCell: &googlepb.Mutation_SetCell{
				FamilyName:      stateFamilyName,
				ColumnQualifier: []byte(lastRenewedColumnQualifier),
				TimestampMicros: -1, // Use server timestamp
				Value:           timestampBytes,
			},
		},
	}
}

// makeSignatureSetCellMutation creates a SetCell mutation for a signature value
func makeSignatureSetCellMutation(signature []int) *googlepb.Mutation {
	signatureBytes := make([]byte, 8*len(signature))
	for i, v := range signature {
		binary.BigEndian.PutUint64(signatureBytes[i*8:], uint64(v))
	}

	return &googlepb.Mutation{
		Mutation: &googlepb.Mutation_SetCell_{
			SetCell: &googlepb.Mutation_SetCell{
				FamilyName:      stateFamilyName,
				ColumnQualifier: []byte(signatureColumnQualifier),
				TimestampMicros: -1, // Use server timestamp
				Value:           signatureBytes,
			},
		},
	}
}

// makeBlobCountSetCellMutation creates a SetCell mutation for a blob count value
func makeBlobCountSetCellMutation(blobCount int) *googlepb.Mutation {
	blobCountBytes := make([]byte, 8)
	binary.BigEndian.PutUint64(blobCountBytes, uint64(blobCount))

	return &googlepb.Mutation{
		Mutation: &googlepb.Mutation_SetCell_{
			SetCell: &googlepb.Mutation_SetCell{
				FamilyName:      stateFamilyName,
				ColumnQualifier: []byte(blobCountColumnQualifier),
				TimestampMicros: -1, // Use server timestamp
				Value:           blobCountBytes,
			},
		},
	}
}

// makeDeleteRowMutation creates a DeleteFromRow mutation
func makeDeleteRowMutation() *googlepb.Mutation {
	return &googlepb.Mutation{
		Mutation: &googlepb.Mutation_DeleteFromRow_{
			DeleteFromRow: &googlepb.Mutation_DeleteFromRow{},
		},
	}
}

// makeMutateRowsEntry creates a MutateRowsRequest_Entry with the given row key and mutation
func makeMutateRowsEntry(rowKey string, mutations ...*googlepb.Mutation) *googlepb.MutateRowsRequest_Entry {
	return &googlepb.MutateRowsRequest_Entry{
		RowKey:    []byte(rowKey),
		Mutations: mutations,
	}
}

// performMutation performs a mutation operation on the bigtable
func (h *bigtableHelper) performMutation(tenantID string, entry *googlepb.MutateRowsRequest_Entry, operationDesc string) error {
	requestContext, err := h.requestContextHelper.GetBackgroundRequestContext(tenantID)
	if err != nil {
		log.Error().Msgf("Failed to get request context: %v", err)
		return err
	}

	_, err = h.client.MutateRows(
		h.ctx,
		tenantID,
		pb.TableName_WORKING_SET,
		[]*googlepb.MutateRowsRequest_Entry{entry},
		requestContext,
	)
	if err != nil {
		return fmt.Errorf("failed to %s: %w", operationDesc, err)
	}

	return nil
}

// readRowsWithPrefix reads all rows with the given prefix from the bigtable
func (h *bigtableHelper) readRowsWithPrefix(tenantID string, prefix string, operationDesc string) ([]*bigtableproxy.Row, error) {
	requestContext, err := h.requestContextHelper.GetBackgroundRequestContext(tenantID)
	if err != nil {
		log.Error().Msgf("Failed to get request context: %v", err)
		return nil, err
	}

	// Create a RowSet that matches all rows with the given prefix
	prefixBytes := []byte(prefix)
	endBytes := append([]byte(prefix), 0xFF)
	rowRange := &googlepb.RowRange{
		StartKey: &googlepb.RowRange_StartKeyClosed{StartKeyClosed: prefixBytes},
		EndKey:   &googlepb.RowRange_EndKeyClosed{EndKeyClosed: endBytes},
	}
	rowSet := &googlepb.RowSet{
		RowRanges: []*googlepb.RowRange{rowRange},
	}

	rows, err := h.client.ReadRows(
		h.ctx,
		tenantID,
		pb.TableName_WORKING_SET,
		rowSet,
		&googlepb.RowFilter{
			Filter: &googlepb.RowFilter_CellsPerColumnLimitFilter{
				CellsPerColumnLimitFilter: 1,
			},
		},
		0, // No row limit
		requestContext,
	)
	if err != nil {
		return nil, fmt.Errorf("failed to %s: %w", operationDesc, err)
	}

	return rows, nil
}

// getActiveCheckpointRowKey returns the row key for an active checkpoint.
func getActiveCheckpointRowKey(checkpointID string) string {
	return fmt.Sprintf("activeCheckpoint#%s", checkpointID)
}

// WriteActiveCheckpoint writes an active checkpoint row (either creates it or updates it)
func (h *bigtableHelper) WriteActiveCheckpoint(tenantID string, checkpointID string, timestamp time.Time, lastRenewedTime time.Time) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getActiveCheckpointRowKey(checkpointID)
	timestampMutation := makeTimestampSetCellMutation(timestamp)
	lastRenewedTimeMutation := makeLastRenewedTimeSetCellMutation(lastRenewedTime)
	entries := makeMutateRowsEntry(rowKey, timestampMutation, lastRenewedTimeMutation)
	return h.performMutation(tenantID, entries, "write active checkpoint")
}

// DeleteActiveCheckpoint deletes an active checkpoint row
func (h *bigtableHelper) DeleteActiveCheckpoint(tenantID string, checkpointID string) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getActiveCheckpointRowKey(checkpointID)
	mutation := makeDeleteRowMutation()
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "delete active checkpoint")
}

// getPendingIndexRowKey returns the row key for a pending index.
func getPendingIndexRowKey(checkpointID string, transformationKey string) string {
	return fmt.Sprintf("pendingIndex#%s#%s", checkpointID, transformationKey)
}

// WritePendingIndex writes a pending index record for a tenant.
func (h *bigtableHelper) WritePendingIndex(tenantID string, checkpointID string, transformationKey string, timestamp time.Time) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getPendingIndexRowKey(checkpointID, transformationKey)
	mutation := makeTimestampSetCellMutation(timestamp)
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "write pending index")
}

// DeletePendingIndex deletes a pending index record for a tenant.
func (h *bigtableHelper) DeletePendingIndex(tenantID string, checkpointID string, transformationKey string) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getPendingIndexRowKey(checkpointID, transformationKey)
	mutation := makeDeleteRowMutation()
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "delete pending index")
}

// getActiveTransformationKeyRowKey returns the row key for an active transformation key.
func getActiveTransformationKeyRowKey(transformationKey string) string {
	return fmt.Sprintf("activeTransformationKey#%s", transformationKey)
}

// WriteActiveTransformationKey writes an active transformation key row (either creates it or updates it)
func (h *bigtableHelper) WriteActiveTransformationKey(tenantID string, transformationKey string, timestamp time.Time) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getActiveTransformationKeyRowKey(transformationKey)
	mutation := makeTimestampSetCellMutation(timestamp)
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "write active transformation key")
}

// DeleteActiveTransformationKey deletes an active transformation key row
func (h *bigtableHelper) DeleteActiveTransformationKey(tenantID string, transformationKey string) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getActiveTransformationKeyRowKey(transformationKey)
	mutation := makeDeleteRowMutation()
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "delete active transformation key")
}

// getTimestampFromCell extracts a timestamp from a cell
func getTimestampFromCell(row *bigtableproxy.Row, entityDesc string) (time.Time, error) {
	cell, err := findCell(row, stateFamilyName, timestampColumnQualifier, entityDesc)
	if err != nil {
		return time.Time{}, err
	}

	timestampNano := binary.BigEndian.Uint64(cell.Value)
	return time.Unix(0, int64(timestampNano)), nil
}

// getLastRenewedTimeFromCell extracts the last renewed time from a cell
func getLastRenewedTimeFromCell(row *bigtableproxy.Row, entityDesc string) (time.Time, error) {
	cell, err := findCell(row, stateFamilyName, lastRenewedColumnQualifier, entityDesc)
	if err != nil {
		return time.Time{}, err
	}

	timestampNano := binary.BigEndian.Uint64(cell.Value)
	return time.Unix(0, int64(timestampNano)), nil
}

// getSignatureFromCell extracts a signature from a cell
func getSignatureFromCell(row *bigtableproxy.Row, entityDesc string) ([]int, error) {
	cell, err := findCell(row, stateFamilyName, signatureColumnQualifier, entityDesc)
	if err != nil {
		return nil, err
	}

	signature := make([]int, len(cell.Value)/8)
	for i := range signature {
		signature[i] = int(binary.BigEndian.Uint64(cell.Value[i*8:]))
	}

	return signature, nil
}

// getBlobCountFromCell extracts a blob count from a cell
func getBlobCountFromCell(row *bigtableproxy.Row, entityDesc string) (int, error) {
	cell, err := findCell(row, stateFamilyName, blobCountColumnQualifier, entityDesc)
	if err != nil {
		return -1, err
	}

	return int(binary.BigEndian.Uint64(cell.Value)), nil
}

// findCell finds a cell in a row by family name and qualifier
func findCell(row *bigtableproxy.Row, familyName string, qualifier string, entityDesc string) (*bigtableproxy.RowCell, error) {
	if len(row.Cells) < 1 {
		return nil, fmt.Errorf("expected at least one cell for %s, got %d", entityDesc, len(row.Cells))
	}
	// The result will have multiple cells but we only care about the latest one. Log a warning if we end up
	// having too many cells in the result - we can implement a filter to only get the top result, but this
	// will require fixing the fake bigtable proxy client to support that filter.
	if len(row.Cells) > 1000 {
		log.Warn().Msgf("getTimestampFromCell: got more than 1000 cells for %s, got %d", entityDesc, len(row.Cells))
	}

	for _, cell := range row.Cells {
		if cell.FamilyName == familyName && string(cell.Qualifier) == qualifier {
			return cell, nil
		}
	}
	return nil, fmt.Errorf("cell not found for %s/%s", familyName, qualifier)
}

// GetAllActiveCheckpoints retrieves all active checkpoints for a tenant.
func (h *bigtableHelper) GetAllActiveCheckpoints(tenantID string) ([]struct {
	CheckpointID    string
	Timestamp       time.Time
	LastRenewedTime time.Time
}, error,
) {
	prefix := "activeCheckpoint#"
	rows, err := h.readRowsWithPrefix(tenantID, prefix, "read active checkpoints")
	if err != nil {
		return nil, err
	}

	var results []struct {
		CheckpointID    string
		Timestamp       time.Time
		LastRenewedTime time.Time
	}

	for _, row := range rows {
		rowKey := string(row.RowKey)
		checkpointID := rowKey[len(prefix):]

		timestamp, err := getTimestampFromCell(row, fmt.Sprintf("active checkpoint %s", checkpointID))
		if err != nil {
			return nil, err
		}

		lastRenewedTime, err := getLastRenewedTimeFromCell(row, fmt.Sprintf("active checkpoint %s", checkpointID))
		if err != nil {
			// Needed to support backward compatibility
			log.Warn().Err(err).Msgf("Failed to get last renewed time for active checkpoint %s", checkpointID)
		}
		results = append(results, struct {
			CheckpointID    string
			Timestamp       time.Time
			LastRenewedTime time.Time
		}{
			CheckpointID:    checkpointID,
			Timestamp:       timestamp,
			LastRenewedTime: lastRenewedTime,
		})
	}

	return results, nil
}

// GetAllPendingIndexes retrieves all pending indexes for a tenant.
func (h *bigtableHelper) GetAllPendingIndexes(tenantID string) ([]struct {
	CheckpointID      string
	TransformationKey string
	Timestamp         time.Time
}, error,
) {
	prefix := "pendingIndex#"
	rows, err := h.readRowsWithPrefix(tenantID, prefix, "read pending indexes")
	if err != nil {
		return nil, err
	}

	var results []struct {
		CheckpointID      string
		TransformationKey string
		Timestamp         time.Time
	}

	for _, row := range rows {
		rowKey := string(row.RowKey)
		// Extract checkpointID and transformationKey from the row key
		// Format: pendingIndex#<checkpointID>#<transformationKey>
		parts := strings.Split(rowKey[len(prefix):], "#")
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid row key format for pending index: %s", rowKey)
		}

		timestamp, err := getTimestampFromCell(row, fmt.Sprintf("pending index %s#%s", parts[0], parts[1]))
		if err != nil {
			return nil, err
		}

		results = append(results, struct {
			CheckpointID      string
			TransformationKey string
			Timestamp         time.Time
		}{
			CheckpointID:      parts[0],
			TransformationKey: parts[1],
			Timestamp:         timestamp,
		})
	}

	return results, nil
}

// GetAllActiveTransformationKeys retrieves all active transformation keys for a tenant.
func (h *bigtableHelper) GetAllActiveTransformationKeys(tenantID string) ([]struct {
	TransformationKey string
	Timestamp         time.Time
}, error,
) {
	prefix := "activeTransformationKey#"
	rows, err := h.readRowsWithPrefix(tenantID, prefix, "read active transformation keys")
	if err != nil {
		return nil, err
	}

	var results []struct {
		TransformationKey string
		Timestamp         time.Time
	}

	for _, row := range rows {
		rowKey := string(row.RowKey)
		transformationKey := rowKey[len(prefix):]

		timestamp, err := getTimestampFromCell(row, fmt.Sprintf("active transformation key %s", transformationKey))
		if err != nil {
			return nil, err
		}

		results = append(results, struct {
			TransformationKey string
			Timestamp         time.Time
		}{
			TransformationKey: transformationKey,
			Timestamp:         timestamp,
		})
	}

	return results, nil
}

func getActiveIndexRowKey(version string, checkpointID string, transformationKey string) string {
	return fmt.Sprintf("activeIndex#%s#%s#%s", version, checkpointID, transformationKey)
}

func (h *bigtableHelper) WriteActiveIndex(versionID string, tenantID string, indexID string, transformationKey string, timestamp time.Time, signature []int, blobCount int) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getActiveIndexRowKey(versionID, indexID, transformationKey)
	timestampMutation := makeTimestampSetCellMutation(timestamp)
	signatureMutation := makeSignatureSetCellMutation(signature)
	blobCountMutation := makeBlobCountSetCellMutation(blobCount)
	entry := makeMutateRowsEntry(rowKey, timestampMutation, signatureMutation, blobCountMutation)
	return h.performMutation(tenantID, entry, "write active index")
}

func (h *bigtableHelper) DeleteActiveIndex(versionID string, tenantID string, indexID string, transformationKey string) error {
	if !h.persistToBigtable() {
		return nil
	}
	rowKey := getActiveIndexRowKey(versionID, indexID, transformationKey)
	mutation := makeDeleteRowMutation()
	entry := makeMutateRowsEntry(rowKey, mutation)
	return h.performMutation(tenantID, entry, "delete active index")
}

func (h *bigtableHelper) GetAllActiveIndexes(versionID string, tenantID string) ([]struct {
	CheckpointID      string
	TransformationKey string
	Timestamp         time.Time
	Signature         []int
	BlobCount         int
}, error,
) {
	prefix := fmt.Sprintf("activeIndex#%s#", versionID)
	rows, err := h.readRowsWithPrefix(tenantID, prefix, "read active indexes")
	if err != nil {
		return nil, err
	}

	var results []struct {
		CheckpointID      string
		TransformationKey string
		Timestamp         time.Time
		Signature         []int
		BlobCount         int
	}

	for _, row := range rows {
		rowKey := string(row.RowKey)
		// Extract checkpointID and transformationKey from the row key
		// Format: activeIndex#<versionID>#<checkpointID>#<transformationKey>
		remaining := rowKey[len(prefix):]
		parts := strings.Split(remaining, "#")
		if len(parts) != 2 {
			return nil, fmt.Errorf("invalid row key format for active index: %s", rowKey)
		}

		timestamp, err := getTimestampFromCell(row, fmt.Sprintf("active index %s#%s", parts[0], parts[1]))
		if err != nil {
			return nil, err
		}

		signature, err := getSignatureFromCell(row, fmt.Sprintf("active index %s#%s", parts[0], parts[1]))
		if err != nil {
			return nil, err
		}

		blobCount, err := getBlobCountFromCell(row, fmt.Sprintf("active index %s#%s", parts[0], parts[1]))
		if err != nil {
			// Needed to support backward compatibility
			log.Warn().Err(err).Msgf("Failed to get blob count for active index %s#%s", parts[0], parts[1])
			continue
		}

		results = append(results, struct {
			CheckpointID      string
			TransformationKey string
			Timestamp         time.Time
			Signature         []int
			BlobCount         int
		}{
			CheckpointID:      parts[0],
			TransformationKey: parts[1],
			Timestamp:         timestamp,
			Signature:         signature,
			BlobCount:         blobCount,
		})
	}

	return results, nil
}
