syntax = "proto3";

package token_exchange.test;

import "services/token_exchange/auth_options.proto";
import "services/token_exchange/token_scopes.proto";

option go_package = "github.com/augmentcode/augment/services/token_exchange/test";

// Test service demonstrating the use of authorization annotations
service TestAuthService {
  // Simple read operation requiring only AUTH_R scope
  rpc GetTestData(GetTestDataRequest) returns (GetTestDataResponse) {
    option (auth_options.required_token_scopes) = AUTH_R;
  }

  // Write operation requiring AUTH_RW scope
  rpc UpdateTestData(UpdateTestDataRequest) returns (UpdateTestDataResponse) {
    option (auth_options.required_token_scopes) = AUTH_RW;
  }

  // Complex operation requiring multiple scopes
  rpc GetUserContent(GetUserContentRequest) returns (GetUserContentResponse) {
    option (auth_options.required_token_scopes) = AUTH_R;
    option (auth_options.required_token_scopes) = CONTENT_R;
  }

  // Admin operation with additional metadata
  rpc DeleteTestUser(DeleteTestUserRequest) returns (DeleteTestUserResponse) {
    option (auth_options.required_token_scopes) = AUTH_RW;
    option (auth_options.required_token_scopes) = PII_ADMIN;
    option (auth_options.authorization_metadata) = {requires_iap: true};
  }

  // Operation with custom requirements
  rpc SpecialOperation(SpecialOperationRequest) returns (SpecialOperationResponse) {
    option (auth_options.required_token_scopes) = CONTENT_ADMIN;
  }

  // Method without any authorization requirements (for testing)
  rpc PublicMethod(PublicMethodRequest) returns (PublicMethodResponse) {
    // No authorization options specified
  }
}

// Request/Response messages for testing
message GetTestDataRequest {
  string test_id = 1;
}

message GetTestDataResponse {
  string data = 1;
}

message UpdateTestDataRequest {
  string test_id = 1;
  string data = 2;
}

message UpdateTestDataResponse {
  bool success = 1;
}

message GetUserContentRequest {
  string user_id = 1;
}

message GetUserContentResponse {
  repeated string content_items = 1;
}

message DeleteTestUserRequest {
  string user_id = 1;
}

message DeleteTestUserResponse {
  bool deleted = 1;
}

message SpecialOperationRequest {
  string operation_id = 1;
  string parameters = 2;
}

message SpecialOperationResponse {
  string result = 1;
}

message PublicMethodRequest {
  string message = 1;
}

message PublicMethodResponse {
  string response = 1;
}
