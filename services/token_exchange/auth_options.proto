syntax = "proto3";

package auth_options;

import "google/protobuf/descriptor.proto";
import "services/token_exchange/token_scopes.proto";

option go_package = "github.com/augmentcode/augment/services/token_exchange/auth_options_proto";

// Custom protobuf options for documenting authorization requirements in gRPC methods.
// These options enable declarative specification of required token scopes and other
// authorization metadata directly in proto definitions.

extend google.protobuf.MethodOptions {
  // Specifies the token scopes required to call this gRPC method.
  // Multiple scopes can be specified, and all are typically required unless
  // otherwise documented in the service.
  //
  // Example usage:
  //   rpc GetUser(GetUserRequest) returns (GetUserResponse) {
  //     option (required_token_scopes) = ["AUTH_R", "CONTENT_R"];
  //   }
  //
  // The scope values should correspond to the Scope enum values defined in
  // token_scopes.proto (e.g., "AUTH_R", "CONTENT_RW", etc.).
  repeated token_scopes.Scope required_token_scopes = 50001;

  // Additional authorization metadata that may be needed for future extensibility.
  // This allows for specifying other authorization requirements beyond token scopes.
  AuthorizationMetadata authorization_metadata = 50002;
}

// Container for additional authorization metadata that may be needed
// beyond just token scopes. This provides extensibility for future
// authorization requirements.
message AuthorizationMetadata {
  // requires IAP authentication
  bool requires_iap = 1;
}
