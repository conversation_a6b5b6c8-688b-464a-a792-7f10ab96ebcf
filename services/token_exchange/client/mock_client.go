package token_exchange_client

import (
	"context"
	"fmt"
	"os"
	"time"

	"github.com/augmentcode/augment/base/go/secretstring"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	scopepb "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/stretchr/testify/mock"
)

// FakeTokenExchangeClient provides a fake implementation of the TokenExchangeClient interface for testing.
// It reads tokens and verification keys from files instead of making actual gRPC calls.
type FakeTokenExchangeClient struct {
	SignedTokenPath         string
	PublicJwksPath          string
	GetVerificationKeyCount int
}

// NewFakeTokenExchangeClient creates a new fake token exchange client.
//
// Parameters:
//   - signedTokenPath: Path to a file containing a signed JWT token to return for user requests.
//   - publicJwksPath: Path to a file containing the JWKS (JSON Web Key Set) to return for verification key requests.
//
// Returns:
//   - A FakeTokenExchangeClient instance.
func NewFakeTokenExchangeClient(
	signedTokenPath string, publicJwksPath string,
) *FakeTokenExchangeClient {
	return &FakeTokenExchangeClient{
		SignedTokenPath:         signedTokenPath,
		PublicJwksPath:          publicJwksPath,
		GetVerificationKeyCount: 0,
	}
}

// GetSignedTokenForUser returns a signed token read from the SignedTokenPath file.
// This fake implementation ignores all parameters except the context.
func (c *FakeTokenExchangeClient) GetSignedTokenForUser(
	ctx context.Context, userID string, opaqueUserID *authentitiesproto.UserId,
	userEmail *string, tenantID string, namespace *string,
	additionalClaims map[string]any,
) (secretstring.SecretString, error) {
	data, err := os.ReadFile(c.SignedTokenPath)
	if err != nil {
		return secretstring.SecretString{}, err
	}
	return secretstring.New(string(data)), nil
}

func (c *FakeTokenExchangeClient) GetSignedTokenForUserWithScopes(
	ctx context.Context, userID string, opaqueUserID *authentitiesproto.UserId,
	userEmail *string, tenantID string, namespace *string,
	additionalClaims map[string]any, scopes []scopepb.Scope,
) (secretstring.SecretString, error) {
	data, err := os.ReadFile(c.SignedTokenPath)
	if err != nil {
		return secretstring.SecretString{}, err
	}
	return secretstring.New(string(data)), nil
}

// GetSignedTokenForService returns a signed token read from the SignedTokenPath file.
// This fake implementation ignores all parameters except the context.
func (c *FakeTokenExchangeClient) GetSignedTokenForService(
	ctx context.Context, tenantID string, scopes []scopepb.Scope,
) (secretstring.SecretString, error) {
	data, err := os.ReadFile(c.SignedTokenPath)
	if err != nil {
		return secretstring.SecretString{}, err
	}
	return secretstring.New(string(data)), nil
}

// GetSignedTokenForServiceWithNamespace returns a signed token read from the SignedTokenPath file.
// This fake implementation ignores all parameters except the context.
func (c *FakeTokenExchangeClient) GetSignedTokenForServiceWithNamespace(
	ctx context.Context, tenantID string, shardNamespace string, scopes []scopepb.Scope,
) (secretstring.SecretString, error) {
	data, err := os.ReadFile(c.SignedTokenPath)
	if err != nil {
		return secretstring.SecretString{}, err
	}
	return secretstring.New(string(data)), nil
}

// GetSignedTokenForIAPToken returns an error as it is not implemented in the fake.
func (c *FakeTokenExchangeClient) GetSignedTokenForIAPToken(
	ctx context.Context, iapToken secretstring.SecretString, tenantID string, scopes []scopepb.Scope,
	expiration time.Duration,
) (secretstring.SecretString, error) {
	return secretstring.SecretString{}, fmt.Errorf("not implemented")
}

// GetVerificationKey returns the JWKS read from the PublicJwksPath file.
// It also increments the GetVerificationKeyCount counter for testing purposes.
func (c *FakeTokenExchangeClient) GetVerificationKey(ctx context.Context) ([]byte, error) {
	c.GetVerificationKeyCount += 1
	return os.ReadFile(c.PublicJwksPath)
}

// Close is a no-op for the fake client as there are no resources to release.
func (c *FakeTokenExchangeClient) Close() {}

// MockTokenExchangeClient is a mock implementation of the TokenExchangeClient
// interface for testing, that can be mocked by clients.
type MockTokenExchangeClient struct {
	TokenExchangeClient
	mock.Mock
}

func NewMockTokenExchangeClient() *MockTokenExchangeClient {
	return &MockTokenExchangeClient{}
}

func (m *MockTokenExchangeClient) AlwaysReturnSignedToken(token string) {
	m.On("GetSignedTokenForUser", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(token, nil)
	m.On("GetSignedTokenForService", mock.Anything, mock.Anything, mock.Anything).
		Return(token, nil)
	m.On("GetSignedTokenForServiceWithNamespace", mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(token, nil)
	m.On("GetSignedTokenForIAPToken", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(token, nil)
	m.On("Close").Return()
}

func (m *MockTokenExchangeClient) GetSignedTokenForUser(
	ctx context.Context, userID string, opaqueUserID *authentitiesproto.UserId,
	userEmail *string, tenantID string, namespace *string,
	additionalClaims map[string]any,
) (secretstring.SecretString, error) {
	args := m.Called(ctx, userID, opaqueUserID, userEmail, tenantID, namespace, additionalClaims)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetSignedTokenForService(
	ctx context.Context, tenantID string, scopes []scopepb.Scope,
) (secretstring.SecretString, error) {
	args := m.Called(ctx, tenantID, scopes)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetSignedTokenForServiceWithNamespace(
	ctx context.Context, tenantID string, shardNamespace string, scopes []scopepb.Scope,
) (secretstring.SecretString, error) {
	args := m.Called(ctx, tenantID, shardNamespace, scopes)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetSignedTokenForIAPToken(
	ctx context.Context, iapToken secretstring.SecretString, tenantID string, scopes []scopepb.Scope,
	expiration time.Duration,
) (secretstring.SecretString, error) {
	args := m.Called(ctx, iapToken, tenantID, scopes, expiration)
	return secretstring.New(args.String(0)), args.Error(1)
}

func (m *MockTokenExchangeClient) GetVerificationKey(ctx context.Context) ([]byte, error) {
	args := m.Called(ctx)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockTokenExchangeClient) Close() {
	m.Called()
}
