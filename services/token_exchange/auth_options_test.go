package token_exchange

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/encoding/protowire"
	"google.golang.org/protobuf/proto"
	"google.golang.org/protobuf/reflect/protoreflect"

	auth_options "github.com/augmentcode/augment/services/token_exchange/auth_options_proto"
	test_proto "github.com/augmentcode/augment/services/token_exchange/test"
	scopedpb "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
)

// Test that the auth options proto compiles and can be used
func TestAuthOptionsCompilation(t *testing.T) {
	// Test that the extension field number is correctly defined
	// This ensures our custom option doesn't conflict with other extensions
	assert.Equal(t, protowire.Number(50001), auth_options.E_RequiredTokenScopes.TypeDescriptor().Number())
}

// Test that the extension can be referenced (basic compilation test)
func TestExtensionReference(t *testing.T) {
	// This test ensures the extension is properly generated and accessible
	ext := auth_options.E_RequiredTokenScopes
	assert.NotNil(t, ext)
	assert.Equal(t, protoreflect.Name("required_token_scopes"), ext.TypeDescriptor().Name())
}

// Helper function to extract required token scopes from a method descriptor
func getRequiredTokenScopes(method protoreflect.MethodDescriptor) []scopedpb.Scope {
	opts := method.Options()
	if !proto.HasExtension(opts, auth_options.E_RequiredTokenScopes) {
		return nil
	}

	scopes := proto.GetExtension(opts, auth_options.E_RequiredTokenScopes).([]scopedpb.Scope)
	return scopes
}

// Test that uses the actual test proto to verify annotations work
func TestAuthOptionsWithTestProto(t *testing.T) {
	// Get the test service descriptor from the generated proto
	service := test_proto.File_services_token_exchange_auth_options_test_proto.Services().ByName("TestAuthService")
	require.NotNil(t, service, "TestAuthService not found")

	tests := []struct {
		methodName     string
		expectedScopes []scopedpb.Scope
	}{
		{
			methodName:     "GetTestData",
			expectedScopes: []scopedpb.Scope{scopedpb.Scope_AUTH_R},
		},
		{
			methodName:     "UpdateTestData",
			expectedScopes: []scopedpb.Scope{scopedpb.Scope_AUTH_RW},
		},
		{
			methodName:     "GetUserContent",
			expectedScopes: []scopedpb.Scope{scopedpb.Scope_AUTH_R, scopedpb.Scope_CONTENT_R},
		},
		{
			methodName:     "DeleteTestUser",
			expectedScopes: []scopedpb.Scope{scopedpb.Scope_AUTH_RW, scopedpb.Scope_PII_ADMIN},
		},
		{
			methodName:     "SpecialOperation",
			expectedScopes: []scopedpb.Scope{scopedpb.Scope_CONTENT_ADMIN},
		},
		{
			methodName:     "PublicMethod",
			expectedScopes: nil, // No scopes required
		},
	}

	for _, tt := range tests {
		t.Run(tt.methodName, func(t *testing.T) {
			// Find the method descriptor
			method := service.Methods().ByName(protoreflect.Name(tt.methodName))
			require.NotNil(t, method, "Method %s not found", tt.methodName)

			// Test required token scopes
			actualScopes := getRequiredTokenScopes(method)
			assert.Equal(t, tt.expectedScopes, actualScopes, "Token scopes mismatch for %s", tt.methodName)
		})
	}
}
