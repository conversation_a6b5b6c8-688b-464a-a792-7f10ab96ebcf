syntax = "proto3";
package token_scopes;

option go_package = "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto";

// scope defines the permissions that a token can have.
// usually the ultimately map to bigtable tables, but they also impact accesses to any
// in-memory caches.
enum Scope {
  // read access to the auth data, e.g. user accountsdata
  AUTH_R = 0;

  // read/write access to the user data and tenant management
  AUTH_RW = 1;

  // read access to the requests data, in particular all request insight api
  // This is for the legacy support site. The new support site uses REQUEST_CONFIDENTIAL_R and
  // REQUEST_RESTRICTED_R.
  REQUEST_R = 2;

  // read/write access to the requests data
  // note only needed for request insight internal subscription handlers. Every service
  // is allowed to emit events to the request insight pubsub topic.
  REQUEST_RW = 3;

  // read to the content data, e.g. all content manager GRPC and content manager table
  CONTENT_R = 4;

  // read/write access to the content data, e.g. all content manager GRPC and content manager table
  CONTENT_RW = 5;

  // full access to all content data including the user index via the GetUserBlobs rpc
  CONTENT_ADMIN = 8;

  // read access to the settings data
  SETTINGS_R = 6;

  // read/write access to the settings data
  SETTINGS_RW = 7;

  // Read access to confidential request data. This is for use by the support site, and does not
  // require Genie permissions to grant.
  REQUEST_CONFIDENTIAL_R = 9;

  // Read access to restricted request data (including PII). This is for use by the support site,
  // and requires Genie permissions.
  REQUEST_RESTRICTED_R = 10;

  // Read/write access to restricted request data (including PII).
  // This is for use by the proto exporter.
  REQUEST_RESTRICTED_RW = 11;

  // Delete access to bigtable rows.
  // only given out to IAP users with full access
  BIGTABLE_DELETE = 12;

  // For GDPR deletion requests.
  PII_ADMIN = 14;

  // Read metadata about deletion requests in shredder.
  SHREDDER_R = 15;

  // Make deletion requests.
  SHREDDER_RW = 16;

  // Update metadata about deletion requests in shredder. This is for things like manually marking
  // a deletion as completed, not normal operation.
  SHREDDER_ADMIN = 17;

  // Gives permissions to delete request events from the support database.
  REQUEST_ADMIN = 18;

  // Read access to notifications data.
  NOTIFICATION_R = 19;

  // Read/write access to notifications data.
  NOTIFICATION_RW = 20;

  // Admin access to notifications data.
  NOTIFICATION_ADMIN = 21;

  // Gives permissions to perform admin tasks for managing users from auth central.
  AUTH_ADMIN = 22;

  // Gives permissions to mint/create frontend API tokens for users.
  // This is used by services that need to create tokens on behalf of users.
  AUTH_MINT = 23;

  reserved 13;
}
