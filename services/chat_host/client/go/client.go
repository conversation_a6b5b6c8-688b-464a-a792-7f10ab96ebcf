// Package chathostclient provides a shared Go client for the Augment chat host service
package chathostclient

import (
	"context"
	"fmt"
	"time"

	blob_names_pb "github.com/augmentcode/augment/base/blob_names/proto"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	modelfinderproto "github.com/augmentcode/augment/services/api_proxy/model_finder_proto"
	chatpb "github.com/augmentcode/augment/services/chat_host/proto"
	modelinstanceproto "github.com/augmentcode/augment/services/deploy/model_instance/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

// ChatHostClient provides LLM functionality using the internal Augment chat host service
type ChatHostClient struct {
	conn      *grpc.ClientConn
	client    chatpb.ChatClient
	modelName string
}

// NewChatHostClient creates a new client for the Augment chat host service
// modelNameFlag should be a feature flag that returns the model name to use
func NewChatHostClient(modelFinderEndpoint string, creds credentials.TransportCredentials, modelNameFlag featureflags.StringFlag, featureFlagHandle featureflags.FeatureFlagHandle) (*ChatHostClient, error) {
	// Get the model name from feature flag
	modelName, err := modelNameFlag.Get(featureFlagHandle)
	if err != nil {
		return nil, fmt.Errorf("failed to get model name from feature flag: %w", err)
	}

	modelFinderConn, err := grpc.NewClient(modelFinderEndpoint, grpc.WithTransportCredentials(creds))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to model finder service: %w", err)
	}
	defer modelFinderConn.Close()

	modelFinderClient := modelfinderproto.NewModelFinderClient(modelFinderConn)
	models, err := modelFinderClient.GetGenerationModels(context.Background(), &modelfinderproto.GetModelsRequest{})
	if err != nil {
		return nil, fmt.Errorf("failed to get generation models: %w", err)
	}

	// Find model
	var selectedModel *modelinstanceproto.ModelInstanceConfig
	for _, model := range models.Models {
		if *model.ModelType == modelinstanceproto.ModelType_CHAT && *model.Name == modelName {
			selectedModel = model
			break
		}
	}

	if selectedModel == nil {
		log.Error().
			Str("model_name", modelName).
			Int("available_models", len(models.Models)).
			Msg("LLM model not found in model finder service")
		return nil, fmt.Errorf("model %s not found", modelName)
	}

	// Check if the model has chat configuration
	chatConfig, ok := selectedModel.ModelConfig.(*modelinstanceproto.ModelInstanceConfig_Chat)
	if !ok || chatConfig.Chat == nil {
		return nil, fmt.Errorf("selected model does not have chat configuration")
	}

	chatHostEndpoint := *chatConfig.Chat.ChatEndpoint
	conn, err := grpc.NewClient(chatHostEndpoint, grpc.WithTransportCredentials(creds))
	if err != nil {
		return nil, fmt.Errorf("failed to connect to chat host service: %w", err)
	}

	client := chatpb.NewChatClient(conn)
	return &ChatHostClient{
		conn:      conn,
		client:    client,
		modelName: modelName,
	}, nil
}

// GenerateResponse generates a response using the LLM for the given prompt
func (c *ChatHostClient) GenerateResponse(ctx context.Context, requestContext *requestcontext.RequestContext, prompt string) (string, error) {
	// Create outgoing context for gRPC call
	outgoingCtx := requestcontext.NewOutgoingContext(ctx, requestContext)

	// Create a timeout context for the LLM call
	timeoutCtx, cancel := context.WithTimeout(outgoingCtx, 30*time.Second)
	defer cancel()

	// Create the chat request
	chatReq := &chatpb.ChatRequest{
		ModelName: c.modelName,
		Message:   prompt,
		Blobs: []*blob_names_pb.Blobs{
			{
				// Empty blobs - most use cases don't need code context
			},
		},
	}

	// Make the chat request
	resp, err := c.client.Chat(timeoutCtx, chatReq)
	if err != nil {
		return "", fmt.Errorf("failed to generate LLM response: %w", err)
	}

	if resp.Text == "" {
		return "", fmt.Errorf("LLM returned empty response")
	}

	return resp.Text, nil
}

// Close closes the connection to the chat host service
func (c *ChatHostClient) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}
