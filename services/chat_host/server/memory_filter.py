"""Memory filtering module for chat server.

This module detects if it's a classify_and_distill_prompt
(see tools/feature_flags/flags.jsonnet)
If so, it detects if the user message has keywords, e.g. "remember".
If the user message has no keywords, we skip the LLM call.
"""

import json
import re
from dataclasses import dataclass

from base import feature_flags
from services.chat_host import chat_pb2
from services.chat_host.server.handler import Cha<PERSON><PERSON><PERSON><PERSON>, ChatResultStatusCode

MEMORY_PARAMS = feature_flags.StringFlag("memories_params", "{}")

MEMORY_FILTER = feature_flags.StringFlag("memory_filter", ".*")


def _get_text(request: chat_pb2.ChatRequest) -> str | None:
    if request.message:
        return request.message
    for node in request.nodes:
        if node.type == chat_pb2.ChatRequestNodeType.TEXT and node.text_node:
            return node.text_node.content

    return None


@dataclass(frozen=True)
class ClassifyAndDistillPrompt:
    prefix: str
    suffix: str

    def extract_user_message(self, text: str) -> str | None:
        if not text.startswith(self.prefix):
            return None
        if not text.endswith(self.suffix):
            return None
        return text[len(self.prefix) : len(text) - len(self.suffix)]


def _get_classify_and_distill_prompt(
    context: feature_flags.Context,
) -> ClassifyAndDistillPrompt | None:
    memory_params_str = MEMORY_PARAMS.get(context)
    try:
        memory_params = json.loads(memory_params_str)
    except (json.JSONDecodeError, TypeError):
        return None

    classify_and_distill_prompt = memory_params.get("classify_and_distill_prompt", "")
    if not classify_and_distill_prompt:
        return None

    parts = classify_and_distill_prompt.split("{message}")
    if len(parts) != 2:
        return None

    prefix, suffix = parts

    return ClassifyAndDistillPrompt(prefix, suffix)


def should_skip_classify_and_distill(
    request: chat_pb2.ChatRequest, context: feature_flags.Context
) -> bool:
    """Filter memory requests based on keywords.

    If this is a classify_and_distill_prompt request and the user message
    contains no memory-related keywords, returns a ChatResult with
    worthRemembering = False. Otherwise, returns None to proceed normally.

    Args:
        request: The chat request to filter.
        context: The feature flags context.

    Returns:
        A ChatResult if the request should be filtered (no memory keywords),
        None if the request should proceed normally.
    """
    if not request.silent:
        return False

    text = _get_text(request)
    if not text:
        return False

    classify_and_distill_prompt = _get_classify_and_distill_prompt(context)
    if not classify_and_distill_prompt:
        return False

    user_message = classify_and_distill_prompt.extract_user_message(text)
    if not user_message:
        return False

    # Special case: Empty filter always skips.
    memory_filter = MEMORY_FILTER.get(context)
    if not memory_filter:
        return True

    # Found keywords, proceed with classify_and_distill
    if re.search(memory_filter, user_message, re.IGNORECASE):
        return False

    return True


def get_skip_classify_and_distill_result():
    response_json = {
        "explanation": "The message does not contain any memory-related keywords or requests.",
        "worthRemembering": False,
        "content": "",
    }

    return ChatResult(
        text=json.dumps(response_json),
        unknown_blob_names=[],
        status_code=ChatResultStatusCode.OK,
    )
