"""Simple test for memory_filter module."""

import json

import pytest

from base import feature_flags
from services.chat_host import chat_pb2
from services.chat_host.server.memory_filter import (
    MEMORY_FILTER,
    MEMORY_PARAMS,
    get_skip_classify_and_distill_result,
    should_skip_classify_and_distill,
)


@pytest.fixture
def feature_flags_context():
    """Fixture for feature flags testing."""
    yield from feature_flags.feature_flag_fixture()


@pytest.mark.parametrize(
    "memory_params, memory_filter, message, expected",
    [
        pytest.param(
            json.dumps({"classify_and_distill_prompt": "A\n{message}\nB"}),
            "remember",
            "A\nremember foo.\nB",
            False,
            id="basic_keyword_match",
        ),
        pytest.param(
            json.dumps({"classify_and_distill_prompt": "A\n{message}\nB"}),
            "remember",
            "A\nfoo memory.\nB",
            True,
            id="basic_keyword_nonmatch",
        ),
        pytest.param(
            json.dumps({"classify_and_distill_prompt": "\nA\n{message}\nB\n"}),
            "remember",
            "\nA\nfoo memory.\nB\n",
            True,
            id="basic_keyword_whitespace_nonmatch",
        ),
        pytest.param(
            json.dumps({"classify_and_distill_prompt": "A\n{message}\nB"}),
            r"remember|memory",
            "A\nmake a memory.\nB",
            False,
            id="regex_keyword_match",
        ),
        pytest.param(
            json.dumps({"classify_and_distill_prompt": "A\n{message}\nB"}),
            r"A|B",
            "A\nfoo.\nB",
            True,
            id="strip_prefix_suffix",
        ),
        pytest.param(
            json.dumps({"classify_and_distill_prompt": "A\n{message}\nB"}),
            "",
            "A\nfoo.\nB",
            True,
            id="reject_all",
        ),
        pytest.param(
            json.dumps({"classify_and_distill_prompt": "A\n{message}\nB"}),
            None,
            "A\nfoo.\nB",
            False,
            id="default_filter",
        ),
        pytest.param(
            json.dumps({"classify_and_distill_prompt": "A\n{message}\nB"}),
            None,
            "A\nfoo.\nbar.\nB",
            False,
            id="default_filter_multiline_user_message",
        ),
        pytest.param(
            None,
            "remember",
            "A\nremember foo.\nB",
            False,
            id="default_params",
        ),
        pytest.param(
            "bad json",
            None,
            "A\nremember foo.\nB",
            False,
            id="bad_json",
        ),
    ],
)
def test_should_skip_classify_and_distill(
    memory_params, memory_filter, message, expected, feature_flags_context
):
    """Basic test for memory filter functionality."""
    if memory_filter is not None:
        feature_flags_context.set_flag(MEMORY_FILTER, memory_filter)
    if memory_params is not None:
        feature_flags_context.set_flag(MEMORY_PARAMS, memory_params)
    context = feature_flags.get_global_context()
    request = chat_pb2.ChatRequest()
    request.message = message
    request.silent = True
    result = should_skip_classify_and_distill(request, context)
    assert result == expected


def test_get_skip_classify_and_distill_result():
    result = get_skip_classify_and_distill_result()
    response_data = json.loads(result.text)
    assert response_data["worthRemembering"] is False
    assert response_data["content"] == ""
    assert "memory-related keywords" in response_data["explanation"]
