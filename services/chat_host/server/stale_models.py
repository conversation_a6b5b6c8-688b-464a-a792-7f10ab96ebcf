"""Stale model module for handling requests for stale models.

This module provides functionality to check if a requested model is stale and
respond with a configurable message.
"""

import logging
from typing import Optional

import base.feature_flags
from services.chat_host.server.handler import Chat<PERSON><PERSON><PERSON>, ChatResultStatusCode

logger = logging.getLogger(__name__)

# We expect stale_models to be a dict flag with shape: {"model_list": ["modelA", ...]}
STALE_MODELS_FLAG_NAME = "stale_models"
STALE_MODELS_DEFAULT = {"model_list": []}

# Keep response as a plain string flag
STALE_MODELS_RESPONSE = base.feature_flags.StringFlag("stale_models_response", "")


def check_stale_model(
    model_name: str, context: base.feature_flags.Context
) -> Optional[ChatResult]:
    """Check if the requested model is stale and return a response if it is.

    Args:
        model_name: The name of the requested model.

    Returns:
        A ChatResult response if the model is stale, None otherwise.
    """
    flag_val = context.lookup(STALE_MODELS_FLAG_NAME, STALE_MODELS_DEFAULT)
    if not isinstance(flag_val, dict):
        logger.error(
            "stale_models flag has unsupported type (expected dict): %s",
            type(flag_val).__name__,
        )
        return None

    stale_models = flag_val.get("model_list", [])

    if model_name in stale_models:
        logger.info("Stale model requested: %s", model_name)
        return ChatResult(
            text=STALE_MODELS_RESPONSE.get(context),
            unknown_blob_names=[],
            status_code=ChatResultStatusCode.OK,
        )

    return None
