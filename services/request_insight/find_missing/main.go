package main

import (
	"context"
	"encoding/json"
	"flag"
	"fmt"
	"net/http"
	"os"
	"time"

	"github.com/prometheus/client_golang/prometheus/promhttp"
	"github.com/rs/zerolog/log"

	_ "github.com/KimMachineGun/automemlimit"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/logging"
	contentmanager "github.com/augmentcode/augment/services/content_manager/client"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	"github.com/augmentcode/augment/services/lib/pubsub"
	"github.com/augmentcode/augment/services/request_insight/lib/subscriber"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
)

type Config struct {
	pubsub.SubscribeClientConfig
	FindMissingExporterConfig

	HealthFile string
	PromPort   int
	Namespace  string

	ContentManagerEndpoint string
	TokenExchangeEndpoint  string

	ClientMtls        *tlsconfig.ClientConfig
	CentralClientMtls *tlsconfig.ClientConfig

	FeatureFlagsSdkKeyPath      string
	DynamicFeatureFlagsEndpoint string
}

func loadConfig(configFile string) (*Config, error) {
	var config Config
	if configFile == "" {
		return nil, fmt.Errorf("missing config file")
	}

	f, err := os.Open(configFile)
	if err != nil {
		return nil, err
	}
	defer f.Close()

	decoder := json.NewDecoder(f)
	decoder.DisallowUnknownFields()
	if err := decoder.Decode(&config); err != nil {
		return nil, err
	}

	log.Info().Msgf("Config: %v", config)
	return &config, nil
}

func declareHealthy(healthFile string) {
	log.Info().Msgf("Declaring healthy in %s", healthFile)
	f, err := os.Create(healthFile)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create health file")
	}
	defer f.Close()
	f.WriteString("OK")
}

func main() {
	ctx := context.Background()
	logging.SetupServerLogging()

	// Parse flags.
	configFile := flag.String("config", "", "Path to config file")
	flag.Parse()

	// Load config.
	config, err := loadConfig(*configFile)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error loading config")
	}

	// Start metrics server.
	go func() {
		http.Handle("/metrics", promhttp.Handler())
		err := http.ListenAndServe(fmt.Sprintf(":%d", config.PromPort), nil)
		if err != nil {
			log.Fatal().Err(err).Msg("Error starting Prometheus metrics server")
		}
	}()

	centralClientCreds, err := tlsconfig.GetClientTls(config.CentralClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}
	clientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	featureFlagsHandle, err := featureflags.NewFeatureFlagHandleFromFile(
		config.FeatureFlagsSdkKeyPath, config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	contentManagerClient, err := contentmanager.NewContentManagerClient(
		config.ContentManagerEndpoint, clientCreds,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating content manager client")
	}
	defer contentManagerClient.Close()

	tokenExchangeClient, err := tokenexchange.New(
		config.TokenExchangeEndpoint, config.Namespace, centralClientCreds,
	)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating token exchange client")
	}
	defer tokenExchangeClient.Close()

	exporter, err := NewFindMissingExporter(
		ctx,
		&config.FindMissingExporterConfig,
		featureFlagsHandle,
		contentManagerClient,
		tokenExchangeClient,
	)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating exporter")
	}

	subscriber, err := subscriber.New(ctx, &config.SubscribeClientConfig, exporter.ProcessMessage)
	if err != nil {
		log.Fatal().Err(err).Msg("Error initializing RequestInsightSubscriber")
	}
	defer subscriber.Close()

	// Wait 10 seconds before declaring healthy, to give the subscriber time to error out if there's
	// something wrong with the subscription. Ideally we would do this in the subscriber itself, but
	// Go's pubsub library only calls your provided callback when there are messages to process. For
	// low-traffic namespaces this can cause kubernetes crashloops.
	go func() {
		time.Sleep(10 * time.Second)
		declareHealthy(config.HealthFile)
	}()

	// Run the subscriber. This should never return in the happy case.
	subscriber.Run(ctx)
}
