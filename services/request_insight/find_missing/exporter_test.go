package main

import (
	"context"
	"testing"
	"time"

	"github.com/augmentcode/augment/base/blob_names"
	blob_names_proto "github.com/augmentcode/augment/base/blob_names/proto"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/durationutil"
	chatpb "github.com/augmentcode/augment/services/chat_host/proto"
	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/proto"
)

func getTestExporter(t *testing.T) *FindMissingExporter {
	mockFeatureFlagHandle := featureflags.NewLocalFeatureFlagHandler()
	mockContentManager := contentmanagerclient.NewMockContentManagerClient()
	mockTokenExchange := tokenexchangeclient.NewMockTokenExchangeClient()

	config := &FindMissingExporterConfig{
		BlobNameCacheSize:        100,
		BlobNameCacheTTL:         durationutil.JSONDuration(1 * time.Hour),
		CheckpointCacheSize:      100,
		FindMissingCallBatchSize: 100,
	}
	exporter, err := NewFindMissingExporter(
		context.Background(), config, mockFeatureFlagHandle, mockContentManager, mockTokenExchange,
	)
	if err != nil {
		t.Fatal(err)
	}
	return exporter
}

func TestBasic(t *testing.T) {
	checkpointID := "test-checkpoint"
	addedBlobs := []blob_names.BlobName{
		blob_names.GetBlobName("test-path", []byte("blob1")),
		blob_names.GetBlobName("test-path", []byte("blob2")),
	}
	checkpointBlobs := []blob_names.BlobName{
		blob_names.GetBlobName("test-path", []byte("checkpoint1")),
		blob_names.GetBlobName("test-path", []byte("checkpoint2")),
	}
	blobBytes := make([][]byte, 0, len(addedBlobs))
	for _, blobName := range addedBlobs {
		decoded, err := blob_names.DecodeHexBlobName(blobName)
		if err != nil {
			t.Fatal(err)
		}
		blobBytes = append(blobBytes, decoded)
	}

	cases := []struct {
		name  string
		event *pb.RequestEvent
	}{
		{
			name: "CompletionHostRequest",
			event: &pb.RequestEvent{
				Event: &pb.RequestEvent_CompletionHostRequest{
					CompletionHostRequest: &pb.CompletionHostRequest{
						Blobs: &blob_names_proto.Blobs{
							BaselineCheckpointId: &checkpointID,
							Added:                blobBytes,
						},
					},
				},
			},
		},
		{
			name: "ChatHostRequest",
			event: &pb.RequestEvent{
				Event: &pb.RequestEvent_ChatHostRequest{
					ChatHostRequest: &pb.RIChatRequest{
						Request: &chatpb.ChatRequest{
							Blobs: []*blob_names_proto.Blobs{
								{
									BaselineCheckpointId: &checkpointID,
									Added:                blobBytes,
								},
							},
						},
					},
				},
			},
		},
	}

	for _, c := range cases {
		t.Run(c.name, func(t *testing.T) {
			exporter := getTestExporter(t)
			mockTokenExchange := exporter.tokenExchangeClient.(*tokenexchangeclient.MockTokenExchangeClient)
			mockTokenExchange.On("GetSignedTokenForService",
				mock.Anything, mock.Anything, mock.Anything,
			).Return("test-token", nil)

			// GetAllBlobsFromCheckpoint is expected to be called exactly once.
			mockContentManager := exporter.contentManagerClient.(*contentmanagerclient.MockContentManagerClient)
			mockContentManager.On("GetAllBlobsFromCheckpoint",
				mock.Anything, checkpointID, mock.Anything, mock.Anything,
			).Return(checkpointBlobs, nil).Once()

			// FindMissing is expected to be called exactly once.
			expectedBlobs := append(addedBlobs, checkpointBlobs...)
			mockContentManager.On("FindMissingBlobs",
				mock.Anything, expectedBlobs, "", "", proto.String("test-tenant"), mock.Anything,
			).Return([]string{}, nil).Once()

			message := &pb.RequestInsightMessage{
				Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
					UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
						RequestId: "test-request",
						TenantInfo: &pb.TenantInfo{
							TenantId: "test-tenant",
						},
						Events: []*pb.RequestEvent{
							c.event,
						},
					},
				},
			}

			// Process the message once
			err := exporter.ProcessMessage(context.Background(), message)
			require.NoError(t, err)
			mockTokenExchange.AssertExpectations(t)
			mockContentManager.AssertExpectations(t)

			// Re-processing should hit the cache and not result in any additional calls.
			err = exporter.ProcessMessage(context.Background(), message)
			require.NoError(t, err)
			mockTokenExchange.AssertExpectations(t)
			mockContentManager.AssertExpectations(t)
		})
	}
}

// Processing the same message with two different tenants should cause FindMissing to be called
// twice.
func TestTenantIsolation(t *testing.T) {
	exporter := getTestExporter(t)
	mockTokenExchange := exporter.tokenExchangeClient.(*tokenexchangeclient.MockTokenExchangeClient)
	mockTokenExchange.On("GetSignedTokenForService",
		mock.Anything, mock.Anything, mock.Anything,
	).Return("test-token", nil)

	// FindMissing is expected to be called exactly once per tenant.
	mockContentManager := exporter.contentManagerClient.(*contentmanagerclient.MockContentManagerClient)
	mockContentManager.On("FindMissingBlobs",
		mock.Anything, mock.Anything, "", "", proto.String("test-tenant1"), mock.Anything,
	).Return([]string{}, nil).Once()
	mockContentManager.On("FindMissingBlobs",
		mock.Anything, mock.Anything, "", "", proto.String("test-tenant2"), mock.Anything,
	).Return([]string{}, nil).Once()

	message := &pb.RequestInsightMessage{
		Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
			UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
				RequestId: "test-request",
				TenantInfo: &pb.TenantInfo{
					TenantId: "test-tenant1",
				},
				Events: []*pb.RequestEvent{
					{
						Event: &pb.RequestEvent_CompletionHostRequest{
							CompletionHostRequest: &pb.CompletionHostRequest{
								Blobs: &blob_names_proto.Blobs{
									Added: [][]byte{{1, 2, 3}},
								},
							},
						},
					},
				},
			},
		},
	}

	// Process the message once for each tenant
	err := exporter.ProcessMessage(context.Background(), message)
	require.NoError(t, err)
	message.GetUpdateRequestInfoRequest().TenantInfo.TenantId = "test-tenant2"
	err = exporter.ProcessMessage(context.Background(), message)
	require.NoError(t, err)

	mockTokenExchange.AssertExpectations(t)
	mockContentManager.AssertExpectations(t)
}

func TestCheckpointNotFound(t *testing.T) {
	exporter := getTestExporter(t)
	mockTokenExchange := exporter.tokenExchangeClient.(*tokenexchangeclient.MockTokenExchangeClient)
	mockTokenExchange.On("GetSignedTokenForService",
		mock.Anything, mock.Anything, mock.Anything,
	).Return("test-token", nil)

	// GetAllBlobsFromCheckpoint is expected to be called exactly once.
	mockContentManager := exporter.contentManagerClient.(*contentmanagerclient.MockContentManagerClient)
	mockContentManager.On("GetAllBlobsFromCheckpoint",
		mock.Anything, "test-checkpoint", mock.Anything, mock.Anything,
	).Return([]blob_names.BlobName{}, status.Errorf(codes.NotFound, "not found")).Once()

	message := &pb.RequestInsightMessage{
		Message: &pb.RequestInsightMessage_UpdateRequestInfoRequest{
			UpdateRequestInfoRequest: &pb.UpdateRequestInfoRequest{
				RequestId: "test-request",
				TenantInfo: &pb.TenantInfo{
					TenantId: "test-tenant",
				},
				Events: []*pb.RequestEvent{
					{
						Event: &pb.RequestEvent_CompletionHostRequest{
							CompletionHostRequest: &pb.CompletionHostRequest{
								Blobs: &blob_names_proto.Blobs{
									BaselineCheckpointId: proto.String("test-checkpoint"),
								},
							},
						},
					},
				},
			},
		},
	}

	// Process the message once
	err := exporter.ProcessMessage(context.Background(), message)
	require.NoError(t, err)
	mockTokenExchange.AssertExpectations(t)
	mockContentManager.AssertExpectations(t)
}
