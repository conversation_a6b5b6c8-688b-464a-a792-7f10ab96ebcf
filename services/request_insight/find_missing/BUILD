load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:metadata.bzl", "metadata_test")

go_library(
    name = "find_missing_exporter_lib",
    srcs = [
        "exporter.go",
        "main.go",
    ],
    importpath = "github.com/augmentcode/augment/services/request_insight/find_missing",
    deps = [
        "//base/blob_names:blob_names_go",
        "//base/blob_names:blob_names_go_proto",
        "//base/feature_flags:feature_flags_go",
        "//base/go/durationutil",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//services/chat_host:chat_host_go_proto",
        "//services/content_manager/client:client_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/pubsub:pubsub_go",
        "//services/lib/request_context:request_context_go",
        "//services/request_insight:request_insight_go_proto",
        "//services/request_insight/lib:request_insight_subscriber_go",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange:token_scopes_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_hashicorp_golang_lru_v2//:golang-lru",
        "@com_github_hashicorp_golang_lru_v2//expirable",
        "@com_github_kimmachinegun_automemlimit//:automemlimit",
        "@com_github_prometheus_client_golang//prometheus",
        "@com_github_prometheus_client_golang//prometheus/promauto",
        "@com_github_prometheus_client_golang//prometheus/promhttp",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_google_grpc//status:go_default_library",
        "@org_golang_google_protobuf//proto",
    ],
)

go_test(
    name = "find_missing_exporter_test",
    srcs = ["exporter_test.go"],
    embed = [":find_missing_exporter_lib"],
    deps = [
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
    ],
)

go_binary(
    name = "find_missing_exporter",
    embed = [":find_missing_exporter_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":find_missing_exporter",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        ":image",
    ],
    visibility = [
        "//services/deploy:__subpackages__",
        "//services/request_insight:__subpackages__",
    ],
    deps = [
        "//deploy/common:autoscaling_lib",
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

metadata_test(
    name = "metadata_test",
    src = "METADATA.jsonnet",
    deps = [
        ":kubecfg",
        "//deploy/tenants:namespaces",
    ],
)
