package main

import (
	"context"
	"fmt"
	"sync"
	"time"

	"github.com/augmentcode/augment/base/blob_names"
	blobspb "github.com/augmentcode/augment/base/blob_names/proto"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/durationutil"
	"github.com/augmentcode/augment/base/go/secretstring"
	contentmanager "github.com/augmentcode/augment/services/content_manager/client"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/request_insight/proto"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopes "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	lru "github.com/hashicorp/golang-lru/v2"
	"github.com/hashicorp/golang-lru/v2/expirable"
	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
)

var (
	enabledFlag = featureflags.NewBoolFlag("request_insight_find_missing_enabled", true)

	findMissingCallCounter = promauto.NewCounter(
		prometheus.CounterOpts{
			Name: "au_request_insight_find_missing_find_missing_call_count",
			Help: "Number of calls to find_missing by the find_missing exporter",
		},
	)

	findMissingBlobCountHistogram = promauto.NewHistogram(
		prometheus.HistogramOpts{
			Name: "au_request_insight_find_missing_find_missing_blob_count",
			Help: "Number of blobs found by the find_missing subscriber",
			Buckets: []float64{
				1, 2, 4, 8, 16, 32, 64, 128, 256, 512, 1024, 2048, 4096, 8192,
			},
		},
	)

	blobNameCacheCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_request_insight_find_missing_blob_name_cache_count",
			Help: "Number of blob names found in the cache",
		},
		[]string{"status"}, // values: missing | present
	)

	checkpointCacheCounter = promauto.NewCounterVec(
		prometheus.CounterOpts{
			Name: "au_request_insight_find_missing_checkpoint_cache_count",
			Help: "Number of checkpoint names found in the cache",
		},
		[]string{"status"}, // values: missing | present
	)
)

type FindMissingExporterConfig struct {
	BlobNameCacheSize        int
	BlobNameCacheTTL         durationutil.JSONDuration
	CheckpointCacheSize      int
	FindMissingCallBatchSize int
}

type FindMissingExporter struct {
	config            *FindMissingExporterConfig
	featureFlagHandle featureflags.FeatureFlagHandle
	sessionID         requestcontext.RequestSessionId

	serviceToken       *secretstring.SecretString
	serviceTokenMutex  sync.Mutex
	serviceTokenExpiry time.Time

	contentManagerClient contentmanager.ContentManagerClient
	tokenExchangeClient  tokenexchange.TokenExchangeClient

	// Cache from (blob, tenant) pairs to bool.
	blobCache *expirable.LRU[ProcessedBlob, bool]

	// Cache from checkpoint ids to the list of blobs in that checkpoint.
	checkpointCache *lru.Cache[string, []blob_names.BlobName]
}

type ProcessedBlob struct {
	BlobName blob_names.BlobName
	TenantID string
}

func NewFindMissingExporter(
	ctx context.Context,
	config *FindMissingExporterConfig,
	featureFlagHandle featureflags.FeatureFlagHandle,
	contentManagerClient contentmanager.ContentManagerClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
) (*FindMissingExporter, error) {
	blobCache := expirable.NewLRU[ProcessedBlob, bool](
		config.BlobNameCacheSize, nil, config.BlobNameCacheTTL.ToDuration())
	checkpointCache, err := lru.New[string, []blob_names.BlobName](
		config.CheckpointCacheSize)
	if err != nil {
		return nil, fmt.Errorf("failed to create checkpoint cache: %w", err)
	}

	return &FindMissingExporter{
		config:               config,
		featureFlagHandle:    featureFlagHandle,
		sessionID:            requestcontext.NewRandomRequestSessionId(),
		contentManagerClient: contentManagerClient,
		tokenExchangeClient:  tokenExchangeClient,
		blobCache:            blobCache,
		checkpointCache:      checkpointCache,
	}, nil
}

func (e *FindMissingExporter) ProcessMessage(
	ctx context.Context, message *pb.RequestInsightMessage,
) error {
	enabled, _ := enabledFlag.Get(e.featureFlagHandle)
	if !enabled {
		return nil
	}

	if message.GetUpdateRequestInfoRequest() == nil {
		return nil
	}

	tenantID := message.GetUpdateRequestInfoRequest().GetTenantInfo().GetTenantId()
	if tenantID == "" {
		// This is unexpected.
		log.Warn().Msgf("Skipping message missing tenant_id")
		return nil
	}

	// Note(Jacqueline): It's unexpected for any of the messages we care about to have multiple
	// events, so just process each event serially. (I want to change the protocol to only allow a
	// single event per message for unrelated reasons.)
	for _, event := range message.GetUpdateRequestInfoRequest().GetEvents() {
		// Get any uncached blob names from this event.
		// TODO(jacqueline): Are there other events we should be processing?
		var blobNames []blob_names.BlobName
		var eventType string
		var err error
		switch event.Event.(type) {
		case *pb.RequestEvent_CompletionHostRequest:
			eventType = "CompletionHostRequest"
			blobNames, err = e.getUncachedBlobNamesFromBlobs(ctx, event.GetCompletionHostRequest().GetBlobs(), tenantID)
		case *pb.RequestEvent_ChatHostRequest:
			eventType = "ChatHostRequest"
			for _, blobs := range event.GetChatHostRequest().GetRequest().GetBlobs() {
				b, err := e.getUncachedBlobNamesFromBlobs(ctx, blobs, tenantID)
				if err != nil {
					break
				}
				blobNames = append(blobNames, b...)
			}
		}
		if err != nil {
			return err
		}
		if len(blobNames) > 0 {
			log.Info().Msgf("Found %d uncached blob names in %s event", len(blobNames), eventType)
		}

		if err := e.findMissing(ctx, blobNames, tenantID); err != nil {
			return err
		}
	}
	return nil
}

func (e *FindMissingExporter) getUncachedBlobNamesFromBlobs(
	ctx context.Context, blobs *blobspb.Blobs, tenantID string,
) ([]blob_names.BlobName, error) {
	result := []blob_names.BlobName{}
	for _, blob := range blobs.GetAdded() {
		if !e.checkAndUpdateBlobCache(blob_names.NewBlobNameFromBytes(blob), tenantID) {
			result = append(result, blob_names.NewBlobNameFromBytes(blob))
		}
	}

	if blobs.GetBaselineCheckpointId() != "" {
		checkpointBlobs, err := e.getBlobsFromCheckpoint(ctx, blobs.GetBaselineCheckpointId(), tenantID)
		if err != nil {
			return nil, err
		}
		for _, blob := range checkpointBlobs {
			if !e.checkAndUpdateBlobCache(blob, tenantID) {
				result = append(result, blob)
			}
		}
	}

	return result, nil
}

func (e *FindMissingExporter) findMissing(
	ctx context.Context, blobNames []blob_names.BlobName, tenantID string,
) error {
	requestContext, err := e.getRequestContext(ctx)
	if err != nil {
		return err
	}

	findMissingBlobCountHistogram.Observe(float64(len(blobNames)))
	batchSize := e.config.FindMissingCallBatchSize
	for i := 0; i < len(blobNames); i += batchSize {
		end := i + batchSize
		if end > len(blobNames) {
			end = len(blobNames)
		}
		batch := blobNames[i:end]

		findMissingCallCounter.Inc()
		unknownBlobNames, err := e.contentManagerClient.FindMissingBlobs(
			ctx, batch, "", "", &tenantID, requestContext,
		)
		if err != nil {
			return fmt.Errorf("failed to call find-missing: %w", err)
		}
		log.Info().Msgf("Called find-missing on batch of %d blobs and found %d unknown blob names",
			len(batch), len(unknownBlobNames))
	}

	return nil
}

// Returns true if the blob was already in the cache.
func (e *FindMissingExporter) checkAndUpdateBlobCache(
	blobName blob_names.BlobName, tenantID string,
) (alreadyPresent bool) {
	if e.blobCache.Contains(ProcessedBlob{blobName, tenantID}) {
		blobNameCacheCounter.WithLabelValues("present").Inc()
		return true
	}
	blobNameCacheCounter.WithLabelValues("missing").Inc()
	e.blobCache.Add(ProcessedBlob{blobName, tenantID}, true)
	return false
}

// Returned blobs may be cached or uncached.
func (e *FindMissingExporter) getBlobsFromCheckpoint(
	ctx context.Context, checkpointID string, tenantID string,
) ([]blob_names.BlobName, error) {
	if blobs, ok := e.checkpointCache.Get(checkpointID); ok {
		checkpointCacheCounter.WithLabelValues("present").Inc()
		return blobs, nil
	}
	checkpointCacheCounter.WithLabelValues("missing").Inc()

	requestContext, err := e.getRequestContext(ctx)
	if err != nil {
		return nil, err
	}
	blobs, err := e.contentManagerClient.GetAllBlobsFromCheckpoint(
		ctx, checkpointID, &tenantID, requestContext,
	)
	if err != nil {
		if e, ok := status.FromError(err); ok && e.Code() == codes.NotFound {
			// Ignore "not found" errors.
			log.Info().Msgf("Checkpoint %s not found", checkpointID)
			return blobs, nil
		} else {
			log.Error().Err(err).Msgf("Failed to get blobs from checkpoint %s", checkpointID)
			return nil, fmt.Errorf("failed to get blobs from checkpoint: %w", err)
		}
	} else {
		e.checkpointCache.Add(checkpointID, blobs)
	}

	return blobs, nil
}

// Returns a request context authorized to call FindMissing and GetAllBlobsFromCheckpoint.
func (e *FindMissingExporter) getRequestContext(
	ctx context.Context,
) (*requestcontext.RequestContext, error) {
	e.serviceTokenMutex.Lock()
	defer e.serviceTokenMutex.Unlock()

	if e.serviceToken != nil && time.Now().Before(e.serviceTokenExpiry) {
		// Use the cached token.
		return requestcontext.New(
			requestcontext.NewRandomRequestId(),
			e.sessionID,
			"request-insight-find-missing",
			*e.serviceToken,
		), nil
	}

	// Fetch a new token.
	log.Info().Msgf("Requesting new service token")
	token, err := e.tokenExchangeClient.GetSignedTokenForService(
		ctx, "", []tokenscopes.Scope{tokenscopes.Scope_CONTENT_RW},
	)
	if err != nil {
		return nil, fmt.Errorf("failed to get token: %w", err)
	}
	e.serviceToken = &token
	// Tokens are configured in token-exchange to last for an hour.
	e.serviceTokenExpiry = time.Now().Add(time.Minute * 50)

	return requestcontext.New(
		requestcontext.NewRandomRequestId(),
		e.sessionID,
		"request-insight-find-missing",
		token,
	), nil
}
