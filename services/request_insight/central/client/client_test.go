package client

import (
	"context"
	"errors"
	"io"
	"log"
	"net"
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials/insecure"
	"google.golang.org/grpc/test/bufconn"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/request_insight/central/proto"
)

type mockServer struct {
	pb.RequestInsightCentralRestrictedServer

	responses map[string]*pb.DeleteGcsExportedBlobsResponse
	errors    map[string]error
}

func (server *mockServer) DeleteGcsExportedBlobs(
	stream pb.RequestInsightCentralRestricted_DeleteGcsExportedBlobsServer,
) error {
	for {
		req, err := stream.Recv()
		if err == io.EOF {
			return nil
		}
		if err != nil {
			return err
		}
		if err, ok := server.errors[req.BlobName]; ok {
			return err
		}
		resp, ok := server.responses[req.BlobName]
		if !ok {
			return errors.New("blob not found")
		}
		if err := stream.Send(resp); err != nil {
			return err
		}
	}
}

// newTestClient creates a client for testing using the older grpc.Dial API
// which works better with bufconn for in-memory testing. This is kind of weird
// but was the easiest way I could find to test a client with bidirectional
// streaming.
func newTestClient(t *testing.T, server *mockServer) (RequestInsightCentralClient, error) {
	dialer := func(context.Context, string) (net.Conn, error) {
		listener := bufconn.Listen(1024 * 1024)

		grpcServer := grpc.NewServer()

		pb.RegisterRequestInsightCentralRestrictedServer(grpcServer, server)

		go func() {
			if err := grpcServer.Serve(listener); err != nil {
				log.Fatal(err)
			}
		}()

		return listener.Dial()
	}

	conn, err := grpc.Dial(
		"bufnet",
		grpc.WithContextDialer(dialer),
		grpc.WithTransportCredentials(insecure.NewCredentials()),
	)
	if err != nil {
		return nil, err
	}
	restrictedClient := pb.NewRequestInsightCentralRestrictedClient(conn)
	return &RequestInsightCentralClientImpl{conn: conn, restrictedClient: restrictedClient}, nil
}

func TestDeleteGcsExportedBlobs_Success(t *testing.T) {
	server := &mockServer{
		responses: map[string]*pb.DeleteGcsExportedBlobsResponse{
			"blob1": {BlobName: "blob1", Found: true, Deleted: true, ErrorMessage: ""},
			"blob2": {BlobName: "blob2", Found: true, Deleted: true, ErrorMessage: ""},
		},
		errors: map[string]error{},
	}
	client, err := newTestClient(t, server)
	require.NoError(t, err)

	requests := []*pb.DeleteGcsExportedBlobsRequest{
		{TenantId: "test-tenant", BlobName: "blob1"},
		{TenantId: "test-tenant", BlobName: "blob2"},
	}

	// Execute
	ctx := context.Background()
	requestCtx := &requestcontext.RequestContext{}
	responses, err := client.DeleteGcsExportedBlobs(ctx, requestCtx, requests)
	require.NoError(t, err)

	// Verify
	require.Len(t, responses, 2)

	// Check first response
	assert.Equal(t, "blob1", responses[0].BlobName)
	assert.True(t, responses[0].Found)
	assert.True(t, responses[0].Deleted)
	assert.Equal(t, "", responses[0].ErrorMessage)

	// Check second response
	assert.Equal(t, "blob2", responses[1].BlobName)
	assert.True(t, responses[1].Found)
	assert.True(t, responses[1].Deleted)
	assert.Equal(t, "", responses[1].ErrorMessage)
}

func TestDeleteGcsExportedBlobs_ServerError(t *testing.T) {
	server := &mockServer{
		responses: map[string]*pb.DeleteGcsExportedBlobsResponse{
			"blob1": {BlobName: "blob1", Found: true, Deleted: true, ErrorMessage: ""},
		},
		errors: map[string]error{
			"blob2": errors.New("blob not found"),
		},
	}
	client, err := newTestClient(t, server)
	require.NoError(t, err)

	requests := []*pb.DeleteGcsExportedBlobsRequest{
		{TenantId: "test-tenant", BlobName: "blob1"},
		{TenantId: "test-tenant", BlobName: "blob2"},
	}

	// Execute
	ctx := context.Background()
	requestCtx := &requestcontext.RequestContext{}
	_, err = client.DeleteGcsExportedBlobs(ctx, requestCtx, requests)

	// Verify
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "blob not found")
}
