package client

import (
	"context"
	"io"
	"strings"
	"time"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"golang.org/x/sync/errgroup"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"
	"google.golang.org/protobuf/types/known/timestamppb"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/request_insight/central/proto"
)

type RequestInsightCentralClient interface {
	Close()

	DeleteEventsForUser(
		ctx context.Context,
		requestContext *requestcontext.RequestContext,
		userID string,
		startTime *time.Time,
		endTime time.Time,
	) (*pb.DeleteEventsForUserResponse, error)

	DeleteGcsExportedBlobs(
		ctx context.Context,
		requestContext *requestcontext.RequestContext,
		requests []*pb.DeleteGcsExportedBlobsRequest,
	) ([]*pb.DeleteGcsExportedBlobsResponse, error)
}

type RequestInsightCentralClientImpl struct {
	// gRPC channel.
	conn *grpc.ClientConn

	// gRPC client to use to make requests.
	restrictedClient pb.RequestInsightCentralRestrictedClient
}

func NewRequestInsightCentralClient(
	endpoint string, creds credentials.TransportCredentials, extraOpts ...grpc.DialOption,
) (RequestInsightCentralClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(creds),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	opts = append(opts, extraOpts...)

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}
	restrictedClient := pb.NewRequestInsightCentralRestrictedClient(conn)
	return &RequestInsightCentralClientImpl{conn: conn, restrictedClient: restrictedClient}, nil
}

func (c *RequestInsightCentralClientImpl) Close() {
	c.conn.Close()
}

func (c *RequestInsightCentralClientImpl) DeleteEventsForUser(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	userID string,
	startTime *time.Time,
	endTime time.Time,
) (*pb.DeleteEventsForUserResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())
	req := &pb.DeleteEventsForUserRequest{
		UserId:  userID,
		EndTime: timestamppb.New(endTime),
	}
	if startTime != nil {
		req.StartTime = timestamppb.New(*startTime)
	}
	return c.restrictedClient.DeleteEventsForUser(ctx, req)
}

func (c *RequestInsightCentralClientImpl) DeleteGcsExportedBlobs(
	ctx context.Context,
	requestContext *requestcontext.RequestContext,
	requests []*pb.DeleteGcsExportedBlobsRequest,
) ([]*pb.DeleteGcsExportedBlobsResponse, error) {
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())

	stream, err := c.restrictedClient.DeleteGcsExportedBlobs(ctx)
	if err != nil {
		return nil, err
	}

	var grp errgroup.Group

	// Send requests and responses in parallel, so we don't let either queue up
	// too much. We particularly don't want the server to stall because it has
	// queued up too many responses that haven't been processed.
	grp.Go(func() error {
		for _, req := range requests {
			if err := stream.Send(req); err != nil {
				return err
			}
		}

		// Close the send direction
		if err := stream.CloseSend(); err != nil {
			return err
		}
		return nil
	})

	responsesChan := make(chan *pb.DeleteGcsExportedBlobsResponse, len(requests))
	grp.Go(func() error {
		defer close(responsesChan)
		for {
			resp, err := stream.Recv()
			if err == io.EOF {
				return nil
			}
			if err != nil {
				return err
			}
			select {
			case responsesChan <- resp:
			case <-ctx.Done():
				return ctx.Err()
			}
		}
	})

	responses := make([]*pb.DeleteGcsExportedBlobsResponse, 0, len(requests))
	for {
		select {
		case resp, more := <-responsesChan:
			if more {
				responses = append(responses, resp)
			} else {
				return responses, grp.Wait()
			}
		case <-ctx.Done():
			return nil, ctx.Err()
		}
	}
}
