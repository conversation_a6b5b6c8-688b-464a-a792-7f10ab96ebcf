local lib = import 'deploy/common/lib.jsonnet';

function(cloud, env, namespace, namespace_config)
  local cloudInfo = (import 'deploy/common/cloud_info.jsonnet')[cloud];
  local datasetLib = (import 'services/request_insight/analytics_dataset/dataset_lib.jsonnet')(
    cloud, env, namespace, namespace_config.flags.useSharedDevRequestInsightBigquery
  );
  local appName = 'ri-analytics-remove-user-from-tenant-drop-email';

  local job = {
    apiVersion: 'bigquery.cnrm.cloud.google.com/v1beta1',
    kind: 'BigQueryJob',
    metadata: {
      // This name must be unique across all jobs in GCP. If we need to rerun anything, this name
      // needs to be changed.
      name: std.asciiLower('%s-%s-%s' % [datasetLib.location, env, appName]),
      namespace: namespace,
      labels: {
        app: appName,
      },
    },
    spec: {
      location: std.asciiUpper(datasetLib.location),
      query: {
        query: |||
          ALTER TABLE %(projectId)s.%(dataset)s.remove_user_from_tenant
          DROP COLUMN user_email
        ||| % {
          projectId: cloudInfo.projectId,
          dataset: datasetLib.datasetGcp,
        },
        useLegacySql: false,
        priority: 'INTERACTIVE',
        writeDisposition: '',  // Needed or the job will return an error.
        createDisposition: '',  // Needed or the job will return an error.
      },
    },
  };

  lib.flatten([
    job,
  ])
