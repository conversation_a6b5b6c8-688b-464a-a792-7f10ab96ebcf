"""Security tests for remote agents.

This test suite covers security aspects of remote agents functionality:
- Testing that agents cannot access GCP metadata server

These tests interact with:
- a live deployment of the remote agents service
- a running beachhead instance
- agent service that supports beachhead

Note: The bogon test and the metadata test overlap, but I (<PERSON><PERSON><PERSON><PERSON>) thought
that the metadata test was important enough to justify its own test.
"""

from pathlib import Path

import pytest

from base.augment_client.client import AugmentClient, GithubCommitRef
from services.test.remote_agents_testing_utils import (
    full_response_text,
    get_tool_results,
    log_tool_calls,
    setup_test_agent,
    wait_for_agent_idle,
    wait_for_agent_status,
)
from services.api_proxy.public_api_pb2 import RemoteAgentStatus


# Override the kubecfg script to use the remote agents kubecfg
@pytest.fixture(scope="session")
def kubecfg_script():
    """Returns the kubecfg script to use."""
    return Path("services/test/remote_agents_kubecfg.sh")


def test_gcp_metadata_server_access_blocked(augment_client: AugmentClient):
    """Tests that agents cannot access the GCP metadata server.

    This test verifies that:
    1. The agent cannot access the GCP metadata server by hostname (metadata.google.internal)
    2. The agent cannot access the GCP metadata server by IP address (***************)
    3. Network requests to these endpoints are properly blocked or fail
    4. The agent reports appropriate error messages indicating access is denied

    Args:
        augment_client: The client to use for API calls
    """
    # Use a simple test repository
    test_repo = GithubCommitRef(
        repository_url="https://github.com/augment-test-org/test-empty",
        git_ref="main",
    )

    initial_prompt = (
        "You are a security test agent. I will ask you to test network connectivity "
        "to verify security boundaries are properly enforced."
        "Please try to access the GCP metadata server using the hostname "
        "'metadata.google.internal'. Use curl or similar tools to attempt to "
        "connect to 'http://metadata.google.internal/computeMetadata/v1/'. "
        "Report exactly what happens - whether the connection succeeds, fails, "
        "times out, or is blocked. Include the full command output."
    )

    # Setup test agent
    with setup_test_agent(
        augment_client, initial_prompt=initial_prompt, commit_ref=test_repo
    ) as agent_id:
        # Get the response for hostname test
        response = augment_client.get_remote_agent_history(remote_agent_id=agent_id)

        print("\n=== GCP METADATA HOSTNAME TEST - Agent Response ===")
        agent_response = full_response_text(response)
        print(f"Full Agent Response:\n{agent_response}")
        log_tool_calls(response, "GCP METADATA HOSTNAME TEST")

        # Verify that the connection was blocked or failed
        tool_content = "".join(get_tool_results(response))
        response_content = agent_response + tool_content

        # Check for indicators that access was properly blocked
        blocked_indicators = [
            "connection refused",
            "connection timed out",
            "network unreachable",
            "could not resolve host",
            "name resolution failed",
            "no route to host",
            "connection failed",
            "curl: (6)",  # Could not resolve host
            "curl: (7)",  # Failed to connect
            "curl: (28)",  # Connection timed out
        ]

        access_blocked = any(
            indicator.lower() in response_content.lower()
            for indicator in blocked_indicators
        )

        assert access_blocked, (
            f"GCP metadata server access by hostname should be blocked. "
            f"Response: {response_content}"
        )

        print("✓ GCP metadata server access by hostname is properly blocked")

        # Test 2: Try to access GCP metadata server by IP address
        ip_test_message = (
            "Now please try to access the GCP metadata server using the IP address "
            "'***************'. Use curl or similar tools to attempt to connect to "
            "'http://***************/computeMetadata/v1/'. Report exactly what happens - "
            "whether the connection succeeds, fails, times out, or is blocked. "
            "Include the full command output."
        )

        augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=ip_test_message,
        )

        # Wait for the agent to process the request
        wait_for_agent_status(
            augment_client,
            agent_id,
            RemoteAgentStatus.AGENT_IDLE,
            timeout=120,
        )

        # Get the updated response for IP test
        response = augment_client.get_remote_agent_history(remote_agent_id=agent_id)

        print("\n=== GCP METADATA IP TEST - Agent Response ===")
        agent_response = full_response_text(response)
        print(f"Full Agent Response:\n{agent_response}")
        log_tool_calls(response, "GCP METADATA IP TEST")

        # Get the latest tool results (from the IP test)
        tool_results = get_tool_results(response)
        if len(tool_results) >= 2:
            # Get the second tool result (IP test)
            latest_tool_content = tool_results[-1]
        else:
            latest_tool_content = "".join(tool_results)

        # Check for indicators that IP access was also blocked
        ip_access_blocked = any(
            indicator.lower() in latest_tool_content.lower()
            for indicator in blocked_indicators
        )

        assert ip_access_blocked, (
            f"GCP metadata server access by IP should be blocked. "
            f"Latest tool output: {latest_tool_content}"
        )

        print("✓ GCP metadata server access by IP address is properly blocked")
        print("=== END GCP METADATA SERVER SECURITY TEST ===\n")


def test_ipv4_bogon_ranges_blocked(augment_client: AugmentClient):
    """Tests that all IPv4 bogon ranges defined in nftables.conf are blocked.

    This test verifies that the agent cannot access any of the IPv4 bogon ranges
    that should be blocked according to the nftables configuration:
    - 0.0.0.0/8 (RFC 1122 "This host on this network")
    - 10.0.0.0/8 (RFC 1918 private space)
    - **********/10 (RFC 6598 Carrier-grade NAT)
    - *********/8 (RFC 1122 localhost)
    - ***********/16 (RFC 3927 link-local)
    - **********/12 (RFC 1918 private space)
    - *********/24 (RFC 6890 IETF Protocol Assignments)
    - *********/24 (RFC 5737 TEST-NET-1)
    - ***********/16 (RFC 1918 private space)
    - **********/15 (RFC 2544 benchmarking)
    - ************/24 (RFC 5737 TEST-NET-2)
    - ***********/24 (RFC 5737 TEST-NET-3)
    - *********/4 (RFC 5771 multicast)
    - 240.0.0.0/4 (RFC 1112 reserved)
    - ***************/32 (RFC 919 limited broadcast)

    Args:
        augment_client: The client to use for API calls
    """
    # Use a simple test repository
    test_repo = GithubCommitRef(
        repository_url="https://github.com/augment-test-org/test-empty",
        git_ref="main",
    )

    initial_prompt = (
        "You are a network security test agent. I will ask you to test network "
        "connectivity to verify that bogon IP ranges are properly blocked."
        "Please install nmap using the package manager. Try 'apt-get update && apt-get install -y nmap' "
        "or 'yum install -y nmap' depending on the system. Report if the installation succeeds."
    )

    # Setup test agent
    with setup_test_agent(
        augment_client, initial_prompt=initial_prompt, commit_ref=test_repo
    ) as agent_id:
        # Define representative IP addresses from each bogon range to test
        # We'll test one IP from each range to verify blocking
        bogon_test_ips = [
            ("*******", "RFC 1122 This host on this network"),
            ("********", "RFC 1918 private space"),
            ("**********", "RFC 6598 Carrier-grade NAT"),
            ("127.0.0.1", "RFC 1122 localhost"),
            ("***********", "RFC 3927 link-local"),
            ("**********", "RFC 1918 private space"),
            ("*********", "RFC 6890 IETF Protocol Assignments"),
            ("*********", "RFC 5737 TEST-NET-1"),
            ("***********", "RFC 1918 private space"),
            ("**********", "RFC 2544 benchmarking"),
            ("************", "RFC 5737 TEST-NET-2"),
            ("***********", "RFC 5737 TEST-NET-3"),
            ("*********", "RFC 5771 multicast"),
            ("24*******", "RFC 1112 reserved"),
            ("***************", "RFC 919 limited broadcast"),
        ]

        # Test each bogon IP range
        for ip, description in bogon_test_ips:
            test_message = (
                f"Please use nmap to test connectivity to {ip} ({description}). "
                f"Run 'nmap -Pn -p 80,443,22 --host-timeout 10s {ip}' and report the results. "
                f"The connection should be blocked or filtered."
            )

            augment_client.remote_agent_chat(
                remote_agent_id=agent_id,
                message=test_message,
            )

            # Wait for the test to complete
            wait_for_agent_status(
                augment_client,
                agent_id,
                RemoteAgentStatus.AGENT_IDLE,
                timeout=60,
            )

        # Get the complete response after all tests
        response = augment_client.get_remote_agent_history(remote_agent_id=agent_id)

        print("\n=== IPv4 BOGON RANGES TEST - Agent Response ===")
        agent_response = full_response_text(response)
        print(f"Full Agent Response:\n{agent_response}")
        log_tool_calls(response, "IPv4 BOGON RANGES TEST")

        # Verify that the bogon ranges are blocked
        tool_content = "".join(get_tool_results(response))
        response_content = agent_response + tool_content

        # Check for indicators that connections were blocked
        blocked_indicators = [
            "host seems down",
            "no response received",
            "filtered",
            "host timeout",
            "network unreachable",
            "no route to host",
            "connection refused",
            "0 hosts up",
        ]

        # Count how many bogon IPs showed blocking indicators
        blocked_count = 0
        for ip, description in bogon_test_ips:
            ip_mentioned = ip in response_content
            if ip_mentioned:
                # Check if this specific IP showed blocking indicators
                # Look for the IP and nearby text for blocking indicators
                ip_context_start = response_content.find(ip)
                if ip_context_start != -1:
                    # Get context around the IP (500 chars before and after)
                    context_start = max(0, ip_context_start - 500)
                    context_end = min(len(response_content), ip_context_start + 500)
                    ip_context = response_content[context_start:context_end].lower()

                    ip_blocked = any(
                        indicator in ip_context for indicator in blocked_indicators
                    )
                    if ip_blocked:
                        blocked_count += 1
                        print(f"✓ {ip} ({description}) - properly blocked")
                    else:
                        print(f"⚠ {ip} ({description}) - blocking status unclear")

        print(
            f"\nBogon blocking summary: {blocked_count}/{len(bogon_test_ips)} ranges showed clear blocking indicators"
        )

        # We expect most bogon ranges to be blocked, but some might have unclear results
        # due to nmap behavior or network configuration
        assert blocked_count >= len(bogon_test_ips) // 2, (
            f"Expected at least half of the bogon ranges to show clear blocking indicators. "
            f"Got {blocked_count}/{len(bogon_test_ips)}. Response: {response_content[:1000]}..."
        )

        print("✓ IPv4 bogon ranges are properly blocked")
        print("=== END IPv4 BOGON RANGES TEST ===\n")
