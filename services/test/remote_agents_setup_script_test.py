"""Integration tests for remote agents setup scripts.

This test suite covers the setup script functionality of the remote agents API:
- Creating remote agents with setup scripts
- Verifying setup scripts execute successfully
- Checking that setup scripts create expected files/changes in the workspace

These tests interact with
- a live deployment of the remote agents service.
- a running beachhead instance.
- agent service that supports beachhead.
"""

import time
from pathlib import Path

import pytest

from base.augment_client.client import (
    AugmentClient,
    GithubCommitRef,
)
from services.api_proxy.public_api_pb2 import (
    RemoteAgentStatus,
    RemoteAgentWorkspaceStatus,
)
from services.test.remote_agents_testing_utils import (
    full_response_text,
    get_all_changed_files,
    get_setup_scripts,
    get_tool_results,
    log_all_changed_files,
    log_tool_calls,
    setup_test_agent,
    wait_for_agent_status,
    wait_for_agent_workspace_status,
    wait_for_chat_message_in_chat_history,
)


# Override the kubecfg script to use the remote agents kubecfg
@pytest.fixture(scope="session")
def kubecfg_script():
    """Returns the kubecfg script to use."""
    return Path("services/test/remote_agents_kubecfg.sh")


def test_setup_script_creates_file(augment_client: AugmentClient):
    """Test that a setup script can create a file in the workspace."""

    # Define a simple setup script that creates a test file
    setup_script = """#!/bin/bash
echo "Setup script executed successfully" > ./setup_test_file.txt
echo "Current directory: $(pwd)" >> ./setup_test_file.txt
echo "Files in workspace:" >> ./setup_test_file.txt
ls -la >> ./setup_test_file.txt
"""

    commit_ref = GithubCommitRef(
        repository_url="https://github.com/augment-test-org/simple-api-client",
        git_ref="main",
    )

    # Create a test agent with the setup script
    with setup_test_agent(
        augment_client=augment_client,
        initial_prompt="Please check if the file 'setup_test_file.txt' exists in the workspace and show me its contents.",
        commit_ref=commit_ref,
        is_setup_script_agent=False,
        setup_script=setup_script,
        max_wait_time=300,  # Give more time for setup script to complete
    ) as agent_id:
        print(f"Created setup script agent {agent_id}")

        # Get the chat history to verify the setup script worked
        history_response = augment_client.get_remote_agent_history(
            remote_agent_id=agent_id
        )

        _ = log_all_changed_files(history_response)

        # Check that the response contains evidence that the file was created
        full_response = full_response_text(history_response)

        print(f"Full chat history response: {full_response}")

        # Verify that the setup script output is present in the file
        assert (
            "Setup script executed successfully" in full_response
        ), "Setup script output not found in workspace file"

        print(
            "✅ Setup script test passed - file was created and contains expected content"
        )


def test_setup_script_with_git_operations(augment_client: AugmentClient):
    """Test that a setup script can perform git operations in the workspace."""

    # Define a setup script that performs git operations
    setup_script = """#!/bin/bash
echo "Git status before changes:" > git_operations_log.txt
git status >> git_operations_log.txt 2>&1
echo "Creating a new file..." >> git_operations_log.txt
echo "This file was created by setup script" > setup_created_file.md
echo "Git status after creating file:" >> git_operations_log.txt
git status >> git_operations_log.txt 2>&1
echo "Setup script git operations completed" >> git_operations_log.txt
"""

    commit_ref = GithubCommitRef(
        repository_url="https://github.com/augment-test-org/simple-api-client",
        git_ref="main",
    )

    # Create a test agent with the setup script
    with setup_test_agent(
        augment_client,
        initial_prompt="Please show me the contents of 'git_operations_log.txt' and 'setup_created_file.md' files if they exist.",
        commit_ref=commit_ref,
        is_setup_script_agent=False,
        setup_script=setup_script,
        max_wait_time=300,
    ) as agent_id:
        print(f"Created setup script agent with git operations {agent_id}")

        # Wait for the chat to complete
        wait_for_agent_status(
            augment_client, agent_id, RemoteAgentStatus.AGENT_IDLE, timeout=120
        )

        # Get the chat history to verify the setup script worked
        history_response = augment_client.get_remote_agent_history(
            remote_agent_id=agent_id
        )

        # Check that the response contains evidence that git operations were performed
        full_response = full_response_text(history_response)

        print(f"Full chat history response: {full_response}")

        assert (
            "Setup script git operations completed" in full_response
        ), "Setup script git operations log not found in chat history"

        print("✅ Setup script git operations test passed")


def test_setup_script_failure(augment_client: AugmentClient):
    """Test that a setup script failure is handled correctly.

    The expectation is that the agent should still launch.
    """

    # Define a setup script that fails
    setup_script = """#!/bin/bash
echo "Setup script is about to fail" > setup_failed_log.txt
exit 1
"""

    commit_ref = GithubCommitRef(
        repository_url="https://github.com/augment-test-org/simple-api-client",
        git_ref="main",
    )

    # Create a test agent with the setup script
    with setup_test_agent(
        augment_client,
        commit_ref=commit_ref,
        is_setup_script_agent=False,
        setup_script=setup_script,
        max_wait_time=300,
    ) as agent_id:
        print(f"Created setup script agent that will fail {agent_id}")

        # Wait for the agent to fail
        agent_status = wait_for_agent_status(
            augment_client,
            agent_id,
            [
                RemoteAgentStatus.AGENT_IDLE,
                RemoteAgentStatus.AGENT_RUNNING,
                RemoteAgentStatus.AGENT_FAILED,
            ],
            timeout=120,
        )

        print(f"Agent status: {agent_status}")
        assert (
            agent_status == RemoteAgentStatus.AGENT_IDLE
        ), f"Expected agent to be IDLE, but got {agent_status}"

        print("✅ Setup script failure test passed")


@pytest.mark.xfail(reason="AU-11438")
def test_setup_script_set_env_var(augment_client: AugmentClient):
    """Test that a setup script can set an environment variable.

    This test is currently known to fail but should be fixed eventually.
    """

    # Define a setup script that sets an environment variable
    setup_script = """#!/bin/bash
echo "Setup script is setting an environment variable" > setup_env_var_log.txt
export TEST_ENV_VAR="test_value_123456789"
echo "TEST_ENV_VAR is set to $TEST_ENV_VAR" >> setup_env_var_log.txt
"""

    commit_ref = GithubCommitRef(
        repository_url="https://github.com/augment-test-org/simple-api-client",
        git_ref="main",
    )

    # Create a test agent with the setup script
    with setup_test_agent(
        augment_client,
        initial_prompt="Please run the command 'echo $PATH' and show me the output.",
        commit_ref=commit_ref,
        is_setup_script_agent=False,
        setup_script=setup_script,
        max_wait_time=300,
    ) as agent_id:
        print(
            f"Created setup script agent that sets an environment variable {agent_id}"
        )

        # Wait for the chat message to appear in the chat history
        wait_for_chat_message_in_chat_history(
            augment_client,
            agent_id,
            "show me the output",
            chat_timeout=240,
        )

        # Send a chat message to verify the setup script set the environment variable
        chat_message = (
            "Please run the command 'echo $TEST_ENV_VAR' and show me the output."
        )
        chat_response = augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
        )

        print(f"Chat response: {chat_response}")

        # Wait for the chat message to appear in the chat history
        wait_for_chat_message_in_chat_history(
            augment_client,
            agent_id,
            "show me the output",
            chat_timeout=240,
        )

        # Wait for the chat to complete
        wait_for_agent_status(
            augment_client, agent_id, RemoteAgentStatus.AGENT_IDLE, timeout=120
        )

        # Get the chat history to verify the setup script worked
        history_response = augment_client.get_remote_agent_history(
            remote_agent_id=agent_id
        )

        log_tool_calls(history_response, "SET ENV VAR TEST")

        tool_results = "".join(get_tool_results(history_response))
        print(f"Tool results: {tool_results}")

        assert "/usr/local/sbin" in tool_results, "PATH not set displayed correctly"
        assert "test_value_123456789" in tool_results, "Environment variable not set"


def test_setup_script_profile_env_var(augment_client: AugmentClient):
    """Test that a setup script can set an environment variable via the profile."""

    # Define a setup script that sets an environment variable
    setup_script = """#!/bin/bash
echo "#!/bin/bash" > /tmp/93-agent.sh
echo export PROFILE_TEST_ENV_VAR="test_value_123456789" >> /tmp/93-agent.sh
chmod 755 /tmp/93-agent.sh
sudo cp /tmp/93-agent.sh /etc/profile.d/93-agent.sh
"""

    commit_ref = GithubCommitRef(
        repository_url="https://github.com/augment-test-org/simple-api-client",
        git_ref="main",
    )

    # Create a test agent with the setup script
    with setup_test_agent(
        augment_client,
        initial_prompt="Please run the command 'env' and show me the output.",
        commit_ref=commit_ref,
        is_setup_script_agent=False,
        setup_script=setup_script,
        max_wait_time=300,
    ) as agent_id:
        print(
            f"Created setup script agent that sets an environment variable {agent_id}"
        )

        # Wait for the chat message to appear in the chat history
        wait_for_chat_message_in_chat_history(
            augment_client,
            agent_id,
            "show me the output",
            chat_timeout=240,
        )

        # Send a chat message to verify the setup script set the environment variable
        chat_message = "Please run the command 'echo $PROFILE_TEST_ENV_VAR' and show me the output."
        chat_response = augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
        )

        print(f"Chat response: {chat_response}")

        wait_for_chat_message_in_chat_history(
            augment_client,
            agent_id,
            "show me the output",
            chat_timeout=240,
        )

        chat_message = (
            "Please run the command 'ls -l /etc/profile.d' and show me the output."
        )
        chat_response = augment_client.remote_agent_chat(
            remote_agent_id=agent_id,
            message=chat_message,
        )

        print(f"Chat response: {chat_response}")

        wait_for_chat_message_in_chat_history(
            augment_client,
            agent_id,
            "show me the output",
            chat_timeout=240,
        )

        history_response = augment_client.get_remote_agent_history(
            remote_agent_id=agent_id
        )

        log_tool_calls(history_response, "SET ENV VAR PROFILE TEST")

        tool_results = "".join(get_tool_results(history_response))
        print(f"Tool results: {tool_results}")

        assert "/usr/local/sbin" in tool_results, "PATH not set displayed correctly"
        assert "test_value_123456789" in tool_results, "Environment variable not set"


if __name__ == "__main__":
    pytest.main([__file__])
