use crate::circuit_breaker::CircuitBreaker;
use crate::handler_utils::{
    gate_on_circuit_breaker, request_context_from_req, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>,
};
use crate::model_registry::ModelRegistry;
use crate::public_api_proto;
use actix_web::{HttpRequest, HttpResponse};
use content_manager_client::ContentManagerClient;
use lazy_static::lazy_static;
use notification_client::{
    self,
    proto::notification::{self},
    NotificationClient,
};
use std::sync::Mutex;
use tonic::Status;
use tracing_actix_web::RootSpan;

// Feature flag to enable/disable notification endpoints
pub const ENABLE_NOTIFICATIONS: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("enable_notifications", false);

// Circuit breaker feature flags
pub const NOTIFICATION_CIRCUIT_BREAKER_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_circuit_breaker_notification", false);

// Rate limiting feature flags
pub const NOTIFICATION_THROTTLE_FLAG: feature_flags::BoolFlag =
    feature_flags::BoolFlag::new("api_proxy_throttle_notification", false);

pub const NOTIFICATION_THROTTLE_FILL_RATE_PER_SEC_FLAG: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("api_proxy_throttle_notification_fill_rate", 10.0);

pub const NOTIFICATION_THROTTLE_CAPACITY_FLAG: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_throttle_notification_capacity", 100);

// Circuit breaker configuration flags
pub const NOTIFICATION_CBF_FAILURE_THRESHOLD: feature_flags::FloatFlag =
    feature_flags::FloatFlag::new("api_proxy_notification_cbf_failure_threshold", 0.1);

pub const NOTIFICATION_CBF_MINIMUM_REQUESTS: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_notification_cbf_minimum_requests", 5);

pub const NOTIFICATION_CBF_FAILURE_WINDOW_SECONDS: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_notification_cbf_failure_window_seconds", 300);

pub const NOTIFICATION_CBF_RESET_THRESHOLD_SECONDS: feature_flags::IntFlag =
    feature_flags::IntFlag::new("api_proxy_notification_cbf_reset_threshold_seconds", 600);

lazy_static! {
    // Initialize circuit breaker with reasonable defaults, these will be overridden by feature flags
    pub static ref NOTIFICATION_CBF: Mutex<CircuitBreaker> = Mutex::new(
        CircuitBreaker::new(
            0.1,  // failure_threshold
            5,    // minimum_requests
            300,  // failure_window_seconds (5 minutes)
            600   // reset_threshold_seconds (10 minutes)
        )
    );
}

pub fn register_flags(registry: &feature_flags::RegistryHandle) {
    ENABLE_NOTIFICATIONS
        .register(registry)
        .expect("Registering ENABLE_NOTIFICATIONS");
    NOTIFICATION_CIRCUIT_BREAKER_FLAG
        .register(registry)
        .expect("Registering NOTIFICATION_CIRCUIT_BREAKER_FLAG");
    NOTIFICATION_THROTTLE_FLAG
        .register(registry)
        .expect("Registering NOTIFICATION_THROTTLE_FLAG");
    NOTIFICATION_THROTTLE_FILL_RATE_PER_SEC_FLAG
        .register(registry)
        .expect("Registering NOTIFICATION_THROTTLE_FILL_RATE_PER_SEC_FLAG");
    NOTIFICATION_THROTTLE_CAPACITY_FLAG
        .register(registry)
        .expect("Registering NOTIFICATION_THROTTLE_CAPACITY_FLAG");
    NOTIFICATION_CBF_FAILURE_THRESHOLD
        .register(registry)
        .expect("Registering NOTIFICATION_CBF_FAILURE_THRESHOLD");
    NOTIFICATION_CBF_MINIMUM_REQUESTS
        .register(registry)
        .expect("Registering NOTIFICATION_CBF_MINIMUM_REQUESTS");
    NOTIFICATION_CBF_FAILURE_WINDOW_SECONDS
        .register(registry)
        .expect("Registering NOTIFICATION_CBF_FAILURE_WINDOW_SECONDS");
    NOTIFICATION_CBF_RESET_THRESHOLD_SECONDS
        .register(registry)
        .expect("Registering NOTIFICATION_CBF_RESET_THRESHOLD_SECONDS");
}

// Helper function to acquire circuit breaker lock
async fn acquire_notification_cbf_lock(
) -> Result<std::sync::MutexGuard<'static, CircuitBreaker>, ()> {
    match NOTIFICATION_CBF.try_lock() {
        Ok(guard) => Ok(guard),
        Err(_) => {
            tracing::warn!("Failed to acquire notification circuit breaker lock");
            Err(())
        }
    }
}

// Helper function to update circuit breaker configuration from feature flags
fn update_cbf_config(
    cbf: &mut CircuitBreaker,
    feature_flags: &feature_flags::FeatureFlagsServiceHandle,
) {
    cbf.set_failure_threshold(NOTIFICATION_CBF_FAILURE_THRESHOLD.get_from(feature_flags));
    cbf.set_minimum_requests(NOTIFICATION_CBF_MINIMUM_REQUESTS.get_from(feature_flags) as u64);
    cbf.set_failure_window(NOTIFICATION_CBF_FAILURE_WINDOW_SECONDS.get_from(feature_flags) as u64);
    cbf.set_reset_threshold(
        NOTIFICATION_CBF_RESET_THRESHOLD_SECONDS.get_from(feature_flags) as u64,
    );
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::ReadNotificationsRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::ReadNotificationsRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        // Check if notifications feature is enabled
        if !ENABLE_NOTIFICATIONS.get_from(&feature_flags) {
            return Ok(
                HttpResponse::Ok().json(public_api_proto::ReadNotificationsResponse::default())
            );
        }

        // Check circuit breaker
        gate_on_circuit_breaker(
            &NOTIFICATION_CIRCUIT_BREAKER_FLAG,
            &feature_flags,
            req,
            &tenant_info,
        )?;

        // Check rate limiting
        if NOTIFICATION_THROTTLE_FLAG.get_from(&feature_flags) {
            let fill_rate_per_second =
                NOTIFICATION_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags);
            let capacity = NOTIFICATION_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64;
            self.throttle_cache
                .should_throttle(
                    &user.user_id,
                    "read_notifications",
                    fill_rate_per_second,
                    capacity,
                    1.0,
                )
                .await?;
        }

        // Convert public API request to notification service request
        let notification_request = convert_read_notifications_request(request);

        // Call the notification service and record result in circuit breaker
        let response_result = self
            .notification_client
            .read_notifications(&request_context, notification_request)
            .await;

        // Record the result in the circuit breaker
        if let Ok(cbf) = acquire_notification_cbf_lock().await {
            let mut cbf = cbf;
            update_cbf_config(&mut cbf, &feature_flags);
            let success = response_result.is_ok();
            cbf.record_request(success);
        }

        let response = response_result.map_err(|e| {
            tracing::error!("Failed to read notifications: {}", e);
            Status::internal("Failed to read notifications")
        })?;

        // Convert notification service response to public API response
        let public_response = convert_read_notifications_response(response);

        Ok(HttpResponse::Ok().json(public_response))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::MarkNotificationAsReadRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::MarkNotificationAsReadRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, Status> {
        let (user, tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        let feature_flags = self.get_feature_flags(&user, &tenant_info, Some(req))?;

        // Check if notifications feature is enabled
        if !ENABLE_NOTIFICATIONS.get_from(&feature_flags) {
            return Err(Status::not_found(
                "Notifications feature is not enabled in this environment",
            ));
        }

        // Check circuit breaker
        gate_on_circuit_breaker(
            &NOTIFICATION_CIRCUIT_BREAKER_FLAG,
            &feature_flags,
            req,
            &tenant_info,
        )?;

        // Check rate limiting
        if NOTIFICATION_THROTTLE_FLAG.get_from(&feature_flags) {
            let fill_rate_per_second =
                NOTIFICATION_THROTTLE_FILL_RATE_PER_SEC_FLAG.get_from(&feature_flags);
            let capacity = NOTIFICATION_THROTTLE_CAPACITY_FLAG.get_from(&feature_flags) as f64;
            self.throttle_cache
                .should_throttle(
                    &user.user_id,
                    "mark_notification_as_read",
                    fill_rate_per_second,
                    capacity,
                    1.0,
                )
                .await?;
        }

        // Convert public API request to notification service request
        let notification_request = convert_mark_notification_as_read_request(request);

        // Call the notification service and record result in circuit breaker
        let response_result = self
            .notification_client
            .mark_notifications_as_read(&request_context, notification_request)
            .await;

        // Record the result in the circuit breaker
        if let Ok(cbf) = acquire_notification_cbf_lock().await {
            let mut cbf = cbf;
            update_cbf_config(&mut cbf, &feature_flags);
            let success = response_result.is_ok();
            cbf.record_request(success);
        }

        let response = response_result.map_err(|e| {
            tracing::error!("Failed to read and mark notifications: {}", e);
            Status::internal("Failed to read and mark notifications")
        })?;

        // Convert notification service response to public API response
        let public_response = convert_mark_notification_as_read_response(response);

        Ok(HttpResponse::Ok().json(public_response))
    }
}

// Convert notification service response to public API response
fn convert_read_notifications_response(
    response: notification::ReadNotificationsResponse,
) -> public_api_proto::ReadNotificationsResponse {
    let mut public_response = public_api_proto::ReadNotificationsResponse {
        notifications: Vec::new(),
    };

    // Convert notifications
    for notification in response.notifications {
        public_response
            .notifications
            .push(convert_notification(notification));
    }

    public_response
}

// Convert public API request to notification service request
fn convert_read_notifications_request(
    _request: public_api_proto::ReadNotificationsRequest,
) -> notification::ReadNotificationsRequest {
    notification::ReadNotificationsRequest {
        ..Default::default()
    }
}

// Convert public API request to notification service request
fn convert_mark_notification_as_read_request(
    request: public_api_proto::MarkNotificationAsReadRequest,
) -> notification::MarkNotificationsAsReadRequest {
    notification::MarkNotificationsAsReadRequest {
        notifications_to_mark: vec![notification::NotificationMarkRequest {
            key: Some(
                notification::notification_mark_request::Key::NotificationId(
                    request.notification_id,
                ),
            ),
            action_item_title: request.action_item_title,
        }],
        ..Default::default()
    }
}

// Convert notification service response to public API response
fn convert_mark_notification_as_read_response(
    _response: notification::MarkNotificationsAsReadResponse,
) -> public_api_proto::MarkNotificationAsReadResponse {
    // we don't report the details back to the client
    public_api_proto::MarkNotificationAsReadResponse {}
}

// Convert notification from notification service to public API
fn convert_notification(
    notification: notification::Notification,
) -> public_api_proto::Notification {
    let mut public_notification = public_api_proto::Notification {
        notification_id: notification.notification_id,
        level: notification.level,
        message: notification.message,
        action_items: Vec::new(),
        display_type: notification.display_type,
    };

    // Convert action items
    for item in notification.action_items {
        public_notification
            .action_items
            .push(public_api_proto::ActionItem {
                title: item.title,
                url: item.url,
            });
    }

    public_notification
}

#[cfg(test)]
mod tests {
    use super::*;

    #[tokio::test]
    async fn test_notification_circuit_breaker_initialization() {
        // Test that the circuit breaker can be initialized and configured
        let cbf_result = acquire_notification_cbf_lock().await;
        assert!(cbf_result.is_ok());

        if let Ok(mut cbf) = cbf_result {
            // Test configuration updates
            cbf.set_failure_threshold(0.5);
            cbf.set_minimum_requests(10);
            cbf.set_failure_window(60);
            cbf.set_reset_threshold(120);

            // Test that circuit breaker starts in non-tripped state
            assert!(!cbf.is_tripped());
        }
    }

    #[test]
    fn test_convert_read_notifications_request() {
        let public_request = public_api_proto::ReadNotificationsRequest {};

        let notification_request = convert_read_notifications_request(public_request);
        assert!(!notification_request.read_all_versions);
        assert!(!notification_request.read_receipts);
    }
}
