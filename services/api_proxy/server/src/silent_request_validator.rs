use crate::chat::ChatRequest;
use feature_flags::{FeatureFlagProvider, FeatureFlagsServiceHandle};

/// Validates that silent requests contain only approved prompt patterns
/// to prevent abuse of the non-charged silent parameter.
pub fn validate_silent_request(
    chat_request: &ChatRequest,
    feature_flags: &FeatureFlagsServiceHandle,
) -> Result<(), String> {
    // Only validate if the request is marked as silent
    if !chat_request.silent {
        return Ok(());
    }

    // Extract the message content from the request
    let message_content = extract_message_content(chat_request);

    // Check against all approved prompt patterns
    if is_rewrite_prompt_instruction(&message_content) {
        tracing::info!("Silent request validated: rewrite prompt instruction");
        return Ok(());
    }

    if is_history_summary_prompt(&message_content, feature_flags) {
        tracing::info!("Silent request validated: history summary prompt");
        return Ok(());
    }

    if is_classify_and_distill_prompt(&message_content, feature_flags) {
        tracing::info!("Silent request validated: classify and distill prompt");
        return Ok(());
    }

    if is_classify_and_distill_success_prompt(&message_content, feature_flags) {
        tracing::info!("Silent request validated: classify and distill success prompt");
        return Ok(());
    }

    if is_summary_exchange_prompt(&message_content) {
        tracing::info!("Silent request validated: summary exchange prompt");
        return Ok(());
    }

    if is_update_task_list_prompt(&message_content) {
        tracing::info!("Silent request validated: update task list prompt");
        return Ok(());
    }

    if is_generate_questions_prompt(&message_content) {
        tracing::info!("Silent request validated: generate questions prompt");
        return Ok(());
    }

    // If none of the approved patterns match, reject the request
    tracing::warn!(
        "Silent request rejected: message does not match any approved pattern. Message length: {}, first 100 chars: {:?}",
        message_content.len(),
        message_content.chars().take(100).collect::<String>()
    );
    Err("Silent requests must contain only approved prompt patterns".to_string())
}

/// Extracts the message content from a ChatRequest
fn extract_message_content(chat_request: &ChatRequest) -> String {
    // First check if there's a direct message
    if !chat_request.message.is_empty() {
        return chat_request.message.clone();
    }

    // Then check nodes for text content
    let mut concatenated_content = String::new();
    for node in &chat_request.nodes {
        if let Some(text_node) = &node.text_node {
            concatenated_content.push_str(&text_node.content);
        }
    }
    concatenated_content
}

/// Validates the rewrite prompt instruction from ChatInput.svelte
/// Source: clients/common/webviews/src/apps/chat/components/ChatInput/ChatInput.svelte
fn is_rewrite_prompt_instruction(message: &str) -> bool {
    let expected_instruction =
        "Here is an instruction that I'd like to give you, but it needs to be improved. \
        Rewrite and enhance this instruction to make it clearer, more specific, \
        less ambiguous, and correct any mistakes. \
        Do not use any tools: reply immediately with your answer, even if you're not sure. \
        Consider the context of our conversation history when enhancing the prompt. \
        Reply with the following format:\n\n\
        ### BEGIN RESPONSE ###\n\
        Here is an enhanced version of the original instruction that is more specific and clear:\n\
        <augment-enhanced-prompt>enhanced prompt goes here</augment-enhanced-prompt>\n\n\
        ### END RESPONSE ###\n\n\
        Here is my original instruction:";

    message.contains(expected_instruction)
}

/// Validates the history summary prompt from feature flags
/// Source: history_summary_prompt feature flag
fn is_history_summary_prompt(message: &str, feature_flags: &FeatureFlagsServiceHandle) -> bool {
    // Check if the message contains the history summary prompt

    // First check the legacy history_summary_prompt flag
    let history_summary_prompt: String = feature_flags.lookup("history_summary_prompt", "");
    if !history_summary_prompt.is_empty() && message.contains(&history_summary_prompt) {
        return true;
    }

    // Then check the prompt in history_summary_param flag
    let history_summary_params: String = feature_flags.lookup("history_summary_params", "{}");

    // Parse the JSON to extract prompt
    if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(&history_summary_params) {
        if let Some(prompt) = json_value.get("prompt") {
            if let Some(prompt_str) = prompt.as_str() {
                if message.contains(prompt_str) {
                    return true;
                }
            }
        }
    }

    false
}

/// Validates the classify and distill prompt from memories_param feature flag
/// Source: classify_and_distill_prompt in memories_param feature flag
fn is_classify_and_distill_prompt(
    message: &str,
    feature_flags: &FeatureFlagsServiceHandle,
) -> bool {
    // Get the memories_param JSON string from feature flags
    let memories_params: String = feature_flags.lookup("memories_params", "{}");

    // Parse the JSON to extract classify_and_distill_prompt
    if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(&memories_params) {
        if let Some(classify_prompt) = json_value.get("classify_and_distill_prompt") {
            if let Some(prompt_str) = classify_prompt.as_str() {
                // Split the prompt by {message} placeholder and check if message contains all parts
                let parts: Vec<&str> = prompt_str.split("{message}").collect();
                if parts.len() > 1 {
                    // Check that message contains all parts (excluding empty ones)
                    return parts
                        .iter()
                        .filter(|part| !part.trim().is_empty())
                        .all(|part| message.contains(part.trim()));
                } else {
                    // If no {message} placeholder, do exact match
                    return message.contains(prompt_str);
                }
            }
        }
    }

    false
}

/// Validates the classify and distill success prompt from memories_param feature flag
/// Source: classify_and_distill_success_prompt in memories_param feature flag
fn is_classify_and_distill_success_prompt(
    message: &str,
    feature_flags: &FeatureFlagsServiceHandle,
) -> bool {
    // Get the memories_param JSON string from feature flags
    let memories_params: String = feature_flags.lookup("memories_params", "{}");

    // Parse the JSON to extract classify_and_distill_success_prompt
    if let Ok(json_value) = serde_json::from_str::<serde_json::Value>(&memories_params) {
        if let Some(success_prompt) = json_value.get("classify_and_distill_success_prompt") {
            if let Some(prompt_str) = success_prompt.as_str() {
                // Split the prompt by {message} placeholder and check if message contains all parts
                let parts: Vec<&str> = prompt_str.split("{message}").collect();
                if parts.len() > 1 {
                    // Check that message contains all parts (excluding empty ones)
                    return parts
                        .iter()
                        .filter(|part| !part.trim().is_empty())
                        .all(|part| message.contains(part.trim()));
                } else {
                    // If no {message} placeholder, do exact match
                    return message.contains(prompt_str);
                }
            }
        }
    }

    false
}

/// Validates the summary exchange prompt from conversation-model.ts
/// Source: clients/common/webviews/src/apps/chat/models/conversation-model.ts sendSummaryExchange
fn is_summary_exchange_prompt(message: &str) -> bool {
    let expected_prompt = "Please provide a clear and concise summary of our \
        conversation so far. The summary must be less than 6 words long. \
        The summary must contain the key points of the conversation. The \
        summary must be in the form of a title which will represent the \
        conversation. The response should not include any additional \
        formatting such as wrapping the response with quotation marks.";

    message.contains(expected_prompt)
}

/// Validates the update task list prompt from task-store.ts
/// Source: clients/common/webviews/src/apps/chat/models/task-store.ts _updateTaskList
fn is_update_task_list_prompt(message: &str) -> bool {
    // Check for common patterns in task list update prompts
    let task_list_indicators = [
        "Follow these rules when updating the task list:",
        "Maintain the hierarchical structure, given by the `Example task list structure`",
        "Always structure each task with [ ] for not started",
        "[/] for in progress, [x] for completed, and [-] for cancelled",
    ];

    // Check if message contains all the task list indicators
    task_list_indicators
        .iter()
        .all(|&indicator| message.contains(indicator))
}

/// Validates the generate questions prompt from onboarding-workspace-model.ts
/// Source: clients/common/webviews/src/apps/chat/models/onboarding-workspace-model.ts generateQuestions
fn is_generate_questions_prompt(message: &str) -> bool {
    let expected_prompt =
        "Give me the five most important questions a developer would need answered about this codebase. \
        These questions should be separated by a newline. I want you to say absolutely nothing else.";

    message.contains(expected_prompt)
}

#[cfg(test)]
mod tests {
    use super::*;
    use crate::chat::{ChatRequestNode, ChatRequestNodeType, ChatRequestText};
    use feature_flags::FeatureFlagsServiceHandle;
    use std::sync::Arc;

    // Mock feature flags service for testing
    struct MockFeatureFlagsService {
        history_summary_prompt: String,
        memories_params: String,
    }

    impl MockFeatureFlagsService {
        fn new() -> Self {
            let success_prompt = "###\n# ENTER MESSAGE ANALYSIS MODE\n# IN THIS MODE YOU ONLY ANALYZE THE MESSAGE AND DECIDE IF IT HAS INFORMATION WORTH REMEMBERING\n# YOU DON'T USE TOOLS OR ANSWER TO THE NEXT MESSAGE ITSELF\n# YOU RETURN ONLY JSON\n# ###\n\nHere is the next message from the user:\n```\n{message}\n```\n\nYour task is to detect if the next message contains some information worth remembering in long-term.";

            let memories_params = serde_json::json!({
                "classify_and_distill_prompt": "Classify and distill the following",
                "classify_and_distill_success_prompt": success_prompt
            });

            Self {
                history_summary_prompt: "Please provide a clear and concise summary".to_string(),
                memories_params: memories_params.to_string(),
            }
        }
    }

    impl feature_flags::FeatureFlagsService for MockFeatureFlagsService {
        fn lookup_bool(&self, flag: &feature_flags::BoolFlag) -> bool {
            flag.default
        }

        fn lookup_i64(&self, flag: &feature_flags::IntFlag) -> i64 {
            flag.default
        }

        fn lookup_f64(&self, flag: &feature_flags::FloatFlag) -> f64 {
            flag.default
        }

        fn lookup_string(&self, flag: &feature_flags::StringFlag) -> String {
            match flag.name {
                "history_summary_prompt" => self.history_summary_prompt.clone(),
                "memories_params" => self.memories_params.clone(),
                _ => flag.default.to_string(),
            }
        }

        fn set_local_bool(&self, _name: &str, _value: bool) {}
        fn set_local_string(&self, _name: &str, _value: &str) {}
        fn set_local_i64(&self, _name: &str, _value: i64) {}
        fn set_local_f64(&self, _name: &str, _value: f64) {}

        fn bind_attribute(
            &self,
            _extra_attribute_name: &str,
            _extra_attribute_value: &str,
        ) -> Result<FeatureFlagsServiceHandle, Box<dyn std::error::Error>> {
            Ok(Arc::new(MockFeatureFlagsService::new()))
        }
    }

    fn create_test_feature_flags() -> FeatureFlagsServiceHandle {
        Arc::new(MockFeatureFlagsService::new())
    }

    #[test]
    fn test_non_silent_request_passes() {
        let chat_request = ChatRequest {
            silent: false,
            message: "This is not an approved prompt".to_string(),
            ..Default::default()
        };

        let feature_flags = create_test_feature_flags();
        let result = validate_silent_request(&chat_request, &feature_flags);

        assert!(result.is_ok());
    }

    #[test]
    fn test_rewrite_prompt_validation() {
        let chat_request = ChatRequest {
            silent: true,
            message: "Here is an instruction that I'd like to give you, but it needs to be improved. \
            Rewrite and enhance this instruction to make it clearer, more specific, \
            less ambiguous, and correct any mistakes. \
            Do not use any tools: reply immediately with your answer, even if you're not sure. \
            Consider the context of our conversation history when enhancing the prompt. \
            Reply with the following format:\n\n\
            ### BEGIN RESPONSE ###\n\
            Here is an enhanced version of the original instruction that is more specific and clear:\n\
            <augment-enhanced-prompt>enhanced prompt goes here</augment-enhanced-prompt>\n\n\
            ### END RESPONSE ###\n\n\
            Here is my original instruction: Test prompt".to_string(),
            ..Default::default()
        };

        let feature_flags = create_test_feature_flags();
        let result = validate_silent_request(&chat_request, &feature_flags);

        assert!(result.is_ok());
    }

    #[test]
    fn test_summary_exchange_validation() {
        let chat_request = ChatRequest {
            silent: true,
            message: "Please provide a clear and concise summary of our \
            conversation so far. The summary must be less than 6 words long. \
            The summary must contain the key points of the conversation. The \
            summary must be in the form of a title which will represent the \
            conversation. The response should not include any additional \
            formatting such as wrapping the response with quotation marks."
                .to_string(),
            ..Default::default()
        };

        let feature_flags = create_test_feature_flags();
        let result = validate_silent_request(&chat_request, &feature_flags);

        assert!(result.is_ok());
    }

    #[test]
    fn test_generate_questions_validation() {
        let chat_request = ChatRequest {
            silent: true,
            message: "Give me the five most important questions a developer would need answered about this codebase. \
            These questions should be separated by a newline. I want you to say absolutely nothing else.".to_string(),
            ..Default::default()
        };

        let feature_flags = create_test_feature_flags();
        let result = validate_silent_request(&chat_request, &feature_flags);

        assert!(result.is_ok());
    }

    #[test]
    fn test_classify_and_distill_success_prompt_validation() {
        let chat_request = ChatRequest {
            silent: true,
            message: "###\n# ENTER MESSAGE ANALYSIS MODE\n# IN THIS MODE YOU ONLY ANALYZE THE MESSAGE AND DECIDE IF IT HAS INFORMATION WORTH REMEMBERING\n# YOU DON'T USE TOOLS OR ANSWER TO THE NEXT MESSAGE ITSELF\n# YOU RETURN ONLY JSON\n# ###\n\nHere is the next message from the user:\n```\nUser: How do I commit my changes?\n\nAssistant: I'll help you commit your changes.\n```\n\nYour task is to detect if the next message contains some information worth remembering in long-term.".to_string(),
            ..Default::default()
        };

        let feature_flags = create_test_feature_flags();
        let result = validate_silent_request(&chat_request, &feature_flags);

        assert!(result.is_ok());
    }

    #[test]
    fn test_unapproved_prompt_rejected() {
        let chat_request = ChatRequest {
            silent: true,
            message: "This is not an approved prompt pattern".to_string(),
            ..Default::default()
        };

        let feature_flags = create_test_feature_flags();
        let result = validate_silent_request(&chat_request, &feature_flags);

        assert!(result.is_err());
        assert!(result.unwrap_err().contains("approved prompt patterns"));
    }

    #[test]
    fn test_message_extraction_from_nodes() {
        let chat_request = ChatRequest {
            silent: true,
            nodes: vec![
                ChatRequestNode {
                    id: 1,
                    r#type: ChatRequestNodeType::Text as i32,
                    text_node: Some(ChatRequestText {
                        content: "Give me the five most important questions a developer would need answered about this codebase. These questions should be separated by a newline. I want you to say absolutely nothing else.".to_string(),
                    }),
                    ..Default::default()
                }
            ],
            ..Default::default()
        };

        let feature_flags = create_test_feature_flags();
        let result = validate_silent_request(&chat_request, &feature_flags);

        assert!(result.is_ok());
    }
}
