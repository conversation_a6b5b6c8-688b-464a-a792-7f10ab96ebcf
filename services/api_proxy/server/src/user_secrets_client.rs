use std::sync::Arc;

use async_lock::Mutex;
use async_trait::async_trait;
use grpc_client::create_channel;

use request_context::RequestContext;
use tracing_tonic::client::TracingService;

// Include the generated user secrets proto
pub mod user_secrets {
    tonic::include_proto!("user_secrets");
}

const USER_SECRETS_TIMEOUT_MS: u64 = 30 * 1000; // 30 seconds

#[async_trait]
pub trait UserSecretsClient: Send + Sync {
    async fn upsert_user_secret(
        &self,
        request_context: &RequestContext,
        request: user_secrets::UpsertUserSecretRequest,
    ) -> Result<user_secrets::UpsertUserSecretResponse, tonic::Status>;

    async fn get_user_secret(
        &self,
        request_context: &RequestContext,
        request: user_secrets::GetUserSecretRequest,
    ) -> Result<user_secrets::GetUserSecretResponse, tonic::Status>;

    async fn list_user_secrets(
        &self,
        request_context: &RequestContext,
        request: user_secrets::ListUserSecretsRequest,
    ) -> Result<user_secrets::ListUserSecretsResponse, tonic::Status>;

    async fn delete_user_secret(
        &self,
        request_context: &RequestContext,
        request: user_secrets::DeleteUserSecretRequest,
    ) -> Result<user_secrets::DeleteUserSecretResponse, tonic::Status>;
}

pub struct UserSecretsClientImpl {
    endpoint: String,
    tls_config: Option<tonic::transport::ClientTlsConfig>,
    client: Arc<
        Mutex<
            Option<
                user_secrets::user_secrets_service_client::UserSecretsServiceClient<TracingService>,
            >,
        >,
    >,
}

impl UserSecretsClientImpl {
    pub fn new(endpoint: &str, tls_config: Option<tonic::transport::ClientTlsConfig>) -> Self {
        Self {
            endpoint: endpoint.to_string(),
            tls_config,
            client: Arc::new(Mutex::new(None)),
        }
    }

    async fn get_client(
        &self,
    ) -> Result<
        user_secrets::user_secrets_service_client::UserSecretsServiceClient<TracingService>,
        tonic::transport::Error,
    > {
        let mut m = self.client.lock().await;
        match m.as_ref() {
            Some(c) => Ok(c.clone()),
            None => {
                let channel =
                    create_channel(self.endpoint.to_string(), None, &self.tls_config).await?;
                let client =
                    user_secrets::user_secrets_service_client::UserSecretsServiceClient::new(
                        channel,
                    );
                *m = Some(client.clone());
                Ok(client)
            }
        }
    }
}

#[async_trait]
impl UserSecretsClient for UserSecretsClientImpl {
    async fn upsert_user_secret(
        &self,
        request_context: &RequestContext,
        request: user_secrets::UpsertUserSecretRequest,
    ) -> Result<user_secrets::UpsertUserSecretResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("user secrets client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("user secrets service not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(USER_SECRETS_TIMEOUT_MS));
        request_context.annotate(request.metadata_mut());
        let response = client.upsert_user_secret(request).await?;
        Ok(response.into_inner())
    }

    async fn get_user_secret(
        &self,
        request_context: &RequestContext,
        request: user_secrets::GetUserSecretRequest,
    ) -> Result<user_secrets::GetUserSecretResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("user secrets client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("user secrets service not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(USER_SECRETS_TIMEOUT_MS));
        request_context.annotate(request.metadata_mut());
        let response = client.get_user_secret(request).await?;
        Ok(response.into_inner())
    }

    async fn list_user_secrets(
        &self,
        request_context: &RequestContext,
        request: user_secrets::ListUserSecretsRequest,
    ) -> Result<user_secrets::ListUserSecretsResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("user secrets client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("user secrets service not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(USER_SECRETS_TIMEOUT_MS));
        request_context.annotate(request.metadata_mut());
        let response = client.list_user_secrets(request).await?;
        Ok(response.into_inner())
    }

    async fn delete_user_secret(
        &self,
        request_context: &RequestContext,
        request: user_secrets::DeleteUserSecretRequest,
    ) -> Result<user_secrets::DeleteUserSecretResponse, tonic::Status> {
        let mut client = self.get_client().await.map_err(|e| {
            tracing::error!("user secrets client to {} not ready: {}", self.endpoint, e);
            tonic::Status::unavailable("user secrets service not ready")
        })?;
        let mut request = tonic::Request::new(request);
        request.set_timeout(std::time::Duration::from_millis(USER_SECRETS_TIMEOUT_MS));
        request_context.annotate(request.metadata_mut());
        let response = client.delete_user_secret(request).await?;
        Ok(response.into_inner())
    }
}

#[cfg(test)]
pub struct MockUserSecretsClient {}

#[cfg(test)]
impl MockUserSecretsClient {
    pub fn new() -> Self {
        Self {}
    }
}

#[cfg(test)]
#[async_trait]
impl UserSecretsClient for MockUserSecretsClient {
    async fn upsert_user_secret(
        &self,
        _request_context: &RequestContext,
        _request: user_secrets::UpsertUserSecretRequest,
    ) -> Result<user_secrets::UpsertUserSecretResponse, tonic::Status> {
        Ok(user_secrets::UpsertUserSecretResponse {
            secret: Some(user_secrets::UserSecret {
                name: "mock-secret".to_string(),
                value: "mock-value".to_string(),
                tags: std::collections::HashMap::new(),
                created_at: None,
                updated_at: None,
                description: "mock description".to_string(),
                version: "mock-version".to_string(),
                value_size_bytes: 10,
            }),
        })
    }

    async fn get_user_secret(
        &self,
        _request_context: &RequestContext,
        _request: user_secrets::GetUserSecretRequest,
    ) -> Result<user_secrets::GetUserSecretResponse, tonic::Status> {
        Ok(user_secrets::GetUserSecretResponse {
            secret: Some(user_secrets::UserSecret {
                name: "mock-secret".to_string(),
                value: "mock-value".to_string(),
                tags: std::collections::HashMap::new(),
                created_at: None,
                updated_at: None,
                description: "mock description".to_string(),
                version: "mock-version".to_string(),
                value_size_bytes: 10,
            }),
        })
    }

    async fn list_user_secrets(
        &self,
        _request_context: &RequestContext,
        _request: user_secrets::ListUserSecretsRequest,
    ) -> Result<user_secrets::ListUserSecretsResponse, tonic::Status> {
        Ok(user_secrets::ListUserSecretsResponse {
            secrets: vec![],
            next_page_token: "".to_string(),
            total_count: 0,
        })
    }

    async fn delete_user_secret(
        &self,
        _request_context: &RequestContext,
        _request: user_secrets::DeleteUserSecretRequest,
    ) -> Result<user_secrets::DeleteUserSecretResponse, tonic::Status> {
        Ok(user_secrets::DeleteUserSecretResponse { deleted: true })
    }
}
