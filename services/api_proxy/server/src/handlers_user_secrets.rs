use actix_web::{HttpRequest, HttpResponse};
use tracing_actix_web::RootSpan;

use crate::handler_utils::{request_context_from_req, EndpointH<PERSON><PERSON>, <PERSON><PERSON>};
use crate::model_registry::ModelRegistry;
use crate::public_api_proto;
use crate::user_secrets_client::user_secrets;
use content_manager_client::ContentManagerClient;

/// Helper function to preserve gRPC error status codes instead of converting everything to Internal
fn map_grpc_error(e: tonic::Status) -> tonic::Status {
    // Preserve the original status code and message from the User Secrets service
    e
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::UpsertUserSecretRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::UpsertUserSecretRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (_user, _tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        // Convert public API request to internal gRPC request
        let grpc_request = user_secrets::UpsertUserSecretRequest {
            name: request.name,
            value: request.value,
            tags: request.tags,
            description: request.description,
            expected_version: request.expected_version,
            max_value_size_bytes: request.max_value_size_bytes,
        };

        // Make the gRPC call using the pre-built client
        let grpc_response = self
            .user_secrets_client
            .upsert_user_secret(&request_context, grpc_request)
            .await
            .map_err(map_grpc_error)?;

        // Convert internal gRPC response to public API response
        let api_response = public_api_proto::UpsertUserSecretResponse {
            secret: grpc_response.secret.map(|s| public_api_proto::UserSecret {
                name: s.name,
                value: s.value,
                tags: s.tags,
                created_at: s.created_at,
                updated_at: s.updated_at,
                description: s.description,
                version: s.version,
                value_size_bytes: s.value_size_bytes,
            }),
        };

        Ok(HttpResponse::Ok().json(api_response))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::GetUserSecretRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::GetUserSecretRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (_user, _tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        // Convert public API request to internal gRPC request
        let grpc_request = user_secrets::GetUserSecretRequest { name: request.name };

        // Make the gRPC call using the pre-built client
        let grpc_response = self
            .user_secrets_client
            .get_user_secret(&request_context, grpc_request)
            .await
            .map_err(map_grpc_error)?;

        // Convert internal gRPC response to public API response
        let api_response = public_api_proto::GetUserSecretResponse {
            secret: grpc_response.secret.map(|s| public_api_proto::UserSecret {
                name: s.name,
                value: s.value,
                tags: s.tags,
                created_at: s.created_at,
                updated_at: s.updated_at,
                description: s.description,
                version: s.version,
                value_size_bytes: s.value_size_bytes,
            }),
        };

        Ok(HttpResponse::Ok().json(api_response))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::ListUserSecretsRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::ListUserSecretsRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (_user, _tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        // Convert public API request to internal gRPC request
        let grpc_request = user_secrets::ListUserSecretsRequest {
            name_pattern: request.name_pattern,
            tag_filters: request
                .tag_filters
                .into_iter()
                .map(|tf| user_secrets::TagFilter {
                    key: tf.key,
                    values: tf.values,
                    match_any: tf.match_any,
                    invert: tf.invert,
                })
                .collect(),
            include_values: request.include_values,
            page_size: request.page_size,
            page_token: request.page_token,
        };

        // Make the gRPC call using the pre-built client
        let grpc_response = self
            .user_secrets_client
            .list_user_secrets(&request_context, grpc_request)
            .await
            .map_err(map_grpc_error)?;

        // Convert internal gRPC response to public API response
        let api_response = public_api_proto::ListUserSecretsResponse {
            secrets: grpc_response
                .secrets
                .into_iter()
                .map(|s| public_api_proto::UserSecret {
                    name: s.name,
                    value: s.value,
                    tags: s.tags,
                    created_at: s.created_at,
                    updated_at: s.updated_at,
                    description: s.description,
                    version: s.version,
                    value_size_bytes: s.value_size_bytes,
                })
                .collect(),
            next_page_token: grpc_response.next_page_token,
            total_count: grpc_response.total_count,
        };

        Ok(HttpResponse::Ok().json(api_response))
    }
}

impl<MR: ModelRegistry, CNC: ContentManagerClient>
    EndpointHandler<public_api_proto::DeleteUserSecretRequest> for Handler<MR, CNC>
{
    async fn handle(
        &self,
        req: &HttpRequest,
        request: public_api_proto::DeleteUserSecretRequest,
        _root_span: RootSpan,
    ) -> Result<HttpResponse, tonic::Status> {
        let (_user, _tenant_info, request_context, _start_time) = request_context_from_req(req)?;

        // Convert public API request to internal gRPC request
        let grpc_request = user_secrets::DeleteUserSecretRequest {
            name: request.name,
            expected_version: request.expected_version,
        };

        // Make the gRPC call using the pre-built client
        let grpc_response = self
            .user_secrets_client
            .delete_user_secret(&request_context, grpc_request)
            .await
            .map_err(map_grpc_error)?;

        // Convert internal gRPC response to public API response
        let api_response = public_api_proto::DeleteUserSecretResponse {
            deleted: grpc_response.deleted,
        };

        Ok(HttpResponse::Ok().json(api_response))
    }
}
