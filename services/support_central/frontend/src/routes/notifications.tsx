import React, { useState, useEffect, useCallback, useMemo } from "react";
import { useParams, useNavigate } from "react-router-dom";
import {
  Button,
  Card,
  Form,
  Input,
  List,
  Select,
  Space,
  Typography,
  message,
  Tag,
  Popconfirm,
  Tabs,
  Tooltip,
  Progress,
  Upload,
  Divider,
  Alert,
} from "antd";
import {
  PlusOutlined,
  DeleteOutlined,
  BellOutlined,
  UploadOutlined,
  FileTextOutlined,
} from "@ant-design/icons";
import { LayoutComponent } from "../lib/layout";
import { createPromiseClient } from "@connectrpc/connect";
import { createConnectTransport } from "@connectrpc/connect-web";
import { SupportCentralService } from "../../../support_central_connect";

import {
  CreateNotificationRequest,
  ReadNotificationsRequest,
  Notification,
  ActionItem,
  NotificationLevel,
  NotificationDisplayType,
  DeleteNotificationsRequest,
} from "../../../../notification/notification_pb";

const { Title, Text } = Typography;

// Utility functions for generating request IDs and session IDs
const generateRequestId = (): string => {
  return crypto.randomUUID();
};

const generateSessionId = (): string => {
  return crypto.randomUUID();
};

const NotificationsPageComponent: React.FC = () => {
  const { userId } = useParams<{ userId?: string }>();
  const navigate = useNavigate();
  const [notifications, setNotifications] = useState<
    Notification[] | undefined
  >(undefined);
  const [loading, setLoading] = useState(false);
  const [selectedUserId, setSelectedUserId] = useState<string>(userId || "");
  const [form] = Form.useForm();

  // Progress tracking for notification creation
  const [creationProgress, setCreationProgress] = useState(0);
  const [isCreating, setIsCreating] = useState(false);
  const [totalUsers, setTotalUsers] = useState(0);
  const [progressType, setProgressType] = useState<"users" | "notifications">(
    "users",
  );

  // JSON upload feature state
  const [uploadedNotifications, setUploadedNotifications] = useState<
    CreateNotificationRequest[]
  >([]);
  const [showUploadPreview, setShowUploadPreview] = useState(false);
  const [uploadError, setUploadError] = useState<string>("");
  const [isProcessingFile, setIsProcessingFile] = useState(false);

  // Navigate to user-specific route when loading notifications
  const navigateToUserNotifications = (userIdToLoad: string) => {
    if (userIdToLoad) {
      navigate(`/notifications/${userIdToLoad}`);
    }
  };

  // Create ConnectRPC client with request ID and session ID headers
  const client = useMemo(() => {
    const sessionId = generateSessionId();
    const transport = createConnectTransport({
      baseUrl: window.location.origin,
      useBinaryFormat: true,
      interceptors: [
        (next) => async (req) => {
          const requestId = generateRequestId();
          req.header.set("x-request-id", requestId);
          req.header.set("x-request-session-id", sessionId);

          return await next(req);
        },
      ],
    });
    return createPromiseClient(SupportCentralService, transport);
  }, []);

  // Load notifications for a specific user
  const loadNotifications = useCallback(
    async (userId: string) => {
      if (!userId) return;

      console.log(`Loading notifications for user ${userId}`);

      setLoading(true);
      try {
        const response = await client.readNotifications(
          new ReadNotificationsRequest({
            opaqueUserId: userId,
          }),
        );
        setNotifications(response.notifications || []);
      } catch (error) {
        console.error("Failed to load notifications:", error);
        message.error("Failed to load notifications");
      } finally {
        setLoading(false);
      }
    },
    [client],
  );

  // Load notifications when component mounts or userId parameter changes
  useEffect(() => {
    if (!selectedUserId) return;

    loadNotifications(selectedUserId);
  }, [selectedUserId, loadNotifications]);

  // Load notifications when component mounts or userId parameter changes
  useEffect(() => {
    console.log("User ID changed to:", userId);
    if (!userId) return;

    setSelectedUserId(userId);
  }, [userId]);

  // Delete a notification
  const deleteNotification = async (notificationName: string) => {
    try {
      await client.deleteNotifications(
        new DeleteNotificationsRequest({
          opaqueUserId: selectedUserId,
          notificationNames: [notificationName],
        }),
      );
      message.success("Notification deleted successfully");
      // Remove from local state
      setNotifications((prev) =>
        prev?.filter((n) => n.notificationName !== notificationName),
      );
    } catch (error) {
      console.error("Failed to delete notification:", error);
      message.error("Failed to delete notification");
    }
  };

  // Handle form submission
  const handleCreateSubmit = async (values: any) => {
    // Convert action items from form format to protobuf format
    const actionItems =
      values.actionItems?.map(
        (item: any) =>
          new ActionItem({
            title: item.title,
            url: item.url,
          }),
      ) || [];

    // Create notifications for multiple users
    const userIds = values.userIds || [];
    const totalCount = userIds.length;

    // Initialize progress tracking
    setIsCreating(true);
    setTotalUsers(totalCount);
    setCreationProgress(0);
    setProgressType("users");

    let successCount = 0;
    let errorCount = 0;

    for (let i = 0; i < userIds.length; i++) {
      const userId = userIds[i];
      try {
        const request = new CreateNotificationRequest({
          opaqueUserId: userId,
          level: values.level,
          message: values.message,
          actionItems: actionItems,
          notificationName: values.notificationName,
          tenantId: values.tenantId,
          displayType:
            values.displayType ||
            NotificationDisplayType.DISPLAY_TYPE_UNSPECIFIED,
        });
        await client.createNotification(request);
        successCount++;

        // Reload notifications if viewing the same user
        if (userId === selectedUserId) {
          loadNotifications(selectedUserId);
        }
      } catch (error) {
        console.error(
          `Failed to create notification for user ${userId}:`,
          error,
        );
        errorCount++;
      }

      // Update progress
      const progress = Math.round(((i + 1) / totalCount) * 100);
      setCreationProgress(progress);
    }

    // Reset progress tracking
    setIsCreating(false);
    setCreationProgress(0);
    setTotalUsers(0);

    // Show summary message
    if (successCount > 0 && errorCount === 0) {
      message.success(
        `Successfully created notifications for ${successCount} user(s)`,
      );
    } else if (successCount > 0 && errorCount > 0) {
      message.warning(
        `Created notifications for ${successCount} user(s), failed for ${errorCount} user(s)`,
      );
    } else {
      message.error("Failed to create notifications for all users");
    }

    if (successCount > 0) {
      form.resetFields();
    }
  };

  const getLevelColor = (level: NotificationLevel) => {
    switch (level) {
      case NotificationLevel.INFO:
        return "blue";
      case NotificationLevel.WARNING:
        return "orange";
      case NotificationLevel.ERROR:
        return "red";
      case NotificationLevel.UNSPECIFIED:
      default:
        return "default";
    }
  };

  const getLevelText = (level: NotificationLevel) => {
    switch (level) {
      case NotificationLevel.INFO:
        return "Info";
      case NotificationLevel.WARNING:
        return "Warning";
      case NotificationLevel.ERROR:
        return "Error";
      case NotificationLevel.UNSPECIFIED:
      default:
        return "Unspecified";
    }
  };

  const getDisplayTypeText = (displayType: NotificationDisplayType) => {
    switch (displayType) {
      case NotificationDisplayType.DISPLAY_TYPE_BANNER:
        return "Banner";
      case NotificationDisplayType.DISPLAY_TYPE_TOAST:
        return "Toast";
      case NotificationDisplayType.DISPLAY_TYPE_UNSPECIFIED:
      default:
        return "Unspecified";
    }
  };

  // JSON file processing functions
  const validateNotificationLevel = (level: string): NotificationLevel => {
    switch (level?.toUpperCase()) {
      case "INFO":
        return NotificationLevel.INFO;
      case "WARNING":
        return NotificationLevel.WARNING;
      case "ERROR":
        return NotificationLevel.ERROR;
      case "UNSPECIFIED":
        return NotificationLevel.UNSPECIFIED;
      default:
        throw new Error(
          `Invalid notification level: ${level}. Must be one of: INFO, WARNING, ERROR, UNSPECIFIED`,
        );
    }
  };

  const validateNotificationDisplayType = (
    displayType: string,
  ): NotificationDisplayType => {
    switch (displayType?.toUpperCase()) {
      case "TOAST":
        return NotificationDisplayType.DISPLAY_TYPE_TOAST;
      case "BANNER":
        return NotificationDisplayType.DISPLAY_TYPE_BANNER;
      case "UNSPECIFIED":
        return NotificationDisplayType.DISPLAY_TYPE_UNSPECIFIED;
      default:
        // If not specified or invalid, default to UNSPECIFIED
        return NotificationDisplayType.DISPLAY_TYPE_UNSPECIFIED;
    }
  };

  const validateNotificationRequest = (
    obj: any,
    index: number,
  ): CreateNotificationRequest => {
    if (!obj || typeof obj !== "object") {
      throw new Error(`Item ${index + 1}: Must be an object`);
    }

    if (!obj.opaqueUserId || typeof obj.opaqueUserId !== "string") {
      throw new Error(
        `Item ${index + 1}: Missing or invalid 'opaqueUserId' field (must be a string)`,
      );
    }

    if (!obj.message || typeof obj.message !== "string") {
      throw new Error(
        `Item ${index + 1}: Missing or invalid 'message' field (must be a string)`,
      );
    }

    if (!obj.notificationName || typeof obj.notificationName !== "string") {
      throw new Error(
        `Item ${index + 1}: Missing or invalid 'notificationName' field (must be a string)`,
      );
    }

    const level = validateNotificationLevel(obj.level);
    const displayType = validateNotificationDisplayType(obj.displayType);

    // Validate action items if present
    const actionItems: ActionItem[] = [];
    if (obj.actionItems) {
      if (!Array.isArray(obj.actionItems)) {
        throw new Error(`Item ${index + 1}: 'actionItems' must be an array`);
      }

      obj.actionItems.forEach((item: any, itemIndex: number) => {
        if (!item || typeof item !== "object") {
          throw new Error(
            `Item ${index + 1}, ActionItem ${itemIndex + 1}: Must be an object`,
          );
        }
        if (!item.title || typeof item.title !== "string") {
          throw new Error(
            `Item ${index + 1}, ActionItem ${itemIndex + 1}: Missing or invalid 'title' field`,
          );
        }
        actionItems.push(
          new ActionItem({
            title: item.title,
            url: item.url,
          }),
        );
      });
    }

    return new CreateNotificationRequest({
      opaqueUserId: obj.opaqueUserId,
      level: level,
      message: obj.message,
      notificationName: obj.notificationName,
      actionItems: actionItems,
      tenantId: obj.tenantId,
      displayType: displayType,
    });
  };

  const handleFileUpload = (file: File) => {
    setIsProcessingFile(true);
    setUploadError("");

    const reader = new FileReader();
    reader.onload = (e) => {
      try {
        const content = e.target?.result as string;
        const jsonData = JSON.parse(content);

        if (!Array.isArray(jsonData)) {
          throw new Error(
            "JSON file must contain an array of notification objects",
          );
        }

        if (jsonData.length === 0) {
          throw new Error("JSON file cannot be empty");
        }

        if (jsonData.length > 1000) {
          throw new Error("Maximum 1000 notifications allowed per upload");
        }

        // Validate each notification request
        const validatedRequests = jsonData.map((item, index) =>
          validateNotificationRequest(item, index),
        );

        setUploadedNotifications(validatedRequests);
        setShowUploadPreview(true);
        setUploadError("");
        message.success(
          `Successfully parsed ${validatedRequests.length} notifications from file`,
        );
      } catch (error) {
        const errorMessage =
          error instanceof Error ? error.message : "Failed to parse JSON file";
        setUploadError(errorMessage);
        setUploadedNotifications([]);
        setShowUploadPreview(false);
        message.error(`File parsing error: ${errorMessage}`);
      } finally {
        setIsProcessingFile(false);
      }
    };

    reader.onerror = () => {
      setUploadError("Failed to read file");
      setIsProcessingFile(false);
      message.error("Failed to read file");
    };

    reader.readAsText(file);
    return false; // Prevent default upload behavior
  };

  const handleBulkCreate = async () => {
    if (uploadedNotifications.length === 0) return;

    const totalCount = uploadedNotifications.length;

    // Initialize progress tracking
    setIsCreating(true);
    setTotalUsers(totalCount);
    setCreationProgress(0);
    setProgressType("notifications");

    let successCount = 0;
    let errorCount = 0;
    const errors: string[] = [];

    for (let i = 0; i < uploadedNotifications.length; i++) {
      const request = uploadedNotifications[i];
      try {
        await client.createNotification(request);
        successCount++;

        // Reload notifications if viewing the same user
        if (request.opaqueUserId === selectedUserId) {
          loadNotifications(selectedUserId);
        }
      } catch (error) {
        console.error(
          `Failed to create notification for user ${request.opaqueUserId}:`,
          error,
        );
        errorCount++;
        errors.push(
          `User ${request.opaqueUserId}: ${error instanceof Error ? error.message : "Unknown error"}`,
        );
      }

      // Update progress
      const progress = Math.round(((i + 1) / totalCount) * 100);
      setCreationProgress(progress);
    }

    // Reset progress tracking
    setIsCreating(false);
    setCreationProgress(0);
    setTotalUsers(0);

    // Show summary message
    if (successCount > 0 && errorCount === 0) {
      message.success(`Successfully created ${successCount} notifications`);
    } else if (successCount > 0 && errorCount > 0) {
      message.warning(
        `Created ${successCount} notifications, failed ${errorCount}. Check console for details.`,
      );
      console.error("Failed notifications:", errors);
    } else {
      message.error("Failed to create all notifications");
      console.error("All failures:", errors);
    }

    if (successCount > 0) {
      // Clear upload state after successful creation
      setUploadedNotifications([]);
      setShowUploadPreview(false);
    }
  };

  // Generate breadcrumbs based on whether we're viewing a specific user
  const breadcrumbs = userId
    ? [
        { label: "Notifications", link: "/notifications" },
        { label: `User: ${userId}`, link: `/notifications/${userId}` },
      ]
    : [{ label: "Notifications", link: "/notifications" }];

  return (
    <LayoutComponent selectedMenuKey="notifications" breadcrumbs={breadcrumbs}>
      <div style={{ padding: "24px" }}>
        <Title level={2}>
          <BellOutlined /> Notification Management
        </Title>

        {/* Conditional rendering based on whether we have a userId parameter */}
        {userId ? (
          // Direct user view when userId is provided in URL
          <Space direction="vertical" size="large" style={{ width: "100%" }}>
            <Card title={`Notifications for User: ${userId}`}>
              <List
                loading={loading}
                dataSource={notifications}
                renderItem={(notification) => (
                  <List.Item
                    actions={[
                      <Popconfirm
                        key="delete"
                        title="Are you sure you want to delete this notification?"
                        onConfirm={() =>
                          deleteNotification(notification.notificationName)
                        }
                        okText="Yes"
                        cancelText="No"
                      >
                        <Button type="text" danger icon={<DeleteOutlined />} />
                      </Popconfirm>,
                    ]}
                  >
                    <List.Item.Meta
                      title={
                        <Space>
                          <span>Notification</span>
                          <Tag color={getLevelColor(notification.level)}>
                            {getLevelText(notification.level)}
                          </Tag>
                          <Tag color="blue">
                            {getDisplayTypeText(notification.displayType)}
                          </Tag>
                          <Text type="secondary" style={{ fontSize: "12px" }}>
                            Key: {notification.notificationName}
                          </Text>
                        </Space>
                      }
                      description={
                        <div>
                          <Text>{notification.message}</Text>
                          {notification.actionItems &&
                            notification.actionItems.length > 0 && (
                              <div style={{ marginTop: "8px" }}>
                                <Text strong>Actions:</Text>
                                <Space wrap style={{ marginTop: "4px" }}>
                                  {notification.actionItems.map(
                                    (action, index) => (
                                      <Button
                                        key={index}
                                        type="link"
                                        size="small"
                                        onClick={() =>
                                          window.open(action.url, "_blank")
                                        }
                                      >
                                        {action.title}
                                      </Button>
                                    ),
                                  )}
                                </Space>
                              </div>
                            )}
                          <br />
                          <Text type="secondary" style={{ fontSize: "12px" }}>
                            Created:{" "}
                            {notification.createdAt
                              ? new Date(
                                  notification.createdAt.toDate(),
                                ).toLocaleString()
                              : "Unknown"}
                            {notification.receivedAt && (
                              <>
                                {" "}
                                | Received:{" "}
                                {new Date(
                                  notification.receivedAt.toDate(),
                                ).toLocaleString()}
                              </>
                            )}
                          </Text>
                        </div>
                      }
                    />
                  </List.Item>
                )}
                locale={{ emptyText: "No notifications found" }}
              />
            </Card>
          </Space>
        ) : (
          // Tabbed interface when no userId is provided
          <Tabs
            defaultActiveKey="view"
            items={[
              {
                key: "view",
                label: (
                  <Tooltip title="Requires AUTH_R, NOTIFICATION_R">
                    View Notifications
                  </Tooltip>
                ),
                children: (
                  <Space
                    direction="vertical"
                    size="large"
                    style={{ width: "100%" }}
                  >
                    {/* Single User Controls */}
                    <Card>
                      <Space>
                        <Input
                          placeholder="Enter User ID to view notifications"
                          value={selectedUserId}
                          onChange={(e) => setSelectedUserId(e.target.value)}
                          onPressEnter={() =>
                            selectedUserId &&
                            navigateToUserNotifications(selectedUserId)
                          }
                          style={{ width: 300 }}
                        />
                        <Button
                          type="primary"
                          onClick={() => {
                            if (selectedUserId) {
                              navigateToUserNotifications(selectedUserId);
                            }
                          }}
                          disabled={!selectedUserId}
                        >
                          Load Notifications
                        </Button>
                      </Space>
                    </Card>

                    {/* Notifications List */}
                    {selectedUserId && (
                      <Card title={`Notifications for User: ${selectedUserId}`}>
                        <List
                          loading={loading}
                          dataSource={notifications}
                          renderItem={(notification) => (
                            <List.Item
                              actions={[
                                <Popconfirm
                                  title="Are you sure you want to delete this notification?"
                                  onConfirm={() =>
                                    deleteNotification(
                                      notification.notificationName,
                                    )
                                  }
                                  okText="Yes"
                                  cancelText="No"
                                >
                                  <Button
                                    type="text"
                                    danger
                                    icon={<DeleteOutlined />}
                                    size="small"
                                  >
                                    Delete
                                  </Button>
                                </Popconfirm>,
                              ]}
                            >
                              <List.Item.Meta
                                title={
                                  <Space>
                                    <span>Notification</span>
                                    <Tag
                                      color={getLevelColor(notification.level)}
                                    >
                                      {getLevelText(notification.level)}
                                    </Tag>
                                    <Tag color="blue">
                                      {getDisplayTypeText(
                                        notification.displayType,
                                      )}
                                    </Tag>
                                    <Text
                                      type="secondary"
                                      style={{ fontSize: "12px" }}
                                    >
                                      Name: {notification.notificationName}
                                    </Text>
                                  </Space>
                                }
                                description={
                                  <div>
                                    <Text>{notification.message}</Text>
                                    {notification.actionItems &&
                                      notification.actionItems.length > 0 && (
                                        <div style={{ marginTop: "8px" }}>
                                          <Space wrap>
                                            {notification.actionItems.map(
                                              (actionItem, index) => (
                                                <Button
                                                  key={index}
                                                  type="primary"
                                                  size="small"
                                                  href={actionItem.url}
                                                  target="_blank"
                                                  rel="noopener noreferrer"
                                                >
                                                  {actionItem.title}
                                                </Button>
                                              ),
                                            )}
                                          </Space>
                                        </div>
                                      )}
                                    <br />
                                    <Text
                                      type="secondary"
                                      style={{ fontSize: "12px" }}
                                    >
                                      Created:{" "}
                                      {notification.createdAt
                                        ? new Date(
                                            notification.createdAt.toDate(),
                                          ).toLocaleString()
                                        : "Unknown"}
                                      {notification.receivedAt && (
                                        <>
                                          {" "}
                                          | Received:{" "}
                                          {new Date(
                                            notification.receivedAt.toDate(),
                                          ).toLocaleString()}
                                        </>
                                      )}
                                    </Text>
                                  </div>
                                }
                              />
                            </List.Item>
                          )}
                          locale={{ emptyText: "No notifications found" }}
                        />
                      </Card>
                    )}
                  </Space>
                ),
              },
              {
                key: "create",
                label: (
                  <Tooltip title="Requires AUTH_R, NOTIFICATION_RW and NOTIFICATION_ADMIN">
                    Create Notifications
                  </Tooltip>
                ),
                children: (
                  <div>
                    {/* JSON File Upload Section */}
                    <Card
                      title="Bulk Upload from JSON File"
                      style={{ marginBottom: "24px" }}
                    >
                      <Space direction="vertical" style={{ width: "100%" }}>
                        <Alert
                          message="Upload a JSON file containing an array of notification requests"
                          description={
                            <div>
                              <p>Expected format:</p>
                              <pre
                                style={{
                                  fontSize: "12px",
                                  background: "#f5f5f5",
                                  padding: "8px",
                                  borderRadius: "4px",
                                }}
                              >
                                {`[
  {
    "opaqueUserId": "user123",
    "tenantId": "tenant-id-123",
    "level": "INFO",
    "displayType": "BANNER",
    "message": "Welcome!",
    "notificationName": "welcome-user123",
    "actionItems": [
      {
        "title": "Get Started",
        "url": "https://example.com"
      }
    ]
  }
]`}
                              </pre>
                            </div>
                          }
                          type="info"
                          showIcon
                        />

                        <Upload
                          accept=".json"
                          beforeUpload={handleFileUpload}
                          showUploadList={false}
                          disabled={isProcessingFile || isCreating}
                        >
                          <Button
                            icon={<UploadOutlined />}
                            loading={isProcessingFile}
                            disabled={isCreating}
                          >
                            {isProcessingFile
                              ? "Processing..."
                              : "Upload JSON File"}
                          </Button>
                        </Upload>

                        {uploadError && (
                          <Alert
                            message="Upload Error"
                            description={uploadError}
                            type="error"
                            showIcon
                            closable
                            onClose={() => setUploadError("")}
                          />
                        )}

                        {showUploadPreview &&
                          uploadedNotifications.length > 0 && (
                            <Card
                              title={
                                <Space>
                                  <FileTextOutlined />
                                  {`Preview: ${uploadedNotifications.length} notifications ready to create`}
                                </Space>
                              }
                              size="small"
                              extra={
                                <Space>
                                  <Button
                                    type="primary"
                                    onClick={handleBulkCreate}
                                    loading={isCreating}
                                    disabled={isCreating}
                                  >
                                    {isCreating
                                      ? "Creating..."
                                      : "Create All Notifications"}
                                  </Button>
                                  <Button
                                    onClick={() => {
                                      setUploadedNotifications([]);
                                      setShowUploadPreview(false);
                                    }}
                                    disabled={isCreating}
                                  >
                                    Clear
                                  </Button>
                                </Space>
                              }
                            >
                              <div
                                style={{
                                  maxHeight: "200px",
                                  overflowY: "auto",
                                }}
                              >
                                {uploadedNotifications
                                  .slice(0, 10)
                                  .map((req, index) => (
                                    <div
                                      key={index}
                                      style={{
                                        marginBottom: "8px",
                                        padding: "8px",
                                        background: "#fafafa",
                                        borderRadius: "4px",
                                      }}
                                    >
                                      <Text strong>{req.opaqueUserId}</Text> -
                                      <Tag
                                        color={getLevelColor(req.level)}
                                        style={{ margin: "0 4px" }}
                                      >
                                        {getLevelText(req.level)}
                                      </Tag>
                                      <Tag
                                        color="blue"
                                        style={{ margin: "0 4px" }}
                                      >
                                        {getDisplayTypeText(req.displayType)}
                                      </Tag>
                                      <Text
                                        ellipsis
                                        style={{ maxWidth: "300px" }}
                                      >
                                        {req.message}
                                      </Text>
                                    </div>
                                  ))}
                                {uploadedNotifications.length > 10 && (
                                  <Text type="secondary">
                                    ... and {uploadedNotifications.length - 10}{" "}
                                    more
                                  </Text>
                                )}
                              </div>
                            </Card>
                          )}
                      </Space>
                    </Card>

                    <Divider>OR</Divider>

                    {/* Manual Form Section */}
                    <Card title="Create New Notification Manually">
                      <Form
                        form={form}
                        layout="vertical"
                        onFinish={handleCreateSubmit}
                      >
                        <Form.Item
                          name="userIds"
                          label="User IDs"
                          rules={[
                            {
                              required: true,
                              message: "Please enter at least one user ID",
                            },
                          ]}
                        >
                          <Select
                            mode="tags"
                            placeholder="Enter user IDs (press Enter to add multiple)"
                            style={{ width: "100%" }}
                          />
                        </Form.Item>

                        <Form.Item
                          name="tenantId"
                          label="Tenant ID"
                          rules={[
                            {
                              required: true,
                              message: "Please enter a tenant ID",
                            },
                          ]}
                        >
                          <Input placeholder="Tenant ID" />
                        </Form.Item>

                        <Form.Item
                          name="message"
                          label="Message"
                          rules={[
                            {
                              required: true,
                              message: "Please enter a message",
                            },
                          ]}
                        >
                          <Input.TextArea
                            rows={4}
                            placeholder="Notification message"
                          />
                        </Form.Item>

                        <Form.Item
                          name="level"
                          label="Level"
                          rules={[
                            {
                              required: true,
                              message: "Please select a level",
                            },
                          ]}
                        >
                          <Select placeholder="Select notification level">
                            <Select.Option value={NotificationLevel.INFO}>
                              Info
                            </Select.Option>
                            <Select.Option value={NotificationLevel.WARNING}>
                              Warning
                            </Select.Option>
                            <Select.Option value={NotificationLevel.ERROR}>
                              Error
                            </Select.Option>
                          </Select>
                        </Form.Item>

                        <Form.Item
                          name="displayType"
                          label="Display Type"
                          rules={[
                            {
                              required: true,
                              message: "Please select a display type",
                            },
                          ]}
                        >
                          <Select placeholder="Select display type">
                            <Select.Option
                              value={
                                NotificationDisplayType.DISPLAY_TYPE_BANNER
                              }
                            >
                              Banner
                            </Select.Option>
                            <Select.Option
                              value={NotificationDisplayType.DISPLAY_TYPE_TOAST}
                            >
                              Toast
                            </Select.Option>
                          </Select>
                        </Form.Item>

                        <Form.Item
                          name="notificationName"
                          label="Notification Name"
                          rules={[
                            {
                              required: true,
                              message: "Please enter a notification name",
                            },
                          ]}
                        >
                          <Input placeholder="Unique key to prevent duplicate notifications (e.g., 'welcome-bonus-072025')" />
                        </Form.Item>

                        <Form.List name="actionItems">
                          {(fields, { add, remove }) => (
                            <>
                              {fields.map(({ key, name, ...restField }) => (
                                <Space
                                  key={key}
                                  style={{ display: "flex", marginBottom: 8 }}
                                  align="baseline"
                                >
                                  <Form.Item
                                    {...restField}
                                    name={[name, "title"]}
                                    rules={[
                                      {
                                        required: true,
                                        message: "Missing action title",
                                      },
                                    ]}
                                  >
                                    <Input placeholder="Action title" />
                                  </Form.Item>
                                  <Form.Item
                                    {...restField}
                                    name={[name, "url"]}
                                  >
                                    <Input placeholder="Action URL, e.g. https://..." />
                                  </Form.Item>
                                  <Button
                                    type="text"
                                    onClick={() => remove(name)}
                                    icon={<DeleteOutlined />}
                                  />
                                </Space>
                              ))}
                              <Form.Item>
                                <Button
                                  type="dashed"
                                  onClick={() => add()}
                                  block
                                  icon={<PlusOutlined />}
                                >
                                  Add Action Item
                                </Button>
                              </Form.Item>
                            </>
                          )}
                        </Form.List>

                        <Form.Item>
                          <Space>
                            <Button
                              type="primary"
                              htmlType="submit"
                              loading={isCreating}
                            >
                              {isCreating
                                ? "Creating..."
                                : "Create Notifications"}
                            </Button>
                            <Button
                              onClick={() => form.resetFields()}
                              disabled={isCreating}
                            >
                              Reset
                            </Button>
                          </Space>
                        </Form.Item>
                      </Form>

                      {/* Progress bar for notification creation */}
                      {isCreating && (
                        <div
                          style={{ marginTop: "16px", padding: "0 24px 16px" }}
                        >
                          <Progress
                            percent={creationProgress}
                            status="active"
                            format={(percent) =>
                              `${percent}% (${Math.ceil(((percent || 0) * totalUsers) / 100)} of ${totalUsers} ${progressType})`
                            }
                          />
                          <div
                            style={{
                              textAlign: "center",
                              marginTop: "8px",
                              color: "#666",
                            }}
                          >
                            Creating {totalUsers}{" "}
                            {progressType === "users"
                              ? `notification${totalUsers !== 1 ? "s" : ""} for ${totalUsers} user${totalUsers !== 1 ? "s" : ""}`
                              : `notification${totalUsers !== 1 ? "s" : ""}`}
                            ...
                          </div>
                        </div>
                      )}
                    </Card>
                  </div>
                ),
              },
            ]}
          />
        )}
      </div>
    </LayoutComponent>
  );
};

export default NotificationsPageComponent;
