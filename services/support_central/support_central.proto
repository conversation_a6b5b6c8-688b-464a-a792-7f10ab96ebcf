syntax = "proto3";
package support_central;

import "services/lib/grpc/connect_forward/forward_options.proto";
import "services/notification/notification.proto";

service SupportCentralService {
  rpc CreateNotification(notification.CreateNotificationRequest) returns (notification.CreateNotificationResponse) {
    option (forward_options.forward_method_name) = "notification.NotificationService/CreateNotification";
  }

  rpc ReadNotifications(notification.ReadNotificationsRequest) returns (notification.ReadNotificationsResponse) {
    option (forward_options.forward_method_name) = "notification.NotificationService/ReadNotifications";
  }

  rpc DeleteNotifications(notification.DeleteNotificationsRequest) returns (notification.DeleteNotificationsResponse) {
    option (forward_options.forward_method_name) = "notification.NotificationService/DeleteNotifications";
  }
}
