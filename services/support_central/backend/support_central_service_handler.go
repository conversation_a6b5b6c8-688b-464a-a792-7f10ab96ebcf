package main

import (
	"context"

	"connectrpc.com/connect"

	connectforwarder "github.com/augmentcode/augment/services/lib/grpc/connect_forward"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	useridresolver "github.com/augmentcode/augment/services/lib/user_id_resolver"
	notificationproto "github.com/augmentcode/augment/services/notification/proto"
)

// SupportCentralServiceHandler handles connectrpc->grpc message forwarding
type SupportCentralServiceHandler struct {
	userIDResolver   useridresolver.UserIDResolver
	forwarderFactory *connectforwarder.ForwarderFactory
}

func NewSupportCentralServiceHandler(
	userIDResolver useridresolver.UserIDResolver,
	forwarderFactory *connectforwarder.ForwarderFactory, connFactory connectforwarder.GrpcConnFactory,
) *SupportCentralServiceHandler {
	return &SupportCentralServiceHandler{
		userIDResolver:   userIDResolver,
		forwarderFactory: forwarderFactory,
	}
}

func (nh *SupportCentralServiceHandler) CreateNotification(
	ctx context.Context,
	req *connect.Request[notificationproto.CreateNotificationRequest],
) (*connect.Response[notificationproto.CreateNotificationResponse], error) {
	resp := notificationproto.CreateNotificationResponse{}
	return connectforwarder.Forward(ctx, nh.forwarderFactory, req, &resp,
		func(ctx context.Context, userInfo *connectforwarder.ForwardUserInfo, req *notificationproto.CreateNotificationRequest) (*connectforwarder.RouteInfo, error) {
			if req.OpaqueUserId == nil || *req.OpaqueUserId == "" {
				return nil, connect.NewError(connect.CodeInvalidArgument, nil)
			}
			requestContext, _ := requestcontext.FromGrpcContext(ctx)
			resolvedUserInfo, err := nh.userIDResolver.ResolveUser(ctx, *req.OpaqueUserId, requestContext)
			if err != nil {
				return nil, err
			}
			req.TenantId = &resolvedUserInfo.TenantID
			return &connectforwarder.RouteInfo{
				ShardNamespace: resolvedUserInfo.ShardNamespace,
				TenantID:       resolvedUserInfo.TenantID,
			}, nil
		}, nil)
}

func (nh *SupportCentralServiceHandler) ReadNotifications(
	ctx context.Context,
	req *connect.Request[notificationproto.ReadNotificationsRequest],
) (*connect.Response[notificationproto.ReadNotificationsResponse], error) {
	resp := notificationproto.ReadNotificationsResponse{}
	return connectforwarder.Forward(ctx, nh.forwarderFactory, req, &resp, func(ctx context.Context, userInfo *connectforwarder.ForwardUserInfo, req *notificationproto.ReadNotificationsRequest) (*connectforwarder.RouteInfo, error) {
		if req.OpaqueUserId == nil || *req.OpaqueUserId == "" {
			return nil, connect.NewError(connect.CodeInvalidArgument, nil)
		}
		requestContext, _ := requestcontext.FromGrpcContext(ctx)
		resolvedUserInfo, err := nh.userIDResolver.ResolveUser(ctx, *req.OpaqueUserId, requestContext)
		if err != nil {
			return nil, err
		}
		req.TenantId = &resolvedUserInfo.TenantID
		return &connectforwarder.RouteInfo{
			ShardNamespace: resolvedUserInfo.ShardNamespace,
			TenantID:       resolvedUserInfo.TenantID,
		}, nil
	}, nil)
}

func (nh *SupportCentralServiceHandler) DeleteNotifications(
	ctx context.Context,
	req *connect.Request[notificationproto.DeleteNotificationsRequest],
) (*connect.Response[notificationproto.DeleteNotificationsResponse], error) {
	resp := notificationproto.DeleteNotificationsResponse{}
	return connectforwarder.Forward(ctx, nh.forwarderFactory, req, &resp,
		func(ctx context.Context, userInfo *connectforwarder.ForwardUserInfo, req *notificationproto.DeleteNotificationsRequest) (*connectforwarder.RouteInfo, error) {
			if req.OpaqueUserId == nil || *req.OpaqueUserId == "" {
				return nil, connect.NewError(connect.CodeInvalidArgument, nil)
			}
			requestContext, _ := requestcontext.FromGrpcContext(ctx)
			resolvedUserInfo, err := nh.userIDResolver.ResolveUser(ctx, *req.OpaqueUserId, requestContext)
			if err != nil {
				return nil, err
			}
			req.TenantId = &resolvedUserInfo.TenantID
			return &connectforwarder.RouteInfo{
				ShardNamespace: resolvedUserInfo.ShardNamespace,
				TenantID:       resolvedUserInfo.TenantID,
			}, nil
		},
		nil)
}
