package main

import (
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/rs/zerolog/log"
)

var communityAbuseSuspensionEnabledFlag = featureflags.NewBoolFlag("community_abuse_suspension_enabled", false)

// If true, search for users and suspend them.
func communitySuspensionsEnabled(featureFlagHandle featureflags.FeatureFlagHandle) bool {
	communityAccountSuspensionEnabled, err := communityAbuseSuspensionEnabledFlag.Get(featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting community account suspension enabled flag, defaulting to false")
		communityAccountSuspensionEnabled = false
	}
	return communityAccountSuspensionEnabled
}
