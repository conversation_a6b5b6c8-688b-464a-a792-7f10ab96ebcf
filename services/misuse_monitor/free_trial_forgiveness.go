package main

import (
	"context"
	"fmt"
	"strconv"
	"time"

	"cloud.google.com/go/bigquery"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	authclient "github.com/augmentcode/augment/services/auth/central/client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	pb "github.com/augmentcode/augment/services/auth/central/server/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tenantutil "github.com/augmentcode/augment/services/tenant_watcher/util"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/rs/zerolog/log"
	"google.golang.org/api/iterator"
)

// If true, search for users but don't actually forgive suspensions.
var freeTrialForgivenessDryRunFlag = featureflags.NewBoolFlag("free_trial_forgiveness_dry_run", true)

type FreeTrialForgivenessJob struct {
	bqClient            *bigquery.Client
	datasetName         string
	jobName             string
	authClient          authclient.AuthClient
	tokenExchangeClient tokenexchange.TokenExchangeClient
	tenantCache         tenantwatcherclient.TenantCacheSync
	featureFlagHandle   featureflags.FeatureFlagHandle
}

// Ensure FreeTrialFirgivenessJob implements the Job interface
var _ Job = (*FreeTrialForgivenessJob)(nil)

func NewFreeTrialForgivenessJob(
	ctx context.Context,
	projectId string,
	datasetName string,
	authClient authclient.AuthClient,
	tokenExchangeClient tokenexchange.TokenExchangeClient,
	tenantCache tenantwatcherclient.TenantCacheSync,
	featureFlagHandle featureflags.FeatureFlagHandle,
) (*FreeTrialForgivenessJob, error) {
	// The BigQuery API doesn't let us parameterize dataset/table names, so we have to inject this
	// into our query with string manipulation. Make sure it doesn't contain contain anything that
	// could be malicious.
	if !CheckDatasetName(datasetName) {
		return nil, fmt.Errorf("Invalid dataset name %s", datasetName)
	}

	bqClient, err := bigquery.NewClient(ctx, projectId)
	if err != nil {
		return nil, fmt.Errorf("error creating bigquery client: %w", err)
	}

	return &FreeTrialForgivenessJob{
		bqClient:            bqClient,
		datasetName:         datasetName,
		jobName:             "free-trial-forgiveness",
		authClient:          authClient,
		tokenExchangeClient: tokenExchangeClient,
		tenantCache:         tenantCache,
		featureFlagHandle:   featureFlagHandle,
	}, nil
}

func (m *FreeTrialForgivenessJob) Close() {
	m.bqClient.Close()
}

func (m *FreeTrialForgivenessJob) Run(ctx context.Context) error {
	// Get users from BigQuery
	candidates, err := m.getCandidates(ctx)
	if err != nil {
		return fmt.Errorf("error getting candidates users: %w", err)
	}

	log.Info().Msgf("Total of %d users to process", len(candidates))

	// Ban the users
	err = m.forgiveCandidates(ctx, candidates)
	if err != nil {
		return fmt.Errorf("error banning users: %w", err)
	}

	// Correct state for any tagged users
	err = m.correctTaggedUsers(ctx)
	if err != nil {
		return fmt.Errorf("error correcting tagged users: %w", err)
	}

	return nil
}

var campaignTag = "MEA_CULPA_072025"

type forgivenessCandidate struct {
	ID                      string              `bigquery:"opaque_user_id"`
	ExternalPlanID          string              `bigquery:"external_plan_id"`
	OtherAccountCount       bigquery.NullInt64  `bigquery:"other_account_count"`
	FeatureVectorSuspension bool                `bigquery:"feature_vector_suspension"`
	OldSuspension           bool                `bigquery:"old_suspension"`
	VerisoulDecision        bigquery.NullString `bigquery:"verisoul_decision"`
}

func (m *FreeTrialForgivenessJob) getCandidates(ctx context.Context) ([]*forgivenessCandidate, error) {
	// Construct the query.
	query := m.bqClient.Query(`
	WITH
		verisoul_report AS (
			SELECT
				time as report_time,
				JSON_EXTRACT_SCALAR(report, '$.decision') AS verisoul_decision,
				LOWER(JSON_EXTRACT_SCALAR(report, '$.account.email.email')) AS email,
				SAFE_CAST(JSON_EXTRACT_SCALAR(report, '$.account.email.disposable') AS BOOL) is_disposable,
				CAST(JSON_EXTRACT_SCALAR(report, '$.multiple_accounts') AS FLOAT64) AS multiple_accounts,
				CAST(JSON_EXTRACT_SCALAR(report, '$.accounts_linked') AS INT64) AS accounts_linked,
				CAST(JSON_EXTRACT_SCALAR(report, '$.bot') AS FLOAT64) AS bot,
				ROW_NUMBER() OVER (PARTITION BY LOWER(JSON_EXTRACT_SCALAR(report, '$.account.email.email')) ORDER BY time DESC) as rn
			FROM verisoul
			WHERE report IS NOT NULL
			AND JSON_EXTRACT_SCALAR(report, '$.account.email.email') IS NOT NULL
			AND time <= TIMESTAMP('2025-07-08 18:00:00-00:00')
		),
		-- Use only the latest Verisoul report per user
		verisoul_score AS (
			SELECT
				report_time,
				verisoul_decision,
				email,
				is_disposable,
				multiple_accounts,
				accounts_linked,
				bot
			FROM verisoul_report
			WHERE rn = 1
		),
		chat_activity AS (
			SELECT
				opaque_user_id,
				COUNT(*) AS message_count
			FROM chat_user_message
			WHERE opaque_user_id IS NOT NULL
			GROUP BY 1
		),
		suspended_user AS (
			SELECT
				user.id as opaque_user_id,
				user.email,
				CASE WHEN ARRAY_LENGTH(user.tenant_ids) = 1 THEN user.tenant_ids[0] END AS tenant_id,
				user.idp_user_ids as idp_user_ids,
				user.orb_subscription_id,
				chat_activity.message_count AS messages_used,
				suspension.evidence,
				suspension.created_time,
				sub.external_plan_id,
				verisoul_score.verisoul_decision,
				verisoul_score.report_time,
				CASE WHEN suspension.evidence LIKE '%feature vector%' THEN true ELSE false END AS feature_vector_suspension,
				CASE WHEN suspension.evidence LIKE 'Misuse monitor detected free trial duplicate%' THEN true else false END as old_suspension,
				-- Extract counts
				CAST(REGEXP_EXTRACT(suspension.evidence, r'inactive: (\d+)') AS INT64) as inactive_count,
				CAST(REGEXP_EXTRACT(suspension.evidence, r'trial: (\d+)') AS INT64) as trial_count,
				CAST(REGEXP_EXTRACT(suspension.evidence, r'community: (\d+)') AS INT64) as community_count,
				CAST(REGEXP_EXTRACT(suspension.evidence, r'professional: (\d+)') AS INT64) as professional_count,
			FROM user
			CROSS JOIN UNNEST(suspensions) AS suspension
			LEFT JOIN chat_activity ON user.id = chat_activity.opaque_user_id
			LEFT JOIN subscription sub
				ON sub.subscription_id = user.orb_subscription_id
			LEFT JOIN verisoul_score ON LOWER(verisoul_score.email) = LOWER(user.email)
			WHERE
				(suspension.evidence LIKE '%inactive:%' -- if one exists, they all exist
					OR suspension.evidence LIKE 'Misuse monitor detected free trial duplicate%'
				)
				AND ARRAY_LENGTH(user.tenant_ids) = 1
				-- Drop those that would be suspended via Verisoul report features, but not those with no verisoul report.
				AND verisoul_score.bot IS NULL OR (
					verisoul_score.bot < 1
					AND (verisoul_score.multiple_accounts < 1 OR verisoul_score.accounts_linked < 40)
					AND NOT verisoul_score.is_disposable
				)
		)

	SELECT DISTINCT
		opaque_user_id,
		external_plan_id,
		inactive_count + trial_count + community_count + professional_count AS other_account_count,
		feature_vector_suspension,
		old_suspension,
		verisoul_decision,
	FROM suspended_user
	WHERE
		-- Exclude if Verisoul says it's a fake account
		(verisoul_decision IS NULL OR verisoul_decision != 'Fake')
		-- Exclude accounts where full free trial used
		AND (messages_used < 300 OR external_plan_id != 'orb_trial_plan')
		-- Include suspensions with up to 2 other duplicate accounts and feature vector suspensions
		AND (
			old_suspension
			OR feature_vector_suspension
			OR inactive_count + trial_count + community_count + professional_count <= 2
		)
		AND tenant_id IS NOT NULL AND external_plan_id IS NOT NULL
	`)

	// Set the default dataset ID in the query config
	query.QueryConfig.DefaultDatasetID = m.datasetName
	query.Parameters = []bigquery.QueryParameter{}

	// Run the query.
	it, err := query.Read(ctx)
	if err != nil {
		log.Error().Msgf("Query error: %v", err)
		return nil, fmt.Errorf("error running query: %w", err)
	}

	// Parse the results.
	var candidates []*forgivenessCandidate
	for {
		var row forgivenessCandidate
		err := it.Next(&row)
		if err == iterator.Done {
			break
		} else if err != nil {
			log.Error().Msgf("Query results error: %v", err)
			return nil, fmt.Errorf("error parsing query results: %w", err)
		} else {
			candidates = append(candidates, &row)
		}
	}
	log.Info().Msgf("Found %d candidates to forgive", len(candidates))
	return candidates, nil
}

func (m *FreeTrialForgivenessJob) forgiveCandidates(
	ctx context.Context,
	candidates []*forgivenessCandidate,
) error {
	dryRun, err := freeTrialForgivenessDryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not forgiving users. ***")
	}

	MisuseUsersFound.WithLabelValues(m.jobName).Set(float64(len(candidates)))

	sessionId := requestcontext.NewRandomRequestSessionId()

	for _, candidate := range candidates {

		// Get wildcard token for suspension operations (no tenant-specific token needed)
		token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
			ctx, "", "", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_RW, tokenscopesproto.Scope_AUTH_ADMIN},
		)
		if err != nil {
			log.Error().Err(err).Msg("Error getting wildcard token for suspension operations")
			return fmt.Errorf("error getting wildcard token: %w", err)
		}
		requestCtx := requestcontext.New(
			requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token)

		// Check if user is already included in the forgiveness campaign.
		userObj, err := m.authClient.GetUser(ctx, requestCtx, candidate.ID, nil)
		if err != nil {
			MisuseActionOutcome.WithLabelValues(m.jobName, "user_not_found", strconv.FormatBool(dryRun)).Inc()
			log.Error().Msgf("Error getting user %s: %v", candidate.ID, err)
			continue
		}

		// Skip teams and enterprise users
		if len(userObj.Tenants) != 1 {
			log.Error().Int("tenant_count", len(userObj.Tenants)).Msg("User does not have exactly 1 tenant")
			MisuseActionOutcome.WithLabelValues(m.jobName, "not_one_tenant", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		tenantID := userObj.Tenants[0]

		tenant, err := m.tenantCache.GetTenant(ctx, tenantID)
		if err != nil {
			log.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to get tenant")
			MisuseActionOutcome.WithLabelValues(m.jobName, "get_tenant_error", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if tenant == nil {
			log.Error().Str("tenant_id", tenantID).Msg("Tenant not found")
			MisuseActionOutcome.WithLabelValues(m.jobName, "tenant_not_found", strconv.FormatBool(dryRun)).Inc()
			continue
		}

		if tenantutil.IsEnterpriseTenant(tenant) {
			log.Error().Str("tenant_id", tenantID).Msg("User is on enterprise tenant")
			MisuseActionOutcome.WithLabelValues(m.jobName, "in_enterprise_tenant", strconv.FormatBool(dryRun)).Inc()
			continue
		}
		if tenantutil.IsSelfServeTeamTenant(tenant) {
			log.Error().Str("tenant_id", tenantID).Msg("User is on self-serve team tenant")
			MisuseActionOutcome.WithLabelValues(m.jobName, "in_self_serve_team", strconv.FormatBool(dryRun)).Inc()
			continue
		}

		// Determine if user is included in forgiveness campaign
		grantForgiveness := false
		if candidate.VerisoulDecision.Valid && candidate.VerisoulDecision.StringVal == "Real" {
			grantForgiveness = true
		}
		// Other user classes here

		if !grantForgiveness {
			MisuseActionOutcome.WithLabelValues(m.jobName, "skipped", strconv.FormatBool(dryRun)).Inc()
			continue
		}

		if !dryRun {
			// Tag user, then reset and remove suspensions.
			// Users without suspensions won't come up in future queries.

			suspensionIds := make([]string, 0, len(userObj.Suspensions))
			for _, suspension := range userObj.Suspensions {
				suspensionIds = append(suspensionIds, suspension.SuspensionId)
			}
			if len(suspensionIds) == 0 {
				MisuseActionOutcome.WithLabelValues(m.jobName, "no_suspensions", strconv.FormatBool(dryRun)).Inc()
				continue
			}

			err := m.authClient.AddUserTags(ctx, requestCtx, candidate.ID, "", []string{campaignTag})
			if err != nil {
				log.Error().Msgf("Error tagging user %s: %v", candidate.ID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "tagging_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}

			if candidate.ExternalPlanID == "orb_trial_plan" {
				_, err := m.authClient.AllowNewTrialForUsers(
					ctx, requestCtx, []string{candidate.ID},
				)
				if err != nil {
					log.Error().Msgf("Error allowing new trial for user %s: %v", candidate.ID, err)
					MisuseActionOutcome.WithLabelValues(m.jobName, "allow_trial_error", strconv.FormatBool(dryRun)).Inc()
					continue
				}
			}

			_, err = m.authClient.DeleteUserSuspensions(ctx, requestCtx, candidate.ID, "", suspensionIds)
			if err != nil {
				log.Error().Msgf("Error deleting suspensions for user %s: %v", candidate.ID, err)
				MisuseActionOutcome.WithLabelValues(m.jobName, "forgiveness_error", strconv.FormatBool(dryRun)).Inc()
				continue
			}
		}
		MisuseActionOutcome.WithLabelValues(m.jobName, "forgiven", strconv.FormatBool(dryRun)).Inc()
	}

	return nil
}

func (m *FreeTrialForgivenessJob) correctTaggedUsers(
	ctx context.Context,
) error {
	dryRun, err := freeTrialForgivenessDryRunFlag.Get(m.featureFlagHandle)
	if err != nil {
		log.Error().Err(err).Msgf("Error getting dry run flag, defaulting to true")
		dryRun = true
	}
	if dryRun {
		log.Info().Msg("*** DRY RUN! Not correcting tagged users. ***")
		return nil
	}

	// Walk through all users looking for tagged users in invalid states.
	sessionId := requestcontext.NewRandomRequestSessionId()

	token, err := m.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(
		ctx, "", "", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_RW, tokenscopesproto.Scope_AUTH_ADMIN},
	)
	if err != nil {
		log.Error().Err(err).Msg("Error getting wildcard token for tagged user processing")
		return fmt.Errorf("error getting token: %w", err)
	}

	var taggedUsers []*auth_entities.User
	var nextPageToken string

	for {
		requestCtx := requestcontext.New(
			requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token,
		)

		response, err := m.authClient.GetUsers(ctx, requestCtx, &pb.GetUsersRequest{
			PageSize:  1000,
			PageToken: nextPageToken,
		})
		if err != nil {
			return fmt.Errorf("error getting users: %w", err)
		}

		// Filter for tagged users with issues
		for _, user := range response.Users {
			if hasTag(user, campaignTag) {
				taggedUsers = append(taggedUsers, user)
			}
		}

		if response.NextPageToken == "" {
			break
		}
		nextPageToken = response.NextPageToken
	}

	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(), sessionId, "misuse-monitor", token,
	)

	for _, user := range taggedUsers {
		// If user is in a teams tenant, shouldn't have been tagged. Remove the tag.
		if len(user.Tenants) == 1 {
			tenantID := user.Tenants[0]
			tenant, err := m.tenantCache.GetTenant(ctx, tenantID)
			if err != nil {
				log.Error().Err(err).Str("tenant_id", tenantID).Msg("Failed to get tenant")
				continue
			}
			if tenant != nil && tenantutil.IsSelfServeTeamTenant(tenant) {
				err := m.authClient.RemoveUserTags(ctx, requestCtx, user.Id, "", []string{campaignTag})
				if err != nil {
					log.Error().Msgf("Error removing tag from user %s: %v", user.Id, err)
					continue
				}
				log.Info().Msgf("Removed tag %s from teams user %s", campaignTag, user.Id)
			}
		}

		// If user has an old suspension, remove it.
		// Newer suspensions indicate new bad behavior.
		if len(user.Suspensions) > 0 {
			suspensionIds := make([]string, 0, len(user.Suspensions))
			for _, suspension := range user.Suspensions {
				if suspension.CreatedTime.AsTime().Before(time.Date(2025, 7, 6, 0, 0, 0, 0, time.UTC)) {
					suspensionIds = append(suspensionIds, suspension.SuspensionId)
				}
			}
			if len(suspensionIds) > 0 {
				_, err := m.authClient.DeleteUserSuspensions(ctx, requestCtx, user.Id, "", suspensionIds)
				if err != nil {
					log.Error().Msgf("Error deleting suspensions for user %s: %v", user.Id, err)
				} else {
					log.Info().Msgf("Deleted suspensions for user %s", user.Id)
				}
			}
		}

		// If user is on trial plan, allow a new trial.
		if user.OrbSubscriptionId != "" {
			_, err := m.authClient.AllowNewTrialForUsers(
				ctx, requestCtx, []string{user.Id},
			)
			if err != nil {
				log.Error().Msgf("Error allowing new trial for user %s: %v", user.Id, err)
			} else {
				log.Info().Msgf("Allowed new trial for user %s", user.Id)
			}
		}
	}

	return nil
}

func hasTag(user *auth_entities.User, tag string) bool {
	for _, userTag := range user.Tags {
		if userTag == tag {
			return true
		}
	}
	return false
}
