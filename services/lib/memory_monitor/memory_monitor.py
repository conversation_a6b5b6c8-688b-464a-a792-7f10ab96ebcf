import structlog
import tracemalloc
import threading
from typing import Optional

log = structlog.get_logger()


class MemoryMonitor:
    """
    Memory monitoring using tracemalloc with background thread taking periodic snapshots.
    Designed for production use with feature flag support.
    """

    def __init__(self, snapshot_interval_seconds: int = 10 * 60):
        self.snapshot_interval = snapshot_interval_seconds
        self.initial_snapshot: Optional[tracemalloc.Snapshot] = None
        self.monitoring_thread: Optional[threading.Thread] = None
        self.stop_event = threading.Event()
        self._started = False

    def is_started(self):
        return self._started

    def start_monitoring(self):
        """Start memory monitoring if enabled via feature flag."""
        if self._started:
            log.warning("Memory monitoring already started")
            return

        log.info("Starting tracemalloc memory tracking")
        tracemalloc.start()
        self.initial_snapshot = tracemalloc.take_snapshot()

        # Start background monitoring thread
        self.monitoring_thread = threading.Thread(
            target=self._monitoring_loop, daemon=True, name="MemoryMonitor"
        )
        self.monitoring_thread.start()
        self._started = True
        log.info(
            f"Started memory monitoring with {self.snapshot_interval / 60:.1f} minute intervals"
        )

    def stop_monitoring(self):
        """Stop memory monitoring and cleanup."""
        if not self._started:
            return

        self.stop_event.set()
        if self.monitoring_thread and self.monitoring_thread.is_alive():
            self.monitoring_thread.join(timeout=5)

        if tracemalloc.is_tracing():
            tracemalloc.stop()

        self._started = False
        log.info("Stopped memory monitoring")

    def _monitoring_loop(self):
        """Background thread loop that takes periodic snapshots."""
        while not self.stop_event.wait(self.snapshot_interval):
            try:
                self._take_and_analyze_snapshot()
            except Exception as e:
                log.error(f"Error in memory monitoring loop: {e}")

    def _take_and_analyze_snapshot(self):
        """Take a snapshot and compare with initial state."""
        if not self.initial_snapshot:
            log.warning("No initial snapshot available")
            return

        current_snapshot = tracemalloc.take_snapshot()
        current, peak = tracemalloc.get_traced_memory()

        stats = current_snapshot.compare_to(self.initial_snapshot, "lineno")

        log_lines = [
            f"Memory usage - Current: {current/1024/1024:.2f} MB, Peak: {peak/1024/1024:.2f} MB",
            "=== Memory Usage Breakdown (Top 10 differences by line) ===",
        ]
        for i, stat in enumerate(stats[:10], 1):
            size_mb = stat.size_diff / 1024 / 1024
            log_lines.append(
                f"Memory {i:2d}: {stat.traceback.format()} | "
                f"Size diff: {size_mb:+.2f} MB | Count diff: {stat.count_diff:+d}"
            )

        log_lines.append("=== End of Memory Usage Report ===")

        for line in log_lines:
            log.info(line)
