load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "user_id_resolver_go",
    srcs = [
        "user_id_resolver.go",
    ],
    importpath = "github.com/augmentcode/augment/services/lib/user_id_resolver",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/client:auth_client_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:token_scopes_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
    ],
)

go_test(
    name = "user_id_resolver_test",
    srcs = [
        "user_id_resolver_test.go",
    ],
    embed = [":user_id_resolver_go"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/lib/request_context:request_context_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
    ],
)
