/**
 * Create a tuple type of length N.
 * @example
 * type T = TupleOf<number, 3>;
 * //   ^? [number, number, number]
 */
export type TupleOf<
  T,
  N extends number,
  R extends T[] = [],
> = R["length"] extends N ? R : TupleOf<T, N, [T, ...R]>;

/**
 * Split an array into chunks of a given size.
 * @example
 * type C = Chunk<[1, 2, 3, 4, 5, 6, 7, 8], 3>;
 * //   ^? [[1, 2, 3], [4, 5, 6], [7, 8]]
 */
export type Chunk<
  T extends any[],
  Size extends number,
  U extends any[] = [],
  R extends any[][] = [],
> = T extends [infer Head, ...infer Tail]
  ? U["length"] extends Size
    ? Chunk<T, Size, [], [...R, U]>
    : // Otherwise, pull one item off T into U
      Chunk<Tail, Size, [...U, Head], R>
  : U extends []
    ? R
    : [...R, U];
