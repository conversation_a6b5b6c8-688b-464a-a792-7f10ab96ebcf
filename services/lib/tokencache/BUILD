load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "tokencache_go",
    srcs = [
        "backend_service_token_cache.go",
    ],
    importpath = "github.com/augmentcode/augment/services/lib/tokencache",
    visibility = [
        "//services:__subpackages__",
    ],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/token_exchange:token_scopes_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_patrickmn_go_cache//:go-cache",
        "@com_github_rs_zerolog//log",
        "@org_golang_x_sync//singleflight",
    ],
)

go_test(
    name = "backend_service_token_cache_test",
    srcs = [
        "backend_service_token_cache_test.go",
    ],
    embed = [":tokencache_go"],
    deps = [
        "//base/go/secretstring:secretstring_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/token_exchange:token_scopes_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//mock",
        "@com_github_stretchr_testify//require",
    ],
)
