package tokencache

import (
	"context"
	"runtime"
	"sync"
	"testing"
	"time"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"

	token_exchange_client "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
)

func TestBackendServiceTokenCachingClient_CacheKeyConsistency(t *testing.T) {
	mockClient := token_exchange_client.NewMockTokenExchangeClient()
	cache := NewBackendServiceTokenCachingClient(mockClient, time.Hour)

	tenantID := "test-tenant"
	scopes1 := []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_R, tokenscopesproto.Scope_CONTENT_R}
	scopes2 := []tokenscopesproto.Scope{tokenscopesproto.Scope_CONTENT_R, tokenscopesproto.Scope_AUTH_R} // Same scopes, different order

	key1 := cache.generateCacheKey(tenantID, scopes1)
	key2 := cache.generateCacheKey(tenantID, scopes2)

	// Keys should be the same regardless of scope order
	assert.Equal(t, key1, key2, "Cache keys should be consistent regardless of scope order")

	// Different tenant should produce different key
	key3 := cache.generateCacheKey("different-tenant", scopes1)
	assert.NotEqual(t, key1, key3, "Different tenants should produce different cache keys")

	// Different scopes should produce different key
	scopes3 := []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_R}
	key4 := cache.generateCacheKey(tenantID, scopes3)
	assert.NotEqual(t, key1, key4, "Different scopes should produce different cache keys")

	mockClient.AssertExpectations(t)
}

func TestBackendServiceTokenCachingClient_CacheHit(t *testing.T) {
	mockClient := token_exchange_client.NewMockTokenExchangeClient()
	cache := NewBackendServiceTokenCachingClient(mockClient, time.Hour)

	ctx := context.Background()
	tenantID := "test-tenant"
	scopes := []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_R}
	testToken := "test-token"

	// Mock the call to return a token
	mockClient.On("GetSignedTokenForService", ctx, tenantID, scopes).Return(testToken, nil).Once()

	// First call should hit the upstream service
	token1, err := cache.GetSignedTokenForService(ctx, tenantID, scopes)
	require.NoError(t, err)
	assert.Equal(t, testToken, token1.Expose())

	// Second call should hit the cache (no additional mock call expected)
	token2, err := cache.GetSignedTokenForService(ctx, tenantID, scopes)
	require.NoError(t, err)
	assert.Equal(t, testToken, token2.Expose())

	mockClient.AssertExpectations(t)
}

func TestBackendServiceTokenCachingClient_DelegatesMethods(t *testing.T) {
	mockClient := token_exchange_client.NewMockTokenExchangeClient()
	cache := NewBackendServiceTokenCachingClient(mockClient, time.Hour)

	ctx := context.Background()

	// Test delegation of GetSignedTokenForUser
	mockClient.On("GetSignedTokenForUser", mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything, mock.Anything).Return("user-token", nil).Once()
	_, err := cache.GetSignedTokenForUser(ctx, "user-id", nil, nil, "tenant-id", nil, nil)
	require.NoError(t, err)

	// Test delegation of GetVerificationKey
	mockClient.On("GetVerificationKey", ctx).Return([]byte("verification-key"), nil).Once()
	_, err = cache.GetVerificationKey(ctx)
	require.NoError(t, err)

	// Test delegation of Close
	mockClient.On("Close").Return().Once()
	cache.Close()

	mockClient.AssertExpectations(t)
}

func TestBackendServiceTokenCachingClient_ConcurrentAccess(t *testing.T) {
	mockClient := token_exchange_client.NewMockTokenExchangeClient()
	cache := NewBackendServiceTokenCachingClient(mockClient, time.Hour)

	ctx := context.Background()
	tenantID := "test-tenant"
	scopes := []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_R}
	testToken := "test-token"

	// With single-flight pattern, only one upstream call should occur per cache key
	// Use Maybe() to handle potential test timing variations and context differences
	// Use mock.Anything for context to handle both regular and timeout contexts
	mockClient.On("GetSignedTokenForService", mock.Anything, tenantID, scopes).Return(testToken, nil).Maybe()

	const numGoroutines = 10
	results := make(chan string, numGoroutines)
	errors := make(chan error, numGoroutines)

	// Launch multiple goroutines simultaneously
	for i := 0; i < numGoroutines; i++ {
		go func() {
			token, err := cache.GetSignedTokenForService(ctx, tenantID, scopes)
			if err != nil {
				errors <- err
				return
			}
			results <- token.Expose()
		}()
	}

	// Collect all results
	for i := 0; i < numGoroutines; i++ {
		select {
		case token := <-results:
			assert.Equal(t, testToken, token)
		case err := <-errors:
			t.Fatalf("Unexpected error: %v", err)
		case <-time.After(5 * time.Second):
			t.Fatal("Test timed out")
		}
	}

	mockClient.AssertExpectations(t)
}

func TestBackendServiceTokenCachingClient_ConcurrentRefresh(t *testing.T) {
	mockClient := token_exchange_client.NewMockTokenExchangeClient()
	// Use a very short TTL to trigger refresh quickly
	cache := NewBackendServiceTokenCachingClient(mockClient, 100*time.Millisecond)

	ctx := context.Background()
	tenantID := "test-tenant"
	scopes := []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_R}

	// First token for initial cache population
	mockClient.On("GetSignedTokenForService", mock.Anything, tenantID, scopes).Return("token1", nil).Once()

	// Second token for refresh (single-flight reduces but may not eliminate all duplicate calls)
	mockClient.On("GetSignedTokenForService", mock.Anything, tenantID, scopes).Return("token2", nil).Maybe()

	// First call to populate cache
	token1, err := cache.GetSignedTokenForService(ctx, tenantID, scopes)
	require.NoError(t, err)
	assert.Equal(t, "token1", token1.Expose())

	// Wait for refresh threshold (80% of TTL = 80ms)
	time.Sleep(85 * time.Millisecond)

	// Multiple concurrent calls during refresh window
	const numGoroutines = 5
	results := make(chan string, numGoroutines)
	errors := make(chan error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func() {
			token, err := cache.GetSignedTokenForService(ctx, tenantID, scopes)
			if err != nil {
				errors <- err
				return
			}
			results <- token.Expose()
		}()
	}

	// Collect results
	var tokens []string
	for i := 0; i < numGoroutines; i++ {
		select {
		case token := <-results:
			tokens = append(tokens, token)
		case err := <-errors:
			t.Fatalf("Unexpected error: %v", err)
		case <-time.After(5 * time.Second):
			t.Fatalf("Timeout waiting for goroutine %d", i)
		}
	}

	// All tokens should be valid (either token1 or token2)
	for _, token := range tokens {
		assert.True(t, token == "token1" || token == "token2", "Token should be either token1 or token2, got: %s", token)
	}

	// Wait a bit more to ensure refresh completes
	time.Sleep(200 * time.Millisecond)

	mockClient.AssertExpectations(t)
}

func TestBackendServiceTokenCachingClient_SingleFlightBehavior(t *testing.T) {
	mockClient := token_exchange_client.NewMockTokenExchangeClient()
	cache := NewBackendServiceTokenCachingClient(mockClient, time.Hour)

	tenantID := "test-tenant"
	scopes := []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_R}
	testToken := "test-token"

	// Set up mock to expect exactly one call - use mock.Anything for context since each goroutine will have different context
	mockClient.On("GetSignedTokenForService", mock.Anything, tenantID, scopes).
		Run(func(args mock.Arguments) {
			// Yield to scheduler to allow other goroutines to run
			runtime.Gosched()
			time.Sleep(5 * time.Millisecond)
		}).
		Return(testToken, nil).Once()

	// Launch 10 concurrent goroutines, each with its own context (realistic scenario)
	const numGoroutines = 10
	results := make(chan string, numGoroutines)
	errors := make(chan error, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		go func(goroutineID int) {
			// Each goroutine gets its own context, simulating real-world usage
			ctx := context.WithValue(context.Background(), "goroutine_id", goroutineID)
			token, err := cache.GetSignedTokenForService(ctx, tenantID, scopes)
			if err != nil {
				errors <- err
				return
			}
			results <- token.Expose()
		}(i)
	}

	// Collect all results
	for i := 0; i < numGoroutines; i++ {
		select {
		case token := <-results:
			assert.Equal(t, testToken, token)
		case err := <-errors:
			t.Fatalf("Unexpected error: %v", err)
		case <-time.After(5 * time.Second):
			t.Fatal("Test timed out")
		}
	}

	// Verify that the mock was called exactly once (single-flight working)
	mockClient.AssertExpectations(t)
}

func TestBackendServiceTokenCachingClient_ConcurrentDifferentKeys(t *testing.T) {
	mockClient := token_exchange_client.NewMockTokenExchangeClient()
	cache := NewBackendServiceTokenCachingClient(mockClient, time.Hour)

	ctx := context.Background()

	// Set up different test scenarios
	scenarios := []struct {
		tenantID string
		scopes   []tokenscopesproto.Scope
		token    string
	}{
		{"tenant1", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_R}, "token1"},
		{"tenant2", []tokenscopesproto.Scope{tokenscopesproto.Scope_CONTENT_R}, "token2"},
		{"tenant1", []tokenscopesproto.Scope{tokenscopesproto.Scope_CONTENT_R}, "token3"},
		{"tenant3", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_R, tokenscopesproto.Scope_CONTENT_R}, "token4"},
	}

	// Set up mock expectations for each scenario
	for _, scenario := range scenarios {
		mockClient.On("GetSignedTokenForService", mock.Anything, scenario.tenantID, scenario.scopes).
			Run(func(args mock.Arguments) {
				// Yield to scheduler to allow other goroutines to run
				runtime.Gosched()
				time.Sleep(5 * time.Millisecond)
			}).
			Return(scenario.token, nil).Once()
	}

	// Number of goroutines per scenario
	numGoroutines := 10
	var wg sync.WaitGroup

	// Launch concurrent goroutines for each scenario
	for _, scenario := range scenarios {
		for i := 0; i < numGoroutines; i++ {
			wg.Add(1)
			go func(tenantID string, scopes []tokenscopesproto.Scope, expectedToken string) {
				defer wg.Done()

				token, err := cache.GetSignedTokenForService(ctx, tenantID, scopes)
				require.NoError(t, err)
				assert.Equal(t, expectedToken, token.Expose())
			}(scenario.tenantID, scenario.scopes, scenario.token)
		}
	}

	// Wait for all goroutines to complete
	wg.Wait()

	// All mock calls should have been made exactly once
	mockClient.AssertExpectations(t)
}

func TestBackendServiceTokenCachingClient_ConcurrentExpiredToken(t *testing.T) {
	mockClient := token_exchange_client.NewMockTokenExchangeClient()
	// Use a very short TTL to test expiration behavior
	cache := NewBackendServiceTokenCachingClient(mockClient, 10*time.Millisecond)

	ctx := context.Background()
	tenantID := "test-tenant"
	scopes := []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_R}

	// Set up mock to return different tokens for each call
	firstToken := "first-token"
	secondToken := "second-token"

	mockClient.On("GetSignedTokenForService", mock.Anything, tenantID, scopes).
		Run(func(args mock.Arguments) {
			// Yield to scheduler to allow other goroutines to run
			runtime.Gosched()
			time.Sleep(5 * time.Millisecond)
		}).
		Return(firstToken, nil).Once()
	mockClient.On("GetSignedTokenForService", mock.Anything, tenantID, scopes).
		Run(func(args mock.Arguments) {
			// Yield to scheduler to allow other goroutines to run
			runtime.Gosched()
			time.Sleep(5 * time.Millisecond)
		}).
		Return(secondToken, nil).Once()

	// First call should populate the cache
	token1, err := cache.GetSignedTokenForService(ctx, tenantID, scopes)
	require.NoError(t, err)
	assert.Equal(t, firstToken, token1.Expose())

	// Wait for the token to expire
	time.Sleep(20 * time.Millisecond)

	// Launch multiple concurrent requests after expiration
	numGoroutines := 10
	var wg sync.WaitGroup
	results := make([]string, numGoroutines)

	for i := 0; i < numGoroutines; i++ {
		wg.Add(1)
		go func(index int) {
			defer wg.Done()

			token, err := cache.GetSignedTokenForService(ctx, tenantID, scopes)
			require.NoError(t, err)
			results[index] = token.Expose()
		}(i)
	}

	// Wait for all goroutines to complete
	wg.Wait()

	// All results should be the second token (since first expired)
	for i, result := range results {
		assert.Equal(t, secondToken, result, "Goroutine %d should get the new token", i)
	}

	// Both mock calls should have been made
	mockClient.AssertExpectations(t)
}
