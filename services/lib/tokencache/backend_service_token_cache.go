// Package cache provides caching utilities for backend service tokens.
package tokencache

import (
	"context"
	"fmt"
	"sort"
	"strings"
	"sync"
	"time"

	"github.com/patrickmn/go-cache"
	"github.com/rs/zerolog/log"
	"golang.org/x/sync/singleflight"

	"github.com/augmentcode/augment/base/go/secretstring"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
)

// CachedToken represents a cached token with TTL-only expiration
type CachedToken struct {
	Token     secretstring.SecretString
	ExpiresAt time.Time
	RefreshAt time.Time // to allow for refresh-ahead functionality
}

// BackendServiceTokenCachingClient wraps a TokenExchangeClient and provides caching
// with refresh-ahead functionality using patrickmn/go-cache for r TTL-only expiration.
type BackendServiceTokenCachingClient struct {
	client tokenexchangeclient.TokenExchangeClient // The underlying client
	ttl    time.Duration

	// Cache storage using go-cache
	cache *cache.Cache

	// Background refresh management
	refreshMu      sync.Mutex
	refreshingKeys map[string]bool

	// Single-flight coordination to prevent duplicate upstream calls
	group singleflight.Group
}

// NewBackendServiceTokenCachingClient creates a new  caching client that wraps the provided
// TokenExchangeClient with TTL-only caching and refresh-ahead functionality.
func NewBackendServiceTokenCachingClient(
	client tokenexchangeclient.TokenExchangeClient,
	ttl time.Duration,
) *BackendServiceTokenCachingClient {
	return &BackendServiceTokenCachingClient{
		client:         client,
		ttl:            ttl,
		cache:          cache.New(ttl, ttl/10), // cleanup interval is 10% of TTL
		refreshingKeys: make(map[string]bool),
	}
}

// generateCacheKey creates a consistent cache key from tenant ID and scopes
func (c *BackendServiceTokenCachingClient) generateCacheKey(tenantID string, scopes []tokenscopesproto.Scope) string {
	// Sort scopes to ensure consistent key generation regardless of order
	scopeStrings := make([]string, len(scopes))
	for i, scope := range scopes {
		scopeStrings[i] = scope.String()
	}
	sort.Strings(scopeStrings)

	// Create a deterministic key - no need for hashing with patrickmn/go-cache
	return fmt.Sprintf("tenant:%s:scopes:%s", tenantID, strings.Join(scopeStrings, ","))
}

// fetchTokenFromUpstream fetches a new token from the upstream service
func (c *BackendServiceTokenCachingClient) fetchTokenFromUpstream(
	ctx context.Context,
	tenantID string,
	scopes []tokenscopesproto.Scope,
) (*CachedToken, error) {
	token, err := c.client.GetSignedTokenForService(ctx, tenantID, scopes)
	if err != nil {
		return nil, err
	}

	now := time.Now()
	expiresAt := now.Add(c.ttl)
	refreshAt := now.Add(time.Duration(float64(c.ttl) * 0.8)) // 80% of TTL

	return &CachedToken{
		Token:     token,
		ExpiresAt: expiresAt,
		RefreshAt: refreshAt,
	}, nil
}

// refreshTokenInBackground refreshes a token in the background
func (c *BackendServiceTokenCachingClient) refreshTokenInBackground(
	ctx context.Context,
	cacheKey string,
	tenantID string,
	scopes []tokenscopesproto.Scope,
) {
	c.refreshMu.Lock()
	defer c.refreshMu.Unlock()
	if c.refreshingKeys[cacheKey] {
		// Already refreshing this key
		return
	}
	c.refreshingKeys[cacheKey] = true

	go func() {
		defer func() {
			c.refreshMu.Lock()
			delete(c.refreshingKeys, cacheKey)
			c.refreshMu.Unlock()
		}()

		// Use background context for refresh to avoid cancellation
		backgroundCtx := context.Background()
		newCachedToken, err := c.fetchTokenFromUpstream(backgroundCtx, tenantID, scopes)
		if err != nil {
			log.Warn().Err(err).Str("cache_key", cacheKey).Msg("Failed to refresh token in background")
			return
		}

		// Store the refreshed token
		c.cache.Set(cacheKey, newCachedToken, c.ttl)
	}()
}

// GetSignedTokenForService returns a cached token or fetches a new one if needed
func (c *BackendServiceTokenCachingClient) GetSignedTokenForService(
	ctx context.Context,
	tenantID string,
	scopes []tokenscopesproto.Scope,
) (secretstring.SecretString, error) {
	cacheKey := c.generateCacheKey(tenantID, scopes)

	// Try to get from cache first
	if item, found := c.cache.Get(cacheKey); found {
		cachedToken := item.(*CachedToken)
		now := time.Now()

		// Check if token is still valid
		if now.Before(cachedToken.ExpiresAt) {
			// Token is still valid
			if now.After(cachedToken.RefreshAt) {
				// Time to refresh in background
				c.refreshTokenInBackground(ctx, cacheKey, tenantID, scopes)
			}
			return cachedToken.Token, nil
		}
	}

	// Use single-flight to ensure only one goroutine fetches the token for this key
	v, err, _ := c.group.Do(cacheKey, func() (interface{}, error) {
		newCachedToken, err := c.fetchTokenFromUpstream(ctx, tenantID, scopes)
		if err != nil {
			return nil, err
		}

		// Store in cache
		c.cache.Set(cacheKey, newCachedToken, c.ttl)
		return newCachedToken, nil
	})

	if err != nil {
		return secretstring.SecretString{}, err
	}

	cachedToken := v.(*CachedToken)
	return cachedToken.Token, nil
}

// Delegate all other methods to the underlying client
func (c *BackendServiceTokenCachingClient) GetSignedTokenForUser(
	ctx context.Context,
	userID string,
	opaqueUserID *authentitiesproto.UserId,
	userEmail *string,
	tenantID string,
	shardNamespace *string,
	additionalClaims map[string]any,
) (secretstring.SecretString, error) {
	return c.client.GetSignedTokenForUser(ctx, userID, opaqueUserID, userEmail, tenantID, shardNamespace, additionalClaims)
}

func (c *BackendServiceTokenCachingClient) GetSignedTokenForUserWithScopes(
	ctx context.Context,
	userID string,
	opaqueUserID *authentitiesproto.UserId,
	userEmail *string,
	tenantID string,
	shardNamespace *string,
	additionalClaims map[string]any,
	scopes []tokenscopesproto.Scope,
) (secretstring.SecretString, error) {
	return c.client.GetSignedTokenForUserWithScopes(ctx, userID, opaqueUserID, userEmail, tenantID, shardNamespace, additionalClaims, scopes)
}

func (c *BackendServiceTokenCachingClient) GetSignedTokenForServiceWithNamespace(
	ctx context.Context,
	tenantID string,
	shardNamespace string,
	scopes []tokenscopesproto.Scope,
) (secretstring.SecretString, error) {
	return c.client.GetSignedTokenForServiceWithNamespace(ctx, tenantID, shardNamespace, scopes)
}

func (c *BackendServiceTokenCachingClient) GetSignedTokenForIAPToken(
	ctx context.Context,
	iapToken secretstring.SecretString,
	tenantID string,
	scopes []tokenscopesproto.Scope,
	expiration time.Duration,
) (secretstring.SecretString, error) {
	return c.client.GetSignedTokenForIAPToken(ctx, iapToken, tenantID, scopes, expiration)
}

func (c *BackendServiceTokenCachingClient) GetVerificationKey(
	ctx context.Context,
) ([]byte, error) {
	return c.client.GetVerificationKey(ctx)
}

func (c *BackendServiceTokenCachingClient) Close() {
	c.client.Close()
}
