load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_library", "go_proto_library")
load("//tools/bzl:python.bzl", "py_proto_library")

go_library(
    name = "connect_forward_go",
    srcs = [
        "conn_factory.go",
        "connect_forward.go",
        "iap_connect_forward.go",
        "service_connect_forward.go",
        "user_connect_forward.go",
    ],
    importpath = "github.com/augmentcode/augment/services/lib/grpc/connect_forward",
    visibility = ["//services:__subpackages__"],
    deps = [
        ":forward_options_go_proto",
        "//base/cloud/iap:iap_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/proto/redact",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/lib/request_context:request_context_go",
        "//services/token_exchange:auth_options_go_proto",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange:token_scopes_go_proto",
        "//services/token_exchange/client:client_go",
        "//services/web_rpc_proxy:web_rpc_proxy_go_connect",
        "//services/web_rpc_proxy:web_rpc_proxy_go_connect_proto",
        "@com_connectrpc_connect//:connect",
        "@com_github_gorilla_mux//:go_default_library",
        "@com_github_rs_zerolog//log",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_google_grpc//credentials:go_default_library",
        "@org_golang_google_grpc//metadata:go_default_library",
        "@org_golang_google_grpc//status:go_default_library",
        "@org_golang_google_protobuf//encoding/protojson:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
        "@org_golang_google_protobuf//reflect/protoreflect:go_default_library",
    ],
)

proto_library(
    name = "forward_options_proto",
    srcs = ["forward_options.proto"],
    visibility = ["//visibility:public"],
    deps = [
        "@protobuf//:descriptor_proto",
    ],
)

go_proto_library(
    name = "forward_options_go_proto",
    generate_out_directory = "forward_options_proto",
    importpath = "github.com/augmentcode/augment/services/lib/grpc/connect_forward/forward_options_proto",
    proto = ":forward_options_proto",
    visibility = ["//visibility:public"],
)

py_proto_library(
    name = "forward_options_py_proto",
    protos = [":forward_options_proto"],
    visibility = ["//visibility:public"],
)
