package connectforward

import (
	"context"
	"fmt"
	"strings"
	"time"

	"connectrpc.com/connect"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/reflect/protoreflect"

	"github.com/augmentcode/augment/base/go/secretstring"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
)

type IAPForwarderFactoryService struct {
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient // Client for token exchange operations
	sd                  protoreflect.ServiceDescriptor          // Service descriptor for method introspection
	connFactory         GrpcConnFactory
	config              IAPForwarderConfig
}

func NewIAPForwarderFactoryService(
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient,
	connFactory GrpcConnFactory,
	fd protoreflect.FileDescriptor,
	serviceName string,
	config IAPForwarderConfig,
) *IAPForwarderFactoryService {
	sd := fd.Services().ByName(protoreflect.Name(serviceName))
	if sd == nil {
		return nil
	}
	return &IAPForwarderFactoryService{
		tokenExchangeClient: tokenExchangeClient,
		sd:                  sd,
		connFactory:         connFactory,
		config:              config,
	}
}

func (ffs *IAPForwarderFactoryService) GetServiceDescriptor() protoreflect.ServiceDescriptor {
	return ffs.sd
}

func (ffs *IAPForwarderFactoryService) GetConnFactory() GrpcConnFactory {
	return ffs.connFactory
}

type IAPForwarderConfig struct {
	RequestSource string
	ExtraScopes   []tokenscopesproto.Scope
}

// GetForwardUserInfo extracts user information from a ConnectRPC request
// using the configured extractUserInfo function.
func (ff *IAPForwarderFactoryService) GetForwardUserInfo(ctx context.Context, req *connect.Request[any]) (*ForwardUserInfo, error) {
	// get the email from the header
	email := req.Header().Get("X-Goog-Authenticated-User-Email")
	if email == "" {
		return nil, status.Errorf(codes.Unauthenticated, "email not found in request context")
	}
	return &ForwardUserInfo{
		UserID:    fmt.Sprintf("iap:%s", email),
		UserEmail: email,
	}, nil
}

// getUserToken creates a user-specific token for impersonation
func (ffs *IAPForwarderFactoryService) getToken(ctx context.Context, req *connect.Request[any], routeInfo *RouteInfo, scopes []tokenscopesproto.Scope) (secretstring.SecretString, error) {
	// get the iap token from the request from X-Goog-IAP-JWT-Assertion
	iapToken := req.Header().Get("X-Goog-IAP-JWT-Assertion")
	if iapToken == "" {
		return secretstring.New(""), status.Errorf(codes.Unauthenticated, "iap token not found in request context")
	}
	email := req.Header().Get("X-Goog-Authenticated-User-Email")
	if email == "" {
		return secretstring.New(""), status.Errorf(codes.Unauthenticated, "email not found in request context")
	}
	// strip the prefix
	email = email[strings.Index(email, ":")+1:]

	scopes = append(scopes, ffs.config.ExtraScopes...)

	log.Info().Msgf("Getting token for IAP user %s for scopes %v", email, scopes)

	tenantID := ""
	if routeInfo != nil {
		tenantID = routeInfo.TenantID
	}
	resp, err := ffs.tokenExchangeClient.GetSignedTokenForIAPToken(ctx,
		secretstring.New(iapToken), tenantID, scopes, 10*time.Minute,
	)
	if err != nil {
		return secretstring.New(""), fmt.Errorf("failed to get user token: %w", err)
	}
	return resp, nil
}

// GetServiceToken creates a user token with user impersonation for gRPC calls.
// It obtains a user-specific token with the required scopes
//
// Parameters:
//   - ctx: Request context
//   - req: ConnectRPC request containing headers and metadata
//   - userInfo: User information for token creation
//
// Returns a configured RequestContext or an error if token creation fails.
func (ffs *IAPForwarderFactoryService) GetServiceToken(ctx context.Context, userInfo *ForwardUserInfo, routeInfo *RouteInfo, req *connect.Request[any], scopes []tokenscopesproto.Scope) (*secretstring.SecretString, error) {
	token, err := ffs.getToken(ctx, req, routeInfo, scopes)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get token")
		return nil, connect.NewError(connect.CodeInternal, fmt.Errorf("failed to get token: %w", err))
	}

	return &token, nil
}

func (ffs *IAPForwarderFactoryService) GetRequestSource() string {
	return ffs.config.RequestSource
}
