package connectforward

import (
	"context"
	"fmt"

	"connectrpc.com/connect"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/reflect/protoreflect"

	"github.com/augmentcode/augment/base/go/secretstring"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
)

// ServiceForwarderFactoryService implements ForwarderFactoryService for service-based
// request forwarding. It can be used if a request is NOT on behalf of a specific user.
type ServiceForwarderFactoryService struct {
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient // Client for token exchange operations
	sd                  protoreflect.ServiceDescriptor          // Service descriptor for method introspection
	connFactory         GrpcConnFactory
	requestSource       string // Source identifier for requests
}

func NewServiceForwarderFactory(
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient,
	connFactory GrpcConnFactory,
	fd protoreflect.FileDescriptor,
	serviceName string,
	requestSource string,
) *ServiceForwarderFactoryService {
	sd := fd.Services().ByName(protoreflect.Name(serviceName))
	if sd == nil {
		return nil
	}
	return &ServiceForwarderFactoryService{
		tokenExchangeClient: tokenExchangeClient,
		sd:                  sd,
		connFactory:         connFactory,
		requestSource:       requestSource,
	}
}

func (ffs *ServiceForwarderFactoryService) GetServiceDescriptor() protoreflect.ServiceDescriptor {
	return ffs.sd
}

func (ffs *ServiceForwarderFactoryService) GetConnFactory() GrpcConnFactory {
	return ffs.connFactory
}

func (ff *ServiceForwarderFactoryService) GetForwardUserInfo(ctx context.Context, req *connect.Request[any]) (*ForwardUserInfo, error) {
	return nil, nil
}

// getToken creates a service token
func (ffs *ServiceForwarderFactoryService) getToken(ctx context.Context, routeInfo *RouteInfo, scopes []tokenscopesproto.Scope) (secretstring.SecretString, error) {
	tenantID := ""
	shardNamespace := ""
	if routeInfo != nil {
		tenantID = routeInfo.TenantID
		shardNamespace = routeInfo.ShardNamespace
	}
	resp, err := ffs.tokenExchangeClient.GetSignedTokenForServiceWithNamespace(ctx,
		tenantID, shardNamespace, scopes,
	)
	if err != nil {
		return secretstring.New(""), fmt.Errorf("failed to get service token: %w", err)
	}
	return resp, nil
}

// GetServiceToken creates a service token with the required scopes.
//
// Parameters:
//   - ctx: Request context
//   - req: ConnectRPC request containing headers and metadata
//   - userInfo: User information for token creation
//   - scopes: Required token scopes for authorization
//
// Returns a token or an error if token creation fails.
func (ffs *ServiceForwarderFactoryService) GetServiceToken(ctx context.Context, userInfo *ForwardUserInfo, routeInfo *RouteInfo, req *connect.Request[any], scopes []tokenscopesproto.Scope) (*secretstring.SecretString, error) {
	token, err := ffs.getToken(ctx, routeInfo, scopes)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get service token")
		return nil, connect.NewError(connect.CodeInternal, fmt.Errorf("failed to get service token: %w", err))
	}

	return &token, nil
}

func (ffs *ServiceForwarderFactoryService) GetRequestSource() string {
	return ffs.requestSource
}
