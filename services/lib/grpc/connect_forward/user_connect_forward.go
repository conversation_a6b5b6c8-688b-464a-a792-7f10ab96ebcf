package connectforward

import (
	"context"
	"fmt"

	"connectrpc.com/connect"
	"github.com/rs/zerolog/log"
	"google.golang.org/protobuf/reflect/protoreflect"

	"github.com/augmentcode/augment/base/go/secretstring"
	authentitiesproto "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
)

// UserForwarderFactoryService implements ForwarderFactoryService for user-based
// request forwarding. It handles user impersonation by creating user-specific tokens
// and forwarding ConnectRPC requests to gRPC services with proper authentication.
type UserForwarderFactoryService struct {
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient // Client for token exchange operations
	sd                  protoreflect.ServiceDescriptor          // Service descriptor for method introspection
	connFactory         GrpcConnFactory
	extractUserInfo     func(ctx context.Context, req *connect.Request[any]) *ForwardUserInfo // Function to extract user info from requests
	requestSource       string                                                                // Source identifier for requests
}

func NewUserForwarderFactory(
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient,
	fd protoreflect.FileDescriptor,
	serviceName string,
	connFactory GrpcConnFactory,
	requestSource string,
	extractUserInfo func(ctx context.Context, req *connect.Request[any]) *ForwardUserInfo,
) *UserForwarderFactoryService {
	sd := fd.Services().ByName(protoreflect.Name(serviceName))
	if sd == nil {
		return nil
	}
	return &UserForwarderFactoryService{
		tokenExchangeClient: tokenExchangeClient,
		sd:                  sd,
		connFactory:         connFactory,
		requestSource:       requestSource,
		extractUserInfo:     extractUserInfo,
	}
}

func (ffs *UserForwarderFactoryService) GetServiceDescriptor() protoreflect.ServiceDescriptor {
	return ffs.sd
}

func (ffs *UserForwarderFactoryService) GetConnFactory() GrpcConnFactory {
	return ffs.connFactory
}

// GetForwardUserInfo extracts user information from a ConnectRPC request
// using the configured extractUserInfo function.
func (ff *UserForwarderFactoryService) GetForwardUserInfo(ctx context.Context, req *connect.Request[any]) (*ForwardUserInfo, error) {
	return ff.extractUserInfo(ctx, req), nil
}

// getUserToken creates a user-specific token for impersonation
func (ffs *UserForwarderFactoryService) getUserToken(ctx context.Context, userInfo *ForwardUserInfo, scopes []tokenscopesproto.Scope) (secretstring.SecretString, error) {
	userID := userInfo.UserID
	email := userInfo.UserEmail
	shardNamespace := userInfo.ShardNamespace
	tenantID := userInfo.TenantID

	// Create opaque user ID
	opaqueUserID := &authentitiesproto.UserId{
		UserId:     userID,
		UserIdType: authentitiesproto.UserId_AUGMENT,
	}

	// Get user-specific token for impersonation
	resp, err := ffs.tokenExchangeClient.GetSignedTokenForUserWithScopes(ctx,
		email, opaqueUserID, &email, tenantID, &shardNamespace, nil,
		scopes,
	)
	if err != nil {
		return secretstring.New(""), fmt.Errorf("failed to get user token: %w", err)
	}
	return resp, nil
}

// GetServiceToken creates a user token with user impersonation for gRPC calls.
// It obtains a user-specific token with the required scopes
//
// Parameters:
//   - ctx: Request context
//   - req: ConnectRPC request containing headers and metadata
//   - userInfo: User information for token creation
//
// Returns a configured RequestContext or an error if token creation fails.
func (ffs *UserForwarderFactoryService) GetServiceToken(ctx context.Context, userInfo *ForwardUserInfo, routeInfo *RouteInfo, req *connect.Request[any], scopes []tokenscopesproto.Scope) (*secretstring.SecretString, error) {
	token, err := ffs.getUserToken(ctx, userInfo, scopes)
	if err != nil {
		log.Error().Err(err).Msg("Failed to get user token")
		return nil, connect.NewError(connect.CodeInternal, fmt.Errorf("failed to get user token: %w", err))
	}

	return &token, nil
}

func (ffs *UserForwarderFactoryService) GetRequestSource() string {
	return ffs.requestSource
}
