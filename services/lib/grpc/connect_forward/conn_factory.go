package connectforward

import (
	"context"
	"fmt"
	"strings"
	"sync"

	"github.com/rs/zerolog/log"
	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
)

type GrpcConnFactory interface {
	GetConn(ctx context.Context, serviceName string, namespace string) (*grpc.ClientConn, error)
	Close() error
}

type SingleGrpcConnFactory struct {
	conn *grpc.ClientConn
}

func NewSingleGrpcConnFactory(conn *grpc.ClientConn) *SingleGrpcConnFactory {
	return &SingleGrpcConnFactory{conn: conn}
}

func (s *SingleGrpcConnFactory) GetConn(ctx context.Context, serviceName string, namespace string) (*grpc.ClientConn, error) {
	return s.conn, nil
}

func (s *SingleGrpcConnFactory) Close() error {
	return s.conn.Close()
}

type ShardedGrpcConnFactory struct {
	serviceNameEndpointTemplates map[string]string
	credentials                  credentials.TransportCredentials
	conns                        map[string]map[string]*grpc.ClientConn
	connMutex                    sync.Mutex
}

func NewShardedGrpcConnFactory(serviceNameEndpointTemplates map[string]string, credentials credentials.TransportCredentials) *ShardedGrpcConnFactory {
	return &ShardedGrpcConnFactory{
		serviceNameEndpointTemplates: serviceNameEndpointTemplates,
		credentials:                  credentials,
		conns:                        make(map[string]map[string]*grpc.ClientConn),
	}
}

func (s *ShardedGrpcConnFactory) GetConn(ctx context.Context, serviceName string, namespace string) (*grpc.ClientConn, error) {
	s.connMutex.Lock()
	defer s.connMutex.Unlock()

	connMap, exists := s.conns[serviceName]
	if !exists {
		connMap = make(map[string]*grpc.ClientConn)
		s.conns[serviceName] = connMap
	}
	if conn, exists := connMap[namespace]; exists {
		return conn, nil
	}
	endpointTemplate, exists := s.serviceNameEndpointTemplates[serviceName]
	if !exists {
		return nil, fmt.Errorf("endpoint template not found for service %s", serviceName)
	}
	if endpointTemplate == "" {
		return nil, fmt.Errorf("endpoint template is empty for service %s", serviceName)
	}

	endpoint := fmt.Sprintf(endpointTemplate, namespace)
	log.Info().Msgf("Connecting to %s for namespace %s", endpoint, namespace)

	// Create gRPC connection options
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(s.credentials),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	// Apply round-robin load balancing only for headless services
	if strings.Contains(endpoint, "headless") {
		opts = append(opts, grpc.WithDefaultServiceConfig(`{"loadBalancingConfig": [{"round_robin":{}}]}`))
	}

	// Create connection
	conn, err := grpc.Dial(endpoint, opts...)
	if err != nil {
		return nil, fmt.Errorf("failed to connect to %s service for namespace %s: %v", serviceName, namespace, err)
	}
	connMap[namespace] = conn
	return conn, nil
}

func (s *ShardedGrpcConnFactory) Close() error {
	s.connMutex.Lock()
	defer s.connMutex.Unlock()

	var errors []string
	for serviceName, connMap := range s.conns {
		for namespace, conn := range connMap {
			if err := conn.Close(); err != nil {
				errors = append(errors, fmt.Sprintf("failed to close connection for service %s and namespace %s: %v", serviceName, namespace, err))
			}
		}
	}

	if len(errors) > 0 {
		return fmt.Errorf("errors closing connections: %s", strings.Join(errors, "; "))
	}

	return nil
}
