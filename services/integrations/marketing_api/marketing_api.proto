syntax = "proto3";
package marketing_api;

import "services/auth/central/server/auth.proto";
import "services/lib/grpc/connect_forward/forward_options.proto";
import "services/notification/notification.proto";

service MarketingApiService {
  rpc GrantFreeCreditsToUsers(auth.GrantFreeCreditsToUsersRequest) returns (auth.GrantFreeCreditsToUsersResponse) {
    option (forward_options.forward_method_name) = "auth.TeamManagementService/GrantFreeCreditsToUsers";
  }

  rpc CreateNotification(notification.CreateNotificationRequest) returns (notification.CreateNotificationResponse) {
    option (forward_options.forward_method_name) = "notification.NotificationService/CreateNotification";
  }
}
