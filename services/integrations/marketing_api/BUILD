load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_binary", "go_connect_proto_library", "go_library", "go_oci_image")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:python.bzl", "py_grpc_library")

go_library(
    name = "marketing_api_lib",
    srcs = [
        "main.go",
        "service.go",
    ],
    importpath = "github.com/augmentcode/augment/services/integrations/marketing_api",
    deps = [
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/logging/audit:audit_go",
        "//base/proto/redact",
        "//services/auth/central/client:auth_client_go",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/integrations/marketing_api:marketing_api_go_connect",
        "//services/integrations/marketing_api:marketing_api_go_connect_proto",
        "//services/lib/grpc/auth:grpc_auth_go",
        "//services/lib/grpc/connect_forward:connect_forward_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/lib/user_id_resolver:user_id_resolver_go",
        "//services/notification:notification_go_proto",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:auth_options_go_proto",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange:token_scopes_go_proto",
        "//services/token_exchange/client:client_go",
        "@com_connectrpc_connect//:connect",
        "@com_github_gorilla_mux//:go_default_library",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_google_grpc//metadata:go_default_library",
        "@org_golang_google_grpc//status:go_default_library",
        "@org_golang_google_protobuf//encoding/protojson:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
        "@org_golang_google_protobuf//reflect/protoreflect:go_default_library",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "marketing_api",
    embed = [":marketing_api_lib"],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":marketing_api",
)

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        "//services/integrations/marketing_api:image",
    ],
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

go_connect_proto_library(
    name = "marketing_api_go_connect",
    connectimportpath = "github.com/augmentcode/augment/services/integrations/marketing_api/connectproto",
    proto = ":marketing_api_proto",
    protoimportpath = "github.com/augmentcode/augment/services/integrations/marketing_api/proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_go_grpc",
        "//services/lib/grpc/connect_forward:forward_options_go_proto",
        "//services/notification:notification_go_proto",
    ],
)

proto_library(
    name = "marketing_api_proto",
    srcs = ["marketing_api.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_proto",
        "//services/lib/grpc/connect_forward:forward_options_proto",
        "//services/notification:notification_proto",
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "marketing_api_py_proto",
    protos = [":marketing_api_proto"],
    visibility = ["//services:__subpackages__"],
)
