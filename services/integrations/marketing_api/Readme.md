# Marketing API Service

This service provides secure endpoints for marketing-related actions that may be related to a campaign in our system such as granting users messages. Specifically, we are currently supporting Customer.io webhooks (https://docs.customer.io/journeys/webhooks-action/)

## How It Works

The Marketing API service acts as a secure gateway that:

1. **Receives HTTP requests** from Customer.io webhooks
2. **Verifies webhook signatures** using HMAC SHA-256 to ensure requests are authentic
3. **Translates HTTP requests to gRPC calls** using ConnectRPC protocol
4. **Logs all requests** for security auditing and compliance
5. **Forwards requests** to internal services using the forwarder factory:

This architecture allows external marketing platforms to trigger actions in our internal microservices while maintaining security boundaries and proper authentication flows.

- **Webhook Signature Verification**: Validates incoming webhooks using HMAC SHA-256 signature verification with Customer.io's signing key
- **ConnectRPC Integration**: Uses ConnectRPC for gRPC-over-HTTP communication with other services
- **Audit Logging**: Logs all API requests for security and compliance

## Example Usage

Here's an example of making a request to grant free credits to users:

```bash
curl -X POST \
  https://midas.augmentcode.com/marketing_api.MarketingApiService/GrantFreeCreditsToUsers \
  -H "Content-Type: application/json" \
  -H "X-CIO-Signature: <signature>" \
  -H "X-CIO-Timestamp: <timestamp>" \
  -d '{"user_credits":[{"user_id":"1234567890-1234567890","num_user_messages":100}],"idempotency_key":"test-key-12345"}'
```
