local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
{
  deployment: [
    {
      name: 'marketing-api',
      kubecfg: {
        target: '//services/integrations/marketing_api:kubecfg',
        task: [
          {
            cloud: 'GCP_US_CENTRAL1_PROD',
            env: 'STAGING',
            namespace: 'central-staging',
          },
        ],
      },
      deployment_schedule_name: 'EXPERIMENTAL',  // TODO: remove this once we are ready to deploy to prod
      health: {
        tier: 'TIER_2',
        experts: {
          users: ['dirk', 'surbhi'],
          slack_channel: '#team-growth',
        },
      },
    },
  ],
}
