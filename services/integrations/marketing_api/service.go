package main

import (
	"context"
	"fmt"

	"connectrpc.com/connect"
	"github.com/augmentcode/augment/base/logging/audit"
	authcentralproto "github.com/augmentcode/augment/services/auth/central/server/proto"
	"github.com/augmentcode/augment/services/lib/grpc/auth"
	connectforward "github.com/augmentcode/augment/services/lib/grpc/connect_forward"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	useridresolver "github.com/augmentcode/augment/services/lib/user_id_resolver"
	notificationproto "github.com/augmentcode/augment/services/notification/proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
)

// MarketingApiServiceHandler implements the MarketingApiServiceHandler ConnectRPC interface
type MarketingApiServiceHandler struct {
	userIDResolver      useridresolver.UserIDResolver
	forwarderFactory    *connectforward.ForwarderFactory
	auditLogger         *audit.AuditLogger
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient
	centralNamespace    string
}

// NewMarketingApiServiceHandler creates a new marketing api service handler
func NewMarketingApiServiceHandler(
	userIDResolver useridresolver.UserIDResolver,
	forwarderFactory *connectforward.ForwarderFactory,
	auditLogger *audit.AuditLogger,
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient,
	centralNamespace string,
) (*MarketingApiServiceHandler, error) {
	return &MarketingApiServiceHandler{
		userIDResolver:      userIDResolver,
		forwarderFactory:    forwarderFactory,
		auditLogger:         auditLogger,
		tokenExchangeClient: tokenExchangeClient,
		centralNamespace:    centralNamespace,
	}, nil
}

func (h *MarketingApiServiceHandler) GrantFreeCreditsToUsers(
	ctx context.Context,
	req *connect.Request[authcentralproto.GrantFreeCreditsToUsersRequest],
) (*connect.Response[authcentralproto.GrantFreeCreditsToUsersResponse], error) {
	authClaims, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)

	for _, userCredit := range req.Msg.UserCredits {
		if userCredit.UserId == "" {
			return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("user_id is required to grant credits"))
		}
		h.auditLogger.WriteAuditLog(
			authClaims,
			fmt.Sprintf("GrantFreeCreditsToUsers request received from marketing api"),
			audit.NewUser(userCredit.UserId),
			requestContext,
			audit.NewProtoRequest(req.Msg),
		)
	}

	resp := authcentralproto.GrantFreeCreditsToUsersResponse{}
	return connectforward.Forward(ctx, h.forwarderFactory, req, &resp,
		func(ctx context.Context, userInfo *connectforward.ForwardUserInfo, req *authcentralproto.GrantFreeCreditsToUsersRequest) (*connectforward.RouteInfo, error) {
			// grantFreeCredits is in a central service (auth-central), so we don't pass any tenant or shard namespace
			return &connectforward.RouteInfo{
				ShardNamespace: "",
				TenantID:       "",
			}, nil
		}, nil)
}

func (h *MarketingApiServiceHandler) CreateNotification(
	ctx context.Context,
	req *connect.Request[notificationproto.CreateNotificationRequest],
) (*connect.Response[notificationproto.CreateNotificationResponse], error) {
	authClaims, _ := auth.GetAugmentClaims(ctx)
	requestContext, _ := requestcontext.FromGrpcContext(ctx)
	if req.Msg.OpaqueUserId == nil {
		return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("opaque_user_id is required to create a notification"))
	}
	h.auditLogger.WriteAuditLog(
		authClaims,
		fmt.Sprintf("CreateNotification request received from marketing api for user %s", *req.Msg.OpaqueUserId),
		audit.NewUser(*req.Msg.OpaqueUserId),
		requestContext,
		audit.NewProtoRequest(req.Msg),
	)
	resp := notificationproto.CreateNotificationResponse{}
	return connectforward.Forward(ctx, h.forwarderFactory, req, &resp,
		func(ctx context.Context, userInfo *connectforward.ForwardUserInfo, req *notificationproto.CreateNotificationRequest) (*connectforward.RouteInfo, error) {
			if req.OpaqueUserId == nil || *req.OpaqueUserId == "" {
				return nil, connect.NewError(connect.CodeInvalidArgument, nil)
			}
			// need AUTH_R to get user info
			serviceToken, err := h.tokenExchangeClient.GetSignedTokenForService(ctx, "", []tokenscopesproto.Scope{
				tokenscopesproto.Scope_AUTH_R,
			})
			if err != nil {
				return nil, err
			}
			authCtx := requestcontext.New(
				requestcontext.NewRandomRequestId(),
				requestcontext.NewRandomRequestSessionId(),
				"marketing-api",
				serviceToken,
			)
			resolvedUserInfo, err := h.userIDResolver.ResolveUser(ctx, *req.OpaqueUserId, authCtx)
			if err != nil {
				return nil, err
			}
			req.TenantId = &resolvedUserInfo.TenantID
			return &connectforward.RouteInfo{
				ShardNamespace: resolvedUserInfo.ShardNamespace,
				TenantID:       resolvedUserInfo.TenantID,
			}, nil
		}, nil)
}
