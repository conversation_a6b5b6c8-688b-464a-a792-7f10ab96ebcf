package client

import (
	"context"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/settings/proto"
	"github.com/stretchr/testify/mock"
)

type MockSettingsClient struct {
	SettingsClient
	mock.Mock
}

func NewMockSettingsClient() *MockSettingsClient {
	return &MockSettingsClient{}
}

func (m *MockSettingsClient) GetTenantSettings(ctx context.Context, requestContext *requestcontext.RequestContext) (*pb.GetTenantSettingsResponse, error) {
	args := m.Called(ctx, requestContext)
	return args.Get(0).(*pb.GetTenantSettingsResponse), args.Error(1)
}

func (m *MockSettingsClient) UpdateTenantSettings(ctx context.Context, requestContext *requestcontext.RequestContext, settings *pb.TenantSettings, expectedVersion string) (*pb.UpdateTenantSettingsResponse, error) {
	args := m.Called(ctx, requestContext, settings, expectedVersion)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*pb.UpdateTenantSettingsResponse), args.Error(1)
}

func (m *MockSettingsClient) GetUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, userId *string) (*pb.GetUserSettingsResponse, error) {
	args := m.Called(ctx, requestContext, userId)
	return args.Get(0).(*pb.GetUserSettingsResponse), args.Error(1)
}

func (m *MockSettingsClient) UpdateUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, userId *string, settings *pb.UserSettings, expectedVersion string) (*pb.UpdateUserSettingsResponse, error) {
	args := m.Called(ctx, requestContext, userId, settings, expectedVersion)
	if args.Get(0) == nil {
		return nil, args.Error(1)
	}
	return args.Get(0).(*pb.UpdateUserSettingsResponse), args.Error(1)
}

func (m *MockSettingsClient) DeleteUserSettings(ctx context.Context, requestContext *requestcontext.RequestContext, userId *string) (*pb.DeleteUserSettingsResponse, error) {
	args := m.Called(ctx, requestContext, userId)
	return args.Get(0).(*pb.DeleteUserSettingsResponse), args.Error(1)
}

func (m *MockSettingsClient) Close() error {
	args := m.Called()
	return args.Error(0)
}
