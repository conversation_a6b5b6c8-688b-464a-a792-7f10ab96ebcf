syntax = "proto3";

package user_secrets;

import "google/protobuf/timestamp.proto";

// Service for managing user-defined secrets
service UserSecretsService {
  // Create or update a user secret
  rpc UpsertUserSecret(UpsertUserSecretRequest) returns (UpsertUserSecretResponse);

  // Get a specific user secret by exact name
  rpc GetUserSecret(GetUserSecretRequest) returns (GetUserSecretResponse);

  // List/search user secrets with flexible filtering
  rpc ListUserSecrets(ListUserSecretsRequest) returns (ListUserSecretsResponse);

  // Delete a user secret
  rpc DeleteUserSecret(DeleteUserSecretRequest) returns (DeleteUserSecretResponse);
}

// Individual secret entry (stored per row in Bigtable)
message UserSecret {
  // The name/key of the secret
  string name = 1 [debug_redact = true];

  // The secret value (will be redacted in logs)
  string value = 2 [debug_redact = true];

  // Arbitrary tags for grouping and organization
  map<string, string> tags = 3 [debug_redact = true];

  // When this secret was created
  google.protobuf.Timestamp created_at = 4;

  // When this secret was last updated
  google.protobuf.Timestamp updated_at = 5;

  // Optional description for the secret
  string description = 6 [debug_redact = true];

  // Version of this specific secret entry
  string version = 7;

  // Size of the secret value in bytes
  int32 value_size_bytes = 8;
}

// Metadata about user's secrets collection (stored in user settings)
message UserSecretsMetadata {
  // Total count of secrets
  int32 total_count = 1;

  // When secrets were last modified
  google.protobuf.Timestamp last_modified = 2;

  // Total size of all secrets (for quota enforcement)
  int64 total_size_bytes = 3;

  // Maximum allowed secrets per user
  int32 max_secrets_limit = 4;

  // Maximum total size allowed
  int64 max_total_size_bytes = 5;
}

// Request/Response messages

message UpsertUserSecretRequest {
  string name = 1 [debug_redact = true];
  string value = 2 [debug_redact = true];
  map<string, string> tags = 3 [debug_redact = true];
  string description = 4 [debug_redact = true];
  // Optional: Version for optimistic concurrency control. If provided, the update will only
  // succeed if the current version matches this value. Leave empty to skip version checking.
  string expected_version = 5;

  // Validation limits
  int32 max_value_size_bytes = 6; // Default 4KB, configurable
}

message UpsertUserSecretResponse {
  UserSecret secret = 1;
}

message GetUserSecretRequest {
  string name = 1 [debug_redact = true];
}

message GetUserSecretResponse {
  UserSecret secret = 1;
}

message ListUserSecretsRequest {
  // Optional name pattern (regex) to filter by secret names
  string name_pattern = 1 [debug_redact = true];

  // Optional tag filters - can specify keys with or without values
  // Note: All filters are AND'd together - a secret must match ALL specified filters to be included
  repeated TagFilter tag_filters = 2;

  // Whether to include secret values in response (default: false for security)
  bool include_values = 3;

  // Pagination
  int32 page_size = 4;
  string page_token = 5;
}

message ListUserSecretsResponse {
  repeated UserSecret secrets = 1; // Full secrets (with or without values based on include_values)
  string next_page_token = 2;
  int32 total_count = 3;
}

message TagFilter {
  // Tag key to filter on (required)
  string key = 1 [debug_redact = true];

  // Optional values for this tag key
  // If empty, matches any secret that has this tag key (regardless of value)
  // If specified, matches secrets where tag key has one of these values
  repeated string values = 2 [debug_redact = true];

  // Whether this is an OR (any value matches) or AND (all values must match)
  // Only relevant when multiple values are specified
  bool match_any = 3;

  // Whether to invert the filter logic (NOT matching)
  // If true, matches secrets that DON'T match the filter criteria
  bool invert = 4;
}

message DeleteUserSecretRequest {
  string name = 1 [debug_redact = true];
  // Optional: Version for optimistic concurrency control. If provided, the delete will only
  // succeed if the current version matches this value. Leave empty to skip version checking.
  string expected_version = 2;
}

message DeleteUserSecretResponse {
  bool deleted = 1;
}
