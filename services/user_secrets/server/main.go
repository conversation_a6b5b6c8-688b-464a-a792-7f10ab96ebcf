package main

import (
	"encoding/json"
	"flag"
	"fmt"
	"net"
	"net/http"
	"os"
	"os/signal"
	"syscall"
	"time"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promhttp"
	"google.golang.org/grpc"
	"google.golang.org/grpc/health"
	"google.golang.org/grpc/health/grpc_health_v1"

	grpc_auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	grpc_recovery "github.com/augmentcode/augment/services/lib/grpc/recovery"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	tokenexchange "github.com/augmentcode/augment/services/token_exchange/client"
	usersecretsroto "github.com/augmentcode/augment/services/user_secrets/proto"
	"github.com/rs/zerolog/log"
)

type Config struct {
	Port                        int                    `json:"port"`
	SettingsEndpoint            string                 `json:"settings_endpoint"`
	BigtableProxyEndpoint       string                 `json:"bigtable_proxy_endpoint"`
	TokenExchangeEndpoint       string                 `json:"token_exchange_endpoint"`
	DefaultMaxSecretsPerUser    int32                  `json:"default_max_secrets_per_user"`
	DefaultMaxTotalSizeBytes    int64                  `json:"default_max_total_size_bytes"`
	DefaultMaxSecretSizeBytes   int32                  `json:"default_max_secret_size_bytes"`
	ClientMTLS                  tlsconfig.ClientConfig `json:"client_mtls"`
	ServerMTLS                  tlsconfig.ServerConfig `json:"server_mtls"`
	CentralClientMTLS           tlsconfig.ClientConfig `json:"central_client_mtls"`
	CentralServerMTLS           tlsconfig.ServerConfig `json:"central_server_mtls"`
	ShutdownGracePeriodS        int                    `json:"shutdown_grace_period_s"`
	FeatureFlagsSDKKeyPath      *string                `json:"feature_flags_sdk_key_path"`
	DynamicFeatureFlagsEndpoint *string                `json:"dynamic_feature_flags_endpoint"`
}

func main() {
	configPath := flag.String("config", "", "Path to config file")
	flag.Parse()

	if *configPath == "" {
		log.Fatal().Msg("--config is required")
	}

	// Load configuration
	configData, err := os.ReadFile(*configPath)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to read config file")
	}

	var config Config
	if err := json.Unmarshal(configData, &config); err != nil {
		log.Fatal().Err(err).Msg("Failed to parse config")
	}

	// Initialize tracing (commented out for now)
	// ctx := context.Background()
	// tracing.InitTracing(ctx, "user-secrets-server")

	// Create server
	server, err := NewUserSecretsServer(&config)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create server")
	}

	// Setup gRPC server with interceptors
	var grpcOptions []grpc.ServerOption

	// Add TLS if configured
	if config.ServerMTLS.CertPath != "" {
		tlsConfig, err := tlsconfig.GetServerTls([]*tlsconfig.ServerConfig{&config.ServerMTLS})
		if err != nil {
			log.Fatal().Err(err).Msg("Failed to load TLS config")
		}
		grpcOptions = append(grpcOptions, grpc.Creds(tlsConfig))
	}

	// Create token exchange client for authentication
	tokenExchangeClientCreds, err := tlsconfig.GetClientTls(&config.CentralClientMTLS)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create token exchange client credentials")
	}

	// Use dedicated token exchange service
	tokenExchangeEndpoint := "token-exchange-central-svc.dev-marcmac:50051"
	tokenExchangeClient, err := tokenexchange.New(tokenExchangeEndpoint, "dev-marcmac", tokenExchangeClientCreds)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to create token exchange client")
	}
	defer tokenExchangeClient.Close()

	// Create authentication interceptor
	serviceTokenAuth := grpc_auth.NewServiceTokenAuth(tokenExchangeClient)
	authInterceptor := grpc_auth.NewAuthServerInterceptor(serviceTokenAuth.ValidateAccess)

	// Add interceptors
	grpcOptions = append(grpcOptions,
		grpc.ChainUnaryInterceptor(
			grpc_recovery.UnaryServerInterceptor(),
			authInterceptor.Intercept,
		),
		grpc.ChainStreamInterceptor(
			grpc_recovery.StreamingServerInterceptor(),
			authInterceptor.StreamIntercept,
		),
	)

	grpcServer := grpc.NewServer(grpcOptions...)

	// Register services
	usersecretsroto.RegisterUserSecretsServiceServer(grpcServer, server)

	// Register health service
	healthServer := health.NewServer()
	healthServer.SetServingStatus("user-secrets-server", grpc_health_v1.HealthCheckResponse_SERVING)
	grpc_health_v1.RegisterHealthServer(grpcServer, healthServer)

	// Start metrics server
	go func() {
		registry := prometheus.NewRegistry()
		registry.MustRegister(prometheus.NewGoCollector())
		registry.MustRegister(prometheus.NewProcessCollector(prometheus.ProcessCollectorOpts{}))

		mux := http.NewServeMux()
		mux.Handle("/metrics", promhttp.HandlerFor(registry, promhttp.HandlerOpts{}))

		metricsServer := &http.Server{
			Addr:    ":8080",
			Handler: mux,
		}

		log.Info().Msg("Starting metrics server on :8080")
		if err := metricsServer.ListenAndServe(); err != nil {
			log.Error().Err(err).Msg("Metrics server error")
		}
	}()

	// Start gRPC server
	listener, err := net.Listen("tcp", fmt.Sprintf(":%d", config.Port))
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to listen")
	}

	log.Info().Int("port", config.Port).Msg("Starting User Secrets server")

	// Handle graceful shutdown
	sigChan := make(chan os.Signal, 1)
	signal.Notify(sigChan, syscall.SIGINT, syscall.SIGTERM)

	go func() {
		<-sigChan
		log.Info().Msg("Received shutdown signal, gracefully stopping...")

		// Stop accepting new connections
		grpcServer.GracefulStop()

		// Give some time for existing requests to complete
		time.Sleep(time.Duration(config.ShutdownGracePeriodS) * time.Second)

		log.Info().Msg("Server stopped")
		os.Exit(0)
	}()

	// Start serving
	if err := grpcServer.Serve(listener); err != nil {
		log.Fatal().Err(err).Msg("Failed to serve")
	}
}
