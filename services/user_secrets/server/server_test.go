package main

import (
	"testing"

	"github.com/stretchr/testify/assert"

	user_secrets_pb "github.com/augmentcode/augment/services/user_secrets/proto"
)

func TestSecretNameValidation(t *testing.T) {
	// Test valid names (should match the regex) // pragma: allowlist secret
	validNames := []string{
		"github-api-token",
		"database_password",
		"JWT_SECRET_KEY",
		"api-key-v2",
		"user123_token",
		"a",          // single letter
		"A",          // single uppercase letter
		"mySecret",   // camelCase
		"MY_SECRET",  // UPPER_CASE
		"secret-123", // ends with number
		"secret_",    // ends with underscore
		"secret-",    // ends with hyphen
	}

	for _, name := range validNames {
		t.Run("valid_"+name, func(t *testing.T) {
			assert.True(t, namePattern.MatchString(name), "Name '%s' should be valid but was rejected", name)
		})
	}

	// Test invalid names (should NOT match the regex) // pragma: allowlist secret
	invalidNames := []string{
		"123-secret",       // starts with number // pragma: allowlist secret
		"-secret",          // starts with hyphen // pragma: allowlist secret
		"_secret",          // starts with underscore // pragma: allowlist secret
		"github api token", // contains spaces
		"api@key",          // contains @
		"secret.name",      // contains . // pragma: allowlist secret
		"key:value",        // contains :
		"my/secret",        // contains / // pragma: allowlist secret
		"secret$",          // contains $ // pragma: allowlist secret
		"secret#tag",       // contains # // pragma: allowlist secret
		"secret%",          // contains % // pragma: allowlist secret
		"secret+plus",      // contains + // pragma: allowlist secret
		"secret=value",     // contains = // pragma: allowlist secret
		"secret[0]",        // contains brackets // pragma: allowlist secret
		"secret{}",         // contains braces // pragma: allowlist secret
		"secret|pipe",      // contains pipe // pragma: allowlist secret
		"secret\\back",     // contains backslash // pragma: allowlist secret
		"secret;semi",      // contains semicolon // pragma: allowlist secret
		"secret'quote",     // contains single quote // pragma: allowlist secret
		"secret\"double",   // contains double quote // pragma: allowlist secret
		"secret<less",      // contains < // pragma: allowlist secret
		"secret>more",      // contains > // pragma: allowlist secret
		"secret,comma",     // contains comma // pragma: allowlist secret
		"secret?question",  // contains ? // pragma: allowlist secret
		"",                 // empty string
	}

	for _, name := range invalidNames {
		t.Run("invalid_"+name, func(t *testing.T) {
			assert.False(t, namePattern.MatchString(name), "Name '%s' should be invalid but was accepted", name)
		})
	}
}

func TestUserSecretsServer_MatchesTagFilters(t *testing.T) {
	server := &UserSecretsServer{}

	secret := &user_secrets_pb.UserSecret{
		Name: "test-secret",
		Tags: map[string]string{
			"team":        "backend",
			"environment": "production",
			"service":     "api",
		},
	}

	// Test empty filters (should match)
	assert.True(t, server.matchesTagFilters(secret, nil))
	assert.True(t, server.matchesTagFilters(secret, []*user_secrets_pb.TagFilter{}))

	// Test key existence filter
	filter := &user_secrets_pb.TagFilter{
		Key: "team",
	}
	assert.True(t, server.matchesTagFilters(secret, []*user_secrets_pb.TagFilter{filter}))

	// Test key existence filter (non-existent key)
	filter = &user_secrets_pb.TagFilter{
		Key: "nonexistent",
	}
	assert.False(t, server.matchesTagFilters(secret, []*user_secrets_pb.TagFilter{filter}))

	// Test value matching
	filter = &user_secrets_pb.TagFilter{
		Key:    "team",
		Values: []string{"backend"},
	}
	assert.True(t, server.matchesTagFilters(secret, []*user_secrets_pb.TagFilter{filter}))

	// Test value not matching
	filter = &user_secrets_pb.TagFilter{
		Key:    "team",
		Values: []string{"frontend"},
	}
	assert.False(t, server.matchesTagFilters(secret, []*user_secrets_pb.TagFilter{filter}))

	// Test invertible filter
	filter = &user_secrets_pb.TagFilter{
		Key:    "team",
		Values: []string{"frontend"},
		Invert: true,
	}
	assert.True(t, server.matchesTagFilters(secret, []*user_secrets_pb.TagFilter{filter}))
}
