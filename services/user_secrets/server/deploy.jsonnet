local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';

function(env, namespace, cloud, namespace_config)
  local appName = 'user-secrets-server';

  local serviceAccount = gcpLib.createServiceAccount(
    app=appName,
    cloud=cloud,
    env=env,
    namespace=namespace,
  );

  local serverCert = certLib.createServerCert(
    name='%s-server-certificate' % appName,
    namespace=namespace,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNames(appName),
    volumeName='server-certs'
  );

  local clientCert = certLib.createClientCert(
    name='%s-client-certificate' % appName,
    namespace=namespace,
    appName=appName,
    volumeName='client-certs'
  );

  local centralServerCert = certLib.createCentralServerCert(
    name='%s-central-server-certificate' % appName,
    namespace=namespace,
    appName=appName,
    dnsNames=grpcLib.grpcServiceNamespaceNames(appName, namespace=namespace),
    env=env,
    volumeName='central-certs'
  );

  local centralClientCert = certLib.createCentralClientCert(
    name='%s-central-client-certificate' % appName,
    namespace=namespace,
    appName=appName,
    env=env,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace),
    volumeName='central-client-certs'
  );

  local configMap = configMapLib.createConfigMap(
    appName=appName,
    namespace=namespace,
    config={
      port: 50051,
      feature_flags_sdk_key_path: null,
      dynamic_feature_flags_endpoint: null,
      auth_config: {
        auth_endpoint: 'auth-central-server.%s.svc.cluster.local:50051' % namespace,
        client_mtls_config: centralClientCert.config,
      },
      settings_endpoint: 'settings-server.%s.svc.cluster.local:50051' % namespace,
      bigtable_proxy_endpoint: 'bigtable-proxy-headless-svc:50051',

      // Default limits
      default_max_secrets_per_user: 100,
      default_max_total_size_bytes: 1024 * 1024,  // 1MB
      default_max_secret_size_bytes: 4096,  // 4KB

      client_mtls: clientCert.config,
      server_mtls: serverCert.config,
      central_client_mtls: centralClientCert.config,
      central_server_mtls: centralServerCert.config,
      shutdown_grace_period_s: 25,
    }
  );

  local container = {
    name: appName,
    target: {
      name: '//services/user_secrets/server:image',
      dst: 'user-secrets-server',
    },
    args: [
      '--config',
      configMap.filename,
    ],
    ports: [
      {
        containerPort: 50051,
        name: 'grpc-svc',
      },
    ],
    env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)),
    volumeMounts: lib.flatten([
      configMap.volumeMountDef,
      serverCert.volumeMountDef,
      clientCert.volumeMountDef,
      centralClientCert.volumeMountDef,
      centralServerCert.volumeMountDef,
    ]),
  };

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      replicas: if env == 'DEV' then 1 else 2,
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: {
          serviceAccountName: serviceAccount.name,
          priorityClassName: cloudInfo.envToPriorityClass(env),
          containers: [container],
          volumes: lib.flatten([
            configMap.podVolumeDef,
            serverCert.podVolumeDef,
            clientCert.podVolumeDef,
            centralClientCert.podVolumeDef,
            centralServerCert.podVolumeDef,
          ]),
        },
      },
    },
  };

  local services = grpcLib.grpcService(appName=appName, namespace=namespace);

  lib.flatten([
    serviceAccount.objects,
    serverCert.objects,
    clientCert.objects,
    centralServerCert.objects,
    centralClientCert.objects,
    configMap.objects,
    deployment,
    services,
  ])
