package main

import (
	"context"
	"encoding/json"
	"fmt"
	"strings"
	"time"

	googlepb "cloud.google.com/go/bigtable/apiv2/bigtablepb"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	bigtable_pb "github.com/augmentcode/augment/services/bigtable_proxy/proto"
	grpc_auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	user_secrets_pb "github.com/augmentcode/augment/services/user_secrets/proto"
)

// Bigtable-specific constants
const (
	TableName      = "user_secrets"
	ColumnFamily   = "data"
	ValueColumn    = "value"
	MetadataColumn = "metadata" // pragma: allowlist secret
)

// SecretData represents the data stored in Bigtable
type SecretData struct {
	Value       string            `json:"value"`
	Description string            `json:"description"`
	Tags        map[string]string `json:"tags"`
	CreatedAt   time.Time         `json:"created_at"`
	UpdatedAt   time.Time         `json:"updated_at"`
	Version     int64             `json:"version"`
}

// Bigtable helper methods

func (s *UserSecretsServer) extractTenantAndRequestContext(ctx context.Context) (string, *requestcontext.RequestContext, error) {
	// Extract AugmentClaims from context (set by auth interceptor)
	claims, ok := grpc_auth.GetAugmentClaims(ctx)
	if !ok {
		return "", nil, status.Error(codes.Unauthenticated, "no authentication claims found")
	}

	// Get tenant ID from claims
	if claims.TenantID == "" {
		return "", nil, status.Error(codes.Unauthenticated, "tenant ID not found in claims")
	}

	// Create request context from gRPC context
	requestCtx, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		return "", nil, fmt.Errorf("failed to create request context: %w", err)
	}

	return claims.TenantID, requestCtx, nil
}

func (s *UserSecretsServer) getSecretFromBigtable(ctx context.Context, rowKey string) (*SecretData, error) {
	// Extract tenant ID and create request context
	tenantID, requestCtx, err := s.extractTenantAndRequestContext(ctx)
	if err != nil {
		return nil, err
	}

	// Create row set for the specific row
	rows := &googlepb.RowSet{
		RowKeys: [][]byte{[]byte(rowKey)},
	}

	// Create filter for the column family
	filter := &googlepb.RowFilter{
		Filter: &googlepb.RowFilter_FamilyNameRegexFilter{
			FamilyNameRegexFilter: ColumnFamily,
		},
	}

	// Read from Bigtable using dedicated USER_SECRETS table
	resultRows, err := s.bigtableClient.ReadRows(ctx, tenantID, bigtable_pb.TableName_USER_SECRETS, rows, filter, 1, requestCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to read from bigtable: %w", err)
	}

	// Process response
	for _, row := range resultRows {
		if string(row.RowKey) == rowKey {
			// Found the row, extract data
			for _, cell := range row.Cells {
				if cell.FamilyName == ColumnFamily && string(cell.Qualifier) == MetadataColumn {
					// Parse the JSON data
					var secretData SecretData
					if err := json.Unmarshal(cell.Value, &secretData); err != nil {
						return nil, fmt.Errorf("failed to unmarshal secret data: %w", err)
					}
					return &secretData, nil
				}
			}
		}
	}

	// Secret not found
	return nil, status.Error(codes.NotFound, "secret not found")
}

func (s *UserSecretsServer) storeSecretInBigtable(ctx context.Context, rowKey string, secretData *SecretData) error {
	// Extract tenant ID and create request context
	tenantID, requestCtx, err := s.extractTenantAndRequestContext(ctx)
	if err != nil {
		return err
	}

	// Serialize secret data to JSON
	data, err := json.Marshal(secretData)
	if err != nil {
		return fmt.Errorf("failed to marshal secret data: %w", err)
	}

	// Create mutations for both metadata and value
	now := time.Now().UnixMicro()
	mutations := []*googlepb.Mutation{
		{
			Mutation: &googlepb.Mutation_SetCell_{
				SetCell: &googlepb.Mutation_SetCell{
					FamilyName:      ColumnFamily,
					ColumnQualifier: []byte(MetadataColumn),
					TimestampMicros: now,
					Value:           data,
				},
			},
		},
		{
			Mutation: &googlepb.Mutation_SetCell_{
				SetCell: &googlepb.Mutation_SetCell{
					FamilyName:      ColumnFamily,
					ColumnQualifier: []byte(ValueColumn),
					TimestampMicros: now,
					Value:           []byte(secretData.Value),
				},
			},
		},
	}

	// Create mutate rows entry
	entries := []*googlepb.MutateRowsRequest_Entry{
		{
			RowKey:    []byte(rowKey),
			Mutations: mutations,
		},
	}

	// Write to Bigtable using dedicated USER_SECRETS table
	responses, err := s.bigtableClient.MutateRows(ctx, tenantID, bigtable_pb.TableName_USER_SECRETS, entries, requestCtx)
	if err != nil {
		return fmt.Errorf("failed to write to bigtable: %w", err)
	}

	// Check if the mutation was successful
	for _, response := range responses {
		for _, entry := range response.Entries {
			if entry.Status != nil && entry.Status.Code != 0 {
				return fmt.Errorf("bigtable mutation failed: %s", entry.Status.Message)
			}
		}
	}

	return nil
}

func (s *UserSecretsServer) listUserSecretsFromBigtable(ctx context.Context, userID string, includeValues bool) ([]*user_secrets_pb.UserSecret, error) {
	// Extract tenant ID and create request context
	tenantID, requestCtx, err := s.extractTenantAndRequestContext(ctx)
	if err != nil {
		return nil, err
	}

	// Create row range for user secrets (prefix scan)
	rowPrefix := fmt.Sprintf("user:%s:secret:", userID)
	rows := &googlepb.RowSet{
		RowRanges: []*googlepb.RowRange{
			{
				StartKey: &googlepb.RowRange_StartKeyClosed{
					StartKeyClosed: []byte(rowPrefix),
				},
				EndKey: &googlepb.RowRange_EndKeyOpen{
					EndKeyOpen: []byte(rowPrefix + "\xff"), // End of prefix range
				},
			},
		},
	}

	// Create filter for the column family
	filter := &googlepb.RowFilter{
		Filter: &googlepb.RowFilter_FamilyNameRegexFilter{
			FamilyNameRegexFilter: ColumnFamily,
		},
	}

	// Read from Bigtable using dedicated USER_SECRETS table
	resultRows, err := s.bigtableClient.ReadRows(ctx, tenantID, bigtable_pb.TableName_USER_SECRETS, rows, filter, 1000, requestCtx)
	if err != nil {
		return nil, fmt.Errorf("failed to read from bigtable: %w", err)
	}

	// Process results
	var secrets []*user_secrets_pb.UserSecret
	for _, row := range resultRows {
		// Extract secret name from row key
		rowKeyStr := string(row.RowKey)
		if !strings.HasPrefix(rowKeyStr, rowPrefix) {
			continue
		}
		secretName := strings.TrimPrefix(rowKeyStr, rowPrefix)

		// Find metadata cell
		for _, cell := range row.Cells {
			if cell.FamilyName == ColumnFamily && string(cell.Qualifier) == MetadataColumn {
				// Parse the JSON data
				var secretData SecretData
				if err := json.Unmarshal(cell.Value, &secretData); err != nil {
					continue // Skip malformed data
				}

				// Create protobuf secret
				secret := &user_secrets_pb.UserSecret{
					Name:           secretName,
					Description:    secretData.Description,
					Tags:           secretData.Tags,
					Version:        fmt.Sprintf("%d", secretData.Version),
					CreatedAt:      timestamppb.New(secretData.CreatedAt),
					UpdatedAt:      timestamppb.New(secretData.UpdatedAt),
					ValueSizeBytes: int32(len(secretData.Value)),
				}

				// Include value if requested
				if includeValues {
					secret.Value = secretData.Value
				}

				secrets = append(secrets, secret)
				break // Found metadata, move to next row
			}
		}
	}

	return secrets, nil
}

func (s *UserSecretsServer) deleteSecretFromBigtable(ctx context.Context, rowKey string) error {
	// Extract tenant ID and create request context
	tenantID, requestCtx, err := s.extractTenantAndRequestContext(ctx)
	if err != nil {
		return err
	}

	// Create delete mutations
	mutations := []*googlepb.Mutation{
		{
			Mutation: &googlepb.Mutation_DeleteFromFamily_{
				DeleteFromFamily: &googlepb.Mutation_DeleteFromFamily{
					FamilyName: ColumnFamily,
				},
			},
		},
	}

	// Create mutate rows entry
	entries := []*googlepb.MutateRowsRequest_Entry{
		{
			RowKey:    []byte(rowKey),
			Mutations: mutations,
		},
	}

	// Delete from Bigtable using dedicated USER_SECRETS table
	responses, err := s.bigtableClient.MutateRows(ctx, tenantID, bigtable_pb.TableName_USER_SECRETS, entries, requestCtx)
	if err != nil {
		return fmt.Errorf("failed to delete from bigtable: %w", err)
	}

	// Check if the mutation was successful
	for _, response := range responses {
		for _, entry := range response.Entries {
			if entry.Status != nil && entry.Status.Code != 0 {
				return fmt.Errorf("bigtable deletion failed: %s", entry.Status.Message)
			}
		}
	}

	return nil
}
