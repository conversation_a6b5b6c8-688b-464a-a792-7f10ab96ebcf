package main

import (
	"context"
	"fmt"
	"regexp"
	"time"

	"google.golang.org/grpc"
	"google.golang.org/grpc/codes"
	"google.golang.org/grpc/status"
	"google.golang.org/protobuf/types/known/timestamppb"

	bigtableproxy "github.com/augmentcode/augment/services/bigtable_proxy/client"
	grpc_auth "github.com/augmentcode/augment/services/lib/grpc/auth"
	grpc_tls_config "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	settings_client "github.com/augmentcode/augment/services/settings/client"
	user_secrets_pb "github.com/augmentcode/augment/services/user_secrets/proto"
)

const (
	QuotaLimit     = 1000
	MaxSecretSize  = 64 * 1024 // 64KB // pragma: allowlist secret
	MaxNameLength  = 255
	MaxDescLength  = 1024
	MaxTagKeyLen   = 128
	MaxTagValueLen = 256
	MaxTagCount    = 50
)

// namePattern for validating secret names - must start with a letter
var namePattern = regexp.MustCompile(`^[a-zA-Z][a-zA-Z0-9_-]*$`)

type UserSecretsServer struct {
	user_secrets_pb.UnimplementedUserSecretsServiceServer
	config         *Config
	settingsClient settings_client.SettingsClient
	bigtableClient bigtableproxy.BigtableProxyClient
}

func NewUserSecretsServer(config *Config) (*UserSecretsServer, error) {
	// Create client credentials
	clientCreds, err := grpc_tls_config.GetClientTls(&config.ClientMTLS)
	if err != nil {
		return nil, fmt.Errorf("failed to create client credentials: %w", err)
	}

	// Create bigtable client
	bigtableClient, err := bigtableproxy.NewBigtableProxyClient(config.BigtableProxyEndpoint, clientCreds)
	if err != nil {
		return nil, fmt.Errorf("failed to create bigtable client: %w", err)
	}

	// TODO: Create settings client when needed
	// settingsClient, err := settings_client.NewSettingsClient(...)

	return &UserSecretsServer{
		config:         config,
		settingsClient: nil, // TODO: implement when needed
		bigtableClient: bigtableClient,
	}, nil
}

func createGRPCConnection(endpoint string, tlsConfig *grpc_tls_config.ClientConfig) (*grpc.ClientConn, error) {
	var opts []grpc.DialOption

	if tlsConfig != nil && tlsConfig.CertPath != "" {
		creds, err := grpc_tls_config.GetClientTls(tlsConfig)
		if err != nil {
			return nil, fmt.Errorf("failed to load TLS config: %w", err)
		}
		opts = append(opts, grpc.WithTransportCredentials(creds))
	} else {
		opts = append(opts, grpc.WithInsecure())
	}

	return grpc.Dial(endpoint, opts...)
}

func (s *UserSecretsServer) UpsertUserSecret(ctx context.Context, req *user_secrets_pb.UpsertUserSecretRequest) (*user_secrets_pb.UpsertUserSecretResponse, error) {
	// Extract user ID from context
	userID, err := s.getUserIDFromContext(ctx)
	if err != nil {
		return nil, err
	}

	// Validate request
	if req.Name == "" {
		return nil, status.Error(codes.InvalidArgument, "secret name is required") // pragma: allowlist secret
	}
	if req.Value == "" {
		return nil, status.Error(codes.InvalidArgument, "secret value is required") // pragma: allowlist secret
	}

	// Check user limits
	if err := s.checkUserLimits(ctx, userID, req); err != nil {
		return nil, err
	}

	// Validate name format
	if !namePattern.MatchString(req.Name) {
		return nil, status.Error(codes.InvalidArgument, "secret name must start with a letter and contain only alphanumeric characters, hyphens, and underscores")
	}

	// Generate row key
	rowKey := fmt.Sprintf("user:%s:secret:%s", userID, req.Name)

	// Get existing secret for version handling
	existingSecret, err := s.getSecretFromBigtable(ctx, rowKey)
	var version int64 = 1
	var createdAt time.Time = time.Now()

	if err == nil && existingSecret != nil { // pragma: allowlist secret
		// User secret exists, increment version // pragma: allowlist secret
		version = existingSecret.Version + 1
		createdAt = existingSecret.CreatedAt

		// Check expected version if provided
		if req.ExpectedVersion != "" {
			expectedVer := existingSecret.Version
			if fmt.Sprintf("%d", expectedVer) != req.ExpectedVersion {
				return nil, status.Error(codes.FailedPrecondition, "version mismatch")
			}
		}
	} else if err != nil && status.Code(err) != codes.NotFound {
		return nil, fmt.Errorf("failed to check existing secret: %w", err)
	} else {
		// Secret doesn't exist - check expected version
		if req.ExpectedVersion != "" && req.ExpectedVersion != "0" {
			// User expects a specific version but secret doesn't exist (version 0)
			return nil, status.Error(codes.FailedPrecondition, "version mismatch")
		}
		// Secret doesn't exist and either no expected version or expected version is 0 - proceed with creation
	}

	// Create secret data
	now := time.Now()
	secretData := &SecretData{
		Value:       req.Value,
		Description: req.Description,
		Tags:        req.Tags,
		CreatedAt:   createdAt,
		UpdatedAt:   now,
		Version:     version,
	}

	// Store in Bigtable
	if err := s.storeSecretInBigtable(ctx, rowKey, secretData); err != nil {
		return nil, fmt.Errorf("failed to store secret: %w", err)
	}

	// Convert to protobuf
	secret := &user_secrets_pb.UserSecret{
		Name:           req.Name,
		Value:          secretData.Value,
		Tags:           secretData.Tags,
		CreatedAt:      timestamppb.New(secretData.CreatedAt),
		UpdatedAt:      timestamppb.New(secretData.UpdatedAt),
		Description:    secretData.Description,
		Version:        fmt.Sprintf("%d", secretData.Version),
		ValueSizeBytes: int32(len(secretData.Value)),
	}

	return &user_secrets_pb.UpsertUserSecretResponse{
		Secret: secret,
	}, nil
}

func (s *UserSecretsServer) GetUserSecret(ctx context.Context, req *user_secrets_pb.GetUserSecretRequest) (*user_secrets_pb.GetUserSecretResponse, error) {
	userID, err := s.getUserIDFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if req.Name == "" {
		return nil, status.Error(codes.InvalidArgument, "secret name is required")
	}

	// Generate row key
	rowKey := fmt.Sprintf("user:%s:secret:%s", userID, req.Name)

	// Get secret from Bigtable
	secretData, err := s.getSecretFromBigtable(ctx, rowKey)
	if err != nil {
		return nil, err
	}

	// Convert to protobuf
	metadata := &user_secrets_pb.UserSecret{
		Name:           req.Name,
		Value:          secretData.Value,
		Description:    secretData.Description,
		Tags:           secretData.Tags,
		Version:        fmt.Sprintf("%d", secretData.Version),
		CreatedAt:      timestamppb.New(secretData.CreatedAt),
		UpdatedAt:      timestamppb.New(secretData.UpdatedAt),
		ValueSizeBytes: int32(len(secretData.Value)),
	}

	return &user_secrets_pb.GetUserSecretResponse{
		Secret: metadata,
	}, nil
}

func (s *UserSecretsServer) ListUserSecrets(ctx context.Context, req *user_secrets_pb.ListUserSecretsRequest) (*user_secrets_pb.ListUserSecretsResponse, error) {
	userID, err := s.getUserIDFromContext(ctx)
	if err != nil {
		return nil, err
	}

	// Get secrets from Bigtable
	secrets, err := s.listUserSecretsFromBigtable(ctx, userID, req.IncludeValues)
	if err != nil {
		return nil, err
	}

	// Apply filters
	filteredSecrets := make([]*user_secrets_pb.UserSecret, 0)
	for _, secret := range secrets { // pragma: allowlist secret
		// Apply name pattern filter
		if req.NamePattern != "" {
			matched, err := regexp.MatchString(req.NamePattern, secret.Name)
			if err != nil {
				return nil, status.Errorf(codes.InvalidArgument, "invalid name pattern: %v", err)
			}
			if !matched {
				continue
			}
		}

		// Apply tag filters
		if !s.matchesTagFilters(secret, req.TagFilters) {
			continue
		}

		filteredSecrets = append(filteredSecrets, secret)
	}

	return &user_secrets_pb.ListUserSecretsResponse{
		Secrets: filteredSecrets,
	}, nil
}

func (s *UserSecretsServer) DeleteUserSecret(ctx context.Context, req *user_secrets_pb.DeleteUserSecretRequest) (*user_secrets_pb.DeleteUserSecretResponse, error) {
	userID, err := s.getUserIDFromContext(ctx)
	if err != nil {
		return nil, err
	}

	if req.Name == "" {
		return nil, status.Error(codes.InvalidArgument, "secret name is required")
	}

	// Generate row key
	rowKey := fmt.Sprintf("user:%s:secret:%s", userID, req.Name)

	// Check if secret exists first (optional, for better error messages)
	_, err = s.getSecretFromBigtable(ctx, rowKey)
	if err != nil {
		if status.Code(err) == codes.NotFound {
			// Secret doesn't exist, but that's okay for delete
			return &user_secrets_pb.DeleteUserSecretResponse{
				Deleted: false,
			}, nil
		}
		return nil, err
	}

	// Delete from Bigtable
	if err := s.deleteSecretFromBigtable(ctx, rowKey); err != nil {
		return nil, fmt.Errorf("failed to delete secret: %w", err)
	}

	return &user_secrets_pb.DeleteUserSecretResponse{
		Deleted: true,
	}, nil
}

// Helper methods

func (s *UserSecretsServer) getUserIDFromContext(ctx context.Context) (string, error) {
	// Extract AugmentClaims from context (set by auth interceptor)
	claims, ok := grpc_auth.GetAugmentClaims(ctx)
	if !ok {
		return "", status.Error(codes.Unauthenticated, "no authentication claims found")
	}

	// Get user ID from claims
	if claims.OpaqueUserID == "" {
		return "", status.Error(codes.Unauthenticated, "user ID not found in claims")
	}

	return claims.OpaqueUserID, nil
}

func (s *UserSecretsServer) checkUserLimits(ctx context.Context, userID string, req *user_secrets_pb.UpsertUserSecretRequest) error {
	// TODO: Check user limits against settings service
	// For now, just check basic size limits
	_ = ctx    // Avoid unused variable warning
	_ = userID // Avoid unused variable warning

	maxSecretSize := s.config.DefaultMaxSecretSizeBytes // pragma: allowlist secret

	// Check secret size
	if int32(len(req.Value)) > maxSecretSize { // pragma: allowlist secret
		return status.Errorf(codes.InvalidArgument, "secret value too large (max %d bytes)", maxSecretSize)
	}

	return nil
}

func (s *UserSecretsServer) matchesTagFilters(secret *user_secrets_pb.UserSecret, filters []*user_secrets_pb.TagFilter) bool {
	if len(filters) == 0 {
		return true
	}

	for _, filter := range filters {
		if !s.matchesSingleTagFilter(secret, filter) {
			return false
		}
	}

	return true
}

func (s *UserSecretsServer) matchesSingleTagFilter(secret *user_secrets_pb.UserSecret, filter *user_secrets_pb.TagFilter) bool {
	baseMatch := s.evaluateBaseTagFilter(secret, filter)

	if filter.Invert {
		return !baseMatch
	}
	return baseMatch
}

func (s *UserSecretsServer) evaluateBaseTagFilter(secret *user_secrets_pb.UserSecret, filter *user_secrets_pb.TagFilter) bool {
	tagValue, hasTag := secret.Tags[filter.Key]

	// If no values specified, just check key existence
	if len(filter.Values) == 0 {
		return hasTag
	}

	// If tag doesn't exist, it can't match any values
	if !hasTag {
		return false
	}

	// Check if tag value matches any of the filter values
	for _, filterValue := range filter.Values {
		if tagValue == filterValue {
			if filter.MatchAny {
				return true // OR logic: found one match
			}
		} else {
			if !filter.MatchAny {
				return false // AND logic: found one non-match
			}
		}
	}

	// For AND logic, all values matched
	// For OR logic, no values matched
	return !filter.MatchAny
}

func (s *UserSecretsServer) marshalMetadata(metadata *user_secrets_pb.UserSecret) []byte {
	// In a real implementation, you'd use protobuf marshaling
	// For now, we'll use a simple JSON approach
	data := fmt.Sprintf(`{"name":"%s","description":"%s","created_at":%d,"updated_at":%d}`, // pragma: allowlist secret
		metadata.Name, metadata.Description, metadata.CreatedAt.AsTime().Unix(), metadata.UpdatedAt.AsTime().Unix()) // pragma: allowlist secret
	return []byte(data)
}

func (s *UserSecretsServer) unmarshalMetadata(data []byte) *user_secrets_pb.UserSecret {
	// In a real implementation, you'd use protobuf unmarshaling
	// For now, we'll create a basic metadata object
	return &user_secrets_pb.UserSecret{
		Name:        "placeholder", // Would be parsed from JSON
		Description: "placeholder",
		CreatedAt:   timestamppb.Now(),
		UpdatedAt:   timestamppb.Now(),
		Tags:        make(map[string]string),
	}
}
