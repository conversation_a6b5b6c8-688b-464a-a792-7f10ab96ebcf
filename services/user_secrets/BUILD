load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_proto_library")
load("//tools/bzl:python.bzl", "py_grpc_library")

proto_library(
    name = "user_secrets_proto",
    srcs = ["user_secrets.proto"],
    visibility = ["//visibility:public"],
    deps = [
        "@protobuf//:timestamp_proto",
    ],
)

py_grpc_library(
    name = "user_secrets_py_proto",
    protos = [":user_secrets_proto"],
    visibility = ["//visibility:public"],
)

go_proto_library(
    name = "user_secrets_go_proto",
    compilers = ["@io_bazel_rules_go//proto:go_grpc"],
    importpath = "github.com/augmentcode/augment/services/user_secrets/proto",
    proto = ":user_secrets_proto",
    visibility = ["//visibility:public"],
)
