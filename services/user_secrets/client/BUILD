load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "client_go",
    srcs = ["client.go"],
    importpath = "github.com/augmentcode/augment/services/user_secrets/client",
    visibility = ["//visibility:public"],
    deps = [
        "//services/lib/request_context:request_context_go",
        "//services/user_secrets:user_secrets_go_proto",
        "@io_opentelemetry_go_contrib_instrumentation_google_golang_org_grpc_otelgrpc//:otelgrpc",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials",
        "@org_golang_google_grpc//metadata",
    ],
)

go_test(
    name = "client_test",
    srcs = ["client_test.go"],
    embed = [":client_go"],
    deps = [
        "//services/lib/request_context:request_context_go",
        "//services/user_secrets:user_secrets_go_proto",
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
        "@org_golang_google_grpc//:grpc",
        "@org_golang_google_grpc//credentials/insecure",
    ],
)
