// Package usersecrets provides a client for the User Secrets service.
package usersecrets

import (
	"context"
	"time"

	"go.opentelemetry.io/contrib/instrumentation/google.golang.org/grpc/otelgrpc"
	"google.golang.org/grpc"
	"google.golang.org/grpc/credentials"
	"google.golang.org/grpc/metadata"

	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	pb "github.com/augmentcode/augment/services/user_secrets/proto"
)

// UserSecretsClient interface for the User Secrets service
type UserSecretsClient interface {
	// UpsertSecret creates or updates a user secret
	UpsertSecret(ctx context.Context, req *pb.UpsertUserSecretRequest, requestContext *requestcontext.RequestContext, opts ...grpc.CallOption) (*pb.UpsertUserSecretResponse, error)

	// GetSecret retrieves a specific user secret by name
	GetSecret(ctx context.Context, req *pb.GetUserSecretRequest, requestContext *requestcontext.RequestContext, opts ...grpc.CallOption) (*pb.GetUserSecretResponse, error)

	// ListSecrets lists/searches user secrets with filtering
	ListSecrets(ctx context.Context, req *pb.ListUserSecretsRequest, requestContext *requestcontext.RequestContext, opts ...grpc.CallOption) (*pb.ListUserSecretsResponse, error)

	// DeleteSecret deletes a user secret
	DeleteSecret(ctx context.Context, req *pb.DeleteUserSecretRequest, requestContext *requestcontext.RequestContext, opts ...grpc.CallOption) (*pb.DeleteUserSecretResponse, error)

	// Close cleans up resources for this client
	Close() error
}

// UserSecretsClientImpl implements the UserSecretsClient interface
type UserSecretsClientImpl struct {
	// gRPC connection
	conn *grpc.ClientConn

	// gRPC client stub
	client pb.UserSecretsServiceClient
}

// NewUserSecretsClient creates a new User Secrets client
func NewUserSecretsClient(endpoint string, credentials credentials.TransportCredentials) (UserSecretsClient, error) {
	opts := []grpc.DialOption{
		grpc.WithTransportCredentials(credentials),
		grpc.WithDefaultCallOptions(grpc.MaxCallSendMsgSize(1<<28), grpc.MaxCallRecvMsgSize(1<<28)),
		grpc.WithStatsHandler(otelgrpc.NewClientHandler()),
	}

	conn, err := grpc.NewClient(endpoint, opts...)
	if err != nil {
		return nil, err
	}

	client := pb.NewUserSecretsServiceClient(conn)

	return &UserSecretsClientImpl{
		conn:   conn,
		client: client,
	}, nil
}

// UpsertSecret creates or updates a user secret
func (c *UserSecretsClientImpl) UpsertSecret(ctx context.Context, req *pb.UpsertUserSecretRequest, requestContext *requestcontext.RequestContext, opts ...grpc.CallOption) (*pb.UpsertUserSecretResponse, error) {
	// Add request context to metadata
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())

	// Set default timeout if not specified
	if _, hasDeadline := ctx.Deadline(); !hasDeadline {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
		defer cancel()
	}

	return c.client.UpsertUserSecret(ctx, req, opts...)
}

// GetSecret retrieves a specific user secret by name
func (c *UserSecretsClientImpl) GetSecret(ctx context.Context, req *pb.GetUserSecretRequest, requestContext *requestcontext.RequestContext, opts ...grpc.CallOption) (*pb.GetUserSecretResponse, error) {
	// Add request context to metadata
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())

	// Set default timeout if not specified
	if _, hasDeadline := ctx.Deadline(); !hasDeadline {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
		defer cancel()
	}

	return c.client.GetUserSecret(ctx, req, opts...)
}

// ListSecrets lists/searches user secrets with filtering
func (c *UserSecretsClientImpl) ListSecrets(ctx context.Context, req *pb.ListUserSecretsRequest, requestContext *requestcontext.RequestContext, opts ...grpc.CallOption) (*pb.ListUserSecretsResponse, error) {
	// Add request context to metadata
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())

	// Set default timeout if not specified
	if _, hasDeadline := ctx.Deadline(); !hasDeadline {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
		defer cancel()
	}

	return c.client.ListUserSecrets(ctx, req, opts...)
}

// DeleteSecret deletes a user secret
func (c *UserSecretsClientImpl) DeleteSecret(ctx context.Context, req *pb.DeleteUserSecretRequest, requestContext *requestcontext.RequestContext, opts ...grpc.CallOption) (*pb.DeleteUserSecretResponse, error) {
	// Add request context to metadata
	ctx = metadata.NewOutgoingContext(ctx, requestContext.ToMetadata())

	// Set default timeout if not specified
	if _, hasDeadline := ctx.Deadline(); !hasDeadline {
		var cancel context.CancelFunc
		ctx, cancel = context.WithTimeout(ctx, 30*time.Second)
		defer cancel()
	}

	return c.client.DeleteUserSecret(ctx, req, opts...)
}

// Close cleans up resources for this client
func (c *UserSecretsClientImpl) Close() error {
	if c.conn != nil {
		return c.conn.Close()
	}
	return nil
}
