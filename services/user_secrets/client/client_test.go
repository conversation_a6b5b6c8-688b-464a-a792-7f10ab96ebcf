package usersecrets

import (
	"testing"

	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/require"
	"google.golang.org/grpc/credentials/insecure"
)

func TestNewUserSecretsClient(t *testing.T) {
	// Test creating a client with insecure credentials (for testing)
	client, err := NewUserSecretsClient("localhost:50051", insecure.NewCredentials())
	require.NoError(t, err)
	require.NotNil(t, client)

	// Clean up
	err = client.Close()
	assert.NoError(t, err)
}

func TestUserSecretsClientImpl_Close(t *testing.T) {
	client, err := NewUserSecretsClient("localhost:50051", insecure.NewCredentials())
	require.NoError(t, err)

	// Should not panic and should return no error on first close
	err = client.Close()
	assert.NoError(t, err)

	// Second close should return an error (connection already closed)
	err = client.Close()
	assert.Error(t, err)
	assert.Contains(t, err.Error(), "connection is closing")
}
