# User Secrets Service

The User Secrets Service provides a secure, scalable system for storing user-defined secrets with rich metadata and tagging capabilities. It complements the existing OAuth integration secrets by allowing users to store their own arbitrary key-value secrets.

## Overview

Users need a way to store custom secrets such as personal API keys, database credentials, configuration secrets, and custom integration tokens. The existing settings system is not suitable for user-defined secrets due to performance impact, size limitations, and security concerns.

## Features

- **Secure Storage**: Dedicated Bigtable storage with field-level redaction
- **Rich Tagging**: Organize secrets with arbitrary key-value tags
- **Flexible Filtering**: Regex name patterns and advanced tag filtering
- **Version Control**: Optimistic concurrency control for safe updates
- **Quota Management**: Built-in limits to prevent abuse
- **Security by Default**: Metadata-only operations prevent accidental value exposure
- **User Isolation**: Each user can only access their own secrets

## Architecture

### Storage Design
- **Dedicated Table**: Uses `USER_SECRETS` table via Bigtable Proxy
- **Row Key Format**: `user:<user_id>:secret:<name>`
- **Encryption**: Automatic tenant-specific encryption via Bigtable Proxy
- **Isolation**: Complete separation from other data

### Quota Limits
| Limit | Default | Configurable |
|-------|---------|-------------|
| Max secrets per user | 100 | ✅ |
| Max secret size | 4KB | ✅ |
| Max name length | 255 chars | ❌ |
| Max description length | 1024 chars | ❌ |
| Max tag key length | 128 chars | ❌ |
| Max tag value length | 256 chars | ❌ |
| Max tags per secret | 50 | ❌ |

**TODO**: Description and tag length limits are defined but not currently enforced in validation.

## API Reference

### Service Operations

- **UpsertUserSecret**: Create or update a secret with optional version control
- **GetUserSecret**: Retrieve a specific secret by name
- **ListUserSecrets**: List/search secrets with flexible filtering (name patterns, tags)
- **DeleteUserSecret**: Delete a secret with optional version control

### Key Features

- **Name Pattern Filtering**: Regex-based secret name matching
- **Tag Filtering**: Filter by tag keys and values with AND/OR logic
- **Invertible Filters**: Support for negative matching (NOT operations)
- **Version Control**: Optional optimistic concurrency control
- **Security by Default**: Values not included in list operations unless requested

### Filtering Capabilities

#### Name Pattern Filtering
- **Regex Support**: Use regular expressions to match secret names
- **Examples**:
  - `"github.*"` - All secrets starting with "github"
  - `".*token.*"` - All secrets containing "token"
  - `"^api-key-[0-9]+$"` - API keys with numeric suffixes

#### Tag Filtering
- **Key-Only**: Match secrets that have a specific tag key
- **Value Filtering**: Match secrets where tag key has specific values
- **Multiple Values**: Use OR/AND logic for multiple values per key
- **Invertible**: Use `invert=true` for negative matching

## Security Features

- 🔒 **Field Redaction**: Secret values never appear in logs
- 🔒 **Metadata-Only Listing**: Default operations don't expose values
- 🔒 **User Isolation**: Users can only access their own secrets
- 🔒 **Tenant Isolation**: All operations scoped by tenant
- 🔒 **Version Control**: Prevents concurrent modification issues
- 🔒 **Name Validation**: Must start with letter, alphanumeric + hyphens/underscores only

## Error Handling

| Error Code | Description | Common Causes |
|------------|-------------|---------------|
| `INVALID_ARGUMENT` | Invalid input | Empty name, oversized value, invalid name format |
| `NOT_FOUND` | Secret doesn't exist | Wrong name, already deleted |
| `RESOURCE_EXHAUSTED` | Quota exceeded | Too many secrets, size limit |
| `FAILED_PRECONDITION` | Version mismatch | Concurrent modification |
| `UNAUTHENTICATED` | Auth failure | Invalid/missing credentials |
