package client

import (
	"context"

	blob_names "github.com/augmentcode/augment/base/blob_names"
	blobsproto "github.com/augmentcode/augment/base/blob_names/proto"
	contentmanagerproto "github.com/augmentcode/augment/services/content_manager/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	"github.com/stretchr/testify/mock"
	"google.golang.org/protobuf/types/known/timestamppb"
)

// MockContentManagerClient is a mock implementation of ContentManagerClient
type MockContentManagerClient struct {
	mock.Mock
	BatchDownloadContentMock      func(args mock.Arguments) (<-chan BatchDownloadContentResult, error)
	GetAllBlobsFromCheckpointMock func(args mock.Arguments) ([]blob_names.BlobName, error)
}

// NewMockContentManagerClient creates a new mock content manager client
func NewMockContentManagerClient() *MockContentManagerClient {
	return new(MockContentManagerClient)
}

func (m *MockContentManagerClient) Close() {
	m.Called()
}

func (m *MockContentManagerClient) UploadBlobContent(ctx context.Context, content []byte, path string, requestContext *requestcontext.RequestContext) (string, bool, error) {
	args := m.Called(ctx, content, path, requestContext)
	return args.String(0), args.Bool(1), args.Error(2)
}

func (m *MockContentManagerClient) BatchUploadBlobContent(ctx context.Context, blobs []*contentmanagerproto.UploadBlobContent, priority contentmanagerproto.IndexingPriority, requestContext *requestcontext.RequestContext) ([]*contentmanagerproto.UploadBlobResult, error) {
	args := m.Called(ctx, blobs, priority, requestContext)
	return args.Get(0).([]*contentmanagerproto.UploadBlobResult), args.Error(1)
}

func (m *MockContentManagerClient) FindMissingBlobs(ctx context.Context, blobNames []blob_names.BlobName, transformationKey string, subKey string, tenantID *string, requestContext *requestcontext.RequestContext) ([]string, error) {
	args := m.Called(ctx, blobNames, transformationKey, subKey, tenantID, requestContext)
	return args.Get(0).([]string), args.Error(1)
}

func (m *MockContentManagerClient) CheckpointBlobs(ctx context.Context, blobs *blobsproto.Blobs, requestContext *requestcontext.RequestContext) (string, error) {
	args := m.Called(ctx, blobs, requestContext)
	return args.String(0), args.Error(1)
}

func (m *MockContentManagerClient) GetAllBlobsFromCheckpoint(ctx context.Context, checkpointID string, tenantID *string, requestContext *requestcontext.RequestContext) ([]blob_names.BlobName, error) {
	args := m.Called(ctx, checkpointID, tenantID, requestContext)
	if m.GetAllBlobsFromCheckpointMock != nil {
		return m.GetAllBlobsFromCheckpointMock(args)
	}
	return args.Get(0).([]blob_names.BlobName), args.Error(1)
}

func (m *MockContentManagerClient) GetBlobInfo(ctx context.Context, blobName blob_names.BlobName, requestContext *requestcontext.RequestContext) (*contentmanagerproto.GetBlobInfoResponse, error) {
	args := m.Called(ctx, blobName, requestContext)
	return args.Get(0).(*contentmanagerproto.GetBlobInfoResponse), args.Error(1)
}

func (m *MockContentManagerClient) BatchGetBlobInfo(ctx context.Context, blobNames []blob_names.BlobName, tenantID string, requestContext *requestcontext.RequestContext) (*contentmanagerproto.BatchGetBlobInfoResponse, error) {
	args := m.Called(ctx, blobNames, tenantID, requestContext)
	return args.Get(0).(*contentmanagerproto.BatchGetBlobInfoResponse), args.Error(1)
}

func (m *MockContentManagerClient) BatchDownloadContent(ctx context.Context, keys []*contentmanagerproto.BlobContentKey, requestContext *requestcontext.RequestContext, tenantID string) (<-chan BatchDownloadContentResult, error) {
	args := m.Called(ctx, keys, requestContext, tenantID)
	if m.BatchDownloadContentMock != nil {
		return m.BatchDownloadContentMock(args)
	}
	return args.Get(0).(<-chan BatchDownloadContentResult), args.Error(1)
}

func (m *MockContentManagerClient) GetBestAnnIndex(ctx context.Context, tenantId *string, transformationKey string, checkpointId string, requestContext *requestcontext.RequestContext) (*GetBestAnnIndexResult, error) {
	args := m.Called(ctx, tenantId, transformationKey, checkpointId, requestContext)
	return args.Get(0).(*GetBestAnnIndexResult), args.Error(1)
}

func (m *MockContentManagerClient) GetAnnIndexBlobInfos(ctx context.Context, keys AnnIndexKey, requestContext requestcontext.RequestContext) (*GetAnnIndexBlobInfosResult, error) {
	args := m.Called(ctx, keys, requestContext)
	return args.Get(0).(*GetAnnIndexBlobInfosResult), args.Error(1)
}

func (m *MockContentManagerClient) GetAnnIndexAsset(ctx context.Context, tenantId *string, transformationKey string, indexId string, subKey string, requestContext requestcontext.RequestContext) ([]byte, error) {
	args := m.Called(ctx, tenantId, transformationKey, indexId, subKey, requestContext)
	return args.Get(0).([]byte), args.Error(1)
}

func (m *MockContentManagerClient) AddAnnIndexMapping(ctx context.Context, tenantId *string, transformationKey string, checkpointId string, indexId string, added []blob_names.BlobName, removed []blob_names.BlobName, requestContext requestcontext.RequestContext) (*contentmanagerproto.AddAnnIndexMappingResponse, error) {
	args := m.Called(ctx, tenantId, transformationKey, checkpointId, indexId, added, removed, requestContext)
	return &contentmanagerproto.AddAnnIndexMappingResponse{}, args.Error(1)
}

func (m *MockContentManagerClient) UploadAnnIndexBlobInfos(ctx context.Context, tenantId *string, transformationKey string, indexId string, infos []AnnIndexBlobInfo, requestContext requestcontext.RequestContext) (*contentmanagerproto.UploadAnnIndexBlobInfosResponse, error) {
	args := m.Called(ctx, tenantId, transformationKey, indexId, infos, requestContext)
	return &contentmanagerproto.UploadAnnIndexBlobInfosResponse{}, args.Error(1)
}

func (m *MockContentManagerClient) UploadAnnIndexAssets(ctx context.Context, tenantId *string, transformationKey string, indexId string, assets []AnnIndexAssetInput, requestContext requestcontext.RequestContext) (*contentmanagerproto.UploadAnnIndexAssetsResponse, error) {
	args := m.Called(ctx, tenantId, transformationKey, indexId, assets, requestContext)
	return &contentmanagerproto.UploadAnnIndexAssetsResponse{}, args.Error(1)
}

func (m *MockContentManagerClient) GetUserBlobs(ctx context.Context, userID string, tenantID string, requestContext *requestcontext.RequestContext, limit *uint32, minTimestamp *timestamppb.Timestamp, maxTimestamp *timestamppb.Timestamp) ([]*contentmanagerproto.UploadInfo, error) {
	args := m.Called(ctx, userID, tenantID, requestContext, limit, minTimestamp, maxTimestamp)
	return args.Get(0).([]*contentmanagerproto.UploadInfo), args.Error(1)
}

func (m *MockContentManagerClient) BatchDeleteBlobs(ctx context.Context, blobUserTuples []*contentmanagerproto.BatchDeleteBlobsRequest_BlobUserPair, requestContext *requestcontext.RequestContext, tenantID string) (bool, error) {
	args := m.Called(ctx, blobUserTuples, requestContext, tenantID)
	return args.Bool(0), args.Error(1)
}

func (m *MockContentManagerClient) ExtendCheckpointLifetimes(ctx context.Context, tenantID string, checkpointID string, requestContext *requestcontext.RequestContext) (*contentmanagerproto.ExtendCheckpointLifetimesResponse, error) {
	args := m.Called(ctx, tenantID, checkpointID, requestContext)
	return &contentmanagerproto.ExtendCheckpointLifetimesResponse{}, args.Error(1)
}
