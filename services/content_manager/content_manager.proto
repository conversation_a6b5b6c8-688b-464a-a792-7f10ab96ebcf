syntax = "proto3";
package content_manager;

import "base/blob_names/blob_names.proto";
import "google/protobuf/timestamp.proto";
import "google/rpc/status.proto";
import "services/token_exchange/auth_options.proto";
import "services/token_exchange/token_scopes.proto";

// content manager service
//
// the content manager service is responsible for managing the contents of blobs
// and the transformation of these blobs in custom representations, e.g. memorization or for
// retrieval.
// The content manager service is also responsible for notifying subscribers about new blob uploads.
//
// It can be seen as a form of content-addressable storage. A blob name is a hash of the content
// and the file path. The content is immutable.
//
// Different indexers denoted by a transformation key.
// When a blob is uploaded, all existing transformation keys (aka all indexers) are informed about a
// blob update. They are supposed to upload a transformed version of the blob to the content manager
// via `UploadTransformedBlobContent`, If a transformation key is added after the upload of a blob,
// the content manager might notify the indexers of the transformation key about blobs that are
// already uploaded to the content manager. This is called "catchup notification".
//
// Catchup notifications are critical for the rollout of new indexers.
//
// The content manager acts as a cache. It is free to delete blob data or transformed version of it
// at any point in time.

service ContentManager {
  // upload raw blob content
  //
  // Required Scope: CONTENT_RW
  rpc UploadBlobContent(UploadBlobContentRequest) returns (UploadBlobContentResponse) {
    option (auth_options.required_token_scopes) = CONTENT_RW;
  }

  // find all blobs whose raw form is not available to the content manager
  //
  // Find missing will also trigger catch-up notifications for all existing blobs for new
  // transformation keys (see above).
  //
  // i.e. a GetContent call on these blobs would fail with NOT_FOUND
  //
  // Required Scope: CONTENT_RW (as it will record new notifications)
  rpc FindMissingBlobs(FindMissingBlobRequest) returns (FindMissingBlobResponse) {
    option (auth_options.required_token_scopes) = CONTENT_RW;
  }
  // informs the caller for all new blob updates
  //
  // the expectation is after a caller receives a notification, it will transform the content
  // and call UploadTransformedBlobContent passing the receipt_handle string to acknowledge the
  // transformation. if there is no such call with the matching receipt handle, a subscriber will
  // eventually be informed about the same blob again.
  //
  // Client specifies a window on the initial request and
  // updates the window when it wishes to receive more blobs.
  //
  // Client can request a graceful shutdown by sending end-of-stream.
  //
  // Required Scope: CONTENT_R
  rpc SubscribeBlobUpdatesFlowControlled(stream SubscribeBlobUpdatesRequest) returns (stream SubscribeBlobUpdatesResponse) {
    option (auth_options.required_token_scopes) = CONTENT_R;
  }

  // update the transformed blob content.
  //
  // Once upload transformed content is called with the receipt handle string from a
  // SubscribeBlobUpdatesResponse the subscriber using that transformation key are no longer
  // re-informed about the blob update
  //
  // The content of a call is only guaranteed to be persisted at the end of the stream.
  // The state of the blobs is not defined should the call fail. However,
  // each blob/transformation/sub_key combination is only guaranteed to be either persisted
  // correctly or not at all.
  //
  // Note: That the term "persisted correctly" above means that it valid from the view of the
  // content manager API. We might have stored the metadata, but not in the content in the
  // underlying bigtable as long as GetContent returns NOT_FOUND, this is consistent with the API.
  //
  // Required Scope: CONTENT_RW
  rpc UploadTransformedBlobContent(stream UploadTransformedBlobContentRequest) returns (UploadTransformedBlobContentResponse) {
    option (auth_options.required_token_scopes) = CONTENT_RW;
  }

  // returns the raw content or transformed content for a given blob
  //
  // Error conditions to note:
  // if the content key doesn't exist, NotFound is returned
  //
  // Required Scope: CONTENT_RW (as it will record new notifications)
  rpc GetContent(GetContentRequest) returns (stream GetContentResponse) {
    option (auth_options.required_token_scopes) = CONTENT_RW;
  }

  // returns the raw content or transformed content for a batch of blobs
  //
  // In contrast to GetContent, if a blob in the batch cannot be found, a BatchGetContentResponse
  // with BatchGetContentNotFound is returned for that blob.
  // The order of the blobs streamed will be in the same order as in the request.
  //
  // Required Scope: CONTENT_RW (as it will record new notifications)
  rpc BatchGetContent(BatchGetContentRequest) returns (stream BatchGetContentResponse) {
    option (auth_options.required_token_scopes) = CONTENT_RW;
  }

  // upload raw blob contents for multiple blobs
  //
  // Note: the caller HAS to check the length of the list of blob names returned.
  // An list that doesn't have the same size as the list of argument blobs means that
  // not all blobs have been uploaded, and the caller should retry with the remaining blob names.
  //
  // blob content uploaded can only be assumed to be stored once the response is returned.
  // if fewer blob names are returned, the caller should retry with the remaining blob names.
  //
  // if the upload of the first blob of the requested blobs fails, an error is returned.
  // if any other upload fails, a shorter list of responses is returned. No error is returned
  // in this case.
  //
  //
  // Required Scope: CONTENT_RW (as it will record new notifications)
  rpc BatchUploadBlobContent(BatchUploadBlobContentRequest) returns (BatchUploadBlobContentResponse) {
    option (auth_options.required_token_scopes) = CONTENT_RW;
  }

  // return the metadata of a given blob
  //
  // Required Scope: CONTENT_R
  rpc GetBlobInfo(GetBlobInfoRequest) returns (GetBlobInfoResponse) {
    option (auth_options.required_token_scopes) = CONTENT_R;
  }

  // return the metadata of a given batch of blobs
  //
  // Required Scope: CONTENT_R
  rpc BatchGetBlobInfo(BatchGetBlobInfoRequest) returns (BatchGetBlobInfoResponse) {
    option (auth_options.required_token_scopes) = CONTENT_R;
  }

  // list all transformation keys/sub keys that were uploaded for a given blob name
  //
  // Required Scope: CONTENT_R
  rpc ListBlobContentKeys(ListBlobContentKeysRequest) returns (ListBlobContentKeysResponse) {
    option (auth_options.required_token_scopes) = CONTENT_R;
  }

  // Deletes a given blob, its transformed content, and the associated user
  // index entry
  //
  // Note: Delete is an administrative operation only.
  // FindMissing and GetBlobInfo are allowed to use cached values
  // Upload operations should re-upload a blob that was deleted.
  //
  // Required Scope: CONTENT_ADMIN
  rpc DeleteBlob(DeleteBlobRequest) returns (DeleteBlobResponse) {
    option (auth_options.required_token_scopes) = CONTENT_ADMIN;
  }

  // Deletes a batch of blobs, their transformed content, and the associated user
  // index entries
  rpc BatchDeleteBlobs(BatchDeleteBlobsRequest) returns (BatchDeleteBlobsResponse) {
    option (auth_options.required_token_scopes) = CONTENT_ADMIN;
  }

  // returns the flat list of blob names that are represented by the given checkpoint name
  //
  // Required Scope: CONTENT_R
  rpc GetAllBlobsFromCheckpoint(GetAllBlobsFromCheckpointRequest) returns (stream GetAllBlobsFromCheckpointResponse) {
    option (auth_options.required_token_scopes) = CONTENT_R;
  }

  //
  // Required Scope: CONTENT_RW
  rpc CheckpointBlobs(CheckpointBlobsRequest) returns (CheckpointBlobsResponse) {
    option (auth_options.required_token_scopes) = CONTENT_RW;
  }

  // Get a list of blob names uploaded by a user
  //
  // The caller needs to page through the results by specifying a limit and a
  // max_timestamp. The max_timestamp should be set to the minimum of the
  // timestamps of the blobs returned in the previous call. The first call should
  // have max_timestamp set to the current time.
  //
  // The blobs are returned sorted by upload
  // time with more recently uploaded blobs coming first.
  //
  // Required Scope: CONTENT_ADMIN
  rpc GetUserBlobs(GetUserBlobsRequest) returns (GetUserBlobsResponse) {
    option (auth_options.required_token_scopes) = CONTENT_ADMIN;
  }

  // ANN (approximate nearest neightbor) index methods
  // tenant_ids are derived from token claims for all of these methods

  // Query for the optimal ANN index for a given checkpoint.
  // Responses are streamed to deal with large deltas. Response messages should be merged
  // client side.
  // Required Scope: CONTENT_R
  rpc GetBestAnnIndex(GetBestAnnIndexRequest) returns (GetBestAnnIndexResponse) {
    option (auth_options.required_token_scopes) = CONTENT_R;
  }

  // Get the list of blob names associated with each index_id specified in the request.
  // Blobs for the same (tenant, tkey, index_id) key should be merged client side, as
  // the message may be split to fit in the gRPC message length limit.
  // Required Scope: CONTENT_R
  rpc GetAnnIndexBlobInfos(GetAnnIndexBlobInfosRequest) returns (stream GetAnnIndexBlobInfosResponse) {
    option (auth_options.required_token_scopes) = CONTENT_R;
  }

  // Get a given index asset for the given ANN index id. Index assets contain the
  // index proper and all of the resources needed to conduct a ANN search.
  // Required Scope: CONTENT_R
  rpc GetAnnIndexAsset(GetAnnIndexAssetRequest) returns (stream GetAnnIndexAssetResponse) {
    option (auth_options.required_token_scopes) = AUTH_R;
    option (auth_options.required_token_scopes) = CONTENT_R;
  }

  // Enter an index mapping for a (tenant, tkey, checkpoint) key tuple
  // Required Scope: CONTENT_RW
  rpc AddAnnIndexMapping(AddAnnIndexMappingRequest) returns (AddAnnIndexMappingResponse) {
    option (auth_options.required_token_scopes) = AUTH_R;
    option (auth_options.required_token_scopes) = CONTENT_RW;
  }

  // upload the blob embeddings for the given ANN index id.
  // Required Scope: CONTENT_RW
  rpc UploadAnnIndexBlobInfos(stream UploadAnnIndexBlobInfosRequest) returns (UploadAnnIndexBlobInfosResponse) {
    option (auth_options.required_token_scopes) = CONTENT_RW;
  }

  // Upload index assets for a (tenant, tkey, index_id) ann index key tuple
  // Required Scope: CONTENT_RW
  rpc UploadAnnIndexAssets(stream UploadAnnIndexAssetsRequest) returns (UploadAnnIndexAssetsResponse) {
    option (auth_options.required_token_scopes) = CONTENT_RW;
  }

  // Extend the lifetime of a checkpoint to prevent it from being garbage collected
  // Required Scope: CONTENT_RW
  rpc ExtendCheckpointLifetimes(ExtendCheckpointLifetimesRequest) returns (stream ExtendCheckpointLifetimesResponse) {
    option (auth_options.required_token_scopes) = CONTENT_RW;
  }
}

enum BlobMetadataKey {
  // pathname of a blob that corresponds to a file name.
  path = 0;
}

// metadata about a blob.
// the content manager will not interpret the results
message BlobMetadata {
  string key = 1 [debug_redact = true];
  string value = 2 [debug_redact = true];
}

// request to uplaod a blob.
//
// Any later upload call for the same blob name will be ignored. A blob is immutable.
// Should there be any concurrent upload calls for the same blob name, any one might "win".
message UploadBlobContentRequest {
  reserved 1;

  // metadata for the blob
  // it is assumed that each key is unique in the metadata list, i.e. it can be used as a
  // dictionary
  repeated BlobMetadata metadata = 2;

  // the content of the blob as bytes
  bytes content = 3 [debug_redact = true];

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 6;
}

message UploadBlobContentResponse {
  // set to true if and only if the blob already existed on the server
  bool existed = 1;

  // the resulting blob name
  string blob_name = 2;
}

// upload for a sub key. The blob key and transformation key
// are fixed by the UploadTransformedBlobContentRequest.
message UploadTransformedBlobContent {
  // the sub key allows to upload multiple transformed contents for each blob and transformation
  // key.
  //
  // The content of a sub key can be thought of as different files or objects for a given
  // blob/transformation key.
  string sub_key = 4;

  // the content to uplaod for a given blob/transformation key/sub-key combination.
  // usually not empty except for the request item with final_chunk set to true
  bytes content = 5 [debug_redact = true];

  // metadata for the blob
  // it is assumed that each key is unique in the metadata list, i.e. it can be used as a
  // dictionary
  repeated BlobMetadata metadata = 6;
}

// uploads a set of sub_key objects for a given blob name and a given transformation key
message UploadTransformedBlobContentRequest {
  // receipt handle from a notification
  // needs to be set for the first request item in a request
  // if an empty string for the first request, no notification is deleted.
  string receipt_handle = 1;

  // blob name.
  // needs to be in the first request item in a request.
  // needs to be the same value in later requests or empty
  string blob_name = 2;

  // transformation key for which to upload data.
  //
  // the transformation key is a unique identifier to an subscriber to blob updates, e.g.
  // an indexer.
  //
  // needs to be in the first request item in a request.
  // needs to be the same value in later requests or empty
  string transformation_key = 3;

  reserved 4, 5, 6;

  repeated UploadTransformedBlobContent contents = 7;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 8;
}

message UploadTransformedBlobContentResponse {}

// This request is used to subscribe to blob updates and, for
// SubscribeBlobUpdatesFlowControlled, to set an initial window.
//
// For SubscribeBlobUpdatesFlowControlled, the client can also
// send window updates using this message.
message SubscribeBlobUpdatesRequest {
  // the transformation key is a unique identifier to an subscriber to blob updates, e.g.
  // an indexer.
  //
  // For SubscribeBlobUpdatesFlowControlled, the transformation key is ignored after the first
  // SubscribeBlobUpdatesRequest.
  string transformation_key = 1;

  // The number of blob updates the client is willing to accept, cumulative
  // since the start of the RPC
  uint64 window = 2;
}

// informs a subscriber about a newly uploaded
//
// The server might send responses where blob_name is set to the empty string. The subscriber should
// ignore such messages. The message serves as keepalive between the client and the server. The
// server might inform about a blob multiple times and/or about blobs that no longer exist.
message SubscribeBlobUpdatesResponse {
  // the blob name that was newly updated
  string blob_name = 1;

  // the content of the blob as bytes.
  bytes raw_content = 2 [debug_redact = true];

  // metadata about the blob, e.g. the path name
  repeated BlobMetadata metadata = 3;

  // the receipt handle that should be presented to UploadTransformedBlobContent
  // to stop the subscribers to be notified about the update
  string receipt_handle = 4;

  // timestamp of the time the blob was uploaded.
  // The time is approximate and should not be relied on for correctness.
  google.protobuf.Timestamp time = 5;

  // the name of the tenant id
  string tenant_id = 6;
}

message FindMissingBlobRequest {
  // names of blobs to check
  repeated string blob_names = 1;

  // transformation key to check
  string transformation_key = 2;

  // sub_key to check
  string sub_key = 3;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 4;
}

message FindMissingBlobResponse {
  repeated string unknown_blob_names = 1;
}

message GetContentRequest {
  string blob_name = 1;

  // if the transformation key is set to the empty string, the raw blob is requested
  string transformation_key = 2;
  string sub_key = 3;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 4;
}

message GetContentResponse {
  bytes content = 1 [debug_redact = true];

  // this request indicates final content chunk for the object.
  // the content is a sha256 hash of the uploaded content for the given sub key
  string final_hash = 2;

  // metadata about the blob, e.g. the path name
  // only send in the final chunk
  repeated BlobMetadata metadata = 3;
}

message BatchGetContentRequest {
  // blob content keys whose content and metadata to return
  repeated GetContentRequest requests = 1;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 2;
}

// the given blob content is is not found the the content manager
message BatchGetContentNotFound {
  // blob name of the content not found
  string blob_name = 1;

  // transformation key of the content not found
  //
  // if the transformation key is set to the empty string, the raw blob is requested
  string transformation_key = 2;

  // sub key of the blob content not found
  string sub_key = 3;
}

message BatchGetContentFinalContent {
  bytes content = 1 [debug_redact = true];

  // this request indicates final content chunk for the object.
  // the content is a sha256 hash of the uploaded content for the given sub key
  string final_hash = 2;

  // metadata about the blob:
  // - path: the path of the file that the blob represents
  // - upload_time: the time the blob was uploaded, in UTC in RFC3339 format, e.g. 2024-07-08T10:05:57.453Z
  // only send in the final chunk
  repeated BlobMetadata metadata = 3;

  string blob_name = 4;
  string transformation_key = 5;
  string sub_key = 6;
}

// a response for a content key of a blob content
// for each key:
// Either an unknown_blob or a final content is emitted

// The keys are reported in the same order as in the request. However, bigtable may be faster when
// reading keys that are close together, so callers should sort the input if possible.
message BatchGetContentResponse {
  oneof response {
    // one of ther requested keys could not be found
    BatchGetContentNotFound not_found_content = 1;

    // the final content chunk for a blob content
    BatchGetContentFinalContent final_content = 3;
  }

  reserved 2;
}

message GetBlobInfoRequest {
  string blob_name = 1;

  // if the transformation key is set to the empty string, the metadata for the main ("raw") blob
  // is returned
  string transformation_key = 2;

  // the subkey for which to return the metadata if transformation_key is set
  string sub_key = 3;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 4;
}

message GetBlobInfoResponse {
  // sha-256 hash of the content
  string content_hash = 1;

  // size of the content in bytes
  uint64 size = 2;

  // metadata about the blob, e.g. the path name
  // only send in the final chunk
  repeated BlobMetadata metadata = 3;

  // the list of transformation keys informed about the blob
  //
  // if a transformation key was added after the blob was uploaded, the key
  // might not yet be informed about the blob. If the blob is still used, i.e.
  // if it is part of a find missing call, the subscribers will be notified
  // about the blob.
  //
  // the informed transformation keys is a part of the blob info that is not
  // immutable and can change. Most of the blob info is immutable.
  // Due to caching, the information of this field and this field only might be outdated.
  repeated string informed_transformation_keys = 4;

  // the list of transformation keys that have finished uploading information about the blob.
  //
  // The uploaded transformation keys is a part of the blob info that is not
  // immutable and can change. Most of the blob info is immutable.
  // Due to caching, the information of this field and this field only might be outdated.
  repeated string uploaded_transformation_keys = 6;

  google.protobuf.Timestamp time = 5;
}

message BlobContentKey {
  string blob_name = 1;
  string transformation_key = 2;
  string sub_key = 3;
}

message BatchGetBlobInfoRequest {
  reserved 1;

  // blob content keys whose info to return
  repeated BlobContentKey blob_content_keys = 3;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 2;
}

message BatchBlobInfoResponse {
  BlobContentKey blob_content_key = 1;
  // if the blob key wasn't found, the blob_info is not set
  GetBlobInfoResponse blob_info = 2;
}

message BatchGetBlobInfoResponse {
  reserved 1;

  // blob infos for the requested blob names
  //
  // the blob infos will contain an entry for blob content key requested
  repeated BatchBlobInfoResponse blob_infos = 2;
}

message ListBlobContentKeysRequest {
  // blob name for which to list all transformation keys/sub keys
  string blob_name = 1;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 2;
}

message BlobContentsKey {
  string transformation_key = 1;
  string sub_key = 2;
}

message ListBlobContentKeysResponse {
  repeated BlobContentsKey keys = 1;
}

message UploadBlobContent {
  // metadata for the blob
  // it is assumed that each key is unique in the metadata list, i.e. it can be used as a
  // dictionary
  repeated BlobMetadata metadata = 2;

  // the content of the blob as bytes
  bytes content = 3 [debug_redact = true];
}

// enum for indexing priorities (as defined by the caller)
enum IndexingPriority {
  DEFAULT = 0;
  // should be indexed with lower priority than default
  LOW = 1;
}

// request for upload multiple blob contents
message BatchUploadBlobContentRequest {
  // entries
  repeated UploadBlobContent entries = 1;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 2;

  IndexingPriority priority = 3;
}

message UploadBlobResult {
  // set to true if and only if the blob already existed on the server
  bool existed = 1;

  string blob_name = 2;
}

message BatchUploadBlobContentResponse {
  repeated UploadBlobResult results = 1;
}

message DeleteBlobRequest {
  string blob_name = 1;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 2;

  // To ensure that the user index and blob contents always stay in sync, we
  // require that the user id be provided here so that the user index entry can
  // be deleted in addition to the blob itself. If the user index entry is not
  // found, the request will fail.
  //
  // Note that the user id is the email, not the augment opaque user id.
  string user_id = 3;

  // Optional timestamp specifying when the blob was uploaded. This is useful for
  // deleting user index entries even when the associated blob content has already
  // been deleted. This helps clean up duplicate user index entries that might exist
  // due to a bug where batch uploading an already existing blob multiple times in
  // a single request created duplicate user index entries.
  //
  // When provided, the deletion will proceed without retreiving the upload time from the
  // underlying blob content or info, using this timestamp to locate and delete the user index
  // entry, as well as info and content entries if they exist.
  google.protobuf.Timestamp timestamp = 4;
}

message DeleteBlobResponse {}

message BatchDeleteBlobsRequest {
  message BlobUserPair {
    string blob_name = 1;
    // To ensure that the user index and blob contents always stay in sync, we
    // require that the user id be provided here so that the user index entry can
    // be deleted in addition to the blob itself. If the user index entry is not
    // found, the request will fail.
    string user_id = 2;
    // Optional timestamp specifying when the blob was uploaded. See DeleteBlobRequest
    // for more details.
    google.protobuf.Timestamp timestamp = 3;
  }

  // List of blob-user pairs to delete
  // Maximum number of entries is limited by batch_delete_blob_limit in the server configuration
  // (typically 1000 entries). Requests exceeding this limit will be rejected with OUT_OF_RANGE error.
  repeated BlobUserPair entries = 1;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 2;

  // If true, will not delete the user index entries.
  bool ignore_index = 3;
}

message BatchDeleteBlobsResponse {}

message GetAllBlobsFromCheckpointRequest {
  string checkpoint_id = 1;
  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 2;
}

message GetAllBlobsFromCheckpointResponse {
  repeated bytes blob_names = 1;
}

message CheckpointBlobsRequest {
  base.blob_names.Blobs blobs = 1;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 2;
}

message CheckpointBlobsResponse {
  string checkpoint_id = 1;
}

message GetUserBlobsRequest {
  // The id of the user - this is the email, not the augment user id
  string user_id = 1;

  // The id of the tenant. Note that the caller must have permission to operate
  // on that tenant and if not set, the tenant will be extracted from the
  // caller's token.
  string tenant_id = 2;

  // The maximum number of blob names to return. Note that the server will
  // impose a maximum for this value in order to prevent memory issues. If not
  // provided, the server will automatically apply the maximum value.
  optional uint32 limit = 3;

  // min and max_timestamp define the limits of the search. The response will
  // return blobs with upload time between min and max_timestamp in reverse
  // chronological order (more recent uploads coming first). The specified
  // window is inclusive on both ends and the returned blobs are guaranteed to
  // be the most recently uploaded in the window. Though the backend only
  // stores upload times to single-second precision, the nanos field on the
  // input timestamps are rounded such the filter works as expected
  // (min_timestamp rounded up when nanos are nonzero, max rounded down).
  optional google.protobuf.Timestamp min_timestamp = 4;
  optional google.protobuf.Timestamp max_timestamp = 5;
}

message GetUserBlobsResponse {
  repeated UploadInfo entries = 1;
}

message UploadInfo {
  // The blob name
  string blob_name = 1;

  // Timestamp of the time the blob was uploaded. Since the backend only tracks
  // upload times to single-second precision, it is expected for the nanos
  // field to always be 0.
  google.protobuf.Timestamp time = 2;
}

message GetBestAnnIndexRequest {
  optional string tenant_id = 1;
  string transformation_key = 2;
  string checkpoint_id = 3;
}

message GetBestAnnIndexResponse {
  string index_id = 1;
  // Checkpoints may diverge from on index. This is the delta of the blobs that are
  // present in the checkpoint and not in the index.
  repeated bytes added_blobs = 2;
  // Delta of the blobs that are present in the index but not in the checkpoint
  repeated bytes removed_blobs = 3;
}

// Key that uniquely identifies an ANN index. Used for lookups.
message AnnIndexKey {
  optional string tenant_id = 1;
  string transformation_key = 2;
  string index_id = 3;
}

message GetAnnIndexBlobInfosRequest {
  // keys to query blobs for
  AnnIndexKey key = 1;
}

// Information about a blob embedding
message AnnIndexBlobInfo {
  bytes blob_name = 1;
  // number of chunks in the blob for this index key (tenant_id, tkey, index_id)
  uint32 chunk_count = 2;
}

message GetAnnIndexBlobInfosResponse {
  // a BlobInfo metadata object for each blob in the index.
  repeated AnnIndexBlobInfo blob_infos = 1;
}

message GetAnnIndexAssetRequest {
  AnnIndexAssetKey key = 1;
}

message GetAnnIndexAssetResponse {
  // data chunk of asset. consecutive chunks should be merged.
  bytes data = 1;
}

message AddAnnIndexMappingRequestKey {
  optional string tenant_id = 1;
  string transformation_key = 2;
  string checkpoint_id = 3;
}

message AddAnnIndexMappingRequest {
  AddAnnIndexMappingRequestKey key = 1;
  // target index for mapping
  string index_id = 2;
  // blobs in checkpoint but not included in the index
  repeated bytes added_blobs = 3;
  // blobs not in checkpoint but included in the index
  repeated bytes removed_blobs = 4;
}

message AddAnnIndexMappingResponse {}

message UploadAnnIndexBlobInfosRequest {
  optional AnnIndexKey key = 1;
  // Must be set on the last message in a blob embedding upload/download stream to inform the
  // server/client that the transaction is over. Any messages sent on the stream after
  // a message with the flag is set is an error.
  bool last_message = 2;
  repeated AnnIndexBlobInfo blob_infos = 3;
}

message UploadAnnIndexBlobInfosResponse {}

message AnnIndexAssetKey {
  // tenant to query (leave out to use tenant from token)
  optional string tenant_id = 1;
  // transformation key of asset
  string transformation_key = 2;
  // index_id of asset
  string index_id = 3;
  // The unique identifier for the asset in the context of the index
  string sub_key = 4;
}

message UploadAnnIndexAssetsRequest {
  // key of asset to upload. Should be set for first message of each asset in stream
  optional AnnIndexAssetKey key = 1;
  // data of asset chunk to upload
  bytes data = 2;
  // Must be set on the last message in a asset upload/download stream to inform the
  // server/client that the transaction is over. Any messages sent on the stream after
  // a message with the flag is set is an error.
  bool last_message = 3;
}

message UploadAnnIndexAssetsResponse {}

message ExtendCheckpointLifetimesRequest {
  // The checkpoint ID to extend the lifetime for
  string checkpoint_id = 1;

  // the name of the tenant id
  // the caller has to have permission to operate on that tenant.
  // if not set, the tenant will be extracted from the caller's token.
  string tenant_id = 2;
}

message ExtendCheckpointLifetimesResponse {
  // Total number of blobs processed so far
  uint32 total_blobs_processed = 1;

  // Total number of blobs to process
  uint32 total_blobs_count = 2;
}
