"""Test user-keyed tables for proper user isolation and data separation."""

import pytest

from services.bigtable_proxy import bigtable_proxy_pb2
from services.token_exchange import token_scopes_pb2

from services.bigtable_proxy.test.bigtable_test_utils import (
    check_mutate_resp,
    check_read_with_retry,
    make_entry,
    make_cell_mutation,
    Mutation,
    RowFilter,
    RowSet,
    RowRange,
    generate_unique_row_key,
)


def _make_notification_mutation(value, timestamp_micros=1643723400000):
    """Create a notification mutation for testing."""
    return make_cell_mutation("Notifications", b"Timestamp", value, timestamp_micros)


def _check_notification_read_with_retry(fn, row_key, value):
    """Check notification read with retry."""
    check_read_with_retry(fn, row_key, value, "Notifications", b"Timestamp")


def _verify_read_responses(responses, expected_key_values):
    """
    Verify that read responses contain exactly the expected keys and values.

    Args:
        responses: List of ReadRowsResponse objects
        expected_key_values: Dict mapping row_keys to expected values
    """
    assert len(responses) == len(
        expected_key_values
    ), f"Expected {len(expected_key_values)} responses, got {len(responses)}"

    found_keys = set()
    for response in responses:
        for chunk in response.chunks:
            found_keys.add(chunk.row_key)
            assert (
                chunk.row_key in expected_key_values
            ), f"Unexpected row key: {chunk.row_key}"
            assert (
                chunk.value == expected_key_values[chunk.row_key]
            ), f"Value mismatch for {chunk.row_key}: expected {expected_key_values[chunk.row_key]}, got {chunk.value}"

    assert found_keys == set(
        expected_key_values.keys()
    ), f"Missing keys: {set(expected_key_values.keys()) - found_keys}"


def _create_request_context_for_user(token_exchange_server, tenant_id, namespace):
    """Create a request context for a specific user."""
    return token_exchange_server.create_test_request_context(
        tenant_id=tenant_id,
        namespace=namespace,
        scopes=[
            token_scopes_pb2.REQUEST_RW,
            token_scopes_pb2.CONTENT_RW,
            token_scopes_pb2.NOTIFICATION_RW,
        ],
    )


@pytest.mark.timeout(30)
def test_user_keyed_table_basic_operations(
    bigtable_emulator,
    bigtable_tables,
    bigtable_proxy_server,
    token_exchange_server,
):
    """Test basic read/write operations for user-keyed tables."""
    table_name = bigtable_proxy_pb2.TableName.NOTIFICATION
    tenant_id = "test_tenant_id_1"
    user_id = "user123"

    # Create request context for the user
    request_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )

    # Test 1: Write data for user
    row_key = generate_unique_row_key("notification")
    value = b"test_notification_value"
    mut = _make_notification_mutation(value)
    entries = [make_entry(row_key, mut)]

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        user_id=user_id,
        table_name=table_name,
        entries=entries,
    )
    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Test 2: Read data back for the same user
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user_id,
        table_name=table_name,
        rows=RowSet(row_keys=[row_key]),
    )
    _check_notification_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr, metadata=request_context.to_metadata()
        ),
        row_key,
        value,
    )

    # Test 3: Update data for the same user
    updated_value = b"updated_notification_value"
    mut_update = _make_notification_mutation(updated_value)
    entries_update = [make_entry(row_key, mut_update)]

    mutate_update_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        user_id=user_id,
        table_name=table_name,
        entries=entries_update,
    )
    mutate_update_resp = bigtable_proxy_server.MutateRows(
        mutate_update_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_update_resp)

    # Test 4: Verify updated data
    _check_notification_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr, metadata=request_context.to_metadata()
        ),
        row_key,
        updated_value,
    )


@pytest.mark.timeout(30)
def test_user_data_isolation(
    bigtable_emulator,
    bigtable_tables,
    bigtable_proxy_server,
    token_exchange_server,
):
    """Test that users cannot access each other's data."""
    table_name = bigtable_proxy_pb2.TableName.NOTIFICATION
    tenant_id = "test_tenant_id_1"
    user1_id = "user1"
    user2_id = "user2"

    # Create request contexts for both users
    user1_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )
    user2_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )

    # Use the same row key for both users to test isolation
    common_row_key = b"common_notification_key"
    user1_value = b"user1_notification_data"
    user2_value = b"user2_notification_data"

    # Test 1: User1 writes data
    mut1 = _make_notification_mutation(user1_value)
    entries1 = [make_entry(common_row_key, mut1)]

    mutate_rr1 = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        user_id=user1_id,
        table_name=table_name,
        entries=entries1,
    )
    mutate_resp1 = bigtable_proxy_server.MutateRows(
        mutate_rr1, metadata=user1_context.to_metadata()
    )
    check_mutate_resp(mutate_resp1)

    # Test 2: User2 writes data with the same row key
    mut2 = _make_notification_mutation(user2_value)
    entries2 = [make_entry(common_row_key, mut2)]

    mutate_rr2 = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        user_id=user2_id,
        table_name=table_name,
        entries=entries2,
    )
    mutate_resp2 = bigtable_proxy_server.MutateRows(
        mutate_rr2, metadata=user2_context.to_metadata()
    )
    check_mutate_resp(mutate_resp2)

    # Test 3: User1 can only read their own data
    read_rr1 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user1_id,
        table_name=table_name,
        rows=RowSet(row_keys=[common_row_key]),
    )
    _check_notification_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr1, metadata=user1_context.to_metadata()
        ),
        common_row_key,
        user1_value,
    )

    # Test 4: User2 can only read their own data
    read_rr2 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user2_id,
        table_name=table_name,
        rows=RowSet(row_keys=[common_row_key]),
    )
    _check_notification_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr2, metadata=user2_context.to_metadata()
        ),
        common_row_key,
        user2_value,
    )

    # Test 5: User1 cannot see User2's data when reading all rows
    read_all_rr1 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user1_id,
        table_name=table_name,
    )
    responses1 = list(
        bigtable_proxy_server.ReadRows(
            read_all_rr1, metadata=user1_context.to_metadata()
        )
    )
    _verify_read_responses(responses1, {common_row_key: user1_value})

    # Test 6: User2 cannot see User1's data when reading all rows
    read_all_rr2 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user2_id,
        table_name=table_name,
    )
    responses2 = list(
        bigtable_proxy_server.ReadRows(
            read_all_rr2, metadata=user2_context.to_metadata()
        )
    )
    _verify_read_responses(responses2, {common_row_key: user2_value})


@pytest.mark.timeout(30)
def test_user_keyed_table_requires_user_id(
    bigtable_emulator,
    bigtable_tables,
    bigtable_proxy_server,
    token_exchange_server,
):
    """Test that user-keyed tables require user_id to be set."""
    table_name = bigtable_proxy_pb2.TableName.NOTIFICATION
    tenant_id = "test_tenant_id_1"

    # Create request context without user_id (using tenant context)
    request_context = token_exchange_server.create_test_request_context(
        tenant_id=tenant_id,
        namespace="augment",
        scopes=[
            token_scopes_pb2.REQUEST_RW,
            token_scopes_pb2.CONTENT_RW,
            token_scopes_pb2.NOTIFICATION_RW,
        ],
    )

    # Test 1: Try to mutate without user_id (should fail)
    row_key = b"test_key"
    value = b"test_value"
    mut = _make_notification_mutation(value)
    entries = [make_entry(row_key, mut)]

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        # user_id is intentionally omitted
        table_name=table_name,
        entries=entries,
    )

    # This should result in an error for user-keyed tables
    with pytest.raises(Exception) as exc_info:
        bigtable_proxy_server.MutateRows(
            mutate_rr, metadata=request_context.to_metadata()
        )

    # Verify the error is related to missing user_id
    assert (
        "user_id" in str(exc_info.value).lower()
        or "user" in str(exc_info.value).lower()
    )

    # Test 2: Try to read without user_id (should fail)
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        # user_id is intentionally omitted
        table_name=table_name,
        rows=RowSet(row_keys=[row_key]),
    )

    # This should result in an error for user-keyed tables
    with pytest.raises(Exception) as exc_info:
        list(
            bigtable_proxy_server.ReadRows(
                read_rr, metadata=request_context.to_metadata()
            )
        )

    # Verify the error is related to missing user_id
    assert (
        "user_id" in str(exc_info.value).lower()
        or "user" in str(exc_info.value).lower()
    )


@pytest.mark.timeout(30)
def test_user_data_separation_multiple_keys(
    bigtable_emulator,
    bigtable_tables,
    bigtable_proxy_server,
    token_exchange_server,
):
    """Test user data separation with multiple row keys."""
    table_name = bigtable_proxy_pb2.TableName.NOTIFICATION
    tenant_id = "test_tenant_id_1"
    user1_id = "user_alpha"
    user2_id = "user_beta"

    # Create request contexts for both users
    user1_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )
    user2_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )

    # Multiple row keys for each user (same keys, different users)
    common_keys = [b"key_1", b"key_2", b"key_3"]

    # User 1 mutations
    user1_mutations = []
    user1_expected_values = {}
    for i, key in enumerate(common_keys):
        value = f"user1_data_{i}".encode()
        user1_mutations.append(make_entry(key, _make_notification_mutation(value)))
        user1_expected_values[key] = value

    # User 2 mutations
    user2_mutations = []
    user2_expected_values = {}
    for i, key in enumerate(common_keys):
        value = f"user2_data_{i}".encode()
        user2_mutations.append(make_entry(key, _make_notification_mutation(value)))
        user2_expected_values[key] = value

    # Test 1: Write data for both users
    mutate_rr_user1 = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        user_id=user1_id,
        table_name=table_name,
        entries=user1_mutations,
    )
    mutate_resp1 = bigtable_proxy_server.MutateRows(
        mutate_rr_user1, metadata=user1_context.to_metadata()
    )
    check_mutate_resp(mutate_resp1)

    mutate_rr_user2 = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        user_id=user2_id,
        table_name=table_name,
        entries=user2_mutations,
    )
    mutate_resp2 = bigtable_proxy_server.MutateRows(
        mutate_rr_user2, metadata=user2_context.to_metadata()
    )
    check_mutate_resp(mutate_resp2)

    # Test 2: Read all data for user1
    read_rr_user1 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user1_id,
        table_name=table_name,
        rows=RowSet(row_keys=common_keys),
    )
    responses1 = list(
        bigtable_proxy_server.ReadRows(
            read_rr_user1, metadata=user1_context.to_metadata()
        )
    )
    _verify_read_responses(responses1, user1_expected_values)

    # Test 3: Read all data for user2
    read_rr_user2 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user2_id,
        table_name=table_name,
        rows=RowSet(row_keys=common_keys),
    )
    responses2 = list(
        bigtable_proxy_server.ReadRows(
            read_rr_user2, metadata=user2_context.to_metadata()
        )
    )
    _verify_read_responses(responses2, user2_expected_values)

    # Test 4: Verify each user gets exactly their own data count
    assert len(responses1) == len(
        common_keys
    ), f"User1 should get {len(common_keys)} responses"
    assert len(responses2) == len(
        common_keys
    ), f"User2 should get {len(common_keys)} responses"


@pytest.mark.timeout(30)
def test_user_keyed_table_row_ranges(
    bigtable_emulator,
    bigtable_tables,
    bigtable_proxy_server,
    token_exchange_server,
):
    """Test row range queries on user-keyed tables."""
    table_name = bigtable_proxy_pb2.TableName.NOTIFICATION
    tenant_id = "test_tenant_id_1"
    user_id = "range_test_user"

    # Create request context for the user
    request_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )

    # Create multiple rows with ordered keys
    row_keys = [f"row_{i:03d}".encode() for i in range(10)]
    mutations = []
    expected_values = {}

    for i, key in enumerate(row_keys):
        value = f"value_{i:03d}".encode()
        mutations.append(make_entry(key, _make_notification_mutation(value)))
        expected_values[key] = value

    # Test 1: Write all data
    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        user_id=user_id,
        table_name=table_name,
        entries=mutations,
    )
    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=request_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Test 2: Range query (rows 3-7, exclusive end)
    start_key = b"row_003"
    end_key = b"row_008"  # Exclusive end

    row_range = RowRange(
        start_key_closed=start_key,
        end_key_open=end_key,
    )

    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user_id,
        table_name=table_name,
        rows=RowSet(row_ranges=[row_range]),
    )

    responses = list(
        bigtable_proxy_server.ReadRows(read_rr, metadata=request_context.to_metadata())
    )

    # Should return rows 3, 4, 5, 6, 7 (5 rows total)
    expected_range_keys = [b"row_003", b"row_004", b"row_005", b"row_006", b"row_007"]
    expected_range_values = {key: expected_values[key] for key in expected_range_keys}

    _verify_read_responses(responses, expected_range_values)
    assert (
        len(responses) == 5
    ), f"Expected 5 responses for range query, got {len(responses)}"

    # Test 3: Open-ended range query (from row_007 onwards)
    open_range = RowRange(start_key_closed=b"row_007")

    read_open_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user_id,
        table_name=table_name,
        rows=RowSet(row_ranges=[open_range]),
    )

    open_responses = list(
        bigtable_proxy_server.ReadRows(
            read_open_rr, metadata=request_context.to_metadata()
        )
    )

    # Should return rows 7, 8, 9 (3 rows total)
    expected_open_keys = [b"row_007", b"row_008", b"row_009"]
    expected_open_values = {key: expected_values[key] for key in expected_open_keys}

    _verify_read_responses(open_responses, expected_open_values)
    assert (
        len(open_responses) == 3
    ), f"Expected 3 responses for open range query, got {len(open_responses)}"


@pytest.mark.timeout(30)
def test_user_keyed_check_and_mutate(
    bigtable_emulator,
    bigtable_tables,
    bigtable_proxy_server,
    token_exchange_server,
):
    """Test CheckAndMutateRow with user isolation."""
    table_name = bigtable_proxy_pb2.TableName.NOTIFICATION
    tenant_id = "test_tenant_id_1"
    user1_id = "check_user_1"
    user2_id = "check_user_2"

    # Create request contexts for both users
    user1_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )
    user2_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )

    # Same row key for both users
    row_key = b"check_and_mutate_key"

    # Test 1: Initial data for user 1
    initial_value = b"initial_value_user1"
    initial_mut = _make_notification_mutation(initial_value)
    initial_entries = [make_entry(row_key, initial_mut)]

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        user_id=user1_id,
        table_name=table_name,
        entries=initial_entries,
    )
    mutate_resp = bigtable_proxy_server.MutateRows(
        mutate_rr, metadata=user1_context.to_metadata()
    )
    check_mutate_resp(mutate_resp)

    # Test 2: CheckAndMutateRow for user 1 (should succeed - data exists)
    predicate_filter = RowFilter(family_name_regex_filter="Notifications")

    true_mutation = Mutation(
        set_cell=Mutation.SetCell(
            family_name="Notifications",
            column_qualifier=b"Timestamp",
            value=b"updated_value_user1",
            timestamp_micros=1643723400001,  # Different timestamp to ensure update
        )
    )

    check_mutate_rr_user1 = bigtable_proxy_pb2.CheckAndMutateRowRequest(
        tenant_id=tenant_id,
        user_id=user1_id,
        table_name=table_name,
        row_key=row_key,
        predicate_filter=predicate_filter,
        true_mutations=[true_mutation],
    )

    check_resp1 = bigtable_proxy_server.CheckAndMutateRow(
        check_mutate_rr_user1, metadata=user1_context.to_metadata()
    )

    # User 1's check should succeed (data exists)
    assert check_resp1.predicate_matched, "User1's predicate should match (data exists)"

    # Test 3: CheckAndMutateRow for user 2 with same row key (should fail - no data for user 2)
    check_mutate_rr_user2 = bigtable_proxy_pb2.CheckAndMutateRowRequest(
        tenant_id=tenant_id,
        user_id=user2_id,
        table_name=table_name,
        row_key=row_key,
        predicate_filter=predicate_filter,
        true_mutations=[true_mutation],
    )

    check_resp2 = bigtable_proxy_server.CheckAndMutateRow(
        check_mutate_rr_user2, metadata=user2_context.to_metadata()
    )

    # User 2's check should fail (no data for user 2 with that key)
    assert (
        not check_resp2.predicate_matched
    ), "User2's predicate should not match (no data for user2)"

    # Test 4: Verify user 1's data was updated
    read_rr_user1 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user1_id,
        table_name=table_name,
        rows=RowSet(row_keys=[row_key]),
    )
    _check_notification_read_with_retry(
        lambda: bigtable_proxy_server.ReadRows(
            read_rr_user1, metadata=user1_context.to_metadata()
        ),
        row_key,
        b"updated_value_user1",
    )

    # Test 5: Verify user 2 has no data for this key
    read_rr_user2 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user2_id,
        table_name=table_name,
        rows=RowSet(row_keys=[row_key]),
    )
    responses2 = list(
        bigtable_proxy_server.ReadRows(
            read_rr_user2, metadata=user2_context.to_metadata()
        )
    )

    # User 2 should get no data
    assert (
        len(responses2) == 0
    ), f"User2 should have no data, but got {len(responses2)} responses"


@pytest.mark.timeout(30)
def test_user_keyed_table_requires_user_id(
    bigtable_emulator,
    bigtable_tables,
    bigtable_proxy_server,
    token_exchange_server,
):
    """Test that user-keyed tables require user_id parameter."""
    table_name = bigtable_proxy_pb2.TableName.NOTIFICATION
    tenant_id = "test_tenant_id_1"

    # Create request context without user_id
    request_context = token_exchange_server.create_test_request_context(
        tenant_id=tenant_id,
        namespace="augment",
        scopes=[
            token_scopes_pb2.REQUEST_RW,
            token_scopes_pb2.CONTENT_RW,
            token_scopes_pb2.NOTIFICATION_RW,
        ],
    )

    # Test 1: MutateRows without user_id should fail
    row_key = generate_unique_row_key("notification")
    value = b"test_value"
    mut = _make_notification_mutation(value)
    entries = [make_entry(row_key, mut)]

    mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
        tenant_id=tenant_id,
        # user_id is intentionally omitted
        table_name=table_name,
        entries=entries,
    )

    with pytest.raises(Exception) as exc_info:
        list(
            bigtable_proxy_server.MutateRows(
                mutate_rr, metadata=request_context.to_metadata()
            )
        )
    assert "user_id is required" in str(exc_info.value).lower()

    # Test 2: ReadRows without user_id should fail
    read_rr = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        # user_id is intentionally omitted
        table_name=table_name,
        rows=RowSet(row_keys=[row_key]),
    )

    with pytest.raises(Exception) as exc_info:
        list(
            bigtable_proxy_server.ReadRows(
                read_rr, metadata=request_context.to_metadata()
            )
        )
    assert "user_id is required" in str(exc_info.value).lower()


@pytest.mark.timeout(30)
def test_user_data_separation_multiple_keys(
    bigtable_emulator,
    bigtable_tables,
    bigtable_proxy_server,
    token_exchange_server,
):
    """Test that multiple row keys for different users are properly separated."""
    table_name = bigtable_proxy_pb2.TableName.NOTIFICATION
    tenant_id = "test_tenant_id_1"
    user1_id = "user1"
    user2_id = "user2"

    # Create request contexts for both users
    user1_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )
    user2_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )

    # Create multiple row keys and values for each user
    user1_data = {
        b"notification1": b"user1_notification1_data",
        b"notification2": b"user1_notification2_data",
        b"notification3": b"user1_notification3_data",
    }

    user2_data = {
        b"notification1": b"user2_notification1_data",  # Same key as user1
        b"notification4": b"user2_notification4_data",
        b"notification5": b"user2_notification5_data",
    }

    # Test 1: Write data for user1
    for row_key, value in user1_data.items():
        mut = _make_notification_mutation(value)
        entries = [make_entry(row_key, mut)]

        mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
            tenant_id=tenant_id,
            table_name=table_name,
            entries=entries,
        )
        mutate_rr.user_id = user1_id
        mutate_resp = bigtable_proxy_server.MutateRows(
            mutate_rr, metadata=user1_context.to_metadata()
        )
        check_mutate_resp(mutate_resp)

    # Test 2: Write data for user2
    for row_key, value in user2_data.items():
        mut = _make_notification_mutation(value)
        entries = [make_entry(row_key, mut)]

        mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
            tenant_id=tenant_id,
            table_name=table_name,
            entries=entries,
        )
        mutate_rr.user_id = user2_id
        mutate_resp = bigtable_proxy_server.MutateRows(
            mutate_rr, metadata=user2_context.to_metadata()
        )
        check_mutate_resp(mutate_resp)

    # Test 3: User1 can only read their own data
    read_all_rr1 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
    )
    read_all_rr1.user_id = user1_id
    responses1 = list(
        bigtable_proxy_server.ReadRows(
            read_all_rr1, metadata=user1_context.to_metadata()
        )
    )
    _verify_read_responses(responses1, user1_data)

    # Test 4: User2 can only read their own data
    read_all_rr2 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        table_name=table_name,
    )
    read_all_rr2.user_id = user2_id
    responses2 = list(
        bigtable_proxy_server.ReadRows(
            read_all_rr2, metadata=user2_context.to_metadata()
        )
    )
    _verify_read_responses(responses2, user2_data)

    # Test 5: Verify specific key reads work correctly
    for row_key, expected_value in user1_data.items():
        read_rr = bigtable_proxy_pb2.ReadRowsRequest(
            tenant_id=tenant_id,
            table_name=table_name,
            rows=RowSet(row_keys=[row_key]),
        )
        read_rr.user_id = user1_id
        _check_notification_read_with_retry(
            lambda rk=row_key, ev=expected_value: bigtable_proxy_server.ReadRows(
                bigtable_proxy_pb2.ReadRowsRequest(
                    tenant_id=tenant_id,
                    table_name=table_name,
                    rows=RowSet(row_keys=[rk]),
                    user_id=user1_id,
                ),
                metadata=user1_context.to_metadata(),
            ),
            row_key,
            expected_value,
        )


@pytest.mark.timeout(30)
def test_user_keyed_table_row_ranges(
    bigtable_emulator,
    bigtable_tables,
    bigtable_proxy_server,
    token_exchange_server,
):
    """Test row range queries for user-keyed tables."""
    table_name = bigtable_proxy_pb2.TableName.NOTIFICATION
    tenant_id = "test_tenant_id_1"
    user1_id = "user1"
    user2_id = "user2"

    # Create request contexts for both users
    user1_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )
    user2_context = _create_request_context_for_user(
        token_exchange_server, tenant_id, "augment"
    )

    # Create test data with predictable ordering
    user1_data = {
        b"a/notification1": b"user1_a_notification1",
        b"a/notification2": b"user1_a_notification2",
        b"b/notification1": b"user1_b_notification1",
        b"c/notification1": b"user1_c_notification1",
    }

    user2_data = {
        b"a/notification1": b"user2_a_notification1",  # Same keys as user1
        b"a/notification2": b"user2_a_notification2",
        b"b/notification1": b"user2_b_notification1",
        b"d/notification1": b"user2_d_notification1",  # Different key
    }

    # Write data for both users
    for user_id, user_data, context in [
        (user1_id, user1_data, user1_context),
        (user2_id, user2_data, user2_context),
    ]:
        for row_key, value in user_data.items():
            mut = _make_notification_mutation(value)
            entries = [make_entry(row_key, mut)]

            mutate_rr = bigtable_proxy_pb2.MutateRowsRequest(
                tenant_id=tenant_id,
                user_id=user_id,
                table_name=table_name,
                entries=entries,
            )
            mutate_resp = bigtable_proxy_server.MutateRows(
                mutate_rr, metadata=context.to_metadata()
            )
            check_mutate_resp(mutate_resp)

    # Test 1: Range query for user1 - prefix "a/"
    read_range_rr1 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user1_id,
        table_name=table_name,
        rows=RowSet(
            row_ranges=[
                RowRange(
                    start_key_closed=b"a/",
                    end_key_closed=b"a/\xff",
                )
            ]
        ),
    )
    responses1 = list(
        bigtable_proxy_server.ReadRows(
            read_range_rr1, metadata=user1_context.to_metadata()
        )
    )
    expected_user1_a_data = {k: v for k, v in user1_data.items() if k.startswith(b"a/")}
    _verify_read_responses(responses1, expected_user1_a_data)

    # Test 2: Range query for user2 - prefix "a/"
    read_range_rr2 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user2_id,
        table_name=table_name,
        rows=RowSet(
            row_ranges=[
                RowRange(
                    start_key_closed=b"a/",
                    end_key_closed=b"a/\xff",
                )
            ]
        ),
    )
    responses2 = list(
        bigtable_proxy_server.ReadRows(
            read_range_rr2, metadata=user2_context.to_metadata()
        )
    )
    expected_user2_a_data = {k: v for k, v in user2_data.items() if k.startswith(b"a/")}
    _verify_read_responses(responses2, expected_user2_a_data)

    # Test 3: Open range query for user1
    read_open_range_rr1 = bigtable_proxy_pb2.ReadRowsRequest(
        tenant_id=tenant_id,
        user_id=user1_id,
        table_name=table_name,
        rows=RowSet(
            row_ranges=[
                RowRange(
                    start_key_open=b"a/notification1",
                    end_key_closed=b"b/notification1",
                )
            ]
        ),
    )
    responses3 = list(
        bigtable_proxy_server.ReadRows(
            read_open_range_rr1, metadata=user1_context.to_metadata()
        )
    )
    expected_open_range_data = {
        b"a/notification2": b"user1_a_notification2",
        b"b/notification1": b"user1_b_notification1",
    }
    _verify_read_responses(responses3, expected_open_range_data)
