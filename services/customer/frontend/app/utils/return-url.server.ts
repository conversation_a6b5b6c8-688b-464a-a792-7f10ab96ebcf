import { Config } from "../.server/config";

/**
 * Returns a vetted return URL for Stripe flows.
 *
 * Origin preference: first Config.AUTH_CALLBACK_URL, otherwise the (https-forced) request URL origin.
 * A caller-provided JSON body field `returnUrl` is only accepted if it resolves to the same origin.
 * Falls back to `<origin>/<defaultPath>` when absent or invalid. The request body is read from a clone.
 */
export async function deriveReturnUrl(
  request: Request,
  defaultPath = "/account/subscription",
): Promise<string> {
  let configuredOrigin: string | undefined;
  try {
    configuredOrigin = new URL(Config.AUTH_CALLBACK_URL).origin;
  } catch {
    // ignore error
  }

  let requestOrigin: string | undefined;
  try {
    const reqUrl = new URL(request.url);
    reqUrl.protocol = "https:"; // enforce https
    requestOrigin = reqUrl.origin;
  } catch {
    // ignore error
  }

  const baseOrigin = configuredOrigin || requestOrigin;
  if (!baseOrigin) return defaultPath; // relative fallback (should not happen)

  const normalizedDefaultPath = defaultPath.startsWith("/")
    ? defaultPath
    : `/${defaultPath}`;
  let finalUrl = `${baseOrigin}${normalizedDefaultPath}`;

  try {
    const body = await request.clone().json();
    const candidate = body?.returnUrl;
    if (typeof candidate === "string") {
      try {
        const candidateUrl = new URL(candidate, baseOrigin);
        if (candidateUrl.origin === baseOrigin) {
          finalUrl = candidateUrl.href;
        }
      } catch {
        // ignore error
      }
    }
  } catch {
    // ignore error
  }

  return finalUrl;
}
