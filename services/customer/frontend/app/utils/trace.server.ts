import { trace } from "@opentelemetry/api";

export function getCurrentTraceId(): string | undefined {
  const span = trace.getActiveSpan();
  if (!span) return undefined;

  const spanContext = span.spanContext();
  return spanContext.traceId;
}

export function getTraceIdFromHeaders(request: Request): string | undefined {
  // Google Cloud trace header - this should always be the same as the Current Trace ID
  const cloudTrace = request.headers.get("x-cloud-trace-context");
  if (cloudTrace) {
    const [traceId] = cloudTrace.split("/");
    return traceId;
  }

  return undefined;
}
