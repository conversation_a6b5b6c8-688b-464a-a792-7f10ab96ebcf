/**
 * Create a deterministic RNG from an integer seed.
 * Uses the “minimal standard” Park–Miller LCG:
 *    Xₙ₊₁ = (16807 * Xₙ) mod (2³¹ − 1)
 * and returns (Xₙ − 1) / (2³¹ − 2) ∈ [0,1).
 */
const MOD = 2 ** 31 - 1; // 2³¹ − 1
const MULT = 7 ** 5; // multiplier constant

export function makeRng(seed = 12345): () => number {
  // Ensure seed ∈ [1 … MOD−1]
  let state = seed % MOD;
  if (state <= 0) state += MOD - 1;

  return function rng() {
    state = (state * MULT) % MOD;
    return (state - 1) / (MOD - 1);
  };
}

export const random = makeRng();

export function generateRandomString(
  minLength: number,
  maxLength: number,
  charset = "abcdefghijklmnopqrstuvwxyz",
): string {
  const length = generateRandomNumber(minLength, maxLength);
  let result = "";

  for (let i = 0; i < length; i++) {
    result += charset.charAt(Math.floor(random() * charset.length));
  }

  return result;
}

export function generateRandomNumber(min = 0, max = 100) {
  return Math.floor(random() * (max - min + 1)) + min;
}
