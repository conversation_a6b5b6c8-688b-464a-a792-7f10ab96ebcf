import { generateMock } from "@anatine/zod-mock";
import { TeamStatusActiveSchema, TeamStatusNoneSchema } from "app/schemas/team";
import { UserApiGETResponseSchema } from "app/schemas/user";
import { PlansApiGETResponseSchema } from "app/schemas/plan";
import { addDays, format } from "date-fns";
import { OrbCustomerInfoSchema } from "app/schemas/orb";
import { DEFAULT_USAGE_UNIT_DISPLAY_NAME } from "app/data/constants";
import { deepAssign } from "app/utils/object";
import { GetUserOrbCreditsInfoResponseSchema } from "app/schemas/credits";
import { OrbPlanInfo_PlanType } from "~services/auth/central/server/auth_pb";
import { piDay } from "./general";

const seed = 123;

/** mocks for GET '/api/team' */
const team = {
  GET: {
    Response: {
      200: {
        get active() {
          return generateMock(TeamStatusActiveSchema, { seed });
        },
        get none() {
          return generateMock(TeamStatusNoneSchema, { seed });
        },
      },
    },
  },
};

/** mocks for GET '/api/user' */
const user = {
  GET: {
    Response: {
      get 200() {
        return generateMock(UserApiGETResponseSchema, { seed });
      },
    },
  },
};

/** mocks for GET '/api/plans' */
const plans = {
  GET: {
    Response: {
      get 200() {
        return generateMock(PlansApiGETResponseSchema, { seed });
      },
    },
  },
};

/** mocks for GET '/api/credits' */
const credits = {
  GET: {
    Response: {
      get 200() {
        return deepAssign({
          usageUnitsAvailable: 750,
          usageUnitsPending: 0,
          usageUnitsUsedThisBillingCycle: 250,
        })(generateMock(GetUserOrbCreditsInfoResponseSchema, { seed }));
      },
    },
  },
};

const subscriptionMockOverrides = OrbCustomerInfoSchema.parse({
  portalUrl: "https://billing.example.com/portal",
  planId: "orb_developer_plan",
  planType: OrbPlanInfo_PlanType.PAID,
  planName: "Developer Plan",
  billingPeriodEnd: format(addDays(piDay, 30), "yyyy-MM-dd'T'HH:mm:ss'Z'"),
  trialPeriodEnd: null,
  creditsRenewingEachBillingCycle: 1000,
  creditsIncludedThisBillingCycle: 1000,
  billingCycleBillingAmount: "49.00",
  monthlyTotalCost: "49.00",
  pricePerSeat: "49.00",
  numberOfSeatsThisBillingCycle: 1,
  numberOfSeatsNextBillingCycle: 1,
  subscriptionEndDate: null,
  planIsExpired: false,
  addUsageAvailable: true,
  teamsAllowed: true,
  additionalUsageUnitCost: "1.00",
  maxNumSeats: 10,
  scheduledTargetPlanId: null,
  usageUnitDisplayName: DEFAULT_USAGE_UNIT_DISPLAY_NAME,
  usageUnitsPerSeat: 1000,
  planFacts: [
    "1,000 agent requests per month",
    "Team collaboration features",
    "Priority support",
    { text: "Advanced analytics", icon: "chart", spacing: true },
  ],
});

/** mocks for GET/POST '/api/subscription' */
const subscription = {
  GET: {
    Response: {
      get 200() {
        return deepAssign(subscriptionMockOverrides)(
          generateMock(OrbCustomerInfoSchema, { seed }),
        );
      },
    },
  },
  POST: {
    Response: {
      get 200() {
        return {};
      },
    },
  },
};

/** mocks for DELETE '/api/team/invitation/:id' */
const teamInvitation = {
  DELETE: {
    Response: {
      get 200() {
        return { success: true };
      },
    },
  },
};

const payment = {
  GET: {
    Response: {
      get 200() {
        return { hasPaymentMethod: true };
      },
    },
  },
};

const deletions = {
  GET: {
    Response: {
      get 200() {
        return { deletionInfo: [] };
      },
    },
  },
};

const mocks = {
  team,
  user,
  plans,
  credits,
  subscription,
  teamInvitation,
  payment,
  deletions,
};

export default mocks;
