import type { <PERSON>a, StoryObj } from "@storybook/react";
import { TrialTeamInviteWarning } from "./TrialTeamInviteWarning";
import { OrbPlanInfo_PlanType } from "~services/auth/central/server/auth_pb";
import type { OrbCustomerInfoSchema } from "app/schemas/orb";
import { delay, HttpResponse } from "msw";

const meta = {
  title: "Components/Account/TeamPage/TrialTeamInviteWarning",
  component: TrialTeamInviteWarning,
  parameters: {
    layout: "centered",
  },
} satisfies Meta<typeof TrialTeamInviteWarning>;

export default meta;
type Story = StoryObj<typeof TrialTeamInviteWarning>;

// Mock subscription data for trial plan
const trialSubscriptionData: OrbCustomerInfoSchema = {
  portalUrl: "https://billing.example.com/portal",
  planId: "orb_trial_plan",
  planType: OrbPlanInfo_PlanType.TRIAL,
  planName: "Trial Plan",
  billingPeriodEnd: null,
  trialPeriodEnd: "2024-02-15T00:00:00Z",
  creditsRenewingEachBillingCycle: 0,
  creditsIncludedThisBillingCycle: 100,
  billingCycleBillingAmount: "0.00",
  monthlyTotalCost: "0.00",
  pricePerSeat: "0.00",
  maxNumSeats: 1,
  numberOfSeatsThisBillingCycle: 1,
  numberOfSeatsNextBillingCycle: 1,
  subscriptionEndDate: "2024-02-15T00:00:00Z",
  planIsExpired: false,
  addUsageAvailable: false,
  teamsAllowed: false,
  additionalUsageUnitCost: "0.00",
  scheduledTargetPlanId: null,
  usageUnitDisplayName: "messages",
  usageUnitsPerSeat: 100,
  planFacts: [],
};

// Mock subscription data for paid plan
const paidSubscriptionData: OrbCustomerInfoSchema = {
  ...trialSubscriptionData,
  planId: "orb_developer_plan",
  planType: OrbPlanInfo_PlanType.PAID,
  planName: "Developer Plan",
  trialPeriodEnd: null,
  creditsRenewingEachBillingCycle: 1000,
  billingCycleBillingAmount: "49.00",
  monthlyTotalCost: "49.00",
  pricePerSeat: "49.00",
  addUsageAvailable: true,
  teamsAllowed: true,
};

export const TrialPlan: Story = {
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/subscription", async () => {
          await delay(500);
          return HttpResponse.json(trialSubscriptionData);
        }),
      ],
    },
  },
};

export const PaidPlan: Story = {
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/subscription", async () => {
          await delay(500);
          return HttpResponse.json(paidSubscriptionData);
        }),
      ],
    },
  },
};
