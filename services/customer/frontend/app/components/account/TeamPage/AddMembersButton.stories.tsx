import type { <PERSON><PERSON>, StoryObj } from "@storybook/react";
import { AddMembersButton } from "./AddMembersButton";

const meta: Meta<typeof AddMembersButton> = {
  title: "Components/TeamPage/AddMembersButton",
  component: AddMembersButton,
  argTypes: {
    newMembersCount: {
      control: { type: "number", min: 0, max: 10 },
      description: "Number of new members being added",
    },
    additionalSeatsNeeded: {
      control: { type: "number", min: 0, max: 10 },
      description: "Number of additional seats needed",
    },
    isTrialPlan: {
      control: { type: "boolean" },
      description: "Whether the team is on a trial plan",
    },
    userIsAdmin: {
      control: { type: "boolean" },
      description: "Whether the current user is an admin",
    },
    isPending: {
      control: { type: "boolean" },
      description: "Whether the operation is in progress",
    },
    disabled: {
      control: { type: "boolean" },
      description: "Whether the button should be disabled for other reasons",
    },
  },
  args: {
    onClick: () => console.log("Add members clicked"),
  },
};

export default meta;

type Story = StoryObj<typeof AddMembersButton>;

export const Default: Story = {
  args: {
    newMembersCount: 2,
    additionalSeatsNeeded: 0,
    isTrialPlan: false,
    userIsAdmin: true,
    isPending: false,
    disabled: false,
  },
};
