import { Button } from "@radix-ui/themes";
import { Tooltip } from "app/components/ui/Tooltip";
import { pc } from "app/utils/plural";

type AddMembersButtonProps = {
  newMembersCount: number;
  additionalSeatsNeeded: number;
  isTrialPlan: boolean;
  userIsAdmin: boolean;
  isPending: boolean;
  disabled?: boolean;
  hasPaymentMethod?: boolean;
  onClick: () => void;
};

export function AddMembersButton({
  newMembersCount,
  additionalSeatsNeeded,
  isTrialPlan,
  userIsAdmin,
  isPending,
  disabled = false,
  hasPaymentMethod = true,
  onClick,
}: AddMembersButtonProps) {
  // Determine if button should be disabled due to seat purchase restrictions
  const needsSeatPurchase = additionalSeatsNeeded > 0 && !isTrialPlan;
  const isDisabledForNonAdmin = !userIsAdmin && needsSeatPurchase;
  const isDisabledForNoPaymentMethod = needsSeatPurchase && !hasPaymentMethod;
  const finalDisabled =
    disabled || isDisabledForNonAdmin || isDisabledForNoPaymentMethod;

  // Generate button text
  const getButtonText = (): string => {
    if (isPending) return "Processing...";
    if (newMembersCount === 0) return "Add Members";

    const memberText = `Add ${pc(newMembersCount, "Member", "Members")}`;

    // If disabled for non-admin, only show basic member text (no purchase info)
    if (isDisabledForNonAdmin) {
      return memberText;
    }

    if (needsSeatPurchase) {
      return `${memberText} & Purchase ${pc(additionalSeatsNeeded, "Seat", "Seats")}`;
    }

    return memberText;
  };

  // Generate tooltip content for disabled states
  const getTooltipContent = (): string => {
    if (isDisabledForNoPaymentMethod) {
      return "A payment method is required to purchase additional seats. Please add a payment method to your account.";
    }
    if (isDisabledForNonAdmin) {
      return "Contact your admin to add more seats.";
    }
    return "";
  };

  const button = (
    <Button
      onClick={onClick}
      disabled={finalDisabled}
      data-testid="add-members-button"
    >
      {getButtonText()}
    </Button>
  );

  // Wrap with tooltip if there's a reason to show one
  if (isDisabledForNonAdmin || isDisabledForNoPaymentMethod) {
    return (
      <Tooltip content={getTooltipContent()} enabled={true}>
        {button}
      </Tooltip>
    );
  }

  return button;
}
