import { useQuery } from "@tanstack/react-query";
import { Callout } from "app/components/ui/Callout";
import { subscriptionQueryOptions } from "app/client-cache";
import { OrbPlanInfo_PlanType } from "~services/auth/central/server/auth_pb";
import { DEFAULT_USAGE_UNIT_DISPLAY_NAME } from "app/data/constants";

/**
 * A warning callout that displays when a team is on a trial plan,
 * informing users that inviting team members will NOT grant additional usage units
 * and they need to upgrade to a paid plan to invite members and get more usage units.
 */
export function TrialTeamInviteWarning() {
  const { data: subscriptionData } = useQuery(subscriptionQueryOptions);

  // Only show the warning if the team is on a trial plan
  const isTrialPlan = subscriptionData?.planType === OrbPlanInfo_PlanType.TRIAL;

  // Get the usage unit display name from subscription data, fallback to default
  const usageUnitDisplayName =
    subscriptionData?.usageUnitDisplayName || DEFAULT_USAGE_UNIT_DISPLAY_NAME;

  return (
    <Callout type="warning" enabled={isTrialPlan} size="small">
      <strong>Trial Plan Limitation:</strong> Inviting team members will NOT
      grant additional {usageUnitDisplayName} while on the free trial. To invite
      team members and receive more {usageUnitDisplayName}, please upgrade to a
      paid plan.
    </Callout>
  );
}
