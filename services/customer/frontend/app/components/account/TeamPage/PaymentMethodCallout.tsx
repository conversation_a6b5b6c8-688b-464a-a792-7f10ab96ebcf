import { Button, Flex, Text } from "@radix-ui/themes";
import { useMutation } from "@tanstack/react-query";
import { managePaymentMethod } from "app/client-cache";
import { Callout } from "app/components/ui/Callout";
import { IdCardIcon } from "@radix-ui/react-icons";

type PaymentMethodCalloutProps = {
  message: string;
};

export function PaymentMethodCallout({ message }: PaymentMethodCalloutProps) {
  const paymentMethodMutation = useMutation(managePaymentMethod);

  const handleAddPaymentMethod = () => {
    // Get current URL to return to after payment method setup
    const currentUrl = window.location.href;
    paymentMethodMutation.mutate({
      hasPaymentMethod: false,
      returnUrl: currentUrl,
    });
  };

  return (
    <Callout type="warning" size="small">
      <Flex
        direction={{ initial: "column", sm: "row" }}
        gap="3"
        align={{ initial: "start", sm: "center" }}
        justify="between"
      >
        <Text size="2" style={{ flex: 1 }}>
          {message}
        </Text>
        <Button
          size="2"
          variant="soft"
          onClick={handleAddPaymentMethod}
          disabled={paymentMethodMutation.isPending}
        >
          <IdCardIcon />
          {paymentMethodMutation.isPending
            ? "Setting up..."
            : "Add Payment Method"}
        </Button>
      </Flex>
    </Callout>
  );
}
