import type { <PERSON><PERSON>, <PERSON>Obj } from "@storybook/react";
import { SeatAllocationWarning } from "./SeatAllocationWarning";
import { HttpResponse } from "msw";
import mocks from "app/mocks";
import { deepAssign } from "app/utils/object";

const meta: Meta<typeof SeatAllocationWarning> = {
  title: "Components/TeamPage/SeatAllocationWarning",
  component: SeatAllocationWarning,
  argTypes: {
    newMembersCount: {
      control: { type: "number", min: 0, max: 10 },
      description: "Number of new members being invited",
    },
    additionalSeatsNeeded: {
      control: { type: "number", min: 0, max: 10 },
      description: "Number of additional seats that need to be purchased",
    },
  },
};

export default meta;

type Story = StoryObj<typeof SeatAllocationWarning>;

// Admin user - shows full pricing and purchase information
export const AdminUser: Story = {
  args: {
    newMembersCount: 2,
    additionalSeatsNeeded: 1,
  },
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/subscription", async () => {
          await new Promise((resolve) => setTimeout(resolve, 100));
          return HttpResponse.json(mocks.subscription.GET.Response[200]);
        }),
        http.get("/api/user", async () => {
          return HttpResponse.json(
            deepAssign({ isAdmin: true })(mocks.user.GET.Response[200]),
          );
        }),
      ],
    },
  },
};

// Non-admin user - shows simplified "contact admin" message
export const NonAdminUser: Story = {
  args: {
    newMembersCount: 2,
    additionalSeatsNeeded: 1,
  },
  parameters: {
    msw: {
      overrides: (http) => [
        http.get("/api/user", async () => {
          return HttpResponse.json(
            deepAssign({ isAdmin: false })(mocks.user.GET.Response[200]),
          );
        }),
      ],
    },
  },
};
