import { useQuery } from "@tanstack/react-query";
import { Callout } from "app/components/ui/Callout";
import { subscriptionQueryOptions, userQueryOptions } from "app/client-cache";
import { pc } from "app/utils/plural";
import { DEFAULT_USAGE_UNIT_DISPLAY_NAME } from "app/data/constants";

type SeatAllocationWarningProps = {
  newMembersCount: number;
  additionalSeatsNeeded: number;
};

export function SeatAllocationWarning({
  newMembersCount,
  additionalSeatsNeeded,
}: SeatAllocationWarningProps) {
  const { data: userData, error: userError } = useQuery(userQueryOptions);
  const { data: subscriptionData, error: subscriptionError } = useQuery({
    ...subscriptionQueryOptions,
    enabled: !!userData?.isAdmin && additionalSeatsNeeded > 0,
  });

  // Early return when no members are being added
  if (newMembersCount === 0) return null;

  // Early return if not admin
  if (!userData?.isAdmin) {
    // Show non-admin message only when additional seats are needed
    if (additionalSeatsNeeded > 0) {
      return (
        <Callout type="warning" size="small">
          <div>
            There are not enough available seats left. Contact your admin to add
            more seats.
          </div>
        </Callout>
      );
    }
    return null;
  }

  // Handle error states
  if (userError || subscriptionError) {
    return (
      <Callout type="warning" size="small">
        <div>Unable to load seat allocation information. Please try again.</div>
      </Callout>
    );
  }

  // Wait for subscription data when purchasing seats
  if (additionalSeatsNeeded > 0 && !subscriptionData) return null;

  // Case 1: Sufficient seats available - no purchase needed
  if (additionalSeatsNeeded === 0) {
    return (
      <Callout type="info" size="small">
        <div>You have enough seats. No additional charges will apply.</div>
      </Callout>
    );
  }

  // Get credits per seat from subscription data
  const creditsPerSeat = subscriptionData?.usageUnitsPerSeat ?? 0;

  // Get price per seat for charge calculation
  const pricePerSeat = parseFloat(subscriptionData?.pricePerSeat ?? "0");
  const totalCharge = additionalSeatsNeeded * pricePerSeat;

  // Calculate total usage units being added to the shared pool
  const totalUsageUnitsAdded = additionalSeatsNeeded * creditsPerSeat;

  // Get the usage unit display name from subscription data, fallback to default
  const usageUnitDisplayName =
    subscriptionData?.usageUnitDisplayName || DEFAULT_USAGE_UNIT_DISPLAY_NAME;

  // Case 2: Need to purchase additional seats
  return (
    <Callout type="warning" size="small">
      <div style={{ marginTop: "8px" }}>
        We&apos;ll add {pc(additionalSeatsNeeded, "seat", "seats")} at{" "}
        <strong>${pricePerSeat.toFixed(2)}/month each</strong>
        {additionalSeatsNeeded > 1
          ? ` ($${totalCharge.toFixed(2)} total)`
          : ""}{" "}
        with{" "}
        <strong>
          {creditsPerSeat.toLocaleString()} {usageUnitDisplayName} per seat
        </strong>
        {additionalSeatsNeeded > 1
          ? ` (${totalUsageUnitsAdded.toLocaleString()} total)`
          : ""}{" "}
        to your future billing cycles. Prorated charges and credits will apply
        for the current period.
      </div>
    </Callout>
  );
}
