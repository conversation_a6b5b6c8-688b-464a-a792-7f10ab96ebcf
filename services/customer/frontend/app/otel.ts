import { NodeSDK } from "@opentelemetry/sdk-node";
import { getNodeAutoInstrumentations } from "@opentelemetry/auto-instrumentations-node";
import { OTLPTraceExporter } from "@opentelemetry/exporter-trace-otlp-http";
import { CloudPropagator } from "@google-cloud/opentelemetry-cloud-trace-propagator";

// This OpenTelemetry setup exists with the understanding it will need to be reworked as we move to a SPA from Remix.
// See: https://www.notion.so/Google-Cloud-Tracing-for-customer-ui-23abba10175a801abbdfd3d85e9590f8?source=copy_link
const sdk = new NodeSDK({
  textMapPropagator: new CloudPropagator(),
  traceExporter: new OTLPTraceExporter({
    // TODO(dross): Make this configurable via env var & dig into why gRPC isn't working
    url: "http://opentelemetry-collector.opentelemetry:4318/v1/traces",
  }),
  instrumentations: [
    getNodeAutoInstrumentations({
      // disable file system tracing - very noisy and not useful
      "@opentelemetry/instrumentation-fs": { enabled: false },
    }),
  ],
  serviceName: process.env.OTEL_SERVICE_NAME || "customer-ui",
});

sdk.start();

process.on("SIGTERM", () => {
  sdk.shutdown().finally(() => process.exit(0));
});
