/**
 * API Proxy for Agents App
 *
 * This proxies all agents app API requests to avoid CORS issues and
 * read auth from cookie into headers.
 *
 * This is intended to be TEMPORARY (7/9/2025):
 * we plan to remove the generic proxy when we switch to Connect RPC & Go backend for the web apps API
 */

import { logger } from "@augment-internal/logging";
import compression from "compression";
import cookieParser from "cookie-parser";
import express from "express";
import proxy from "express-http-proxy";
import path from "path";

const AGENTS_AUTH_COOKIE = "agents_auth";
const DEFAULT_TOKEN_EXP_SEC = 60 * 60; // 1 hour expiration, if not provided by auth server

type AuthData = {
  accessToken: string;
  tenantUrl: string;
  expiresAt: number;
};

/**
 * In prod, the agents app gets flattened into the same directory as the customer app.
 * But in dev we run it from the nested directory.
 */
export const getAgentsAppDir = (isProduction: boolean, dirName: string) => {
  if (isProduction) {
    return `${dirName}/../../../../clients/webapp/dist`;
  } else {
    return `${dirName}/../../../clients/webapp/dist`;
  }
};

/**
 * Makes a router for the agents app that we can mount at the agents app path.
 * Ensures we don't step on Remix's toes/affect any other routes.
 */
export const createAgentsRouter = (
  agentsAppDir: string,
  production: boolean,
) => {
  const router = express.Router();

  router.use(createAgentsAppApiProxy());

  if (production) {
    // SvelteKit keeps hashed assets in _app/, cache forever
    router.use(
      "/_app",
      express.static(path.join(agentsAppDir, "_app"), {
        immutable: true,
        maxAge: "1y",
      }),
    );

    // Non-hashed assets (HTML, favicon, etc.) - cache for 1hr
    router.use(
      express.static(agentsAppDir, {
        index: "index.html",
        maxAge: "1h",
      }),
    );
  } else {
    // Dev: no cache, force fresh requests
    router.use(
      express.static(agentsAppDir, {
        index: "index.html",
        maxAge: "0",
        etag: false, // Disable ETags to prevent 304 responses
        lastModified: false, // Disable last-modified headers
        setHeaders: (res) => {
          res.setHeader("Cache-Control", "no-cache, no-store, must-revalidate");
          res.setHeader("Pragma", "no-cache");
          res.setHeader("Expires", "0");
        },
      }),
    );
  }

  // SPA fallback for client-side routing
  router.get("*", (_req, res) => {
    if (production) {
      // Fallback is for things like /agents/route which should serve index.html, so cache for 1hr max
      res.setHeader("Cache-Control", "public, max-age=3600");
    }
    res.sendFile(path.join(agentsAppDir, "index.html"));
  });

  return router;
};

/**
 * Creates an Express router that handles API requests
 */
function createAgentsAppApiProxy(
  AUTH_ENDPOINT = process.env.AUTH_ENDPOINT,
  NODE_ENV = process.env.NODE_ENV,
): express.Router {
  const router = express.Router();

  router.use(cookieParser());

  /**
   * Proxy for streaming endpoints
   */
  router.use(
    "/api/stream",
    proxy(
      (req) => {
        logger.info(`API Proxy: forwarding streaming request: ${req.url}`);
        // Read auth from httpOnly cookie
        const authCookie = req.cookies[AGENTS_AUTH_COOKIE];
        let authData;
        try {
          authData = JSON.parse(authCookie) as AuthData;
        } catch (error) {
          throw new Error("Invalid authentication cookie");
        }

        const tenantUrl = authData.tenantUrl;
        if (!tenantUrl || typeof tenantUrl !== "string") {
          throw new Error(
            `Invalid tenant URL in authentication cookie: tenantUrl=${tenantUrl}`,
          );
        }

        return tenantUrl;
      },
      {
        proxyReqPathResolver: (req) => {
          return req.url.replace(/^\/api\/stream/, "") || "/";
        },

        proxyReqOptDecorator: (proxyReqOpts, srcReq) => {
          const authCookie = srcReq.cookies[AGENTS_AUTH_COOKIE];
          let authData;
          try {
            authData = JSON.parse(authCookie) as AuthData;
          } catch (error) {
            throw new Error("Invalid authentication cookie");
          }

          const headers = { ...proxyReqOpts.headers };
          headers["authorization"] = `Bearer ${authData.accessToken}`;

          return {
            ...proxyReqOpts,
            headers,
          };
        },
      },
    ),
  );

  // Only enable compression and express.json after streaming proxy since
  // they'll break the streaming response
  router.use(compression());
  router.use(express.json());

  router.get("/api/auth/config", (_req, res) => {
    res.json({
      authUrl: AUTH_ENDPOINT,
    });
  });

  /**
   * Auth status endpoint - checks if user is authenticated via cookie
   */
  router.get("/api/auth/status", (req, res) => {
    const authCookie = req.cookies[AGENTS_AUTH_COOKIE];

    if (!authCookie) {
      return res.json({ authenticated: false });
    }

    try {
      const authData = JSON.parse(authCookie) as AuthData;

      if (Date.now() > authData.expiresAt) {
        return res.json({ authenticated: false, reason: "expired" });
      }

      return res.json({
        authenticated: true,
        tenantUrl: authData.tenantUrl,
        expiresAt: authData.expiresAt,
      });
    } catch (error) {
      return res.json({ authenticated: false, reason: "invalid_cookie" });
    }
  });

  /**
   * Logout endpoint - clears the auth cookie
   */
  router.post("/api/auth/logout", (_, res) => {
    res.clearCookie(AGENTS_AUTH_COOKIE, {
      path: "/agents",
    });

    res.json({ success: true });
  });

  /**
   * Special handler for token from code that sets httpOnly cookie
   */
  router.post("/api/token", async (req, res) => {
    try {
      const tenantUrl = req.headers["x-tenant-url"] as string;

      if (!tenantUrl || typeof tenantUrl !== "string") {
        return res
          .status(400)
          .json({ error: "Missing or invalid X-Tenant-URL header" });
      }

      const tenantUrlObj = new URL(tenantUrl);
      if (!tenantUrlObj.hostname.endsWith(".augmentcode.com")) {
        logger.warn("Token from code: Invalid tenant domain", {
          tenantUrl,
          hostname: tenantUrlObj.hostname,
        });
        return res.status(400).json({ error: "Invalid tenant domain" });
      }

      const tokenFromCodeEndpoint = new URL("/token", AUTH_ENDPOINT).toString();

      logger.info("Forwarding OAuth2 token request to Auth Central", {
        url: tokenFromCodeEndpoint,
        body: req.body,
        tenantUrl,
      });

      const tokenResponse = await fetch(tokenFromCodeEndpoint, {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(req.body),
      });

      if (!tokenResponse.ok) {
        const errorText = await tokenResponse.text();
        logger.error("OAuth2 token from code failed", {
          status: tokenResponse.status,
          error: errorText,
          tenantUrl,
          requestBody: req.body,
        });
        return res.status(tokenResponse.status).json({ error: errorText });
      }

      const tokenData = await tokenResponse.json();

      // expires_in is in seconds. If it's not provided, fallback to default
      const expiresInMs = tokenData.expires_in || DEFAULT_TOKEN_EXP_SEC;

      const cookieValue: AuthData = {
        accessToken: tokenData.access_token,
        tenantUrl,
        expiresAt: Date.now() + expiresInMs * 1000,
      };

      res.cookie(AGENTS_AUTH_COOKIE, JSON.stringify(cookieValue), {
        httpOnly: true,
        secure: NODE_ENV === "production",
        sameSite: "lax",
        maxAge: expiresInMs * 1000,
        path: "/agents", // Scope to agents app only
      });

      logger.info("Set agents auth cookie", {
        tenantUrl,
        expiresAt: Date.now() + expiresInMs * 1000,
      });

      // Don't tell the client anything about the token
      res.json({ success: true });
    } catch (error) {
      logger.error("OAuth2 token from code error", {
        error: error instanceof Error ? error.message : String(error),
      });
      res
        .status(500)
        .json({ error: "Internal server error during OAuth2 token from code" });
    }
  });

  /**
   * Generic proxy for Augment API endpoints to avoid CORS and read auth from cookie into headers
   */
  router.use(
    "/api",
    proxy(
      (req) => {
        // Read auth from httpOnly cookie
        const authCookie = req.cookies[AGENTS_AUTH_COOKIE];

        if (!authCookie) {
          throw new Error("No authentication cookie found - please log in");
        }

        let authData;
        try {
          authData = JSON.parse(authCookie) as AuthData;
        } catch (error) {
          throw new Error("Invalid authentication cookie");
        }
        // We are trusting an unsigned cookie here about when it says it expires.
        // That's fine though since the underlying token is time-limited, so even
        // if the client tampers with the cookie's expiresAt field it won't be able to use
        // the token after it expires.
        if (Date.now() > authData.expiresAt) {
          throw new Error("Authentication token expired - please log in again");
        }

        const tenantUrl = authData.tenantUrl;
        if (!tenantUrl || typeof tenantUrl !== "string") {
          throw new Error(
            `Invalid tenant URL in authentication cookie: tenantUrl=${tenantUrl}`,
          );
        }

        const tenantUrlObj = new URL(tenantUrl);
        if (!tenantUrlObj.hostname.endsWith(".augmentcode.com")) {
          logger.warn("API proxy: Rejected request to non-Augment domain", {
            tenantUrl,
            hostname: tenantUrlObj.hostname,
          });
          throw new Error("Invalid tenant domain");
        }

        logger.info(`API Proxy: forwarding request: ${req.url}`);

        return tenantUrl;
      },
      {
        // Remove /api prefix and pass thru the rest
        proxyReqPathResolver: (req) => {
          return req.url.replace(/^\/api/, "") || "/";
        },

        // Add Authorization header from cookie
        proxyReqOptDecorator: (proxyReqOpts, srcReq) => {
          const authCookie = srcReq.cookies[AGENTS_AUTH_COOKIE];
          let authData;
          try {
            authData = JSON.parse(authCookie) as AuthData;
          } catch (error) {
            throw new Error("Invalid authentication cookie");
          }

          const headers = { ...proxyReqOpts.headers };
          headers["authorization"] = `Bearer ${authData.accessToken}`;

          return {
            ...proxyReqOpts,
            headers,
          };
        },

        proxyErrorHandler: (err, res) => {
          logger.error("API proxy error:", err);
          res.status(500).json({
            error: "server_error",
            error_description:
              "An unexpected error occurred during API request",
          });
        },
      },
    ),
  );

  return router;
}
