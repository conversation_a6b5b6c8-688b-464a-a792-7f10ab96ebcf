import { defineConfig, devices } from "@playwright/test";
import dotenv from "dotenv";
import { dirname, resolve } from "path";
import { fileURLToPath } from "url";

const e2eDir = dirname(fileURLToPath(import.meta.url));
dotenv.config({ path: resolve(e2eDir, "../.env") });

/** See https://playwright.dev/docs/test-configuration. */
export default defineConfig({
  testDir: resolve(e2eDir, "./tests"),
  testMatch: /.+\.test\.(ts|tsx)$/,
  outputDir: resolve(e2eDir, "./results"),
  /* Run tests synchronously (no parallel execution) */
  fullyParallel: false,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: !!process.env.CI,
  /* Retry on CI only */
  retries: process.env.CI ? 2 : 0,
  workers: 1,
  /* Reporter to use. See https://playwright.dev/docs/test-reporters */
  reporter: [["html", { outputFolder: "./report" }]],
  globalSetup: resolve(e2eDir, "./utils/global-setup.ts"),
  globalTeardown: resolve(e2eDir, "./utils/global-teardown.ts"),
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    actionTimeout: 10_000,
    /* Collect trace when retrying the failed test. See https://playwright.dev/docs/trace-viewer */
    trace: "on-first-retry",
    video: {
      mode: "on",
      size: { width: 1280, height: 720 },
    },
    /* Configure screenshots to be saved in report/{test-name} directory */
    screenshot: {
      mode: "on",
      fullPage: true,
    },
    extraHTTPHeaders: {
      "User-Agent": "Augment-E2E-Test/1.0",
    },
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: "chromium",
      use: { ...devices["Desktop Chrome"] },
    },
  ],
});
