{"compilerOptions": {"lib": ["DOM", "DOM.Iterable", "ES2022"], "types": ["@playwright/test"], "isolatedModules": true, "esModuleInterop": true, "jsx": "react-jsx", "module": "ESNext", "moduleResolution": "<PERSON><PERSON><PERSON>", "resolveJsonModule": true, "target": "ES2022", "strict": true, "allowJs": false, "skipLibCheck": true, "forceConsistentCasingInFileNames": true, "baseUrl": ".", "noEmit": true, "paths": {"e2e/*": ["./*"], "app/*": ["../app/*"], "~services/*": ["../../../*", "../../../../bazel-bin/services/*"]}}, "include": ["**/*.ts", "**/*.tsx"], "exclude": []}