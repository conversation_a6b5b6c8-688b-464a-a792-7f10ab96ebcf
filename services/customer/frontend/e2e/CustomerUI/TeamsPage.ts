import { page } from "e2e/utils/session";
import { next } from "e2e/utils/node";
import { AddTeamMembersModal } from "./AddTeamMembers";

export const TeamsPage = {
  async verify() {
    await page.getByTestId("team-page").waitFor();
  },

  openAddTeamMembersModal() {
    return next(AddTeamMembersModal, async () => {
      await page.getByTestId("add-team-members-button").click();
      await page.screenshot({ name: "add-team-members-modal.png" });
    });
  },

  /**
   * @example
   * ```ts
   * const teamMembers = await TeamsPage.listTeamMembers();
   * expect(teamMembers).toEqual([
   *   { "Email": "<EMAIL>", "Status": "Active", "Date Added": "Jul 28, 2025", "isAdmin": true },
   *   { "Email": "<EMAIL>", "Status": "Active", "Date Added": "Jul 28, 2025", "isAdmin": false },
   * ]);
   * ```
   */
  async listTeamMembers() {
    const [_tableHeader, ...tableData] = await page.getByTestId("team-table").getTableText();
    return tableData.map((row) => {
      const [email, role, joined] = row;
      const result = {
        email,
        role,
        joined,
        isAdmin: row[0].endsWith("Admin"),
      };
      if (result.isAdmin) {
        result.email = row[0].replace(" Admin", "");
      }
      return result;
    });
  },
};
