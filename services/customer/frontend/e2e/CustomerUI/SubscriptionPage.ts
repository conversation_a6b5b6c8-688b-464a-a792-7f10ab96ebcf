import { next } from "e2e/utils/node";
import { PlanPicker } from "./PlanPicker";
import { page } from "e2e/utils/session";

export const SubscriptionPage = {
  async verify() {
    await page.getByTestId("subscription-heading").waitFor();
  },

  async getCurrentPlanName() {
    return await page.getByTestId("current-plan-name").textContent();
  },

  openPlanPicker() {
    return next(PlanPicker, async () => {
      await page.getByTestId("change-plan-button").click();
      await page.getByTestId("plan-picker-dialog").waitFor();
      await page.screenshot({ name: "plan-picker-dialog.png" });
    });
  },
};
