import { next } from "e2e/utils/node";
import { page } from "e2e/utils/session";
import { TeamsPage } from "./TeamsPage";

export const AddTeamMembersModal = {
  async verify() {
    await page.getByTestId("add-team-members-modal").waitFor();
  },

  addTeamMembers(emails: string[]) {
    return next(AddTeamMembersModal, async () => {
      await page.getByTestId("token-input").fill(emails.join());
      await page.screenshot({ name: "add-team-members-modal-filled.png" });
    });
  },

  submitInviteTeamMembers() {
    return next(TeamsPage, async () => {
      await page.getByTestId("add-members-button").click();
      await page.screenshot({ name: "add-members-button-clicked.png" });
    });
  },

  cancelInviteTeamMembers() {
    return next(TeamsPage, async () => {
      await page.getByTestId("cancel-add-members-button").click();
      await page.screenshot({ name: "cancel-add-members-button-clicked.png" });
    });
  },

  async getCalloutsText() {
    return await page.getByTestId("add-members-modal-callouts").allInnerTexts();
  },
};
