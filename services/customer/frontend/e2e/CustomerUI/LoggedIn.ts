import { next } from "e2e/utils/node";
import { CustomerUI } from "./CustomerUI";
import { page } from "e2e/utils/session";

export const LoggedIn = {
  async verify() {
    await page.getByTestId("logout-button").waitFor();
  },
  logout() {
    return next(CustomerUI, async () => {
      await page.getByTestId("logout-button").click();
      await page.waitForLoadState("networkidle");
      await page.screenshot({ name: "logout.png" });
    });
  },
};
