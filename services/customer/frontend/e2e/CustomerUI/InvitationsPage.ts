import { next } from "e2e/utils/node";
import { TeamsPage } from "./TeamsPage";
import { page } from "e2e/utils/session";

export const InvitationsPage = {
  async verify() {
    await page.getByTestId("invitations-page").waitFor();
  },

  acceptInvite(inviterEmail: string) {
    return next(TeamsPage, async () => {
      await page.getByTestId("invitations-page").waitFor();
      const singleInvitation = await page
        .getByTestId("single-invitation-container")
        .isVisible()
        .catch(() => false);

      if (singleInvitation) {
        const acceptButton = page.getByTestId("accept-invite-button");
        await acceptButton.waitFor();
        await acceptButton.click();
      } else {
        const invitationCards = await page.getByTestId("invitation-card").all();

        let foundInvitation = false;
        for (const card of invitationCards) {
          const inviterText = await card.getByTestId("inviter-email").textContent();
          if (inviterText && inviterText.includes(inviterEmail)) {
            const acceptButton = card.getByTestId("accept-invite-button");
            await acceptButton.waitFor();
            await acceptButton.click();
            foundInvitation = true;
            break;
          }
        }

        if (!foundInvitation) {
          throw new Error(`Could not find invitation from ${inviterEmail}`);
        }
      }

      await page.waitForURL((url) => !url.pathname.includes("/invitations"));
    });
  },
};
