import { next } from "e2e/utils/node";
import { page } from "e2e/utils/session";
import { SubscriptionPage } from "./SubscriptionPage";
import { stringToKebab } from "app/utils/string";
import chalk from "chalk";
import type { PlanName } from "e2e/utils/constants";

export const PlanPicker = {
  async verify() {
    await page.getByTestId("plan-picker-dialog").waitFor();
  },
  selectPlan(planName: PlanName) {
    return next(PlanConfirmation, async () => {
      console.info("selecting", chalk.green(planName));
      await page.getByTestId(`plan-option-card-${stringToKebab(planName)}`).click();
      await page.screenshot({ name: "plan-selected.png" });
    });
  },
};

const PlanConfirmation = {
  async verify() {
    await page.getByTestId("change-plan-confirmation-page").waitFor();
  },

  confirmPlanChange() {
    return next(PlanConfirmation, async () => {
      await page.getByTestId("immediate-change-checkbox").check();
    });
  },

  submitPlanChange() {
    return next(SubscriptionPage, async () => {
      await page.getByTestId("plan-submit-button").click();
      await page.screenshot({ name: "plan-change-button-clicked.png" });
      await page.getByTestId("loading-your-account").waitFor();
      await page.screenshot({ name: "plan-change-loading.png" });
      await page.waitForLoadState("networkidle");
    });
  },
};
