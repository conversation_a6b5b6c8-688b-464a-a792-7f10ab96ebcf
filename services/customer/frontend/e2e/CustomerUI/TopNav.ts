import { next } from "e2e/utils/node";
import { SubscriptionPage } from "./SubscriptionPage";
import { TeamsPage } from "./TeamsPage";
import { page } from "e2e/utils/session";

export const TopNav = {
  async verify() {
    await page.getByTestId("topnav").waitFor();
  },

  navigateToTeams() {
    return next(TeamsPage, async () => {
      await page.getByTestId("tab-link-team").click();
      await page.screenshot({ name: "teams-page.png" });
    });
  },

  navigateToSubscription() {
    return next(SubscriptionPage, async () => {
      await page.getByTestId("tab-link-subscription").click();
      await page.screenshot({ name: "subscription-page.png" });
    });
  },
};
