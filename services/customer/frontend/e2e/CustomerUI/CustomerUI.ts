import { APP_CONFIG } from "e2e/utils/app-config";
import { next } from "e2e/utils/node";
import { page, setUser } from "e2e/utils/session";
import { generateTestUser, type TestUser } from "e2e/utils/user";
import chalk from "chalk";
import { TopNav } from "./TopNav";

export const CustomerUI = next({
  async verify() {},

  signup(testUser = generateTestUser()) {
    return next(AcceptTerms, async () => {
      await login(testUser);
    });
  },

  login(testUser: TestUser) {
    return next(TopNav, async () => {
      await login(testUser);
    });
  },
});

async function login(testUser: TestUser) {
  console.info(`Logging in test user ${chalk.green(testUser.email)}`);
  setUser(testUser);
  await page.goto(APP_CONFIG.customerUiUrl, { waitUntil: "networkidle" });
}

export const AcceptTerms = {
  async verify() {
    await page.locator("#terms-of-service-checkbox").waitFor();
  },

  acceptTerms() {
    return next(TopNav, async () => {
      await page.screenshot({ name: "initial-page.png" });
      await page.getByTestId("tos-checkbox").click();
      await page.screenshot({ name: "checkbox-checked.png" });
      await page.locator("#signup-button").click();
      await page.screenshot({ name: "signup-clicked.png" });
      // Wait for navigation to account page
      await page.waitForLoadState("domcontentloaded");
      await page.getByTestId("subscription-heading").waitFor();
    });
  },
};
