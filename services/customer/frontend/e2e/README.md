# Customer UI E2E Tests using Playwright

## Setup

1. Install dependencies

```bash
cd services/customer/frontend
pnpm install
pnpm exec playwright install
```

2. Set environment variables

```bash
export CUSTOMER_UI_URL="https://app.dev-[YOUR DEV DEPLOY].us-central1.dev.augmentcode.com"
```

3. Enable Auth bypass and E2E mode in your dev deploy. Set the following deploy flags. You can use either fake-flags service through bazel (below) or alter `[YOUR-ENV].jsonnet` and redeploy.

```
bazel run //services/test/fake_feature_flags:util -- update --key auth_central_bearer_token_auth_enabled --json "true"
bazel run //services/test/fake_feature_flags:util -- update --key customer_ui_e2e_mode_enabled --json "true"
```

4. run tests from `services/customer/frontend`

```
pnpm e2e
```

At the end of the test run playwright will give you a command you can run to view the report which includes videos of each test.

### Watch Mode

You can also run tests in watch mode. This will re-run tests when files change.
Add this to your `.env` file:

```
PWTEST_WATCH=1
```

**Video Conversion in Watch Mode**: When running in watch mode, video conversion happens after each individual test run (not just when the watch process exits). This ensures you get MP4 videos immediately after each test iteration for debugging purposes.

## Viewing results in the web

You can view the results of the tests in the web by running the following command from `services/customer/frontend`:

`pnpm exec playwright show-report e2e/report`

## Viewing results in VSCode

You can find the results of the tests in the `e2e/results` directory.

## Video Recording and Conversion

Playwright automatically records videos of test runs in WebM format. The test suite includes an automatic video conversion system that converts WebM videos to MP4 format for better compatibility.

### FFmpeg Installation (Optional but Recommended)

To enable automatic WebM to MP4 video conversion, install FFmpeg:

#### Ubuntu/Debian:

```bash
sudo apt-get update
sudo apt-get install -y ffmpeg
```

#### macOS:

```bash
# Using Homebrew
brew install ffmpeg

# Using MacPorts
sudo port install ffmpeg
```

#### Verify Installation:

```bash
ffmpeg -version
```

### Video Conversion Behavior

- **With FFmpeg installed**: Tests will automatically convert WebM videos to MP4 format while keeping the original WebM files
- **Without FFmpeg**: Tests will continue normally with videos remaining in WebM format (with a warning message)
- **Conversion scope**: Only converts videos in `e2e/results/` directory (for development/debugging)

Video files are saved in `e2e/results/[test-name]/` directory.

Refer to this [Notion Doc](https://www.notion.so/End-to-End-Testing-with-Playwright-Complete-Guide-for-Frontend-Engineers-1fbbba10175a80709c8bedb55385a4c6?source=copy_link) for further info
