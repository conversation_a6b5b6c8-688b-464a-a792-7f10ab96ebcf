# Video Conversion for Playwright Tests

This document explains how Playwright videos are automatically converted from WebM to MP4 format.

## Overview

By default, Playwright records videos in WebM format. This configuration automatically converts those videos to MP4 format after test completion, which is more widely supported and compatible with various video players and platforms.

## Requirements

- **FFmpeg**: Must be available in the system PATH
- **Playwright**: Video recording must be enabled in the configuration

### FFmpeg Installation

FFmpeg is already included as a dependency in the Playwright setup (`@playwright//:ffmpeg` in BUILD file).

For local development, you may need to install FFmpeg:

**macOS:**

```bash
brew install ffmpeg
```

**Ubuntu/Debian:**

```bash
sudo apt update
sudo apt install ffmpeg
```

## Troubleshooting

### FFmpeg Not Available

If FFmpeg is not available, you'll see a warning:

```
FFmpeg is not available. Skipping video conversion to MP4 format.
```

**Solution**: Install FFmpeg or ensure it's in your PATH.
