// eslint-disable-next-line no-restricted-imports
import { test as baseTest } from "@playwright/test";
import type {
  TestInfo,
  PlaywrightTestArgs,
  PlaywrightTestOptions,
  PlaywrightWorkerArgs,
  PlaywrightWorkerOptions,
  TestType,
} from "@playwright/test";
import { start } from "./node";
import { convertPlaywrightVideos } from "./video-converter";
import { clearSession, setBrowser, setBrowserContext, setPage } from "./session";

const testExt = baseTest.extend({});

export const test = Object.assign(
  (title: string, body: (testArgs: TestArgs, testInfo: TestInfo) => any) => {
    registerHooks();
    // We must destructure the first arg to guarantee their presence.
    // <PERSON><PERSON> uses a Proxy to make the args lazy, so we can't just pass it through.
    testExt(title, async ({ page, context, browser, request }, testInfo) => {
      await body({ page, context, browser, request } as TestArgs, testInfo);
    });
  },
  testExt,
) as TestType<
  PlaywrightTestArgs & PlaywrightTestOptions,
  PlaywrightWorkerArgs & PlaywrightWorkerOptions
>;

export const { expect, describe } = test;

type TestArgs = PlaywrightTestArgs &
  PlaywrightTestOptions &
  PlaywrightWorkerArgs &
  PlaywrightWorkerOptions;

export let testInfo: TestInfo;

function registerHooks() {
  test.beforeEach(start);

  test.beforeEach(function setGlobals(
    { page, context, browser }: TestArgs,
    nextTestInfo: TestInfo,
  ) {
    testInfo = nextTestInfo;
    setBrowser(browser);
    setBrowserContext(context);
    setPage(page);
  });

  test.afterEach(clearSession);

  test.afterAll(function convertVideosInWatchMode({}: any, testInfo: TestInfo) {
    if (!process.env.PWTEST_WATCH) return;
    convertPlaywrightVideos(testInfo.outputPath());
  });
}
