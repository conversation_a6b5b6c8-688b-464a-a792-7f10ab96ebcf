import { rmSync } from "fs";
import { resolve } from "path";
import globalSetupBase from "./global-setup";

async function globalSetup() {
  await globalSetupBase();

  // Also clean up Bazel test output directories if they exist
  if (process.env.TEST_UNDECLARED_OUTPUTS_DIR) {
    const bazelReportDir = resolve(process.env.TEST_UNDECLARED_OUTPUTS_DIR, "html-report");
    const bazelResultsDir = resolve(process.env.TEST_UNDECLARED_OUTPUTS_DIR, "results");

    try {
      rmSync(bazelReportDir, { recursive: true, force: true });
      console.info("Cleaned up Bazel report directory");
    } catch (error) {
      console.info("Bazel report directory doesn't exist or couldn't be cleaned");
    }

    try {
      rmSync(bazelResultsDir, { recursive: true, force: true });
      console.info("Cleaned up Bazel results directory");
    } catch (error) {
      console.info("Bazel results directory doesn't exist or couldn't be cleaned");
    }
  }

  console.info("Test directories cleaned, starting tests...");
}

export default globalSetup;
