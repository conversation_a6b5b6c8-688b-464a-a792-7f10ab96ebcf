import { convertPlaywrightVideos } from "./video-converter";
import { resolve, dirname, join } from "path";
import { fileURLToPath } from "url";

/**
 * Global teardown function for Playwright tests in Bazel environment
 * This runs after all tests have completed and converts WebM videos to MP4 format
 */
async function globalTeardown() {
  console.info("Running Bazel global teardown - converting videos to MP4 format...");

  try {
    // Handle different output directory configurations for Bazel
    let outputDir: string;

    if (process.env.TEST_UNDECLARED_OUTPUTS_DIR) {
      // Bazel environment with undeclared outputs
      outputDir = join(process.env.TEST_UNDECLARED_OUTPUTS_DIR, "results");
    } else {
      // Fallback to default directory
      const utilsDir = dirname(fileURLToPath(import.meta.url));
      outputDir = resolve(utilsDir, "../results");
    }

    console.info(`Converting videos in directory: ${outputDir}`);
    convertPlaywrightVideos(outputDir);

    console.info("Video conversion completed successfully");
  } catch (error) {
    console.error("Error during video conversion in Bazel global teardown:", error);
    // Don't throw the error to avoid failing the test run
  }
}

export default globalTeardown;
