import type { Page } from "@playwright/test";
import { upperCase } from "app/utils/string";
import chalk from "chalk";

const IGNORE_LOGS: RegExp[] = [
  /\[BROWSER-REQUEST-FAILED\] POST https:\/\/www.google.com\/recaptcha\/enterprise\/clr\?k=.+ - net::ERR_ABORTED/,
  /\[BROWSER-DEBUG\] \[vite\] connecting\.\.\./,
  /\[BROWSER-DEBUG\] \[vite\] connected\./,
];

function removeChalkAnnotations(input: string): string {
  // eslint-disable-next-line no-control-regex
  return input.replace(/\x1B\[[0-?]*[ -/]*[@-~]/g, "");
}

function forwardLog(str: string) {
  if (IGNORE_LOGS.some((r) => r.test(removeChalkAnnotations(str)))) return;
  console.info(str);
}

function toLogLevel(type: string | undefined): number {
  if (type === "error") return 0;
  if (type === "warn") return 1;
  if (type === "info" || type === "log") return 2;
  if (type === "debug") return 3;
  return 0;
}

// state the log level at startup
console.info(`Log level: ${chalk.blue(upperCase(process.env.LOG_LEVEL ?? "unspecified"))}`);

export function forwardBrowserLogs(page: Page) {
  // Capture browser console logs and forward to test output
  page.on("console", async (msg) => {
    const type = msg.type();
    if (toLogLevel(type) < toLogLevel(process.env.LOG_LEVEL)) return;
    const text = msg.text();
    const location = msg.location();

    // Get arguments for more detailed logging
    const args = await Promise.all(
      msg.args().map((arg) => {
        try {
          return arg.jsonValue();
        } catch {
          return arg.toString();
        }
      }),
    );

    const coloredMap = {
      error: chalk.red,
      warning: chalk.yellow,
      info: chalk.blue,
      log: chalk.blue,
      debug: chalk.cyan,
    };
    const color = coloredMap[type as keyof typeof coloredMap] ?? chalk.gray;
    const coloredType = color(`[BROWSER-${upperCase(type)}]`);
    const argsText = args.length > 0 ? ` | Args: ${JSON.stringify(args)}` : "";
    const locationText = location.url
      ? ` ${chalk.gray(`(${location.url}:${location.lineNumber}:${location.columnNumber})`)}`
      : "";

    forwardLog(`${coloredType} ${text}${argsText}${locationText}`);
  });

  // Also capture page errors (uncaught exceptions)
  page.on("pageerror", (error) => {
    forwardLog(chalk.red(`[BROWSER-UNCAUGHT-ERROR] ${error.message}`));
    forwardLog(chalk.red(`Stack: ${error.stack}`));
  });

  // Capture failed requests
  page.on("requestfailed", (request) => {
    forwardLog(
      chalk.red(
        `[BROWSER-REQUEST-FAILED] ${request.method()} ${request.url()} - ${request.failure()?.errorText}`,
      ),
    );
  });
}
