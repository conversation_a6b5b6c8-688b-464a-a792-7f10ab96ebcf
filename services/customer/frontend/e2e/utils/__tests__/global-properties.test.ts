import type { Page } from "@playwright/test";
import { test, expect, describe } from "../fixtures";
import { page } from "../session";

/**
 * Vitest tests for global properties and fixtures.
 *
 * To run these tests, execute:
 * `pnpm exec playwright test e2e/utils/__tests__/global-properties.test.ts`
 *
 * Background: Global properties such as `page`, `context`, `browser`, `request`, and `testInfo`
 * are automatically reset between tests.
 */

let page1: Page;
let page2: Page;
describe("Global Properties 1", () => {
  test("page exists", async () => {
    page1 = page;
    expect(page).toBeTruthy();
    expect(page.screenshot.name).toBe("screenshotExt");
  });

  test("a new page is created for each test", async () => {
    page2 = page;
    expect(page2).not.toBe(page1);
  });
});

describe("Global Properties 2", () => {
  test("a new page is created for each describe", async () => {
    expect(page1).not.toBe(page);
    expect(page2).not.toBe(page);
  });
});
