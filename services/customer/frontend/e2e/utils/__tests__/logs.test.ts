// eslint-disable-next-line no-restricted-imports
import { describe, test, expect, vi, beforeEach, afterEach } from "vitest";
import type { Page, ConsoleMessage, Request } from "@playwright/test";
import { delay } from "~services/lib/ts_utils/ts_dist/timer";
import type { AnyFunction } from "~services/lib/ts_utils/ts_dist/type";
import { forwardBrowserLogs } from "../logs";

/**
 * Vitest tests for logs.ts.
 *
 * To run these tests, execute:
 * `pnpm exec vitest run e2e/utils/__tests__/logs.test.ts --config=e2e/vitest.e2e.config.ts`
 *
 * Background: logs.ts contains utility functions for logging browser console logs,
 * page errors, and failed requests. This test suite ensures that these functions
 * behave as expected.
 */

// Mock chalk to avoid ANSI codes in tests
vi.mock("chalk", () => ({
  default: {
    red: (str: string) => `[RED]${str}[/RED]`,
    yellow: (str: string) => `[YELLOW]${str}[/YELLOW]`,
    blue: (str: string) => `[BLUE]${str}[/BLUE]`,
    cyan: (str: string) => `[CYAN]${str}[/CYAN]`,
    gray: (str: string) => `[GRAY]${str}[/GRAY]`,
  },
}));

describe("logs.ts", () => {
  let mockPage: Page;
  let consoleLogSpy: ReturnType<typeof vi.spyOn>;
  let originalLogLevel: string | undefined;

  beforeEach(() => {
    // Save original LOG_LEVEL
    originalLogLevel = process.env.LOG_LEVEL;

    // Spy on console.log
    consoleLogSpy = vi.spyOn(console, "log").mockImplementation(() => {});

    // Create mock page with event emitter functionality
    const eventListeners: Record<string, AnyFunction[]> = {};

    mockPage = {
      on: vi.fn((event: string, callback: AnyFunction) => {
        if (!eventListeners[event]) {
          eventListeners[event] = [];
        }
        eventListeners[event].push(callback);
      }),
      // Helper to emit events for testing
      _emit: (event: string, ...args: any[]) => {
        if (eventListeners[event]) {
          eventListeners[event].forEach((callback) => callback(...args));
        }
      },
    } as any;
  });

  afterEach(() => {
    // Restore original LOG_LEVEL
    if (originalLogLevel !== undefined) {
      process.env.LOG_LEVEL = originalLogLevel;
    } else {
      delete process.env.LOG_LEVEL;
    }

    vi.restoreAllMocks();
  });

  describe("forwardBrowserLogs", () => {
    test("should register event listeners on page", () => {
      forwardBrowserLogs(mockPage);

      expect(mockPage.on).toHaveBeenCalledWith("console", expect.any(Function));
      expect(mockPage.on).toHaveBeenCalledWith("pageerror", expect.any(Function));
      expect(mockPage.on).toHaveBeenCalledWith("requestfailed", expect.any(Function));
    });

    test("should forward console logs with proper formatting", async () => {
      forwardBrowserLogs(mockPage);

      const mockConsoleMessage = {
        type: () => "log",
        text: () => "Test message",
        location: () => ({ url: "http://example.com", lineNumber: 10, columnNumber: 5 }),
        args: () => [],
      } as unknown as ConsoleMessage;

      // Emit console event
      (mockPage as any)._emit("console", mockConsoleMessage);

      // Wait for async operations
      await delay(0);

      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining("[BLUE][BROWSER-LOG][/BLUE] Test message"),
      );
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining("http://example.com:10:5"),
      );
    });

    test("should handle console logs with successful jsonValue", async () => {
      forwardBrowserLogs(mockPage);

      const mockArg = {
        jsonValue: () => Promise.resolve("successful value"),
        toString: () => "fallback string",
      };

      const mockConsoleMessage = {
        type: () => "info",
        text: () => "Test with successful args",
        location: () => ({ url: "", lineNumber: 0, columnNumber: 0 }),
        args: () => [mockArg],
      } as unknown as ConsoleMessage;

      (mockPage as any)._emit("console", mockConsoleMessage);
      await delay(0);

      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining('Args: ["successful value"]'),
      );
    });

    test("should respect LOG_LEVEL environment variable", async () => {
      // Test the actual logic: if (toLogLevel(type) < toLogLevel(process.env.LOG_LEVEL)) return;
      // When LOG_LEVEL is "debug" (3), error messages (0) should be filtered because 0 < 3 is true
      process.env.LOG_LEVEL = "debug";
      forwardBrowserLogs(mockPage);

      // Error message should be filtered out when LOG_LEVEL is debug
      const errorMessage = {
        type: () => "error",
        text: () => "Error message",
        location: () => ({ url: "", lineNumber: 0, columnNumber: 0 }),
        args: () => [],
      } as unknown as ConsoleMessage;

      (mockPage as any)._emit("console", errorMessage);
      await delay(0);

      // Should not log error messages when LOG_LEVEL is debug (because 0 < 3)
      expect(consoleLogSpy).not.toHaveBeenCalledWith(expect.stringContaining("Error message"));

      // Debug message should still be logged
      const debugMessage = {
        type: () => "debug",
        text: () => "Debug message",
        location: () => ({ url: "", lineNumber: 0, columnNumber: 0 }),
        args: () => [],
      } as unknown as ConsoleMessage;

      (mockPage as any)._emit("console", debugMessage);
      await delay(0);

      expect(consoleLogSpy).toHaveBeenCalledWith(expect.stringContaining("Debug message"));
    });

    test("should handle page errors", () => {
      forwardBrowserLogs(mockPage);

      const mockError = new Error("Test error");
      mockError.stack = "Error: Test error\n    at test.js:1:1";

      (mockPage as any)._emit("pageerror", mockError);

      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining("[BROWSER-UNCAUGHT-ERROR] Test error"),
      );
      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining("Stack: Error: Test error"),
      );
    });

    test("should handle failed requests", () => {
      forwardBrowserLogs(mockPage);

      const mockRequest = {
        method: () => "POST",
        url: () => "http://example.com/api/test",
        failure: () => ({ errorText: "net::ERR_CONNECTION_REFUSED" }),
      } as Request;

      (mockPage as any)._emit("requestfailed", mockRequest);

      expect(consoleLogSpy).toHaveBeenCalledWith(
        expect.stringContaining(
          "[BROWSER-REQUEST-FAILED] POST http://example.com/api/test - net::ERR_CONNECTION_REFUSED",
        ),
      );
    });

    test("should handle different console message types with correct colors", async () => {
      forwardBrowserLogs(mockPage);

      const messageTypes = [
        { type: "error" },
        { type: "warning" },
        { type: "info" },
        { type: "log" },
        { type: "debug" },
        { type: "unknown" },
      ];

      for (const { type } of messageTypes) {
        const mockMessage = {
          type: () => type,
          text: () => `${type} message`,
          location: () => ({ url: "", lineNumber: 0, columnNumber: 0 }),
          args: () => [],
        } as unknown as ConsoleMessage;

        (mockPage as any)._emit("console", mockMessage);
        await delay(0);

        expect(consoleLogSpy).toHaveBeenCalledWith(
          expect.stringContaining(`[BROWSER-${type.toUpperCase()}]`),
        );
      }
    });
  });
});
