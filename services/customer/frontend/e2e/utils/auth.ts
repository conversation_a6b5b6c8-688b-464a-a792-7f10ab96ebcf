import crypto from "crypto";
import { SecretManagerServiceClient } from "@google-cloud/secret-manager";
import { APP_CONFIG } from "./app-config";
import type { Page } from "@playwright/test";
import { user } from "./session";
import type { TestUser } from "./user";
import chalk from "chalk";

async function getSecret(
  projectId = APP_CONFIG.projectId,
  secretId = APP_CONFIG.secretId,
  versionId = "latest",
) {
  const client = new SecretManagerServiceClient();
  const name = `projects/${projectId}/secrets/${secretId}/versions/${versionId}`;
  const [res] = await client.accessSecretVersion({ name });
  const val = res.payload?.data?.toString();
  if (!val) throw new Error(`Secret '${secretId}' is empty`);
  return val;
}

async function createBearerToken(payload: object) {
  const secret = await getSecret();
  const data = Buffer.from(JSON.stringify(payload)).toString("base64url");
  const sig = crypto.createHmac("sha256", secret).update(data).digest("base64url");
  return `${data}.${sig}`;
}

const userBearerMap = new WeakMap<TestUser, string>();
async function getBearerToken(user: TestUser) {
  if (!userBearerMap.has(user)) {
    const token = await createBearerToken(user);
    userBearerMap.set(user, token);
  }
  return userBearerMap.get(user);
}

const authCentralRegex = /https:\/\/auth-central\..+\.augmentcode\.com(\/.*)?/;
const customerUiRegex = new RegExp(`^${APP_CONFIG.customerUiUrl}(/.*)?`);

export function includeAuthHeader(page: Page) {
  page.route("**/*", async (route) => {
    try {
      const url = route.request().url();
      const headers = route.request().headers();
      if ((authCentralRegex.test(url) || customerUiRegex.test(url)) && user) {
        const token = await getBearerToken(user);
        headers["authorization"] = `Bearer ${token}`;
      }
      route.continue({ headers });
    } catch (err) {
      console.error(chalk.bgRed("Error in route handler:"), err);
      route.abort();
    }
  });
}
