import chalk from "chalk";
import { execSync } from "child_process";
import { existsSync, readdirSync } from "fs";
import { join, dirname, basename, extname, relative } from "path";

/** Checks if FFmpeg is available in the system */
function isFFmpegAvailable(): boolean {
  try {
    execSync("ffmpeg -version", { stdio: "pipe" });
    return true;
  } catch (error) {
    return false;
  }
}

/**
 * Converts a WebM video file to MP4 format using FFmpeg
 * @param webmPath - Path to the input WebM file
 * @param mp4Path - Optional path for the output MP4 file. If not provided, will use the same name with .mp4 extension
 * @returns Path to the converted MP4 file
 */
function convertWebmToMp4(outputDir: string, webmPath: string, mp4Path?: string): string {
  if (!existsSync(webmPath)) {
    throw new Error(`WebM file not found: ${webmPath}`);
  }
  if (!isFFmpegAvailable()) {
    console.warn("FFmpeg is not available. Skipping video conversion to MP4 format.");
    return webmPath;
  }
  if (!mp4Path) {
    const dir = dirname(webmPath);
    const name = basename(webmPath, extname(webmPath));
    mp4Path = join(dir, `${name}.mp4`);
  }
  try {
    // Use FFmpeg to convert WebM to MP4
    // -y flag overwrites output file if it exists
    // -i specifies input file
    // -c:v libx264 uses H.264 codec for video
    // -c:a aac uses AAC codec for audio
    // -movflags +faststart optimizes for web playback
    const ffmpegCommand = `ffmpeg -y -i "${webmPath}" -c:v libx264 -c:a aac -movflags +faststart "${mp4Path}"`;
    execSync(ffmpegCommand, { stdio: "pipe" });
    if (existsSync(mp4Path)) {
      console.info(` - ${chalk.green(relative(outputDir, mp4Path))}`);
      return mp4Path;
    } else {
      throw new Error("Conversion failed - output file not created");
    }
  } catch (error) {
    console.error(`Failed to convert ${webmPath} to MP4:`, error);
    return webmPath;
  }
}

/**
 * Converts videos after Playwright test completion
 * This function should be called in the tearDown hook of Playwright tests
 */
export function convertPlaywrightVideos(outputDir: string): void {
  try {
    console.info(`🎬 Converting Playwright videos in \n\t${chalk.green(outputDir)}...`);
    // Recursively find and convert all WebM files in subdirectories
    const convertedFiles: string[] = [];
    function processDirectory(dir: string) {
      if (!existsSync(dir)) return;
      const items = readdirSync(dir, { withFileTypes: true });
      for (const item of items) {
        const fullPath = join(dir, item.name);
        if (item.isDirectory()) {
          if (item.name === "report" || item.name.startsWith(".")) return;
          processDirectory(fullPath);
        } else if (item.name.endsWith(".webm")) {
          try {
            const mp4Path = convertWebmToMp4(outputDir, fullPath);
            convertedFiles.push(mp4Path);
          } catch (error) {
            console.error(`Failed to convert ${fullPath}:`, error);
          }
        }
      }
    }
    processDirectory(outputDir);
    console.info(`Converted ${convertedFiles.length} video files to MP4 format`);
  } catch (error) {
    console.error("Failed to convert Playwright videos:", error);
  }
}
