import * as fs from "fs";
import { join } from "path";
import * as os from "os";

function determineCustomerUiUrl(): string {
  // Primary: Check if CUSTOMER_UI_URL environment variable is set
  if (process.env.CUSTOMER_UI_URL) {
    console.info(`Using customer ui url from env: ${process.env.CUSTOMER_UI_URL}`);
    return process.env.CUSTOMER_UI_URL;
  }

  // Fallback 1: Check for BUILD_USER_NAMESPACE environment variable
  if (process.env.BUILD_USER_NAMESPACE) {
    const url = `https://app.${process.env.BUILD_USER_NAMESPACE}.us-central1.dev.augmentcode.com`;
    console.info(`Using customer ui url from BUILD_USER_NAMESPACE: ${url}`);
    return url;
  }

  // Fallback 2: Read ~/.augment/user.json and extract name field
  try {
    const userJsonPath = join(os.homedir(), ".augment", "user.json");

    if (!fs.existsSync(userJsonPath)) {
      throw new Error(`File ${userJsonPath} does not exist`);
    }

    const userJsonContent = fs.readFileSync(userJsonPath, "utf8");
    let userData;

    try {
      userData = JSON.parse(userJsonContent);
    } catch (parseError) {
      throw new Error(`Failed to parse JSON from ${userJsonPath}: ${parseError}`);
    }

    if (!userData.name || typeof userData.name !== "string") {
      throw new Error(`Missing or invalid 'name' field in ${userJsonPath}`);
    }

    const url = `https://app.dev-${userData.name}.us-central1.dev.augmentcode.com`;
    console.info(`Using customer ui url from user.json: ${url}`);
    return url;
  } catch (fileError) {
    console.error(`Error reading user.json: ${fileError}`);
    // Error handling: All fallback methods failed
    const errorMessage = fileError instanceof Error ? fileError.message : String(fileError);
    throw new Error(`Failed to determine CUSTOMER_UI_URL from any source: ${errorMessage}`);
  }
}

export const APP_CONFIG = {
  projectId: "1035750215372",
  secretId: "dev-auth-central-e2e-customerui", // pragma: allowlist secret
  customerUiUrl: determineCustomerUiUrl(),
} as const;
