import type { Page, TestInfo, Locator, PageScreenshotOptions } from "@playwright/test";

declare module "@playwright/test" {
  interface Locator {
    /** Read the whole table into a 2D string array (headers + rows) */
    getTableText(): Promise<string[][]>;
  }
}

/** Patch locator to support new methods */
const locatorMethods = [
  "locator",
  "getByRole",
  "getByText",
  "getByLabel",
  "getByPlaceholder",
  "getByAltText",
  "getByTitle",
  "getByTestId",
] as const;
export function patchLocatorMethods(page: Page) {
  locatorMethods.forEach((method) => {
    const orig = (page as any)[method].bind(page);
    (page as any)[method] = (...args: any[]) => {
      const locator = orig(...args);
      return patchLocator(locator);
    };
  });
}

function patchLocator(locator: Locator) {
  locator.getTableText = async function getTableText(this: Locator) {
    // eslint-disable-next-line no-restricted-syntax
    const rows = this.locator("tr");
    return rows.evaluateAll((els: Element[]) =>
      els.map((row) =>
        Array.from(row.querySelectorAll("th, td")).map((cell) => (cell.textContent ?? "").trim()),
      ),
    );
  };
  return locator;
}

type ImageFormat = "png" | "jpg" | "jpeg" | "webp";
type ScreenshotOptions = Omit<PageScreenshotOptions, "path"> &
  ({ name: `${string}.${ImageFormat}` } | { path: `${string}.${ImageFormat}` });
export type PageExt = Omit<Page, "screenshot"> & {
  /**
   * - Adds support for `name` or `path`
   * - Respects outputDir when saving screenshots.
   * @default fullPage: true
   * @example
   * // saves screenshot to e2e/results/my-screenshot.png
   * await page.screenshot({ name: "my-screenshot.png" });
   */
  screenshot: (options: ScreenshotOptions) => Promise<Buffer>;
};

let screenShotCount = 0;

/** Patch page.screenshot to support `name` and respect outputDir. */
export function patchScreenshot(page: Page, testInfo: TestInfo) {
  function getFilePath(options: { name: string } | { path: string }) {
    const filePath = "name" in options ? options.name : options.path;
    const parts = filePath.split("/");
    const name = `${++screenShotCount}-${parts.pop()}`;
    return [...parts, name].join("/");
  }
  // patch screenshot
  const origScreenshot = page.screenshot.bind(page);
  page.screenshot = function screenshotExt(options: ScreenshotOptions) {
    const path = testInfo.outputPath(getFilePath(options));
    return origScreenshot({ fullPage: true, ...options, path });
  };
}
