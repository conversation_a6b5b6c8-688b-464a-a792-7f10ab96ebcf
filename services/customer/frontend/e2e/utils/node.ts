import chalk from "chalk";
import { delay, withTimeout } from "~services/lib/ts_utils/ts_dist/timer";

type Node = {
  verify: () => Promise<void>;
  [K: string]: (...args: any[]) => any;
};

export type Chainable<T extends Node> = T & {
  /**
   * Run a function and return the current step.
   *
   * @param fn The function to run.
   * @returns The current step.
   * @example
   * ```ts
   * CustomerUI.signup()
   *   .tap(async (CustomerUI) => {
   *     // ...
   *   })
   *   .openPlanPicker();
   * ```
   */
  tap: <U>(fn: (obj: T) => U) => Chainable<T>;

  /** Makes promise chains awaitable. */
  then: Promise<T>["then"];

  /**
   * Assert the next node. This works if the current page satisfies the `verify` function of the next node.
   *
   * @param nextStep The next step.
   * @returns The next step.
   * @example
   * ```ts
   * CustomerUI.signup()
   *   .expectNode(LoggedIn);
   *   .logout();
   * ```
   */
  expectNode: <E extends Node>(nextStep: E) => Chainable<E>;
};

/**
 * Synchronously chain together a series of steps and returns a single promise that resolves when all steps are complete.
 *
 * ──────────────────────────────────────────────────────────────
 * @typeParam N  The “next‑step” object being returned.
 * @typeParam W  A function (sync or async) whose result is ignored.
 * @returns A chainable version of `nextStep`.
 * ─────────────────────────────────────────────────────────────
 * @example
 * ```ts
 * const PlanPicker = {
 *   selectPlan(planName: string) {
 *     return next(PlanConfirmation, async () => {
 *       // ...
 *     });
 *   },
 * };
 * // ...
 * CustomerUI.signup()
 *  .openPlanPicker()
 *  .selectPlan("Developer Plan");
 *
 * ```
 */
export function next<
  N extends Node,
  W extends (() => unknown | Promise<unknown>) | undefined = undefined,
>(nextStep: N, work?: W): Chainable<N> {
  if (!("verify" in nextStep)) {
    throw new Error(`Verify function not found in node ${JSON.stringify(nextStep)}`);
  }
  if (work) {
    chain = chain.then(work);
  }
  chain = chain.then(nextStep.verify);
  return {
    ...(Object.fromEntries(
      Object.entries(nextStep).map(([prop, value]) => [
        prop,
        typeof value === "function"
          ? (...args: any[]) => {
              chain = chain.then(() => console.info(chalk.yellow(`${++step} - ${prop}`)));
              return value(...args);
            }
          : value,
      ]),
    ) as N),

    tap: (fn: (obj: N) => any) => {
      chain = chain
        .then(() => fn(nextStep))
        .then(() => console.info(chalk.yellow(`${++step} - tap`), fn.name ?? ""));
      return next(nextStep);
    },

    then: (...args: any[]) => chain.then(() => nextStep).then(...args),

    expectNode: <E extends Node>(nextStep: E): Chainable<E> => {
      if (!("verify" in nextStep)) {
        throw new Error(`Verify function not found in node ${nextStep}`);
      }
      chain = chain.then(nextStep.verify);
      return next(nextStep);
    },

    expectNotPresent: <E extends Node>(
      notPresentStep: E,
      { timeout = 10_000 }: { timeout?: number } = {},
    ): Chainable<N> => {
      if (!("verify" in notPresentStep)) {
        throw new Error(`Verify function not found in node ${notPresentStep}`);
      }
      chain = chain.then(async () => {
        async function verified() {
          try {
            await notPresentStep.verify();
            return true;
          } catch {
            return false;
          }
        }
        async function notPresent() {
          while (await verified()) await delay(500);
        }
        await withTimeout(notPresent(), timeout, () => {
          throw new Error(`Expected ${notPresentStep} not to be present`);
        });
      });
      return next(nextStep);
    },
  } as Chainable<N>;
}

let step = 0;
let chain: Promise<any> = Promise.resolve();

/** Reset the step counter and promise chain */
export const start = () => {
  step = 0;
  chain = Promise.resolve();
};
