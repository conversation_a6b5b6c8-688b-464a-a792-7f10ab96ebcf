import { defineConfig, devices } from "@playwright/test";
import { dirname, join, resolve } from "path";
import { fileURLToPath } from "url";

let reporter: any = [["html", { outputFolder: "./report" }]];
if (process.env.XML_OUTPUT_FILE) {
  reporter = [["junit", { outputFile: process.env.XML_OUTPUT_FILE }]];
}
if (process.env.TEST_UNDECLARED_OUTPUTS_DIR) {
  reporter.push([
    "html",
    { outputFolder: `${process.env.TEST_UNDECLARED_OUTPUTS_DIR}/html-report` },
  ]);
  reporter.push([
    "json",
    {
      outputFile: `${process.env.TEST_UNDECLARED_OUTPUTS_DIR}/test-results.json`,
    },
  ]);
}
const e2eDir = dirname(fileURLToPath(import.meta.url));
let outputDir = resolve(e2eDir, "./results");
if (process.env.TEST_UNDECLARED_OUTPUTS_DIR) {
  outputDir = join(process.env.TEST_UNDECLARED_OUTPUTS_DIR, "results");
}
console.info(`outputDir: ${outputDir}`);

/** See https://playwright.dev/docs/test-configuration. */
export default defineConfig({
  testDir: resolve(e2eDir, "./tests"),
  testMatch: /.+\.test\.(ts|tsx)$/,
  outputDir,
  reporter,
  /* Run tests synchronously (no parallel execution) */
  fullyParallel: false,
  /* Fail the build on CI if you accidentally left test.only in the source code. */
  forbidOnly: true,
  retries: 0,
  workers: 1,
  globalSetup: resolve(e2eDir, "./utils/global-setup-bazel.ts"),
  globalTeardown: resolve(e2eDir, "./utils/global-teardown-bazel.ts"),
  /* Shared settings for all the projects below. See https://playwright.dev/docs/api/class-testoptions. */
  use: {
    /* Base URL to use in actions like `await page.goto('/')`. */
    // baseURL: 'http://localhost:3000',

    trace: "on",
    video: {
      mode: "on",
      size: { width: 1280, height: 720 },
    },
    screenshot: {
      mode: "on",
      fullPage: true,
    },
    extraHTTPHeaders: {
      "User-Agent": "Augment-E2E-Test/1.0",
    },
  },

  /* Configure projects for major browsers */
  projects: [
    {
      name: "chromium",
      use: { ...devices["Desktop Chrome"] },
    },
  ],
});
