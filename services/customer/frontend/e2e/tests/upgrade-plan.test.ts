import { expect, test } from "e2e/utils/fixtures";
import { CustomerUI } from "e2e/CustomerUI/CustomerUI";
import { TopNav } from "e2e/CustomerUI/TopNav";
import { page } from "e2e/utils/session";
import { SubscriptionPage } from "e2e/CustomerUI/SubscriptionPage";
import { namedFn } from "app/utils/function";

test("should upgrade plan from Trial to Developer Plan", async () => {
  await CustomerUI.signup()
    .acceptTerms()
    .expectNode(TopNav)
    .navigateToSubscription()
    .tap(verifyPlanName("Trial Plan"))
    .openPlanPicker()
    .selectPlan("Developer Plan")
    .confirmPlanChange()
    .submitPlanChange()
    .tap(verifyPlanName("Developer Plan"));

  function verifyPlanName(planName: string) {
    return namedFn(`verifyPlanName(${planName})`, async () => {
      await page.screenshot({ name: "current-plan-name.png" });
      await expect.poll(async () => await SubscriptionPage.getCurrentPlanName()).toBe(planName);
    });
  }
});
