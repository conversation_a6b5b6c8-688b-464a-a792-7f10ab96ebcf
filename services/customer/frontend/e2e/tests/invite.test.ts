import { test } from "e2e/utils/fixtures";
import { CustomerUI } from "e2e/CustomerUI/CustomerUI";
import { generateTestUser } from "e2e/utils/user";
import { LoggedIn } from "e2e/CustomerUI/LoggedIn";
import { TopNav } from "e2e/CustomerUI/TopNav";
import { delay } from "~services/lib/ts_utils/ts_dist/timer";
// import { InvitationsPage } from "e2e/CustomerUI/InvitationsPage";

test("should invite team member", async () => {
  const adminUser = generateTestUser("admin");
  const teamMember = generateTestUser("teamMember");
  await CustomerUI.signup(adminUser)
    .acceptTerms()
    .expectNode(TopNav)
    .navigateToTeams()
    .openAddTeamMembersModal()
    .addTeamMembers([teamMember.email])
    .submitInviteTeamMembers()
    .expectNode(LoggedIn)
    .logout();
  await delay(5000);

  // TODO
  // await CustomerUI.signup(teamMember)
  //   .acceptTerms()
  //   .expectNode(InvitationsPage)
  //   .acceptInvite(adminUser.email)
  //   .expectNode(TopNav)
  //   .navigateToTeams()
  //   .tap(function getTeamName(TeamsPage) {
  //     return expect(TeamsPage.listTeamMembers()).resolves.toContainEqual({
  //       email: adminUser.email,
  //       role: "Admin",
  //       joined: expect.anything(),
  //       isAdmin: true,
  //     });
  //   })
  //   .expectNode(LoggedIn)
  //   .logout();
});
