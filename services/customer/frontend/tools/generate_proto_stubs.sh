#!/usr/bin/env bash
set -euo pipefail

# Convenience script to copy TypeScript protobuf declaration files into the source tree
# for all proto libraries used by the Customer UI. This runs each ts_proto_library's
# `.generate_stubs_with_all_deps` target which writes .d.ts files at stable paths
# (e.g. services/.../*_pb.d.ts) so IDE/Vite can resolve `~services/**/*_pb` imports.
#
# Instead of hard-coding targets, we parse BUILD_MODULES from services/customer/frontend/BUILD
# and run the generate target for any label ending with `_ts_proto`.

SCRIPT_DIR="$(cd "$(dirname "${BASH_SOURCE[0]}")" && pwd)"
BUILD_FILE="${SCRIPT_DIR}/../BUILD"

if [[ ! -f "${BUILD_FILE}" ]]; then
	echo "BUILD file not found at ${BUILD_FILE}" >&2
	exit 1
fi

echo "Parsing BUILD_MODULES from ${BUILD_FILE} ..."
# Extract quoted labels from the BUILD_MODULES array, filter *_ts_proto labels
mapfile -t MODULE_LABELS < <(
	awk '
    $0 ~ /BUILD_MODULES[[:space:]]*=.*\[/ {in=1; next}
    in && $0 ~ /\]/ {in=0}
    in {print}
  ' "${BUILD_FILE}" |
		grep -oE '"[^"]+"' |
		sed 's/"//g' |
		grep -E '^//.+:.+_ts_proto$' |
		sort -u
)

if [[ ${#MODULE_LABELS[@]} -eq 0 ]]; then
	echo "No *_ts_proto labels found in BUILD_MODULES. Nothing to do."
	exit 0
fi

echo "Found ${#MODULE_LABELS[@]} ts_proto labels:"
printf '  - %s\n' "${MODULE_LABELS[@]}"

echo "Generating TS proto stubs..."
for lbl in "${MODULE_LABELS[@]}"; do
	tgt="${lbl}.generate_stubs_with_all_deps"
	echo "--- bazel run ${tgt}"
	bazel run "${tgt}"
done

echo "Done."
