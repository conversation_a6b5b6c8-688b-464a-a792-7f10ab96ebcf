// Initialize OpenTelemetry BEFORE any other imports
import "./app/otel.js";
import path from "path";
import express from "express";
import { fileURLToPath, pathToFileURL } from "url";
import compression from "compression";
import Prometheus from "prom-client";
import { installGlobals } from "@remix-run/node";
import { createRequestHand<PERSON> } from "@remix-run/express";
import prom from "@isaacs/express-prometheus-middleware";
import { logger } from "@augment-internal/logging";
import { isProduction } from "@augment-internal/systemenv";
import sourceMapSupport from "source-map-support";
import { createAgentsRouter, getAgentsAppDir } from "./agents-app-proxy.js";

sourceMapSupport.install();

const __filename = fileURLToPath(import.meta.url); // get the resolved path to the file
const __dirname = path.dirname(__filename);

const REMIX_DIR = `${__dirname}/../dist`;

const AGENTS_APP_DIR = getAgentsAppDir(isProduction(), __dirname);

const ALLOW_CLIENT_SOURCEMAPS = process.env.ALLOW_CLIENT_SOURCEMAPS === "true";
const ENABLE_AGENTS_WEB_APP = process.env.ENABLE_AGENTS_WEB_APP === "true";

export async function main(production = isProduction()) {
  logger.info(
    `starting customer-ui, env=${process.env.NODE_ENV}, remixDir=${REMIX_DIR}, agentsAppDir=${AGENTS_APP_DIR}`,
  );

  //This doesn't really have a type
  //eslint-disable-next-line @typescript-eslint/no-explicit-any
  let remixIndex: any;

  //eslint-disable-next-line @typescript-eslint/no-explicit-any
  let viteDevServer: any | undefined;

  if (production) {
    try {
      //eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      remixIndex = await import(`${REMIX_DIR}/server/index.js`);
    } catch (e) {
      logger.warn("failed to import remixIndex ", e);
      throw e;
    }
  } else {
    viteDevServer = await (async () => {
      //eslint-disable-next-line @typescript-eslint/ban-ts-comment
      //@ts-ignore
      return (await import("vite")).createServer({
        server: { middlewareMode: true },
      });
    })();
  }

  const starting = logger.startTimer();
  //nativeFetch per https://github.com/remix-run/remix/issues/6118
  installGlobals({ nativeFetch: true });

  const app = express();

  // http://expressjs.com/en/advanced/best-practice-security.html#at-a-minimum-disable-x-powered-by-header
  app.disable("x-powered-by");

  // Only mount the agents app if we've enabled it for this namespace (services/customer/frontend/deploy.jsonnet)
  // Mount before compression since that breaks the streaming response
  if (ENABLE_AGENTS_WEB_APP) {
    app.use("/agents", createAgentsRouter(AGENTS_APP_DIR, production));
  }

  app.use(compression());

  app.get("*.js.map", (_req, res, next) => {
    // we set these to "private, no-cache" because in the future we would like to make
    // source maps available in production for auth'd employees, so customers
    // would get the 204 while employees could get a real sourcemap
    res.setHeader("Cache-control", "private, no-cache");
    next();
  });

  // we do this BEFORE the static asset mounting to disable source maps
  if (!ALLOW_CLIENT_SOURCEMAPS) {
    logger.info("disabling client source maps");
    app.get("*.js.map", (_req, res) => {
      res.sendStatus(204);
    });
  }

  // handle asset requests
  if (production) {
    // Vite fingerprints its assets so we can cache forever.
    app.use(
      "/assets",
      express.static(path.join(REMIX_DIR, "client/assets"), {
        immutable: true,
        maxAge: "1y",
      }),
    );
    // these assets are specifically named and not hashed, so we cannot cache them forever.
    app.use(
      express.static(path.join(REMIX_DIR, "client"), {
        immutable: true,
        maxAge: "1h",
      }),
    );
  } else {
    app.use(viteDevServer!.middlewares);
  }

  // if we are allowed to have source maps, then they are handled by the static
  // asset system above, so then we only do this for sourcemaps that are not found.
  if (ALLOW_CLIENT_SOURCEMAPS) {
    app.get("*.js.map", (_req, res) => res.sendStatus(204));
  }

  // This is not pretty but it was the only way to get a default cache-control on all
  // responses unless the response had already set its own. This should be the
  // most defensive approach and prevent any requests from going out without cache headers.
  // The tricky part here is that Remix has a few ways of setting cache headers, but they
  // do not apply to all routes -- some only apply to the document, some apply to "data" requests
  // but none apply to "resource" (api) routes except for setting them in the route loader
  // itself.
  app.use((_req, res, next) => {
    const originalWriteHead = res.writeHead;

    res.writeHead = function (...args: any) {
      if (!res.getHeader("Cache-Control") && !res.headersSent) {
        res.setHeader("Cache-Control", "no-store");
      }

      return originalWriteHead.apply(this, args);
    };

    next();
  });

  const metricsApp = production ? express() : undefined;
  Prometheus.register.setDefaultLabels({ app: "customer-ui" });
  app.use(
    //https://www.npmjs.com/package/@isaacs/express-prometheus-middleware
    prom({
      metricsApp,
      metricsPath: "/metrics",
      collectDefaultMetrics: true,
      prefix: "au_",
      normalizeStatus: false, // Get specific status codes (404, 500) instead of grouped (4XX, 5XX)
    }),
  );

  app.all(
    "*",
    createRequestHandler({
      build: production
        ? () => remixIndex
        : () => viteDevServer?.ssrLoadModule("virtual:remix/server-build"),
    }),
  );

  const { PORT: port = 5000, METRICS_PORT: metricsPort = 9090 } = process.env;

  const server = app.listen(port, () => {
    starting.done({
      message: `Express server listening at http://localhost:${port}`,
    });
  });

  const metricsServer = metricsApp?.listen(metricsPort, () => {
    logger.info(`Metrics server listening on http://localhost:${metricsPort}`);
  });

  return { server, metricsServer, viteDevServer };
}

// Export servers for cleanup when imported by dev.ts
export let appServers: {
  server: any;
  metricsServer?: any;
  viteDevServer?: any;
} | null = null;

if (isRunDirectly()) {
  main()
    .then((servers) => {
      appServers = servers;
    })
    .catch((e) => {
      console.trace(e);
      process.exit(1);
    });
}

/**
 * Returns true if this file is run directly (not imported).
 * @default true
 */
function isRunDirectly() {
  if (typeof import.meta !== "undefined") {
    const scriptUrl = pathToFileURL(process.argv[1]).href;
    return import.meta.url === scriptUrl;
  }
  return true;
}
