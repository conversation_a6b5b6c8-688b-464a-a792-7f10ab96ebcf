import { describe, expect, it, vi } from "vitest";
import preview from "./preview";
import mainConfig from "./main";

// Mock Vite to avoid esbuild issues
vi.mock("vite", () => ({
  mergeConfig: vi.fn((config1, config2) => ({ ...config1, ...config2 })),
}));

describe("Storybook config", () => {
  it("should have valid preview config", () => {
    expect(preview).toBeDefined();
    expect(preview.parameters).toBeDefined();
    expect(preview.decorators).toBeDefined();
  });

  it("should have valid main config", () => {
    expect(mainConfig).toBeDefined();
    expect(mainConfig.stories).toBeDefined();
    expect(mainConfig.addons).toBeDefined();
    expect(mainConfig.framework).toBeDefined();
  });
});
