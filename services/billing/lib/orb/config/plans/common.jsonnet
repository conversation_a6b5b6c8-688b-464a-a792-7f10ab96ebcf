// Common utilities and defaults for Orb plans
{
  // Environment-specific item IDs
  seatsItemId: {
    DEV: 'fPTPFSjvP7fjF3PZ',
    STAGING: 'fPTPFSjvP7fjF3PZ',
    PROD: 'i6nDyZKNgFu4d4rd',
  },

  includedMessagesItemId: {
    DEV: 'kcwmTLYJpnTb33Nq',
    STAGING: 'kcwmTLYJpnTb33Nq',
    PROD: 'DunFsa2DZzHpVq45',
  },

  userMessageItemId: {
    DEV: 'i8fNmqjVYyXmpht8',
    STAGING: 'i8fNmqjVYyXmpht8',
    PROD: 'iyFgVkZjzWhf3bsv',
  },

  userMessageBillableMetricId: {
    DEV: 'HpZt6dxLggckWTCn',
    STAGING: 'HpZt6dxLggckWTCn',
    PROD: '3M8vZKHnXDqarNX8',
  },

  emptyIdempotencyKeySuffix: {
    DEV: '',
    STAGING: '',
    PROD: '',
  },

  // Environment-specific accounting config
  accounting_config: {
    DEV: {
      enabled: true,
      provider_type: 'netsuite',
      provider_id: '516',
    },
    STAGING: {
      enabled: true,
      provider_type: 'netsuite',
      provider_id: '516',
    },
    PROD: {
      enabled: false,
      provider_type: 'netsuite',
      provider_id: '',  //TODO fill in before turning on in prod
    },
  },

  // Common configuration for plan creation
  planDefaults: function(env)
    {
      currency: 'USD',
      state: 'active',
      net_terms: 0,
      billing_cycle_configuration: {
        duration: 1,
        duration_unit: 'month',
      },
      cadence: 'monthly',
      usage_unit_display_name: 'user messages',
      plan_facts: [
        'Context Engine',
        'MCP & Native Tools',
        'Unlimited completions',
        'Unlimited Next Edit',
        'Email and community support',
      ],
      model_type: 'unit',
      seats_name: 'Seats',
      seats_item: {
        name: 'Seats',
        id: $.seatsItemId[env],
      },
      included_messages_name: 'Included Allocation (User Messages)',
      included_messages_item: {
        name: 'Included Allocation (User Messages)',
        id: $.includedMessagesItemId[env],
      },
      user_message_name: 'User Message',
      user_message_item: {
        name: 'User Message',
        id: $.userMessageItemId[env],
      },
      pricing_unit: 'usermessages',
      fixed_price_type: 'fixed_price',
      usage_price_type: 'usage_price',
      allows_rollover: false,
    },

  // Price template functions to eliminate duplication
  createSeatsPrice: function(unitAmount, env)
    local planDefaults = $.planDefaults(env);
    {
      name: planDefaults.seats_name,
      item: planDefaults.seats_item,
      model_type: planDefaults.model_type,
      price_type: planDefaults.fixed_price_type,
      cadence: planDefaults.cadence,
      currency: planDefaults.currency,
      fixed_price_quantity: 1,
      billing_cycle_configuration: planDefaults.billing_cycle_configuration,
      unit_config: {
        unit_amount: unitAmount,
      },
    },

  createUserMessagePrice: function(env)
    local planDefaults = $.planDefaults(env);
    {
      name: planDefaults.user_message_name,
      item: planDefaults.user_message_item,
      model_type: planDefaults.model_type,
      price_type: planDefaults.usage_price_type,
      cadence: planDefaults.cadence,
      currency: planDefaults.pricing_unit,
      conversion_rate: 0,
      conversion_rate_config: {
        conversion_rate_type: 'unit',
        unit_config: {
          unit_amount: '0.00',
        },
      },
      billable_metric: {
        id: $.userMessageBillableMetricId[env],
      },
      billing_cycle_configuration: planDefaults.billing_cycle_configuration,
      unit_config: {
        unit_amount: '1.00',
      },
    },

  createIncludedMessagesPrice: function(quantity, env)
    local planDefaults = $.planDefaults(env);
    {
      name: planDefaults.included_messages_name,
      item: planDefaults.included_messages_item,
      model_type: planDefaults.model_type,
      price_type: planDefaults.fixed_price_type,
      cadence: planDefaults.cadence,
      currency: planDefaults.currency,
      fixed_price_quantity: quantity,
      billing_cycle_configuration: planDefaults.billing_cycle_configuration,
      unit_config: {
        unit_amount: '0.00',
      },
      credit_allocation: {
        currency: planDefaults.pricing_unit,
        allows_rollover: planDefaults.allows_rollover,
      },
    },

  // Config for plan validation
  validation_config: {
    DEV: {
      dry_run: false,
      auto_create_missing: true,
      fail_on_warnings: false,
      max_retries: 3,
      retry_delay_seconds: 2,
    },
    STAGING: {
      dry_run: true,
      auto_create_missing: false,
      fail_on_warnings: false,
      max_retries: 3,
      retry_delay_seconds: 2,
    },
    PROD: {
      dry_run: true,
      auto_create_missing: false,
      fail_on_warnings: false,
      max_retries: 3,
      retry_delay_seconds: 2,
    },
  },

  // These max purchase amounts are in USD
  min_addon_purchase: 10.0,
  max_addon_purchase: 100.0,
}
