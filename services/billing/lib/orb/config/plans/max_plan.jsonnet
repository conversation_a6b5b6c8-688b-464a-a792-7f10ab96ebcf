// Max Plan configuration
local common = import 'services/billing/lib/orb/config/plans/common.jsonnet';

function(env)
  local planDefaults = common.planDefaults(env);

  {
    id: 'orb_max_plan',
    state: planDefaults.state,
    idempotency_key_suffix: common.emptyIdempotencyKeySuffix[env],

    // UI configuration
    display_info: {
      color: 'gold',
      icon: 'lightning',
      sort_order: 5,
      usage_unit_display_name: planDefaults.usage_unit_display_name,
      plan_facts: planDefaults.plan_facts,
    },

    // Plan features
    features: {
      training_allowed: false,
      teams_allowed: true,
      max_seats: 100,
      add_credits_available: true,
      plan_type: 'paid',
    },

    // Plan Configuration
    orb_plan: {
      external_plan_id: 'orb_max_plan',
      name: 'Max Plan',
      currency: planDefaults.currency,
      invoicing_currency: planDefaults.currency,
      net_terms: planDefaults.net_terms,
      version: {
        DEV: 2,
        STAGING: 2,
        PROD: 1,
      }[env],
      status: 'active',
      prices: [
        common.createSeatsPrice('250.00', env),
        common.createUserMessagePrice(env),
        common.createIncludedMessagesPrice(4500, env),
      ],
    },
  }
