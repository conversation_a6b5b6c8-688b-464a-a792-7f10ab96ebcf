// Developer Plan configuration
local common = import 'services/billing/lib/orb/config/plans/common.jsonnet';

function(env)
  local planDefaults = common.planDefaults(env);

  {
    id: 'orb_developer_plan',
    state: planDefaults.state,
    idempotency_key_suffix: common.emptyIdempotencyKeySuffix[env],

    // UI configuration
    display_info: {
      color: 'indigo',
      icon: 'rocket',
      sort_order: 3,
      usage_unit_display_name: planDefaults.usage_unit_display_name,
      plan_facts: planDefaults.plan_facts,
    },

    // Plan features
    features: {
      training_allowed: false,
      teams_allowed: true,
      max_seats: 100,
      add_credits_available: true,
      plan_type: 'paid',
    },

    // Plan Configuration
    orb_plan: {
      external_plan_id: 'orb_developer_plan',
      name: 'Developer Plan',
      currency: planDefaults.currency,
      invoicing_currency: planDefaults.currency,
      net_terms: planDefaults.net_terms,
      version: {
        DEV: 9,
        STAGING: 9,
        PROD: 5,
      }[env],
      status: 'active',
      prices: [
        common.createSeatsPrice('50.00', env),
        common.createUserMessagePrice(env),
        common.createIncludedMessagesPrice(600, env),
      ],
    },
  }
