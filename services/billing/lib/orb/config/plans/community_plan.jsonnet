// Community Plan configuration
local common = import 'services/billing/lib/orb/config/plans/common.jsonnet';

function(env)
  local planDefaults = common.planDefaults(env);

  {
    id: 'orb_community_plan',
    state: planDefaults.state,
    idempotency_key_suffix: common.emptyIdempotencyKeySuffix[env],

    // UI configuration
    display_info: {
      color: 'blue',
      icon: 'person',
      sort_order: 1,
      usage_unit_display_name: planDefaults.usage_unit_display_name,
      plan_facts: planDefaults.plan_facts,
    },

    // Plan features
    features: {
      training_allowed: true,
      teams_allowed: false,
      max_seats: 1,
      add_credits_available: true,
      plan_type: 'community',
    },

    // Plan Configuration
    orb_plan: {
      external_plan_id: 'orb_community_plan',
      name: 'Community Plan',
      currency: planDefaults.currency,
      invoicing_currency: planDefaults.currency,
      net_terms: planDefaults.net_terms,
      version: {
        DEV: 4,
        STAGING: 4,
        PROD: 4,
      }[env],
      status: 'active',
      prices: [
        common.createSeatsPrice('0.00', env),
        common.createUserMessagePrice(env),
        common.createIncludedMessagesPrice(50, env),
      ],
    },
  }
