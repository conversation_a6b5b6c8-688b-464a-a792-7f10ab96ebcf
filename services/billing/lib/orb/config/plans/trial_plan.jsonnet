// Trial Plan configuration
local common = import 'services/billing/lib/orb/config/plans/common.jsonnet';

function(env)
  local planDefaults = common.planDefaults(env);

  {
    id: 'orb_trial_plan',
    state: planDefaults.state,
    idempotency_key_suffix: common.emptyIdempotencyKeySuffix[env],

    // UI configuration
    display_info: {
      color: 'gray',
      icon: 'clock',
      sort_order: 2,
      usage_unit_display_name: planDefaults.usage_unit_display_name,
      plan_facts: planDefaults.plan_facts,
    },

    // Plan features
    features: {
      training_allowed: false,
      teams_allowed: true,
      max_seats: 5,
      add_credits_available: false,
      plan_type: 'trial',
    },

    orb_plan: {
      external_plan_id: 'orb_trial_plan',
      name: 'Trial Plan',
      currency: planDefaults.currency,
      invoicing_currency: planDefaults.currency,
      net_terms: planDefaults.net_terms,
      version: {
        DEV: 7,
        STAGING: 7,
        PROD: 7,
      }[env],
      status: 'active',
      prices: [
        common.createSeatsPrice('0.00', env),
        common.createUserMessagePrice(env),
        common.createIncludedMessagesPrice(125, env),
      ],
    },
  }
