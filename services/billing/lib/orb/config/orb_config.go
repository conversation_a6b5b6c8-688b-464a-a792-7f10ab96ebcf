package config

import (
	orb_go "github.com/orbcorp/orb-go"
)

type PlanType string

const (
	// Community plan type
	PlanTypeCommunity PlanType = "community"
	// Paid plan in trial
	PlanTypePaidTrial PlanType = "trial"
	// Paid plan type
	PlanTypePaid PlanType = "paid"
)

type PlanState string

const (
	// Plan is being drafted, not visible in GetAllOrbPlans
	PlanStateDraft PlanState = "draft"
	// Plan is visible to all users
	PlanStateActive PlanState = "active"
	// Plan is not visible in GetAllOrbPlans but still works for existing users already on the plan
	PlanStateHidden PlanState = "hidden"
)

type PlanFeatures struct {
	TrainingAllowed     bool `json:"training_allowed"`
	TeamsAllowed        bool `json:"teams_allowed"`
	MaxSeats            int  `json:"max_seats"`
	AddCreditsAvailable bool `json:"add_credits_available"`
	// PlanType is one of "community", "trial", "paid"
	// the plan type gives information around some behaviors of the plan, e.g.
	// the switching behavior. For example, it is not possible to switch into
	// a trial plan
	PlanType PlanType `json:"plan_type"`
}

// BillingCycle represents the billing cycle configuration
type BillingCycle struct {
	Duration     int    `json:"duration"`
	DurationUnit string `json:"duration_unit"`
}

// UnitConfig represents the unit configuration for a price
type UnitConfig struct {
	UnitAmount string `json:"unit_amount"`
}

// CreditAllocation represents credit allocation configuration
type CreditAllocation struct {
	Currency       string `json:"currency"`
	AllowsRollover bool   `json:"allows_rollover"`
}

// UI-specific display configuration for plans
// This struct contains fields that are used by the frontend for display purposes
type PlanDisplayConfig struct {
	Color                string   `json:"color"`                   // color for display purposes only
	Icon                 string   `json:"icon"`                    // icon for display purposes only
	SortOrder            int      `json:"sort_order"`              // sorting order for displaying plans only
	UsageUnitDisplayName string   `json:"usage_unit_display_name"` // name of the usage unit (e.g., "user messages", "credits")
	PlanFacts            []string `json:"plan_facts"`              // list of plan facts for display purposes
}

type PlanConfig struct {
	ID                   string            `json:"id"`           // maps to the external_plan_id in Orb
	State                PlanState         `json:"state"`        // plan state: "draft", "active", "hidden"
	DisplayInfo          PlanDisplayConfig `json:"display_info"` // UI-specific display information
	Features             PlanFeatures      `json:"features"`
	OrbPlan              orb_go.Plan       `json:"orb_plan"`               // Orb plan configurations
	IdempotencyKeySuffix string            `json:"idempotency_key_suffix"` // optional suffix for idempotency key generation
}

func (orbConfig *OrbConfig) GetPlan(planID string) *PlanConfig {
	for _, plan := range orbConfig.Plans {
		if plan.ID == planID {
			return &plan
		}
	}
	return nil
}

// returns the community plan
//
// always exists
func (orbConfig *OrbConfig) GetCommunityPlan() *PlanConfig {
	for _, plan := range orbConfig.Plans {
		if plan.Features.PlanType == PlanTypeCommunity {
			return &plan
		}
	}
	panic("Failed to find community plan")
}

// returns the trial plan
//
// always exists
func (orbConfig *OrbConfig) GetTrialPlan() *PlanConfig {
	for _, plan := range orbConfig.Plans {
		if plan.Features.PlanType == PlanTypePaidTrial {
			return &plan
		}
	}
	panic("Failed to find trial plan")
}

// Find a plan by ID
// returns nil if not found
func (orbConfig *OrbConfig) FindPlan(planID string) *PlanConfig {
	for _, plan := range orbConfig.Plans {
		if plan.ID == planID {
			return &plan
		}
	}
	return nil
}

// Configuration for Orb Plan Validation
type OrbValidationConfig struct {
	Enabled           bool `json:"enabled"`             // whether plan validation is enabled
	IgnorePlanVersion bool `json:"ignore_plan_version"` // whether to ignore plan versions when validating
	DryRun            bool `json:"dry_run"`             // whether to run in dry-run mode
	AutoCreateMissing bool `json:"auto_create_missing"` // whether to auto-create missing plans
	FailOnWarnings    bool `json:"fail_on_warnings"`    // whether to fail validation on warnings
	MaxRetries        int  `json:"max_retries"`         // maximum number of retries for API calls
	RetryDelaySeconds int  `json:"retry_delay_seconds"` // delay between retries in seconds
}

type OrbConfig struct {
	Enabled                     bool                      `json:"enabled"`
	ApiKeyPath                  string                    `json:"api_key_path"`
	SeatsItemID                 string                    `json:"seats_item_id"`
	IncludedMessagesItemID      string                    `json:"included_messages_item_id"`
	PricingUnit                 string                    `json:"pricing_unit"`
	CostPerMessage              float64                   `json:"cost_per_message"`
	Plans                       []PlanConfig              `json:"plans"`
	MinAddonPurchase            float64                   `json:"min_addon_purchase"`
	MaxAddonPurchase            float64                   `json:"max_addon_purchase"`
	Validation                  OrbValidationConfig       `json:"validation"`
	AccountingConfig            SelfServeAccountingConfig `json:"accounting_config"`
	DefaultUsageUnitDisplayName string                    `json:"default_usage_unit_display_name"`
}

type SelfServeAccountingConfig struct {
	Enabled      bool   `json:"enabled"`
	ProviderType string `json:"provider_type"`
	ProviderID   string `json:"provider_id"`
}
