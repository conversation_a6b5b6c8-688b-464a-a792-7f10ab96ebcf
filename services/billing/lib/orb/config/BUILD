load("@rules_jsonnet//jsonnet:jsonnet.bzl", "jsonnet_library")
load("//tools/bzl:go.bzl", "go_library")

go_library(
    name = "config",
    srcs = [
        "orb_config.go",
    ],
    importpath = "github.com/augmentcode/augment/services/billing/lib/orb/config",
    visibility = [
        "//services/auth:__subpackages__",
        "//services/billing:__subpackages__",
    ],
    deps = [
        "@com_github_orbcorp_orb_go//:orb-go",
    ],
)

jsonnet_library(
    name = "orb_config_jsonnet",
    srcs = ["orb_config.jsonnet"],
    visibility = ["//visibility:public"],
    deps = [
        "//services/billing/lib/orb/config/plans:plans_lib",
    ],
)
