// Configuration for Orb billing plans, price IDs, and plan logic
local common = import 'services/billing/lib/orb/config/plans/common.jsonnet';
local communityPlan = import 'services/billing/lib/orb/config/plans/community_plan.jsonnet';
local developerPlan = import 'services/billing/lib/orb/config/plans/developer_plan.jsonnet';
local maxPlan = import 'services/billing/lib/orb/config/plans/max_plan.jsonnet';
local proPlan = import 'services/billing/lib/orb/config/plans/pro_plan.jsonnet';
local trialPlan = import 'services/billing/lib/orb/config/plans/trial_plan.jsonnet';

function(env='PROD', namespace='', namespace_config={})
  // Get all plans
  local allPlans = [
    communityPlan(env),
    trialPlan(env),
    developerPlan(env),
    proPlan(env),
    maxPlan(env),
  ];

  local billingValidationEnabled =
    if std.objectHas(namespace_config, 'flags') &&
       std.objectHas(namespace_config.flags, 'enableBillingPlanValidationAndCreation') then
      namespace_config.flags.enableBillingPlanValidationAndCreation
    else
      false;

  // For DEV environment, we append the dev namespace to the plan names, external_plan_ids, and plan ids
  // to make sure plans defined in different dev environments are separated, and don't affect staging.
  local appendDevNamespaceToPlan = function(plan)
    if env == 'DEV' && billingValidationEnabled then
      plan + {
        id: plan.id + '_' + namespace,
        orb_plan+: {
          name: plan.orb_plan.name + ' ' + namespace,
          external_plan_id: plan.orb_plan.external_plan_id + '_' + namespace,
        },
      }
    else
      plan;

  local allPlansWithDevNamespace = if env == 'DEV' && billingValidationEnabled then
    std.map(appendDevNamespaceToPlan, allPlans)
  else
    allPlans;

  {
    // Item IDs stay constant across plans and as plans change
    seats_item_id: common.seatsItemId,
    included_messages_item_id: common.includedMessagesItemId,
    user_message_item_id: common.userMessageItemId,

    pricing_unit: 'usermessages',
    cost_per_message: 0.1,  // Cost in USD per message sent. Can be moved to be plan-specific if necessary.

    // Config for all the plans
    plans: allPlansWithDevNamespace,

    // These max purchase amounts are in USD
    min_addon_purchase: common.min_addon_purchase,
    max_addon_purchase: common.max_addon_purchase,

    // Config for plan validation
    validation: common.validation_config[env] + {
      enabled: billingValidationEnabled,

      // Skip plan version validation in dev, as plan versions could be different for different dev environments
      ignore_plan_version: env == 'DEV' && billingValidationEnabled,
    },

    // Config for self-serve accounting
    accounting_config: common.accounting_config,

    // Default usage unit display name, e.g. "user messages", "credits"
    default_usage_unit_display_name: common.planDefaults(env).usage_unit_display_name,
  }
