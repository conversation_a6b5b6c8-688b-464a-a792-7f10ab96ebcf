package orb

import (
	"fmt"
	"testing"

	"github.com/orbcorp/orb-go"
	"github.com/stretchr/testify/assert"
)

// Test the selectDueNowInvoice utility function directly
// This tests the core business logic for invoice selection

// Helper function to create test invoices with specific line item price types
func createTestInvoice(id string, lineItemPriceTypes []string) orb.Invoice {
	lineItems := make([]orb.InvoiceLineItem, len(lineItemPriceTypes))
	for i, priceType := range lineItemPriceTypes {
		lineItems[i] = orb.InvoiceLineItem{
			Price: orb.Price{
				PriceType: orb.PricePriceType(priceType),
			},
		}
	}

	return orb.Invoice{
		ID:        id,
		LineItems: lineItems,
		// Add minimal required fields
		AmountDue: "1000",
		Subscription: orb.SubscriptionMinified{
			ID: "sub-id",
		},
		Customer: orb.CustomerMinified{
			ID: "customer-id",
		},
		Status: orb.InvoiceStatusPaid,
		AutoCollection: orb.InvoiceAutoCollection{
			NumAttempts: 0,
		},
		InvoiceSource: orb.InvoiceInvoiceSourceSubscription,
	}
}

func TestSelectDueNowInvoice(t *testing.T) {
	// Test case 1: Should select invoice with all fixed price line items
	t.Run("SelectsInvoiceWithAllFixedPriceLineItems", func(t *testing.T) {
		invoices := []orb.Invoice{
			createTestInvoice("invoice-1", []string{"usage_price", "fixed_price"}),
			createTestInvoice("invoice-2", []string{"fixed_price", "fixed_price"}),
		}

		selectedInvoice, err := selectDueNowInvoice(invoices)

		assert.NoError(t, err)
		assert.NotNil(t, selectedInvoice)
		assert.Equal(t, "invoice-2", selectedInvoice.ID) // Should select the one with all fixed prices
	})

	// Test case 2: Should fallback to second invoice when no invoice has all fixed prices
	t.Run("FallbackToSecondInvoice", func(t *testing.T) {
		invoices := []orb.Invoice{
			createTestInvoice("invoice-1", []string{"usage_price"}),
			createTestInvoice("invoice-2", []string{"usage_price"}),
			createTestInvoice("invoice-3", []string{"usage_price"}),
		}

		selectedInvoice, err := selectDueNowInvoice(invoices)

		assert.NoError(t, err)
		assert.NotNil(t, selectedInvoice)
		assert.Equal(t, "invoice-2", selectedInvoice.ID) // Should fallback to second invoice (index 1)
	})

	// Test case 3: Should fallback to first invoice when only one invoice exists
	t.Run("FallbackToFirstInvoiceWhenOnlyOne", func(t *testing.T) {
		invoices := []orb.Invoice{
			createTestInvoice("invoice-1", []string{"usage_price"}),
		}

		selectedInvoice, err := selectDueNowInvoice(invoices)

		assert.NoError(t, err)
		assert.NotNil(t, selectedInvoice)
		assert.Equal(t, "invoice-1", selectedInvoice.ID) // Should use the only available invoice
	})

	// Test case 4: Should return error when no invoices exist
	t.Run("ErrorWhenNoInvoices", func(t *testing.T) {
		invoices := []orb.Invoice{}

		selectedInvoice, err := selectDueNowInvoice(invoices)

		assert.Error(t, err)
		assert.Nil(t, selectedInvoice)
		assert.Contains(t, err.Error(), "no invoices found")
	})

	// Test case 5: Should skip invoices with no line items
	t.Run("SkipsInvoicesWithNoLineItems", func(t *testing.T) {
		invoices := []orb.Invoice{
			createTestInvoice("invoice-1", []string{}), // No line items
			createTestInvoice("invoice-2", []string{"fixed_price"}),
		}

		selectedInvoice, err := selectDueNowInvoice(invoices)

		assert.NoError(t, err)
		assert.NotNil(t, selectedInvoice)
		assert.Equal(t, "invoice-2", selectedInvoice.ID) // Should skip the empty one and select the fixed price one
	})

	// Test case 6: Should prefer first invoice with all fixed prices
	t.Run("PrefersFirstInvoiceWithAllFixedPrices", func(t *testing.T) {
		invoices := []orb.Invoice{
			createTestInvoice("invoice-1", []string{"usage_price"}),
			createTestInvoice("invoice-2", []string{"fixed_price", "fixed_price"}),
			createTestInvoice("invoice-3", []string{"fixed_price"}),
		}

		selectedInvoice, err := selectDueNowInvoice(invoices)

		assert.NoError(t, err)
		assert.NotNil(t, selectedInvoice)
		assert.Equal(t, "invoice-2", selectedInvoice.ID) // Should select the first one with all fixed prices
	})
}

// Helper function to create test credit notes
func createTestCreditNote(creditNoteNumber string, total string) orb.CreditNote {
	return orb.CreditNote{
		CreditNoteNumber: creditNoteNumber,
		Total:            total,
	}
}

func TestSumCreditNoteAmounts(t *testing.T) {
	// Test case 1: Should sum multiple credit note amounts correctly
	t.Run("SumsMultipleCreditNoteAmounts", func(t *testing.T) {
		creditNotes := []orb.CreditNote{
			createTestCreditNote("CN-001", "10.50"),
			createTestCreditNote("CN-002", "5.25"),
		}

		total := sumCreditNoteAmounts(creditNotes)

		assert.Equal(t, int64(1575), total) // 10.50 + 5.25 = 15.75 = 1575 cents
	})

	// Test case 2: Should handle empty credit note list
	t.Run("HandlesEmptyCreditNoteList", func(t *testing.T) {
		creditNotes := []orb.CreditNote{}

		total := sumCreditNoteAmounts(creditNotes)

		assert.Equal(t, int64(0), total)
	})

	// Test case 3: Should skip credit notes with empty credit note numbers
	t.Run("SkipsCreditNotesWithEmptyNumbers", func(t *testing.T) {
		creditNotes := []orb.CreditNote{
			createTestCreditNote("", "10.00"),      // Should be skipped
			createTestCreditNote("CN-001", "5.00"), // Should be included
		}

		total := sumCreditNoteAmounts(creditNotes)

		assert.Equal(t, int64(500), total) // Only 5.00 should be counted = 500 cents
	})

	// Test case 4: Should skip credit notes with invalid totals but continue processing
	t.Run("SkipsInvalidCreditNoteTotals", func(t *testing.T) {
		creditNotes := []orb.CreditNote{
			createTestCreditNote("CN-001", "invalid"), // Should be skipped
			createTestCreditNote("CN-002", "10.00"),   // Should be included
		}

		total := sumCreditNoteAmounts(creditNotes)

		assert.Equal(t, int64(1000), total) // Only 10.00 should be counted = 1000 cents
	})

	// Test case 5: Should handle decimal precision correctly
	t.Run("HandlesDecimalPrecisionCorrectly", func(t *testing.T) {
		creditNotes := []orb.CreditNote{
			createTestCreditNote("CN-001", "33.33"),
			createTestCreditNote("CN-002", "66.67"),
		}

		total := sumCreditNoteAmounts(creditNotes)

		assert.Equal(t, int64(10000), total) // 33.33 + 66.67 = 100.00 = 10000 cents
	})

	// Test case 6: Should handle zero amounts
	t.Run("HandlesZeroAmounts", func(t *testing.T) {
		creditNotes := []orb.CreditNote{
			createTestCreditNote("CN-001", "0.00"),
			createTestCreditNote("CN-002", "10.00"),
		}

		total := sumCreditNoteAmounts(creditNotes)

		assert.Equal(t, int64(1000), total) // 0.00 + 10.00 = 10.00 = 1000 cents
	})
}

func TestCalculateAmountDue(t *testing.T) {
	// Test case 1: Should calculate amount due with credit deductions
	t.Run("CalculatesAmountDueWithCreditDeductions", func(t *testing.T) {
		invoice := createTestInvoice("invoice-1", []string{"fixed_price"})
		invoice.AmountDue = "100.00"

		creditNotes := []orb.CreditNote{
			createTestCreditNote("CN-001", "10.50"),
			createTestCreditNote("CN-002", "5.25"),
		}

		result, err := calculateAmountDue(&invoice, creditNotes)

		assert.NoError(t, err)
		assert.Equal(t, "100.00", result.OriginalAmount)
		assert.Equal(t, "15.75", result.CreditAmount) // 10.50 + 5.25 = 15.75
		assert.Equal(t, "84.25", result.FinalAmount)  // 100.00 - 10.50 - 5.25 = 84.25
	})

	// Test case 2: Should handle zero credit notes
	t.Run("HandlesZeroCreditNotes", func(t *testing.T) {
		invoice := createTestInvoice("invoice-1", []string{"fixed_price"})
		invoice.AmountDue = "100.00"

		creditNotes := []orb.CreditNote{}

		result, err := calculateAmountDue(&invoice, creditNotes)

		assert.NoError(t, err)
		assert.Equal(t, "100.00", result.OriginalAmount)
		assert.Equal(t, "0.00", result.CreditAmount)  // No credits
		assert.Equal(t, "100.00", result.FinalAmount) // No deduction
	})

	// Test case 3: Should clamp negative amounts to zero
	t.Run("ClampsNegativeAmountsToZero", func(t *testing.T) {
		invoice := createTestInvoice("invoice-1", []string{"fixed_price"})
		invoice.AmountDue = "100.00"

		creditNotes := []orb.CreditNote{
			createTestCreditNote("CN-001", "150.00"),
		}

		result, err := calculateAmountDue(&invoice, creditNotes)

		assert.NoError(t, err)
		assert.Equal(t, "100.00", result.OriginalAmount)
		assert.Equal(t, "150.00", result.CreditAmount)
		assert.Equal(t, "0.00", result.FinalAmount) // Should be clamped to 0.00
	})

	// Test case 4: Should handle invalid invoice amount
	t.Run("ErrorsOnInvalidInvoiceAmount", func(t *testing.T) {
		invoice := createTestInvoice("invoice-1", []string{"fixed_price"})
		invoice.AmountDue = "invalid-amount"

		creditNotes := []orb.CreditNote{
			createTestCreditNote("CN-001", "10.00"),
		}

		result, err := calculateAmountDue(&invoice, creditNotes)

		assert.Error(t, err)
		assert.Nil(t, result)
		assert.Contains(t, err.Error(), "failed to parse invoice amount due")
		assert.Contains(t, err.Error(), "invoice-1")
	})

	// Test case 5: Should handle decimal precision correctly
	t.Run("HandlesDecimalPrecisionCorrectly", func(t *testing.T) {
		invoice := createTestInvoice("invoice-1", []string{"fixed_price"})
		invoice.AmountDue = "100.00"

		creditNotes := []orb.CreditNote{
			createTestCreditNote("CN-001", "33.33"),
			createTestCreditNote("CN-002", "66.67"),
		}

		result, err := calculateAmountDue(&invoice, creditNotes)

		assert.NoError(t, err)
		assert.Equal(t, "100.00", result.OriginalAmount)
		assert.Equal(t, "100.00", result.CreditAmount) // 33.33 + 66.67 = 100.00
		assert.Equal(t, "0.00", result.FinalAmount)    // 100.00 - 33.33 - 66.67 = 0.00
	})

	// Test case 6: Should skip invalid credit notes but still process invoice
	t.Run("SkipsInvalidCreditNotesButProcessesInvoice", func(t *testing.T) {
		invoice := createTestInvoice("invoice-1", []string{"fixed_price"})
		invoice.AmountDue = "100.00"

		creditNotes := []orb.CreditNote{
			createTestCreditNote("CN-001", "invalid"), // Should be skipped
			createTestCreditNote("CN-002", "10.00"),   // Should be included
		}

		result, err := calculateAmountDue(&invoice, creditNotes)

		assert.NoError(t, err)
		assert.Equal(t, "100.00", result.OriginalAmount)
		assert.Equal(t, "10.00", result.CreditAmount) // Only valid credit note counted
		assert.Equal(t, "90.00", result.FinalAmount)  // 100.00 - 10.00 = 90.00 (skipped the invalid one)
	})
}

func TestParseDollarsToCents(t *testing.T) {
	tests := []struct {
		input    string
		expected int64
		hasError bool
	}{
		{"12.34", 1234, false},
		{"0.00", 0, false},
		{"100.00", 10000, false},
		{"33.33", 3333, false},
		{"66.67", 6667, false},
		{"-12.34", -1234, false}, // Negative amount
		{"0.01", 1, false},       // Minimum positive amount
		// Error cases - not in XX.XX format
		{"invalid", 0, true},
		{"", 0, true},
		{"12.5", 0, true},   // Single decimal place
		{"12", 0, true},     // No decimal point
		{"12.345", 0, true}, // Too many decimal places
		{".50", 0, true},    // No dollars part
		{"100.", 0, true},   // No cents part
	}

	for _, test := range tests {
		t.Run(fmt.Sprintf("Input_%s", test.input), func(t *testing.T) {
			result, err := parseDollarsToCents(test.input)
			if test.hasError {
				assert.Error(t, err)
			} else {
				assert.NoError(t, err)
				assert.Equal(t, test.expected, result)
			}
		})
	}
}

func TestFormatCentsToDollars(t *testing.T) {
	tests := []struct {
		input    int64
		expected string
	}{
		{1234, "12.34"},
		{0, "0.00"},
		{10000, "100.00"},
		{1, "0.01"},
		{99, "0.99"},
		// Additional test cases for pure integer implementation
		{-1234, "-12.34"}, // Negative amount
		{1200, "12.00"},   // Even dollars
		{5, "0.05"},       // Single digit cents
	}

	for _, test := range tests {
		t.Run(fmt.Sprintf("Input_%d", test.input), func(t *testing.T) {
			result := formatCentsToDollars(test.input)
			assert.Equal(t, test.expected, result)
		})
	}
}
