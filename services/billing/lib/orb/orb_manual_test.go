package orb

import (
	"context"
	"fmt"
	"os"
	"testing"
	"time"

	featureflags "github.com/augmentcode/augment/base/feature_flags"
)

// Plan IDs for Orb
const (
	FREE_PLAN_ID      = "orb_free_plan"
	TRIAL_PLAN_ID     = "orb_trial_plan"
	DEVELOPER_PLAN_ID = "orb_developer_plan"

	IncludedMessagesItemID = "kcwmTLYJpnTb33Nq"
	SeatsItemID            = "fPTPFSjvP7fjF3PZ"
)

// TestOrbClientManual is a manual test for the Orb client
// To Run this test: bazel test //services/billing/lib/orb:orb_manual_test --test_env=APIKEYORB=your_api_key --test_output=all //pragma:allowlist secret
func TestOrbClientManual(t *testing.T) {
	client := SetupOrbClientTest(t)

	customerId, err := CreateTestCustomer(client)
	if err != nil {
		t.Fatalf("Failed to create customer: %v", err)
	}
	t.Logf("Successfully created customer with orbId: %s", customerId)

	time.Sleep(1 * time.Second) // Avoid Rate Limit

	subscriptionId, err := CreateTestSubscription(client, customerId)
	if err != nil {
		t.Fatalf("Failed to create subscription: %v", err)
	}
	t.Logf("Successfully created subscription")

	time.Sleep(1 * time.Second) // Avoid Rate Limit

	err = SwitchCustomerPlanTypeTest(client, customerId, subscriptionId, DEVELOPER_PLAN_ID)
	if err != nil {
		t.Fatalf("Failed to switch customer plan type: %v", err)
	}
	t.Logf("Successfully switched customer plan type")

	time.Sleep(1 * time.Second) // Avoid Rate Limit

	err = AddAlertsForCustomerTest(client, customerId, "USD")
	if err != nil {
		t.Fatalf("Failed to add alerts for customer: %v", err)
	}
	t.Logf("Successfully added alerts for customer")

	time.Sleep(1 * time.Second) // Avoid Rate Limit

	err = ListAllPlansTest(client)
	if err != nil {
		t.Fatalf("Failed to list all plans: %v", err)
	}
	t.Logf("Successfully listed all plans")
}

// SetupOrbClientTest sets up the Orb client for testing
// It checks for the API key in the environment and creates a new client
func SetupOrbClientTest(t *testing.T) OrbClient {
	// Skip in normal test runs
	if testing.Short() {
		t.Skip("Skipping manual test in short mode")
	}

	// Get API key from environment variable
	apiKey := os.Getenv("APIKEYORB")

	if apiKey == "" {
		t.Fatal("APIKEYORB environment variable must be set")
	}

	// Create a new Orb client
	client := NewOrbClient(apiKey, featureflags.NewLocalFeatureFlagHandler())
	if client == nil {
		t.Fatal("Failed to create Orb client")
	}

	return client
}

func CreateTestCustomer(client OrbClient) (string, error) {
	// Create a unique email, name, stripeId, externalCustomerId for testing
	timestamp := time.Now().Unix()
	email := fmt.Sprintf("<EMAIL>", timestamp)
	name := fmt.Sprintf("Test User %d", timestamp)
	stripeId := fmt.Sprintf("test-stripe-id-%d", timestamp)
	externalCustomerId := fmt.Sprintf("test-augment-id-%d", timestamp)

	// Create a test customer
	customer := OrbCustomer{
		Email:              email,
		Name:               name,
		StripeID:           stripeId,
		Metadata:           map[string]string{},
		Timezone:           "America/Los_Angeles",
		ExternalCustomerID: &externalCustomerId,
		AccountingInfo: &AccountingProviderInfo{
			ProviderType: "netsuite",
			ProviderID:   "516",
		},
	}

	// Create the customer
	ctx := context.Background()
	customerId, err := client.CreateCustomer(ctx, customer, false, nil)
	return customerId, err
}

func CreateTestSubscription(client OrbClient, customerOrbID string) (string, error) {
	subscription := OrbSubscription{
		CustomerOrbID:  customerOrbID,
		ExternalPlanID: TRIAL_PLAN_ID,
	}

	// Create the subscription
	ctx := context.Background()
	return client.CreateSubscription(ctx, subscription, nil, false)
}

func SwitchCustomerPlanTypeTest(client OrbClient, customerOrbID string, subscriptionID string, newPlanId string) error {
	// Switch the subscription plan type to NewPlanId
	ctx := context.Background()

	// Now get the subscription details
	subscription, err := client.GetUserSubscription(ctx, subscriptionID, nil)
	if err != nil {
		return fmt.Errorf("failed to get user subscription: %w", err)
	}

	twoWeeks := time.Now().Add(14 * 24 * time.Hour)
	planChange := OrbPlanChange{
		CustomerOrbID:  customerOrbID,
		SubscriptionID: subscription.OrbSubscriptionID,
		NewPlanID:      newPlanId,
		PlanChangeType: PlanChangeSpecificDate,
		PlanChangeDate: &twoWeeks,
		PriceOverrides: []OrbPriceOverrides{
			{
				PriceID:  "EwrTX7Mtx6gmdDYx", // hardcoded for now
				Quantity: 1.0,                // 1 seat
			},
			{
				PriceID:  "Yu8hCdzmEicQ3Rnd", // hardcoded for now
				Quantity: 550.0,              // 550 credits * 1 seat
			},
		},
	}
	_, err = client.SetCustomerPlanType(ctx, planChange, nil, false)
	return err
}

func AddAlertsForCustomerTest(client OrbClient, customerOrbID string, currency string) error {
	// Add alerts
	ctx := context.Background()
	err := client.AddAlertsForCustomer(ctx, customerOrbID, currency)
	return err
}

func ListAllPlansTest(client OrbClient) error {
	// List all plans
	ctx := context.Background()
	plans, err := client.ListAllPlans(ctx)
	if err != nil {
		return fmt.Errorf("failed to list all plans: %w", err)
	}

	fmt.Printf("Found %d plans:\n", len(plans))
	for _, plan := range plans {
		fmt.Printf("- Plan ID: %s, External ID: %s, Name: %s, Status: %s\n",
			plan.ID, plan.ExternalPlanID, plan.Name, plan.Status)
	}

	return nil
}
