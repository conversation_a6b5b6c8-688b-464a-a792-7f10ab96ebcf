load("//tools/bzl:go.bzl", "go_library", "go_test")

go_library(
    name = "stripe_lib",
    srcs = [
        "stripe.go",
    ],
    importpath = "github.com/augmentcode/augment/services/billing/lib/stripe",
    visibility = [
        "//services/auth:__subpackages__",
        "//services/billing:__subpackages__",
    ],
    deps = [
        "@com_github_rs_zerolog//:zerolog",
        "@com_github_rs_zerolog//log",
        "@com_github_stripe_stripe_go_v80//:go_default_library",
        "@com_github_stripe_stripe_go_v80//charge",
        "@com_github_stripe_stripe_go_v80//checkout/session",
        "@com_github_stripe_stripe_go_v80//customer",
        "@com_github_stripe_stripe_go_v80//paymentintent",
        "@com_github_stripe_stripe_go_v80//paymentmethod",
        "@com_github_stripe_stripe_go_v80//setupintent",
        "@com_github_stripe_stripe_go_v80//subscription",
        "@com_github_stripe_stripe_go_v80//taxid",
    ],
)

go_test(
    name = "stripe_test",
    srcs = [
        "stripe_test.go",
    ],
    embed = [":stripe_lib"],
    deps = [
        "@com_github_stretchr_testify//assert",
        "@com_github_stretchr_testify//require",
    ],
)
