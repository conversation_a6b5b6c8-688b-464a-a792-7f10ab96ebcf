package stripe

import (
	"fmt"
	"strconv"
	"time"

	"github.com/rs/zerolog/log"
	"github.com/stripe/stripe-go/v80"
	"github.com/stripe/stripe-go/v80/charge"
	"github.com/stripe/stripe-go/v80/checkout/session"
	"github.com/stripe/stripe-go/v80/customer"
	"github.com/stripe/stripe-go/v80/paymentintent"
	"github.com/stripe/stripe-go/v80/paymentmethod"
	"github.com/stripe/stripe-go/v80/setupintent"
	"github.com/stripe/stripe-go/v80/subscription"
	"github.com/stripe/stripe-go/v80/taxid"
)

// StripeClient defines the interface for Stripe operations
type StripeClient interface {
	CancelSubscription(subscriptionID string) error
	CreateCheckoutSessionForPlanChange(customerID string, amountDue string, successURL string, cancelURL string, planName string, userID string, eventType string, expiresAt *time.Time) (*stripe.CheckoutSession, error)
	CreateCustomer(params *stripe.CustomerParams) (*stripe.Customer, error)
	CreateSetupIntent(customerID string) (*stripe.SetupIntent, error)
	CreateSubscription(customerID, userID, priceID string, trialDays *int64, idempotencyKey *string) error
	DeleteTaxID(taxID string, customerID string) error
	ExpireCheckoutSession(checkoutSessionID string) error
	FindCustomerByEmail(email string) (*stripe.Customer, error)
	GetCheckoutSession(checkoutSessionID string) (*stripe.CheckoutSession, error)
	GetCustomer(customerID string) (*stripe.Customer, error)
	GetCustomerHasSuccessfulPayment(customerID string) (bool, error)
	GetPaymentIntent(paymentIntentID string) (*stripe.PaymentIntent, error)
	GetPaymentMethod(paymentMethodID string) (*stripe.PaymentMethod, error)
	GetPaymentMethodsForCustomer(customerID string) ([]*stripe.PaymentMethod, error)
	GetSubscription(subscriptionID string) (*stripe.Subscription, error)
	HasPaymentMethod(customerID string) (bool, error)
	ListCheckoutSessions(customerID string) ([]*stripe.CheckoutSession, error)
	ListSubscriptions(customerID string, includeCanceled ...bool) ([]*stripe.Subscription, error)
	RemovePaymentMethodsFromCustomer(customerID string) error
	UpdateSubscriptionSeats(subscriptionID string, seats int) error
}

// DefaultStripeClient implements the StripeClient interface using the Stripe API
type DefaultStripeClient struct{}

func (c *DefaultStripeClient) ListSubscriptions(customerID string, includeCanceled ...bool) ([]*stripe.Subscription, error) {
	params := &stripe.SubscriptionListParams{
		Customer: stripe.String(customerID),
	}

	// Check if includeCanceled parameter was provided and is true
	if len(includeCanceled) > 0 && includeCanceled[0] {
		params.Status = stripe.String("all")
	}

	iter := subscription.List(params)

	var subs []*stripe.Subscription
	for iter.Next() {
		subs = append(subs, iter.Subscription())
	}
	return subs, iter.Err()
}

func (c *DefaultStripeClient) CancelSubscription(subscriptionID string) error {
	_, err := subscription.Cancel(subscriptionID, nil)
	return err
}

func (c *DefaultStripeClient) CreateSubscription(
	customerID, userID, priceID string, trialDays *int64, idempotencyKey *string,
) error {
	params := &stripe.SubscriptionParams{
		Customer: stripe.String(customerID),
		Items: []*stripe.SubscriptionItemsParams{
			{
				Price: stripe.String(priceID),
			},
		},
		Metadata: map[string]string{
			"augment_user_id": userID,
		},
	}
	if idempotencyKey != nil {
		params.IdempotencyKey = idempotencyKey
	}

	if trialDays != nil {
		params.TrialPeriodDays = trialDays
		params.TrialSettings = &stripe.SubscriptionTrialSettingsParams{
			EndBehavior: &stripe.SubscriptionTrialSettingsEndBehaviorParams{
				MissingPaymentMethod: stripe.String(string(stripe.SubscriptionTrialSettingsEndBehaviorMissingPaymentMethodCancel)),
			},
		}
	}

	_, err := subscription.New(params)
	return err
}

func (c *DefaultStripeClient) FindCustomerByEmail(email string) (*stripe.Customer, error) {
	params := &stripe.CustomerListParams{
		Email: stripe.String(email),
	}
	iter := customer.List(params)

	if !iter.Next() {
		return nil, nil
	}
	return iter.Customer(), iter.Err()
}

// CreateCustomer creates a new Stripe customer
func (c *DefaultStripeClient) CreateCustomer(params *stripe.CustomerParams) (*stripe.Customer, error) {
	return customer.New(params)
}

// CreateSetupIntent creates a new SetupIntent for a customer
func (c *DefaultStripeClient) CreateSetupIntent(customerID string) (*stripe.SetupIntent, error) {
	params := &stripe.SetupIntentParams{
		Customer: stripe.String(customerID),
		PaymentMethodTypes: []*string{
			stripe.String("card"),
		},
		Usage: stripe.String(string(stripe.SetupIntentUsageOffSession)),
	}

	return setupintent.New(params)
}

func (c *DefaultStripeClient) UpdateSubscriptionSeats(subscriptionID string, seats int) error {
	// First, get the subscription to find the subscription item ID
	sub, err := subscription.Get(subscriptionID, nil)
	if err != nil {
		return fmt.Errorf("failed to get subscription: %w", err)
	}

	// Make sure there's at least one item
	if len(sub.Items.Data) == 0 {
		return fmt.Errorf("subscription has no items")
	}

	if len(sub.Items.Data) > 1 {
		log.Warn().
			Str("subscription_id", subscriptionID).
			Int("item_count", len(sub.Items.Data)).
			Msg("Subscription has more than one item, only the first will be updated")
	}

	// Create the subscription update params
	params := &stripe.SubscriptionParams{
		Items: []*stripe.SubscriptionItemsParams{
			{
				ID:       stripe.String(sub.Items.Data[0].ID),
				Quantity: stripe.Int64(int64(seats)),
			},
		},
	}

	// Update the subscription
	_, err = subscription.Update(subscriptionID, params)
	if err != nil {
		return fmt.Errorf("failed to update subscription: %w", err)
	}

	return nil
}

func (c *DefaultStripeClient) GetSubscription(subscriptionID string) (*stripe.Subscription, error) {
	return subscription.Get(subscriptionID, nil)
}

// HasPaymentMethod checks if a customer has a valid payment method
func (c *DefaultStripeClient) HasPaymentMethod(customerID string) (bool, error) {
	params := &stripe.CustomerListPaymentMethodsParams{
		Customer: stripe.String(customerID),
	}

	// Only need to check if there's at least one payment method
	params.Limit = stripe.Int64(1)
	i := customer.ListPaymentMethods(params)

	// Check if there's at least one payment method
	return i.Next(), i.Err()
}

func (c *DefaultStripeClient) ListCheckoutSessions(customerID string) ([]*stripe.CheckoutSession, error) {
	params := &stripe.CheckoutSessionListParams{
		Customer: stripe.String(customerID),
		Status:   stripe.String("open"),
	}

	iter := session.List(params)

	var sessions []*stripe.CheckoutSession
	for iter.Next() {
		sessions = append(sessions, iter.CheckoutSession())
	}
	return sessions, iter.Err()
}

func (c *DefaultStripeClient) GetCheckoutSession(checkoutSessionID string) (*stripe.CheckoutSession, error) {
	return session.Get(checkoutSessionID, nil)
}

func (c *DefaultStripeClient) ExpireCheckoutSession(checkoutSessionID string) error {
	_, err := session.Expire(checkoutSessionID, nil)
	return err
}

func (c *DefaultStripeClient) CreateCheckoutSessionForPlanChange(customerID string, amountDue string, successURL string, cancelURL string, planName string, userID string, eventType string, expiresAt *time.Time) (*stripe.CheckoutSession, error) {
	// Parse amount due to cents (Stripe expects amounts in cents)
	amountFloat, err := strconv.ParseFloat(amountDue, 64)
	if err != nil {
		return nil, fmt.Errorf("invalid amount due: %w", err)
	}
	amountCents := int64(amountFloat * 100)

	// Use the plan name if provided, otherwise fall back to generic "Plan Change"
	productName := "Plan Change"
	if planName != "" {
		productName = planName
	}

	params := &stripe.CheckoutSessionParams{
		Mode:     stripe.String(string(stripe.CheckoutSessionModePayment)),
		Customer: stripe.String(customerID),
		LineItems: []*stripe.CheckoutSessionLineItemParams{
			{
				PriceData: &stripe.CheckoutSessionLineItemPriceDataParams{
					Currency: stripe.String("usd"),
					ProductData: &stripe.CheckoutSessionLineItemPriceDataProductDataParams{
						Name: stripe.String(productName),
					},
					UnitAmount: stripe.Int64(amountCents),
				},
				Quantity: stripe.Int64(1),
			},
		},
		SuccessURL: stripe.String(successURL),
		CancelURL:  stripe.String(cancelURL),
		PaymentMethodTypes: stripe.StringSlice([]string{
			"card",
			"amazon_pay",
		}),
		Metadata: map[string]string{
			"augment_user_id": userID,
			"event_type":      eventType,
		},
		PaymentIntentData: &stripe.CheckoutSessionPaymentIntentDataParams{
			SetupFutureUsage: stripe.String(string(stripe.PaymentIntentSetupFutureUsageOffSession)),
		},
		BillingAddressCollection: stripe.String("required"),
		CustomerUpdate: &stripe.CheckoutSessionCustomerUpdateParams{
			Address: stripe.String("auto"),
		},
	}

	// Set expiration time if provided
	if expiresAt != nil {
		params.ExpiresAt = stripe.Int64(expiresAt.Unix())
	}

	return session.New(params)
}

func (c *DefaultStripeClient) GetPaymentMethod(paymentMethodID string) (*stripe.PaymentMethod, error) {
	return paymentmethod.Get(paymentMethodID, nil)
}

func (c *DefaultStripeClient) GetPaymentMethodsForCustomer(customerID string) ([]*stripe.PaymentMethod, error) {
	params := &stripe.CustomerListPaymentMethodsParams{
		Customer: stripe.String(customerID),
	}
	iter := customer.ListPaymentMethods(params)

	var paymentMethods []*stripe.PaymentMethod
	for iter.Next() {
		pm := iter.PaymentMethod()
		paymentMethods = append(paymentMethods, pm)
	}
	return paymentMethods, iter.Err()
}

func (c *DefaultStripeClient) GetCustomerHasSuccessfulPayment(customerID string) (bool, error) {
	// Get the customer's payments
	params := stripe.ChargeListParams{
		Customer: stripe.String(customerID),
		ListParams: stripe.ListParams{
			Limit: stripe.Int64(100),
		},
	}
	iter := charge.List(&params)

	// Check if there's at least one successful payment
	for iter.Next() {
		charge := iter.Charge()
		if charge.Paid && charge.Status == "succeeded" && charge.Amount > 0 {
			return true, nil
		}
	}
	if err := iter.Err(); err != nil {
		return false, fmt.Errorf("error iterating charges: %w", err)
	}

	return false, nil
}

func (c *DefaultStripeClient) RemovePaymentMethodsFromCustomer(customerID string) error {
	// Get the customer's payment methods
	params := &stripe.CustomerListPaymentMethodsParams{
		Customer: stripe.String(customerID),
	}
	iter := customer.ListPaymentMethods(params)

	// Detach each payment method
	for iter.Next() {
		pm := iter.PaymentMethod()
		_, err := paymentmethod.Detach(pm.ID, nil)
		if err != nil {
			return fmt.Errorf("error detaching payment method: %w", err)
		}
	}
	if err := iter.Err(); err != nil {
		return fmt.Errorf("error iterating payment methods: %w", err)
	}

	return nil
}

func (c *DefaultStripeClient) GetCustomer(customerID string) (*stripe.Customer, error) {
	params := &stripe.CustomerParams{}
	params.AddExpand("tax_ids")
	return customer.Get(customerID, params)
}

func (c *DefaultStripeClient) GetPaymentIntent(paymentIntentID string) (*stripe.PaymentIntent, error) {
	params := &stripe.PaymentIntentParams{}
	params.AddExpand("latest_charge")
	return paymentintent.Get(paymentIntentID, params)
}

func (c *DefaultStripeClient) DeleteTaxID(taxID string, customerID string) error {
	_, err := taxid.Del(taxID, &stripe.TaxIDParams{Customer: stripe.String(customerID)})
	return err
}

// MockStripeClient implements a mock for testing with real state
type MockStripeClient struct {
	// Internal state
	Subscriptions                map[string]*stripe.Subscription    // Map of subscription ID to subscription
	CustomerSubs                 map[string]map[string]struct{}     // Map of customer ID to set of subscription IDs
	SubOwnership                 map[string]string                  // Map of subscription ID to customer ID
	Customers                    map[string]*stripe.Customer        // Map of email to customer
	SubscriptionSeats            map[string][]int                   // Map of subscription ID to list of seat updates
	SetupIntents                 map[string]*stripe.SetupIntent     // Map of setup intent ID to setup intent
	CustomerPaymentMethods       map[string][]*stripe.PaymentMethod // Map of customer ID to payment methods
	CustomerHasSuccessfulPayment map[string]bool                    // Map of customer ID to successful payment status
	PaymentIntents               map[string]*stripe.PaymentIntent   // Map of payment intent ID to payment intent
}

// NewMockStripeClient creates a new mock client
func NewMockStripeClient() *MockStripeClient {
	return &MockStripeClient{
		Subscriptions:                make(map[string]*stripe.Subscription),
		CustomerSubs:                 make(map[string]map[string]struct{}),
		SubOwnership:                 make(map[string]string),
		Customers:                    make(map[string]*stripe.Customer),
		SubscriptionSeats:            make(map[string][]int),
		SetupIntents:                 make(map[string]*stripe.SetupIntent),
		CustomerPaymentMethods:       make(map[string][]*stripe.PaymentMethod),
		CustomerHasSuccessfulPayment: make(map[string]bool),
		PaymentIntents:               make(map[string]*stripe.PaymentIntent),
	}
}

// AddSubscription adds a subscription to the mock's state
func (m *MockStripeClient) AddSubscription(customerID string, userID string, subID string, priceID string) {
	sub := &stripe.Subscription{
		ID: subID,
		Items: &stripe.SubscriptionItemList{
			Data: []*stripe.SubscriptionItem{
				{
					Price: &stripe.Price{
						ID: priceID,
					},
				},
			},
		},
		Metadata: map[string]string{
			"augment_user_id": userID,
		},
		Customer: &stripe.Customer{
			ID: customerID,
		},
	}

	m.Subscriptions[subID] = sub
	m.SubOwnership[subID] = customerID

	if _, exists := m.CustomerSubs[customerID]; !exists {
		m.CustomerSubs[customerID] = make(map[string]struct{})
	}
	m.CustomerSubs[customerID][subID] = struct{}{}
}

// AddDetailedSubscription adds a subscription with detailed fields to the mock's state
func (m *MockStripeClient) AddDetailedSubscription(subscription *stripe.Subscription) {
	if subscription == nil {
		return
	}

	// Store the subscription
	m.Subscriptions[subscription.ID] = subscription

	// Set up ownership mapping if customer is provided
	if subscription.Customer != nil {
		customerID := subscription.Customer.ID
		m.SubOwnership[subscription.ID] = customerID

		// Add to customer's subscriptions
		if _, exists := m.CustomerSubs[customerID]; !exists {
			m.CustomerSubs[customerID] = make(map[string]struct{})
		}
		m.CustomerSubs[customerID][subscription.ID] = struct{}{}
	}
}

// ListSubscriptions returns a list of subscriptions for a customer
//
// Ignores the includeCanceled parameter
func (m *MockStripeClient) ListSubscriptions(customerID string, includeCanceled ...bool) ([]*stripe.Subscription, error) {
	var subs []*stripe.Subscription

	// Get subscription IDs for this customer
	subIDs, exists := m.CustomerSubs[customerID]
	if !exists {
		return subs, nil
	}

	// Get the actual subscription objects
	for subID := range subIDs {
		if sub, exists := m.Subscriptions[subID]; exists {
			subs = append(subs, sub)
		}
	}

	return subs, nil
}

func (m *MockStripeClient) GetSubscription(subscriptionID string) (*stripe.Subscription, error) {
	sub, exists := m.Subscriptions[subscriptionID]
	if !exists {
		return nil, fmt.Errorf("subscription not found")
	}
	return sub, nil
}

// CancelSubscription cancels a subscription
func (m *MockStripeClient) CancelSubscription(subscriptionID string) error {
	// Remove from subscriptions map
	delete(m.Subscriptions, subscriptionID)

	// Get the customer ID directly from the ownership map
	if customerID, exists := m.SubOwnership[subscriptionID]; exists {
		// Remove the subscription from the customer's set
		if subSet, ok := m.CustomerSubs[customerID]; ok {
			delete(subSet, subscriptionID)
		}
		// Remove from ownership map
		delete(m.SubOwnership, subscriptionID)
	}

	return nil
}

// CreateSubscription creates a new subscription
func (m *MockStripeClient) CreateSubscription(
	customerID, userID, priceID string, trialDays *int64, idempotencyKey *string,
) error {
	// Generate a new subscription ID
	subID := fmt.Sprintf("sub_new_%d", len(m.Subscriptions)+1)

	// Add the subscription to our state
	m.AddSubscription(customerID, userID, subID, priceID)

	return nil
}

// GetSubscriptionsForCustomer returns all subscriptions for a customer
func (m *MockStripeClient) GetSubscriptionsForCustomer(customerID string) []*stripe.Subscription {
	subs, _ := m.ListSubscriptions(customerID)
	return subs
}

// FindCustomerByEmail finds a customer by email
func (m *MockStripeClient) FindCustomerByEmail(email string) (*stripe.Customer, error) {
	customer, exists := m.Customers[email]
	if !exists {
		return nil, nil
	}
	return customer, nil
}

// AddCustomer adds a customer to the mock's state
func (m *MockStripeClient) AddCustomer(email string, stripeCustomerID string, augmentCustomerID string) {
	m.Customers[email] = &stripe.Customer{
		ID:    stripeCustomerID,
		Email: email,
		Metadata: map[string]string{
			"augment_user_id": augmentCustomerID,
		},
	}
}

func (m *MockStripeClient) CreateCustomer(params *stripe.CustomerParams) (*stripe.Customer, error) {
	// Generate a new customer ID
	customerID := fmt.Sprintf("cus_new_%d", len(m.Customers)+1)

	// Add the customer to our state
	m.AddCustomer(*params.Email, customerID, "")

	return m.Customers[*params.Email], nil
}

// CreateSetupIntent creates a new SetupIntent for a customer
func (m *MockStripeClient) CreateSetupIntent(customerID string) (*stripe.SetupIntent, error) {
	// Generate a new setup intent ID
	setupIntentID := fmt.Sprintf("seti_new_%d", len(m.SetupIntents)+1)

	// Create a new setup intent
	setupIntent := &stripe.SetupIntent{
		ID: setupIntentID,
		Customer: &stripe.Customer{
			ID: customerID,
		},
		Status: stripe.SetupIntentStatusRequiresPaymentMethod,
		Usage:  stripe.SetupIntentUsageOffSession,
	}

	// Add the setup intent to our state
	m.SetupIntents[setupIntentID] = setupIntent

	return setupIntent, nil
}

// UpdateSubscriptionSeats updates the seat count for a subscription
func (m *MockStripeClient) UpdateSubscriptionSeats(subscriptionID string, seats int) error {
	// Check if the subscription exists
	sub, exists := m.Subscriptions[subscriptionID]
	if !exists {
		return fmt.Errorf("subscription not found")
	}

	// Make sure there's at least one item
	if len(sub.Items.Data) == 0 {
		return fmt.Errorf("subscription has no items")
	}

	// Track the seat update
	if _, exists := m.SubscriptionSeats[subscriptionID]; !exists {
		m.SubscriptionSeats[subscriptionID] = []int{}
	}
	m.SubscriptionSeats[subscriptionID] = append(m.SubscriptionSeats[subscriptionID], seats)

	// Note: We don't actually update the subscription in our state
	// because that's the webhook's job. We just track the call.

	return nil
}

// GetSubscriptionUpdates returns the list of seat updates for a subscription
func (m *MockStripeClient) GetSubscriptionUpdates(subscriptionID string) []int {
	if updates, exists := m.SubscriptionSeats[subscriptionID]; exists {
		return updates
	}
	return []int{}
}

// HasPaymentMethod checks if a customer has a valid payment method
func (m *MockStripeClient) HasPaymentMethod(customerID string) (bool, error) {
	// Check if we've explicitly set payment method status for this customer
	if paymentMethods, exists := m.CustomerPaymentMethods[customerID]; exists {
		return len(paymentMethods) > 0, nil
	}

	// Default fallback behavior (can be changed for specific tests)
	return true, nil
}

// SetCustomerHasPaymentMethod allows tests to configure whether a customer has payment methods
func (m *MockStripeClient) SetCustomerHasPaymentMethod(customerID string, hasPayment bool) {
	if m.CustomerPaymentMethods == nil {
		m.CustomerPaymentMethods = make(map[string][]*stripe.PaymentMethod)
	}
	if hasPayment {
		m.CustomerPaymentMethods[customerID] = append(m.CustomerPaymentMethods[customerID], &stripe.PaymentMethod{})
	} else {
		m.CustomerPaymentMethods[customerID] = []*stripe.PaymentMethod{}
	}
}

func (m *MockStripeClient) ListCheckoutSessions(customerID string) ([]*stripe.CheckoutSession, error) {
	return []*stripe.CheckoutSession{}, nil
}

func (m *MockStripeClient) GetCheckoutSession(checkoutSessionID string) (*stripe.CheckoutSession, error) {
	// Return a mock completed session by default
	return &stripe.CheckoutSession{
		ID:            checkoutSessionID,
		Status:        "complete",
		PaymentStatus: "paid", // Add default payment status
		Customer:      &stripe.Customer{ID: "cus_test_customer"},
		AmountTotal:   0, // Default to 0 for testing
	}, nil
}

func (m *MockStripeClient) ExpireCheckoutSession(checkoutSessionID string) error {
	return nil
}

func (m *MockStripeClient) CreateCheckoutSessionForPlanChange(customerID string, amountDue string, successURL string, cancelURL string, planName string, userID string, eventType string, expiresAt *time.Time) (*stripe.CheckoutSession, error) {
	return &stripe.CheckoutSession{
		ID:  "cs_test_" + customerID,
		URL: "https://checkout.stripe.com/pay/cs_test_" + customerID,
	}, nil
}

func (m *MockStripeClient) GetPaymentMethod(paymentMethodID string) (*stripe.PaymentMethod, error) {
	return &stripe.PaymentMethod{
		ID: paymentMethodID,
		BillingDetails: &stripe.PaymentMethodBillingDetails{
			Name: "Test User",
			Address: &stripe.Address{
				Line1:      "123 Test St",
				City:       "Test City",
				State:      "Test State",
				PostalCode: "12345",
				Country:    "US",
			},
		},
	}, nil
}

func (m *MockStripeClient) GetPaymentMethodsForCustomer(customerID string) ([]*stripe.PaymentMethod, error) {
	if paymentMethods, exists := m.CustomerPaymentMethods[customerID]; exists && paymentMethods != nil {
		return paymentMethods, nil
	}
	return []*stripe.PaymentMethod{}, nil
}

func (m *MockStripeClient) GetCustomerHasSuccessfulPayment(customerID string) (bool, error) {
	// Check if we've explicitly set successful payments for this customer
	if hasSuccessfulPayment, exists := m.CustomerHasSuccessfulPayment[customerID]; exists {
		return hasSuccessfulPayment, nil
	}

	// Default fallback behavior -- has a successful payment
	return true, nil
}

// SetCustomerHasSuccessfulPayment allows tests to configure whether a customer has a successful payment in the past
func (m *MockStripeClient) SetCustomerHasSuccessfulPayment(customerID string, hasPayment bool) {
	if m.CustomerHasSuccessfulPayment == nil {
		m.CustomerHasSuccessfulPayment = make(map[string]bool)
	}
	m.CustomerHasSuccessfulPayment[customerID] = hasPayment
}

func (m *MockStripeClient) RemovePaymentMethodsFromCustomer(customerID string) error {
	m.CustomerPaymentMethods[customerID] = []*stripe.PaymentMethod{}
	return nil
}

// Get the customer by Customer ID
func (m *MockStripeClient) GetCustomer(customerID string) (*stripe.Customer, error) {
	for _, customer := range m.Customers {
		if customer.ID == customerID {
			return customer, nil
		}
	}
	return nil, nil
}

// Sets the address for a customer -- used for test setup
func (m *MockStripeClient) SetCustomerAddress(customerID string, address *stripe.Address) {
	for _, customer := range m.Customers {
		if customer.ID == customerID {
			customer.Address = address
			return
		}
	}
}

func (m *MockStripeClient) GetPaymentIntent(paymentIntentID string) (*stripe.PaymentIntent, error) {
	paymentIntent, exists := m.PaymentIntents[paymentIntentID]
	if !exists {
		return nil, fmt.Errorf("payment intent not found")
	}
	return paymentIntent, nil
}

// Ability to add payment intents for testing
func (m *MockStripeClient) AddPaymentIntent(paymentIntentID string, paymentIntent *stripe.PaymentIntent) {
	m.PaymentIntents[paymentIntentID] = paymentIntent
}

// Sets the name for a customer -- used for test setup
func (m *MockStripeClient) SetCustomerName(customerID string, name string) {
	for _, customer := range m.Customers {
		if customer.ID == customerID {
			customer.Name = name
			return
		}
	}
}

// Sets Tax IDs for a customer -- used for test setup
func (m *MockStripeClient) SetCustomerTaxIDs(customerID string, taxIDs []*stripe.TaxID) {
	for _, customer := range m.Customers {
		if customer.ID == customerID {
			customer.TaxIDs = &stripe.TaxIDList{Data: taxIDs}
			return
		}
	}
}

func (m *MockStripeClient) DeleteTaxID(taxID string, customerID string) error {
	for _, customer := range m.Customers {
		if customer.ID == customerID {
			if customer.TaxIDs != nil {
				newTaxIds := make([]*stripe.TaxID, 0, len(customer.TaxIDs.Data)-1)
				for _, id := range customer.TaxIDs.Data {
					if id.ID != taxID {
						newTaxIds = append(newTaxIds, id)
					}
				}
				customer.TaxIDs = &stripe.TaxIDList{Data: newTaxIds}
			}
			return nil
		}
	}
	return nil
}
