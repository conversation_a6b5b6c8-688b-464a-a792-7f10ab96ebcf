package main

import (
	"testing"
	"time"

	orb_config "github.com/augmentcode/augment/services/billing/lib/orb/config"
	orb_go "github.com/orbcorp/orb-go"
)

// Test utilities and helper functions
// createTestPlan creates a test Plan with default values that can be overridden
func createTestPlan(id string, overrides map[string]interface{}) orb_config.PlanConfig {
	plan := orb_config.PlanConfig{
		ID:    id,
		State: orb_config.PlanStateActive,
		DisplayInfo: orb_config.PlanDisplayConfig{
			Color:                "blue",
			Icon:                 "star",
			SortOrder:            1,
			UsageUnitDisplayName: "credits",
			PlanFacts:            []string{"Unlimited support"},
		},
		Features: orb_config.PlanFeatures{
			TrainingAllowed:     true,
			TeamsAllowed:        true,
			MaxSeats:            10,
			AddCreditsAvailable: true,
			PlanType:            orb_config.PlanTypePaid,
		},
		OrbPlan: orb_go.Plan{
			ExternalPlanID:     id,
			Name:               "Test Plan " + id,
			Description:        "Test plan description",
			Status:             orb_go.PlanStatusActive,
			InvoicingCurrency:  "USD",
			NetTerms:           30,
			DefaultInvoiceMemo: "Test invoice memo",
			Version:            1, // Match external plan default
			Prices: []orb_go.Price{
				{
					ID:       "price_1",
					Name:     "Base Price",
					Currency: "USD",
				},
			},
		},
	}

	// Apply overrides
	for key, value := range overrides {
		switch key {
		case "State":
			plan.State = value.(orb_config.PlanState)
		case "Name":
			plan.OrbPlan.Name = value.(string)
		case "Status":
			plan.OrbPlan.Status = value.(orb_go.PlanStatus)
		case "NetTerms":
			plan.OrbPlan.NetTerms = value.(int64)
		case "PlanType":
			plan.Features.PlanType = value.(orb_config.PlanType)
		case "Version":
			plan.OrbPlan.Version = int64(value.(int))
		case "Prices":
			plan.OrbPlan.Prices = value.([]orb_go.Price)
		}
	}

	return plan
}

// createTestOrbPlan creates a test orb_go.Plan with default values
func createTestOrbPlan(externalPlanID string, overrides map[string]interface{}) orb_go.Plan {
	now := time.Now()
	plan := orb_go.Plan{
		ID:                 "orb_plan_" + externalPlanID,
		ExternalPlanID:     externalPlanID,
		Name:               "Test Plan " + externalPlanID,
		Description:        "Test plan description",
		Status:             orb_go.PlanStatusActive,
		InvoicingCurrency:  "USD",
		NetTerms:           30,
		DefaultInvoiceMemo: "Test invoice memo",
		CreatedAt:          now,
		Version:            1,
		Prices: []orb_go.Price{
			{
				ID:       "price_1",
				Name:     "Base Price",
				Currency: "USD",
			},
		},
	}

	// Apply overrides
	for key, value := range overrides {
		switch key {
		case "Name":
			plan.Name = value.(string)
		case "Status":
			plan.Status = value.(orb_go.PlanStatus)
		case "NetTerms":
			plan.NetTerms = value.(int64)
		case "Description":
			plan.Description = value.(string)
		case "Version":
			plan.Version = value.(int64)
		case "Prices":
			plan.Prices = value.([]orb_go.Price)
		}
	}

	return plan
}

func TestPlanComparator_CompareSinglePlan(t *testing.T) {
	// Create a minimal OrbConfig for testing
	orbConfig := &orb_config.OrbConfig{}
	comparator := NewPlanComparator(orbConfig)

	// Helper function to get plan config pointer
	getPlanConfig := func(planID string, overrides map[string]interface{}) *orb_go.Plan {
		plan := createTestPlan(planID, overrides)
		return &plan.OrbPlan
	}

	// Helper function to get orb plan pointer
	getOrbPlan := func(planID string, overrides map[string]interface{}) *orb_go.Plan {
		plan := createTestOrbPlan(planID, overrides)
		return &plan
	}

	tests := []struct {
		name             string
		configPlan       *orb_go.Plan
		externalPlan     *orb_go.Plan
		expectedStatus   PlanValidationStatus
		expectedCreation bool
		expectedWarnings bool
		description      string
	}{
		{
			name:             "nil_external_plan",
			configPlan:       getPlanConfig("plan1", nil),
			externalPlan:     nil,
			expectedStatus:   PlanValidationStatusMissing,
			expectedCreation: true,
			expectedWarnings: true,
			description:      "External plan is nil, should be marked as missing",
		},
		{
			name:             "identical_plans",
			configPlan:       getPlanConfig("plan1", nil),
			externalPlan:     getOrbPlan("plan1", nil),
			expectedStatus:   PlanValidationStatusValid,
			expectedCreation: false,
			expectedWarnings: false,
			description:      "Identical plans should be marked as valid",
		},
		{
			name: "different_names",
			configPlan: getPlanConfig("plan1", map[string]interface{}{
				"Name": "Config Name",
			}),
			externalPlan: getOrbPlan("plan1", map[string]interface{}{
				"Name": "External Name",
			}),
			expectedStatus:   PlanValidationStatusMismatch,
			expectedCreation: false,
			expectedWarnings: true,
			description:      "Plans with different names should be marked as mismatch",
		},
		{
			name:       "version_mismatch",
			configPlan: getPlanConfig("plan1", nil),
			externalPlan: getOrbPlan("plan1", map[string]interface{}{
				"Version": int64(999),
			}),
			expectedStatus:   PlanValidationStatusMismatch,
			expectedCreation: false,
			expectedWarnings: true,
			description:      "Plans with different versions should be marked as mismatch",
		},
		{
			name:             "ignored_fields_difference",
			configPlan:       getPlanConfig("plan1", nil),
			externalPlan:     getOrbPlan("plan1", nil),
			expectedStatus:   PlanValidationStatusValid,
			expectedCreation: false,
			expectedWarnings: false,
			description:      "Differences in ignored fields should not cause mismatch",
		},
		{
			name: "price_order_ignored",
			configPlan: getPlanConfig("plan1", map[string]interface{}{
				"Prices": []orb_go.Price{
					{ID: "price1", Name: "Price A", ModelType: "unit"},
					{ID: "price2", Name: "Price B", ModelType: "package"},
				},
			}),
			externalPlan: getOrbPlan("plan1", map[string]interface{}{
				"Prices": []orb_go.Price{
					{ID: "price2", Name: "Price B", ModelType: "package"},
					{ID: "price1", Name: "Price A", ModelType: "unit"},
				},
			}),
			expectedStatus:   PlanValidationStatusValid,
			expectedCreation: false,
			expectedWarnings: false,
			description:      "Different price order should be ignored",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := comparator.compareSinglePlan(tt.configPlan, tt.externalPlan)

			// Check status
			if result.Status != tt.expectedStatus {
				t.Errorf("Expected status %s, got %s", tt.expectedStatus.String(), result.Status.String())
			}

			// Check creation requirement
			if result.RequiresCreation != tt.expectedCreation {
				t.Errorf("Expected RequiresCreation %v, got %v", tt.expectedCreation, result.RequiresCreation)
			}

			// Check warnings
			hasWarnings := len(result.Warnings) > 0
			if hasWarnings != tt.expectedWarnings {
				t.Errorf("Expected warnings %v, got %v (warnings: %v)", tt.expectedWarnings, hasWarnings, result.Warnings)
			}

			// Verify plan references
			if result.ConfigPlan != tt.configPlan {
				t.Error("ConfigPlan reference mismatch")
			}
			if result.ExternalPlan != tt.externalPlan {
				t.Error("ExternalPlan reference mismatch")
			}
		})
	}
}

func TestPlanComparator_ComparePlans(t *testing.T) {
	// Create a minimal OrbConfig for testing
	orbConfig := &orb_config.OrbConfig{}
	comparator := NewPlanComparator(orbConfig)

	tests := []struct {
		name                 string
		configPlans          []orb_config.PlanConfig
		externalPlans        []orb_go.Plan
		expectedResults      int
		expectedStatuses     map[PlanValidationStatus]int
		expectedCreationReqs int
		description          string
	}{
		{
			name:                 "empty_lists",
			configPlans:          []orb_config.PlanConfig{},
			externalPlans:        []orb_go.Plan{},
			expectedResults:      0,
			expectedStatuses:     map[PlanValidationStatus]int{},
			expectedCreationReqs: 0,
			description:          "Both config and external lists are empty",
		},
		{
			name: "config_only_plans",
			configPlans: []orb_config.PlanConfig{
				createTestPlan("plan1", nil),
				createTestPlan("plan2", nil),
			},
			externalPlans:   []orb_go.Plan{},
			expectedResults: 2,
			expectedStatuses: map[PlanValidationStatus]int{
				PlanValidationStatusMissing: 2,
			},
			expectedCreationReqs: 2,
			description:          "Config plans exist but no external plans",
		},
		{
			name:        "external_only_plans",
			configPlans: []orb_config.PlanConfig{},
			externalPlans: []orb_go.Plan{
				createTestOrbPlan("plan1", nil),
				createTestOrbPlan("plan2", nil),
			},
			expectedResults: 2,
			expectedStatuses: map[PlanValidationStatus]int{
				PlanValidationStatusExtra: 2,
			},
			expectedCreationReqs: 0,
			description:          "External plans exist but no config plans",
		},
		{
			name: "matching_plans",
			configPlans: []orb_config.PlanConfig{
				createTestPlan("plan1", nil),
			},
			externalPlans: []orb_go.Plan{
				createTestOrbPlan("plan1", nil),
			},
			expectedResults: 1,
			expectedStatuses: map[PlanValidationStatus]int{
				PlanValidationStatusValid: 1,
			},
			expectedCreationReqs: 0,
			description:          "Single matching plan between config and external",
		},
		{
			name: "mismatched_plans",
			configPlans: []orb_config.PlanConfig{
				createTestPlan("plan1", map[string]interface{}{
					"Name": "Config Plan Name",
				}),
			},
			externalPlans: []orb_go.Plan{
				createTestOrbPlan("plan1", map[string]interface{}{
					"Name": "Different External Plan Name",
				}),
			},
			expectedResults: 1,
			expectedStatuses: map[PlanValidationStatus]int{
				PlanValidationStatusMismatch: 1,
			},
			expectedCreationReqs: 0,
			description:          "Plans with same ID but different content",
		},
		{
			name: "mixed_scenario",
			configPlans: []orb_config.PlanConfig{
				createTestPlan("plan1", nil),                                    // Should match
				createTestPlan("plan2", nil),                                    // Should be missing
				createTestPlan("plan3", map[string]interface{}{"Name": "Diff"}), // Should mismatch
			},
			externalPlans: []orb_go.Plan{
				createTestOrbPlan("plan1", nil),                                     // Matches config
				createTestOrbPlan("plan3", map[string]interface{}{"Name": "Other"}), // Mismatches config
				createTestOrbPlan("plan4", nil),                                     // Extra plan
			},
			expectedResults: 4,
			expectedStatuses: map[PlanValidationStatus]int{
				PlanValidationStatusValid:    1,
				PlanValidationStatusMissing:  1,
				PlanValidationStatusMismatch: 1,
				PlanValidationStatusExtra:    1,
			},
			expectedCreationReqs: 1,
			description:          "Complex scenario with all status types",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			results := comparator.ComparePlans(tt.configPlans, tt.externalPlans)

			// Check total results count
			if len(results) != tt.expectedResults {
				t.Errorf("Expected %d results, got %d", tt.expectedResults, len(results))
			}

			// Count actual statuses
			actualStatuses := make(map[PlanValidationStatus]int)
			actualCreationReqs := 0
			for _, result := range results {
				actualStatuses[result.Status]++
				if result.RequiresCreation {
					actualCreationReqs++
				}
			}

			// Verify status counts
			for expectedStatus, expectedCount := range tt.expectedStatuses {
				if actualStatuses[expectedStatus] != expectedCount {
					t.Errorf("Expected %d plans with status %s, got %d",
						expectedCount, expectedStatus.String(), actualStatuses[expectedStatus])
				}
			}

			// Verify creation requirements
			if actualCreationReqs != tt.expectedCreationReqs {
				t.Errorf("Expected %d plans requiring creation, got %d", tt.expectedCreationReqs, actualCreationReqs)
			}

			// Verify all results have warnings when expected
			for _, result := range results {
				if result.Status != PlanValidationStatusValid && len(result.Warnings) == 0 {
					t.Errorf("Expected warnings for plan %s with status %s", result.PlanID, result.Status.String())
				}
			}
		})
	}
}

func TestPlanComparator_FilterResultsByStatus(t *testing.T) {
	// Create a minimal OrbConfig for testing
	orbConfig := &orb_config.OrbConfig{}
	comparator := NewPlanComparator(orbConfig)

	// Create test results with various statuses
	testResults := []PlanValidationResult{
		{PlanID: "plan1", Status: PlanValidationStatusValid},
		{PlanID: "plan2", Status: PlanValidationStatusMissing},
		{PlanID: "plan3", Status: PlanValidationStatusValid},
		{PlanID: "plan4", Status: PlanValidationStatusMismatch},
		{PlanID: "plan5", Status: PlanValidationStatusExtra},
		{PlanID: "plan6", Status: PlanValidationStatusUnknown},
	}

	tests := []struct {
		name            string
		results         []PlanValidationResult
		filterStatus    PlanValidationStatus
		expectedCount   int
		expectedPlanIDs []string
		description     string
	}{
		{
			name:            "filter_valid_plans",
			results:         testResults,
			filterStatus:    PlanValidationStatusValid,
			expectedCount:   2,
			expectedPlanIDs: []string{"plan1", "plan3"},
			description:     "Filter for valid plans should return 2 results",
		},
		{
			name:            "filter_missing_plans",
			results:         testResults,
			filterStatus:    PlanValidationStatusMissing,
			expectedCount:   1,
			expectedPlanIDs: []string{"plan2"},
			description:     "Filter for missing plans should return 1 result",
		},
		{
			name:            "filter_mismatch_plans",
			results:         testResults,
			filterStatus:    PlanValidationStatusMismatch,
			expectedCount:   1,
			expectedPlanIDs: []string{"plan4"},
			description:     "Filter for mismatch plans should return 1 result",
		},
		{
			name:            "filter_extra_plans",
			results:         testResults,
			filterStatus:    PlanValidationStatusExtra,
			expectedCount:   1,
			expectedPlanIDs: []string{"plan5"},
			description:     "Filter for extra plans should return 1 result",
		},
		{
			name:            "filter_unknown_plans",
			results:         testResults,
			filterStatus:    PlanValidationStatusUnknown,
			expectedCount:   1,
			expectedPlanIDs: []string{"plan6"},
			description:     "Filter for unknown plans should return 1 result",
		},
		{
			name:            "empty_results_list",
			results:         []PlanValidationResult{},
			filterStatus:    PlanValidationStatusValid,
			expectedCount:   0,
			expectedPlanIDs: []string{},
			description:     "Empty results list should return empty filtered list",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			filtered := comparator.FilterResultsByStatus(tt.results, tt.filterStatus)

			// Check count
			if len(filtered) != tt.expectedCount {
				t.Errorf("Expected %d results, got %d", tt.expectedCount, len(filtered))
			}

			// Check plan IDs
			actualPlanIDs := make([]string, len(filtered))
			for i, result := range filtered {
				actualPlanIDs[i] = result.PlanID
			}

			if len(actualPlanIDs) != len(tt.expectedPlanIDs) {
				t.Errorf("Expected plan IDs %v, got %v", tt.expectedPlanIDs, actualPlanIDs)
			} else {
				// Check each expected plan ID is present
				for _, expectedID := range tt.expectedPlanIDs {
					found := false
					for _, actualID := range actualPlanIDs {
						if actualID == expectedID {
							found = true
							break
						}
					}
					if !found {
						t.Errorf("Expected plan ID %s not found in results %v", expectedID, actualPlanIDs)
					}
				}
			}

			// Verify all filtered results have the correct status
			for _, result := range filtered {
				if result.Status != tt.filterStatus {
					t.Errorf("Filtered result has wrong status: expected %s, got %s", tt.filterStatus.String(), result.Status.String())
				}
			}
		})
	}
}
