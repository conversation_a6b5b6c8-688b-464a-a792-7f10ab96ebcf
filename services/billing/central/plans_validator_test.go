package main

import (
	"context"
	"errors"
	"fmt"
	"testing"
	"time"

	"github.com/augmentcode/augment/services/billing/lib/orb"
	orb_config "github.com/augmentcode/augment/services/billing/lib/orb/config"
	orb_go "github.com/orbcorp/orb-go"
	"github.com/stretchr/testify/assert"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func TestPlansValidator_HasValidationIssues(t *testing.T) {
	// Create a simple validator for testing basic functionality
	orbConfig := &orb_config.OrbConfig{
		Validation: orb_config.OrbValidationConfig{
			FailOnWarnings: false,
		},
	}

	validator := &PlansValidator{
		orbConfig:  orbConfig,
		comparator: NewPlanComparator(orbConfig),
	}

	tests := []struct {
		name     string
		report   ValidationReport
		expected bool
	}{
		{
			name: "successful validation - no issues",
			report: ValidationReport{
				Success: true,
				PlanResults: []PlanValidationResult{
					{Status: PlanValidationStatusValid},
				},
			},
			expected: false, // HasValidationIssues should return false for successful validation
		},
		{
			name: "failed validation - has issues",
			report: ValidationReport{
				Success: false,
				Errors:  []string{"test error"},
			},
			expected: true, // HasValidationIssues should return true for failed validation
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			result := validator.HasValidationIssues(&tt.report)
			if result != tt.expected {
				t.Errorf("expected %t, got %t", tt.expected, result)
			}
		})
	}
}

// Helper function to create test plans
func createTestPlanForValidator(planID string, overrides map[string]interface{}) orb_config.PlanConfig {
	plan := orb_config.PlanConfig{
		ID:    planID,
		State: orb_config.PlanStateActive,
		DisplayInfo: orb_config.PlanDisplayConfig{
			Color:                "blue",
			Icon:                 "star",
			SortOrder:            1,
			UsageUnitDisplayName: "credits",
			PlanFacts:            []string{"Test plan"},
		},
		Features: orb_config.PlanFeatures{
			TrainingAllowed:     true,
			TeamsAllowed:        true,
			MaxSeats:            10,
			AddCreditsAvailable: true,
			PlanType:            orb_config.PlanTypePaid,
		},
		OrbPlan: orb_go.Plan{
			ExternalPlanID:    planID,
			Name:              fmt.Sprintf("Test Plan %s", planID),
			InvoicingCurrency: "USD",
			Status:            orb_go.PlanStatusActive,
			Version:           1,
		},
	}

	// Apply overrides
	if overrides != nil {
		for key, value := range overrides {
			switch key {
			case "Name":
				plan.OrbPlan.Name = value.(string)
			case "Status":
				plan.OrbPlan.Status = value.(orb_go.PlanStatus)
			case "Prices":
				plan.OrbPlan.Prices = value.([]orb_go.Price)
			}
		}
	}

	return plan
}

func createTestOrbPlanForValidator(planID string, overrides map[string]interface{}) orb_go.Plan {
	plan := orb_go.Plan{
		ID:                planID,
		ExternalPlanID:    planID, // This is the key field used for comparison
		Name:              fmt.Sprintf("Test Plan %s", planID),
		InvoicingCurrency: "USD", // Match the config plan field
		Status:            orb_go.PlanStatusActive,
		CreatedAt:         time.Now(),
		Version:           1,
	}

	// Apply overrides
	if overrides != nil {
		for key, value := range overrides {
			switch key {
			case "Name":
				plan.Name = value.(string)
			case "Status":
				plan.Status = value.(orb_go.PlanStatus)
			case "Prices":
				plan.Prices = value.([]orb_go.Price)
			}
		}
	}

	return plan
}

// Comprehensive table-driven test for PlansValidator
func TestPlansValidator_ValidatePlans(t *testing.T) {
	tests := []struct {
		name                 string
		configPlans          []orb_config.PlanConfig
		externalPlans        []orb_go.Plan
		apiError             error
		dryRun               bool
		autoCreateMissing    bool
		failOnWarnings       bool
		expectError          bool
		expectSuccess        bool
		expectedValidCount   int
		expectedMissingCount int
		expectCreateCalls    int
		description          string
	}{
		{
			name: "success_all_plans_valid",
			configPlans: []orb_config.PlanConfig{
				createTestPlanForValidator("plan1", nil),
				createTestPlanForValidator("plan2", nil),
			},
			externalPlans: []orb_go.Plan{
				createTestOrbPlanForValidator("plan1", nil),
				createTestOrbPlanForValidator("plan2", nil),
			},
			dryRun:               false,
			autoCreateMissing:    false,
			failOnWarnings:       false,
			expectError:          false,
			expectSuccess:        true,
			expectedValidCount:   2,
			expectedMissingCount: 0,
			expectCreateCalls:    0,
			description:          "All plans exist and match - should succeed",
		},
		{
			name: "missing_plans_with_warnings",
			configPlans: []orb_config.PlanConfig{
				createTestPlanForValidator("plan1", nil),
				createTestPlanForValidator("plan2", nil),
			},
			externalPlans: []orb_go.Plan{
				createTestOrbPlanForValidator("plan1", nil),
				// plan2 is missing
			},
			dryRun:               false,
			autoCreateMissing:    false,
			failOnWarnings:       false,
			expectError:          false,
			expectSuccess:        true,
			expectedValidCount:   1,
			expectedMissingCount: 1,
			expectCreateCalls:    0,
			description:          "Missing plans should generate warnings but still succeed",
		},
		{
			name: "api_error",
			configPlans: []orb_config.PlanConfig{
				createTestPlanForValidator("plan1", nil),
			},
			externalPlans:        nil,
			apiError:             errors.New("API connection failed"),
			dryRun:               false,
			autoCreateMissing:    false,
			failOnWarnings:       false,
			expectError:          true,
			expectSuccess:        false,
			expectedValidCount:   0,
			expectedMissingCount: 0,
			expectCreateCalls:    0,
			description:          "API errors should cause validation to fail",
		},
		{
			name: "auto_create_missing_plans",
			configPlans: []orb_config.PlanConfig{
				createTestPlanForValidator("plan1", nil),
				createTestPlanForValidator("plan2", nil),
			},
			externalPlans: []orb_go.Plan{
				createTestOrbPlanForValidator("plan1", nil),
				// plan2 is missing and should be created
			},
			dryRun:               false,
			autoCreateMissing:    true,
			failOnWarnings:       false,
			expectError:          false,
			expectSuccess:        true,
			expectedValidCount:   1,
			expectedMissingCount: 1,
			expectCreateCalls:    1,
			description:          "Missing plans should be created when auto-create is enabled",
		},
		{
			name: "dry_run_mode",
			configPlans: []orb_config.PlanConfig{
				createTestPlanForValidator("plan1", nil),
				createTestPlanForValidator("plan2", nil),
			},
			externalPlans: []orb_go.Plan{
				createTestOrbPlanForValidator("plan1", nil),
				// plan2 is missing
			},
			dryRun:               true,
			autoCreateMissing:    true, // Should be ignored in dry run
			failOnWarnings:       false,
			expectError:          false,
			expectSuccess:        true,
			expectedValidCount:   1,
			expectedMissingCount: 1,
			expectCreateCalls:    0, // No creation in dry run
			description:          "Dry run should not create plans even with auto-create enabled",
		},
		{
			name: "fail_on_warnings",
			configPlans: []orb_config.PlanConfig{
				createTestPlanForValidator("plan1", nil),
				createTestPlanForValidator("plan2", nil),
			},
			externalPlans: []orb_go.Plan{
				createTestOrbPlanForValidator("plan1", nil),
				// plan2 is missing
			},
			dryRun:               false,
			autoCreateMissing:    false,
			failOnWarnings:       true,
			expectError:          false,
			expectSuccess:        false, // Should fail due to warnings
			expectedValidCount:   1,
			expectedMissingCount: 1,
			expectCreateCalls:    0,
			description:          "Validation should fail when warnings exist and fail-on-warnings is enabled",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			mockClient := &orb.MockOrbClient{}
			orbConfig := &orb_config.OrbConfig{
				Plans: tt.configPlans,
				Validation: orb_config.OrbValidationConfig{
					DryRun:            tt.dryRun,
					AutoCreateMissing: tt.autoCreateMissing,
					FailOnWarnings:    tt.failOnWarnings,
				},
			}

			validator := &PlansValidator{
				orbClient:  mockClient,
				orbConfig:  orbConfig,
				comparator: NewPlanComparator(orbConfig),
			}

			// Mock the ListAllPlans call
			if tt.apiError != nil {
				mockClient.On("ListAllPlans", mock.Anything).Return([]orb_go.Plan(nil), tt.apiError)
			} else {
				mockClient.On("ListAllPlans", mock.Anything).Return(tt.externalPlans, nil)
			}

			// Mock CreatePlan calls if expected
			if tt.expectCreateCalls > 0 {
				for i := 0; i < tt.expectCreateCalls; i++ {
					createdPlan := createTestOrbPlanForValidator("plan2", nil)
					mockClient.On("CreatePlan", mock.Anything, mock.Anything, mock.Anything).Return(&createdPlan, nil)
				}
			}

			ctx := context.Background()
			report, err := validator.ValidatePlans(ctx)

			// Verify error expectations
			if tt.expectError {
				require.Error(t, err)
			} else {
				require.NoError(t, err)
			}

			// Verify success expectations
			assert.Equal(t, tt.expectSuccess, report.Success, tt.description)

			// Verify plan counts if no API error
			if tt.apiError == nil {
				assert.Len(t, report.PlanResults, len(tt.configPlans))

				// Count validation statuses
				statusCounts := make(map[PlanValidationStatus]int)
				for _, result := range report.PlanResults {
					statusCounts[result.Status]++
				}

				assert.Equal(t, tt.expectedValidCount, statusCounts[PlanValidationStatusValid], "Valid plan count mismatch")
				assert.Equal(t, tt.expectedMissingCount, statusCounts[PlanValidationStatusMissing], "Missing plan count mismatch")
			}

			mockClient.AssertExpectations(t)
		})
	}
}

// TestIdempotencyKeySuffix tests that the idempotency key includes the configured suffix
func TestIdempotencyKeySuffix(t *testing.T) {
	tests := []struct {
		name           string
		suffix         string
		expectedSuffix string
	}{
		{
			name:           "empty suffix",
			suffix:         "",
			expectedSuffix: "",
		},
		{
			name:           "with suffix",
			suffix:         "test-suffix",
			expectedSuffix: "test-suffix",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			// Create mock client
			mockClient := &orb.MockOrbClient{}

			// Create test plan with specific suffix
			testPlan := createTestPlanForValidator("test-plan", nil)
			testPlan.IdempotencyKeySuffix = tt.suffix

			// Create test configuration
			orbConfig := &orb_config.OrbConfig{
				Plans: []orb_config.PlanConfig{testPlan},
				Validation: orb_config.OrbValidationConfig{
					DryRun:            false,
					AutoCreateMissing: true,
					FailOnWarnings:    false,
					MaxRetries:        3,
					RetryDelaySeconds: 2,
				},
			}

			// Set up mock to capture the idempotency key
			var capturedIdempotencyKey *string
			createdPlan := orb_go.Plan{
				ID:             "created-plan-id",
				ExternalPlanID: "test-plan",
				Name:           "Test Plan",
				Version:        1,
			}

			mockClient.On("ListAllPlans", mock.Anything).Return([]orb_go.Plan{}, nil)
			mockClient.On("CreatePlan", mock.Anything, mock.Anything, mock.MatchedBy(func(key *string) bool {
				capturedIdempotencyKey = key
				return true
			})).Return(&createdPlan, nil)

			// Create validator and run validation
			validator := NewPlansValidator(orbConfig, mockClient)
			ctx := context.Background()
			_, err := validator.ValidatePlans(ctx)

			// Verify no error occurred
			require.NoError(t, err)

			// Verify idempotency key was captured and has correct format
			require.NotNil(t, capturedIdempotencyKey)
			expectedKey := fmt.Sprintf("plan-creation-%s-%s", "test-plan", tt.expectedSuffix)
			assert.Equal(t, expectedKey, *capturedIdempotencyKey)

			// Verify mock expectations
			mockClient.AssertExpectations(t)
		})
	}
}

func TestPlansValidator_validateConfig(t *testing.T) {
	now := time.Now()
	tests := []struct {
		name           string
		configPlans    []orb_config.PlanConfig
		expectedErrors []string
		description    string
	}{
		{
			name: "valid_config_no_ignored_fields",
			configPlans: []orb_config.PlanConfig{
				{
					ID:    "plan1",
					State: orb_config.PlanStateActive,
					OrbPlan: orb_go.Plan{
						ExternalPlanID:    "plan1",
						Name:              "Valid Plan",
						InvoicingCurrency: "USD",
						Status:            orb_go.PlanStatusActive,
						Prices: []orb_go.Price{
							{
								Name:      "Base Price",
								ModelType: "unit",
								Currency:  "USD",
							},
						},
					},
				},
			},
			expectedErrors: []string{},
			description:    "Valid config with no ignored fields should pass",
		},
		{
			name: "invalid_pricing_model",
			configPlans: []orb_config.PlanConfig{
				{
					ID:    "plan1",
					State: orb_config.PlanStateActive,
					OrbPlan: orb_go.Plan{
						ExternalPlanID:    "plan1",
						Name:              "Invalid Plan",
						InvoicingCurrency: "USD",
						Status:            orb_go.PlanStatusActive,
						Prices: []orb_go.Price{
							{
								Name:      "Base Price",
								ModelType: "package", // Invalid model type
								Currency:  "USD",
							},
						},
					},
				},
			},
			expectedErrors: []string{
				"Plan 'plan1' contains unsupported pricing model 'package'. Only 'unit' pricing is currently supported. Please update the plan configuration.",
			},
			description: "Invalid pricing model should generate error",
		},
		{
			name: "plan_with_ignored_id_field",
			configPlans: []orb_config.PlanConfig{
				{
					ID:    "plan1",
					State: orb_config.PlanStateActive,
					OrbPlan: orb_go.Plan{
						ID:                "orb_plan_id", // This should not be set
						ExternalPlanID:    "plan1",
						Name:              "Plan with ID",
						InvoicingCurrency: "USD",
						Status:            orb_go.PlanStatusActive,
						Prices: []orb_go.Price{
							{
								Name:      "Base Price",
								ModelType: "unit",
								Currency:  "USD",
							},
						},
					},
				},
			},
			expectedErrors: []string{
				"Plan 'plan1' has 'ID' field set to 'orb_plan_id'. This field is ignored during comparison and should not be set in configuration",
			},
			description: "Plan with ID field should generate error",
		},
		{
			name: "plan_with_ignored_created_at_field",
			configPlans: []orb_config.PlanConfig{
				{
					ID:    "plan1",
					State: orb_config.PlanStateActive,
					OrbPlan: orb_go.Plan{
						ExternalPlanID:    "plan1",
						Name:              "Plan with CreatedAt",
						InvoicingCurrency: "USD",
						Status:            orb_go.PlanStatusActive,
						CreatedAt:         now, // This should not be set
						Prices: []orb_go.Price{
							{
								Name:      "Base Price",
								ModelType: "unit",
								Currency:  "USD",
							},
						},
					},
				},
			},
			expectedErrors: []string{
				"Plan 'plan1' has 'CreatedAt' field set to '" + now.String() + "'. This field is ignored during comparison and should not be set in configuration",
			},
			description: "Plan with CreatedAt field should generate error",
		},
		{
			name: "plan_with_ignored_metadata_field",
			configPlans: []orb_config.PlanConfig{
				{
					ID:    "plan1",
					State: orb_config.PlanStateActive,
					OrbPlan: orb_go.Plan{
						ExternalPlanID:    "plan1",
						Name:              "Plan with Metadata",
						InvoicingCurrency: "USD",
						Status:            orb_go.PlanStatusActive,
						Metadata:          map[string]string{"key": "value"}, // This should not be set
						Prices: []orb_go.Price{
							{
								Name:      "Base Price",
								ModelType: "unit",
								Currency:  "USD",
							},
						},
					},
				},
			},
			expectedErrors: []string{
				"Plan 'plan1' has 'Metadata' field set to map[key:value]. This field is ignored during comparison and should not be set in configuration",
			},
			description: "Plan with Metadata field should generate error",
		},
		{
			name: "price_with_ignored_fields",
			configPlans: []orb_config.PlanConfig{
				{
					ID:    "plan1",
					State: orb_config.PlanStateActive,
					OrbPlan: orb_go.Plan{
						ExternalPlanID:    "plan1",
						Name:              "Plan with Price Issues",
						InvoicingCurrency: "USD",
						Status:            orb_go.PlanStatusActive,
						Prices: []orb_go.Price{
							{
								ID:              "price_id", // This should not be set
								Name:            "Base Price",
								ModelType:       "unit",
								Currency:        "USD",
								CreatedAt:       now,                               // This should not be set
								Metadata:        map[string]string{"key": "value"}, // This should not be set
								ReplacesPriceID: "old_price_id",                    // This should not be set
							},
						},
					},
				},
			},
			expectedErrors: []string{
				"Plan 'plan1' price 0 has 'ID' field set to 'price_id'. This field is ignored during comparison and should not be set in configuration",
				"Plan 'plan1' price 0 has 'CreatedAt' field set to '" + now.String() + "'. This field is ignored during comparison and should not be set in configuration",
				"Plan 'plan1' price 0 has 'Metadata' field set to map[key:value]. This field is ignored during comparison and should not be set in configuration",
				"Plan 'plan1' price 0 has 'ReplacesPriceID' field set to 'old_price_id'. This field is ignored during comparison and should not be set in configuration",
			},
			description: "Price with ignored fields should generate multiple errors",
		},
		{
			name: "multiple_issues_combined",
			configPlans: []orb_config.PlanConfig{
				{
					ID:    "plan1",
					State: orb_config.PlanStateActive,
					OrbPlan: orb_go.Plan{
						ID:                "orb_plan_id", // Ignored field
						ExternalPlanID:    "plan1",
						Name:              "Problem Plan",
						InvoicingCurrency: "USD",
						Status:            orb_go.PlanStatusActive,
						Metadata:          map[string]string{"key": "value"}, // Ignored field
						Prices: []orb_go.Price{
							{
								Name:      "Base Price",
								ModelType: "package", // Invalid model type
								Currency:  "USD",
								ID:        "price_id", // Ignored field
							},
						},
					},
				},
			},
			expectedErrors: []string{
				"Plan 'plan1' contains unsupported pricing model 'package'. Only 'unit' pricing is currently supported. Please update the plan configuration.",
				"Plan 'plan1' has 'ID' field set to 'orb_plan_id'. This field is ignored during comparison and should not be set in configuration",
				"Plan 'plan1' has 'Metadata' field set to map[key:value]. This field is ignored during comparison and should not be set in configuration",
				"Plan 'plan1' price 0 has 'ID' field set to 'price_id'. This field is ignored during comparison and should not be set in configuration",
			},
			description: "Multiple validation issues should generate multiple errors",
		},
	}

	for _, tt := range tests {
		t.Run(tt.name, func(t *testing.T) {
			orbConfig := &orb_config.OrbConfig{
				Plans: tt.configPlans,
				Validation: orb_config.OrbValidationConfig{
					FailOnWarnings: false,
				},
			}

			validator := &PlansValidator{
				orbConfig:  orbConfig,
				comparator: NewPlanComparator(orbConfig),
			}

			report := &ValidationReport{}
			err := validator.validateConfig(tt.configPlans, report)

			// Should not return error (errors are added to report)
			require.NoError(t, err)

			// Check that expected errors are present
			assert.Equal(t, len(tt.expectedErrors), len(report.Errors), "Error count mismatch for %s", tt.description)

			for i, expectedError := range tt.expectedErrors {
				if i < len(report.Errors) {
					assert.Equal(t, expectedError, report.Errors[i], "Error message mismatch for %s", tt.description)
				}
			}
		})
	}
}
