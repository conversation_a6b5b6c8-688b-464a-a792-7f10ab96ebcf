package main

import (
	"fmt"
	"unicode"
	"unicode/utf8"

	orb_config "github.com/augmentcode/augment/services/billing/lib/orb/config"
	"github.com/google/go-cmp/cmp"
	"github.com/google/go-cmp/cmp/cmpopts"
	orb_go "github.com/orbcorp/orb-go"
	"github.com/rs/zerolog/log"
)

// PlanValidationResult represents the result of validating a single plan
type PlanValidationResult struct {
	PlanID               string
	ConfigPlan           *orb_go.Plan
	ExternalPlan         *orb_go.Plan
	Status               PlanValidationStatus
	RequiresCreation     bool
	Warnings             []string
	IdempotencyKeySuffix string
}

// PlanValidationStatus represents the validation status of a plan
type PlanValidationStatus int

const (
	PlanValidationStatusUnknown PlanValidationStatus = iota
	PlanValidationStatusValid
	PlanValidationStatusMissing
	PlanValidationStatusMismatch
	PlanValidationStatusExtra
)

func (s PlanValidationStatus) String() string {
	switch s {
	case PlanValidationStatusValid:
		return "valid"
	case PlanValidationStatusMissing:
		return "missing"
	case PlanValidationStatusMismatch:
		return "mismatch"
	case PlanValidationStatusExtra:
		return "extra"
	default:
		return "unknown"
	}
}

// PlanComparator handles comparison between configuration plans and external plans
type PlanComparator struct {
	orbConfig *orb_config.OrbConfig
}

// NewPlanComparator creates a new plan comparator
func NewPlanComparator(orbConfig *orb_config.OrbConfig) *PlanComparator {
	return &PlanComparator{orbConfig}
}

// ComparePlans compares a list of configuration plans against external plans
func (c *PlanComparator) ComparePlans(configPlans []orb_config.PlanConfig, externalPlans []orb_go.Plan) []PlanValidationResult {
	log.Info().
		Int("config_plans", len(configPlans)).
		Int("external_plans", len(externalPlans)).
		Msg("Starting plan comparison")

	results := make([]PlanValidationResult, 0)

	// Create maps for easy lookup
	configPlanMap := make(map[string]*orb_config.PlanConfig)
	for i := range configPlans {
		configPlanMap[configPlans[i].ID] = &configPlans[i]
	}

	externalPlanMap := make(map[string]*orb_go.Plan)
	for i := range externalPlans {
		externalPlanMap[externalPlans[i].ExternalPlanID] = &externalPlans[i]
	}

	// Check each config plan against external plans
	for _, configPlan := range configPlans {
		result := c.compareSinglePlan(&configPlan.OrbPlan, externalPlanMap[configPlan.ID])
		result.IdempotencyKeySuffix = configPlan.IdempotencyKeySuffix // Set idempotency key suffix
		results = append(results, result)
	}

	// Check for extra plans in external system
	for _, externalPlan := range externalPlans {
		if _, exists := configPlanMap[externalPlan.ExternalPlanID]; !exists {
			result := PlanValidationResult{
				PlanID:       externalPlan.ExternalPlanID,
				ConfigPlan:   nil,
				ExternalPlan: &externalPlan,
				Status:       PlanValidationStatusExtra,
				Warnings:     []string{fmt.Sprintf("Plan %s exists in external system but not in configuration", externalPlan.ExternalPlanID)},
			}
			results = append(results, result)
		}
	}
	return results
}

// compareSinglePlan compares a single configuration plan against its external counterpart
func (c *PlanComparator) compareSinglePlan(configPlan *orb_go.Plan, externalPlan *orb_go.Plan) PlanValidationResult {
	result := PlanValidationResult{
		PlanID:       configPlan.ExternalPlanID,
		ConfigPlan:   configPlan,
		ExternalPlan: externalPlan,
		Warnings:     make([]string, 0),
	}

	// If external plan doesn't exist, it needs to be created
	if externalPlan == nil {
		log.Warn().
			Str("plan_id", configPlan.ExternalPlanID).
			Msgf("Plan %s is missing from external system", configPlan.ExternalPlanID)
		result.Status = PlanValidationStatusMissing
		result.RequiresCreation = true
		result.Warnings = append(result.Warnings, fmt.Sprintf("Plan %s is missing from external system", configPlan.ExternalPlanID))
		return result
	}

	// Custom option to ignore any field named "JSON", which keeps only raw json format of the same object
	var ignoreJSONFields cmp.Option = cmp.FilterPath(
		func(p cmp.Path) bool {
			for _, ps := range p {
				if sf, ok := ps.(cmp.StructField); ok && sf.Name() == "JSON" {
					return true
				}
			}
			return false
		},
		cmp.Ignore(),
	)
	// Hack: filter out unexported fields, reference: https://github.com/google/go-cmp/issues/313
	var ignoreUnexportedFields cmp.Option = cmp.FilterPath(func(p cmp.Path) bool {
		sf, ok := p.Index(-1).(cmp.StructField)
		if !ok {
			return false
		}
		r, _ := utf8.DecodeRuneInString(sf.Name())
		return !unicode.IsUpper(r)
	}, cmp.Ignore())

	// Define a list of other custom fields that we also want to ignore
	planFieldsToIgnore := []string{"ID", "CreatedAt", "Adjustments", "Metadata", "PlanPhases", "Product", "TrialConfig"}
	if c.orbConfig.Validation.IgnorePlanVersion {
		planFieldsToIgnore = append(planFieldsToIgnore, "Version")
	}
	ignorePlanFields := cmpopts.IgnoreFields(orb_go.Plan{}, planFieldsToIgnore...)

	priceFieldsToIgnore := []string{"ID", "CreatedAt", "Metadata", "ReplacesPriceID"}
	ignorePriceFields := cmpopts.IgnoreFields(orb_go.Price{}, priceFieldsToIgnore...)

	// Sort slices so the order of price doesn't matter
	sortSlices := cmpopts.SortSlices(func(a, b orb_go.Price) bool {
		return a.Name < b.Name
	})
	cmpOpts := cmp.Options{
		ignoreJSONFields,
		ignoreUnexportedFields,
		ignorePlanFields,
		ignorePriceFields,
		sortSlices,
	}

	if diff := cmp.Diff(*configPlan, *externalPlan, cmpOpts); diff != "" {
		log.Warn().
			Str("plan_id", configPlan.ExternalPlanID).
			Str("diff", diff).
			Msgf("Plan configuration mismatch detected for plan %s between config and external system", configPlan.ExternalPlanID)
		result.Status = PlanValidationStatusMismatch
		result.Warnings = append(result.Warnings, fmt.Sprintf("Plan %s has configuration differences with external system", configPlan.ExternalPlanID))
	} else {
		log.Info().
			Str("plan_id", configPlan.ExternalPlanID).
			Msgf("Plan configuration matches external system for plan %s", configPlan.ExternalPlanID)
		result.Status = PlanValidationStatusValid
	}

	return result
}

// FilterResultsByStatus filters validation results by status
func (c *PlanComparator) FilterResultsByStatus(results []PlanValidationResult, status PlanValidationStatus) []PlanValidationResult {
	filtered := make([]PlanValidationResult, 0)
	for _, result := range results {
		if result.Status == status {
			filtered = append(filtered, result)
		}
	}
	return filtered
}
