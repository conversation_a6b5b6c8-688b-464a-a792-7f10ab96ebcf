package main

import (
	"context"
	"fmt"
	"time"

	"github.com/augmentcode/augment/services/billing/lib/orb"
	orb_config "github.com/augmentcode/augment/services/billing/lib/orb/config"
	orb_go "github.com/orbcorp/orb-go"
	"github.com/rs/zerolog/log"

	"github.com/prometheus/client_golang/prometheus"
	"github.com/prometheus/client_golang/prometheus/promauto"
)

var plansValidationCounter = promauto.NewCounterVec(
	prometheus.CounterOpts{
		Name: "au_plans_validation_total",
		Help: "Total number of plans validation attempts",
	},
	[]string{"status"},
)

// ValidationReport represents the overall validation results
type ValidationReport struct {
	Success              bool
	ValidationTime       time.Time
	TotalPlansInConfig   int
	TotalPlansInExternal int
	PlanResults          []PlanValidationResult
	CreatedPlans         []string
	Errors               []string
	Warnings             []string
}

// HasErrors returns true if the validation report contains any errors
func (r *ValidationReport) HasErrors() bool {
	return len(r.Errors) > 0
}

// HasWarnings returns true if the validation report contains any warnings
func (r *ValidationReport) HasWarnings() bool {
	return len(r.Warnings) > 0
}

// AddError adds an error to the validation report
func (r *ValidationReport) AddError(err string) {
	r.Errors = append(r.Errors, err)
	r.Success = false
}

// AddWarning adds a warning to the validation report
func (r *ValidationReport) AddWarning(warning string) {
	r.Warnings = append(r.Warnings, warning)
}

// PlansValidator orchestrates the entire validation process
type PlansValidator struct {
	orbConfig  *orb_config.OrbConfig
	comparator *PlanComparator
	orbClient  orb.OrbClient
}

// NewPlansValidator creates a new plans validator
func NewPlansValidator(orbConfig *orb_config.OrbConfig, orbClient orb.OrbClient) *PlansValidator {
	comparator := NewPlanComparator(orbConfig)

	return &PlansValidator{
		orbConfig:  orbConfig,
		comparator: comparator,
		orbClient:  orbClient,
	}
}

func (v *PlansValidator) validateConfig(configPlans []orb_config.PlanConfig, report *ValidationReport) error {
	for _, plan := range configPlans {
		// Validate price model is unit
		for _, price := range plan.OrbPlan.Prices {
			if price.ModelType != "unit" {
				report.AddError(fmt.Sprintf("Plan '%s' contains unsupported pricing model '%s'. Only 'unit' pricing is currently supported. Please update the plan configuration.",
					plan.ID, price.ModelType))
			}
		}

		// Validate that ignored fields are not set in config
		if err := v.validateIgnoredFields(&plan, report); err != nil {
			return err
		}
	}

	return nil
}

// validateIgnoredFields checks if any fields that are ignored by the comparator are set in the config
func (v *PlansValidator) validateIgnoredFields(plan *orb_config.PlanConfig, report *ValidationReport) error {
	planConfig := &plan.OrbPlan

	// Check ignored plan fields
	if planConfig.ID != "" {
		report.AddError(fmt.Sprintf("Plan '%s' has 'ID' field set to '%s'. This field is ignored during comparison and should not be set in configuration",
			plan.ID, planConfig.ID))
	}

	if !planConfig.CreatedAt.IsZero() {
		report.AddError(fmt.Sprintf("Plan '%s' has 'CreatedAt' field set to '%s'. This field is ignored during comparison and should not be set in configuration",
			plan.ID, planConfig.CreatedAt))
	}

	if len(planConfig.Adjustments) > 0 {
		report.AddError(fmt.Sprintf("Plan '%s' has 'Adjustments' field set to %v. This field is ignored during comparison and should not be set in configuration",
			plan.ID, planConfig.Adjustments))
	}

	if len(planConfig.Metadata) > 0 {
		report.AddError(fmt.Sprintf("Plan '%s' has 'Metadata' field set to %v. This field is ignored during comparison and should not be set in configuration",
			plan.ID, planConfig.Metadata))
	}

	if len(planConfig.PlanPhases) > 0 {
		report.AddError(fmt.Sprintf("Plan '%s' has 'PlanPhases' field set to %v. This field is ignored during comparison and should not be set in configuration",
			plan.ID, planConfig.PlanPhases))
	}

	// Check if Product field is set (non-zero value)
	if planConfig.Product.ID != "" || planConfig.Product.Name != "" {
		report.AddError(fmt.Sprintf("Plan '%s' has 'Product' field set to %v. This field is ignored during comparison and should not be set in configuration",
			plan.ID, planConfig.Product))
	}

	// Check if TrialConfig field is set (non-zero value)
	if planConfig.TrialConfig.TrialPeriod != 0 || planConfig.TrialConfig.TrialPeriodUnit != "" {
		report.AddError(fmt.Sprintf("Plan '%s' has 'TrialConfig' field set to %v. This field is ignored during comparison and should not be set in configuration",
			plan.ID, planConfig.TrialConfig))
	}

	// Check ignored price fields
	for i, price := range planConfig.Prices {
		if price.ID != "" {
			report.AddError(fmt.Sprintf("Plan '%s' price %d has 'ID' field set to '%s'. This field is ignored during comparison and should not be set in configuration",
				plan.ID, i, price.ID))
		}

		if !price.CreatedAt.IsZero() {
			report.AddError(fmt.Sprintf("Plan '%s' price %d has 'CreatedAt' field set to '%s'. This field is ignored during comparison and should not be set in configuration",
				plan.ID, i, price.CreatedAt))
		}

		if price.Metadata != nil {
			// price.Metadata has runtime type of [map[string]string]
			if metadata, ok := price.Metadata.(map[string]string); (ok && len(metadata) > 0) || !ok {
				report.AddError(fmt.Sprintf("Plan '%s' price %d has 'Metadata' field set to %v. This field is ignored during comparison and should not be set in configuration",
					plan.ID, i, price.Metadata))
			}
		}

		if price.ReplacesPriceID != "" {
			report.AddError(fmt.Sprintf("Plan '%s' price %d has 'ReplacesPriceID' field set to '%s'. This field is ignored during comparison and should not be set in configuration",
				plan.ID, i, price.ReplacesPriceID))
		}
	}

	return nil
}

// ValidatePlans performs the complete validation process
func (v *PlansValidator) ValidatePlans(ctx context.Context) (*ValidationReport, error) {
	log.Info().
		Bool("dry_run", v.orbConfig.Validation.DryRun).
		Bool("auto_create", v.orbConfig.Validation.AutoCreateMissing).
		Msg("Starting plans validation")

	// Defer the metric recording
	status := "failure"
	defer func() {
		plansValidationCounter.WithLabelValues(status).Inc()
	}()

	report := &ValidationReport{
		ValidationTime: time.Now(),
		Success:        true,
		Errors:         make([]string, 0),
		Warnings:       make([]string, 0),
	}

	// Step 1: Parse configuration
	configPlans := v.orbConfig.Plans
	report.TotalPlansInConfig = len(configPlans)

	// Step 2: Validate configuration
	if err := v.validateConfig(configPlans, report); err != nil {
		return report, err
	}

	// Step 3: Fetch external plans
	externalPlans, err := v.fetchExternalPlans(ctx, report)
	if err != nil {
		return report, err
	}
	report.TotalPlansInExternal = len(externalPlans)

	// Step 4: Compare plans
	results := v.comparePlans(configPlans, externalPlans, report)
	report.PlanResults = results

	// Step 5: Handle missing/mismatched plans if not in dry run mode
	if !v.orbConfig.Validation.DryRun {
		if err := v.handlePlanActions(ctx, results, report); err != nil {
			return report, err
		}
	}

	// Step 6: Generate final report
	// Explicitly check for mismatch and missing plans for metric reporting
	if !report.HasErrors() && !report.HasWarnings() {
		status = "success"
	}

	// Check if warnings should fail validation
	if v.orbConfig.Validation.FailOnWarnings && report.HasWarnings() {
		report.Success = false
		report.AddError("Validation configured to fail on warnings")
	}

	// Calculate summary statistics from plan results
	validPlans := len(v.comparator.FilterResultsByStatus(report.PlanResults, PlanValidationStatusValid))
	missingPlans := len(v.comparator.FilterResultsByStatus(report.PlanResults, PlanValidationStatusMissing))
	mismatchedPlans := len(v.comparator.FilterResultsByStatus(report.PlanResults, PlanValidationStatusMismatch))
	extraPlans := len(v.comparator.FilterResultsByStatus(report.PlanResults, PlanValidationStatusExtra))

	log.Info().
		Bool("success", report.Success).
		Int("valid_plans", validPlans).
		Int("missing_plans", missingPlans).
		Int("mismatched_plans", mismatchedPlans).
		Int("extra_plans", extraPlans).
		Interface("warnings", report.Warnings).
		Interface("errors", report.Errors).
		Msg("Plans validation completed")

	return report, nil
}

// fetchExternalPlans fetches plans from the external billing service
func (v *PlansValidator) fetchExternalPlans(ctx context.Context, report *ValidationReport) ([]orb_go.Plan, error) {
	log.Info().Msg("Fetching plans from external billing service")

	orbPlans, err := v.orbClient.ListAllPlans(ctx)
	if err != nil {
		report.AddError(fmt.Sprintf("Failed to fetch plans from external billing service: %s", err.Error()))
		return nil, fmt.Errorf("failed to fetch external plans: %w", err)
	}

	log.Info().
		Int("total_external_plans", len(orbPlans)).
		Msg("External plans fetched successfully")

	return orbPlans, nil
}

// comparePlans compares configuration plans against external plans
func (v *PlansValidator) comparePlans(configPlans []orb_config.PlanConfig, externalPlans []orb_go.Plan, report *ValidationReport) []PlanValidationResult {
	log.Info().Msg("Comparing configuration plans against external plans")

	results := v.comparator.ComparePlans(configPlans, externalPlans)

	// Add results to report and collect warnings
	report.PlanResults = append(report.PlanResults, results...)
	for _, result := range results {
		// Add warnings from individual results
		for _, warning := range result.Warnings {
			report.AddWarning(fmt.Sprintf("Plan %s: %s", result.PlanID, warning))
		}
	}

	// Calculate summary statistics for logging
	missingPlans := len(v.comparator.FilterResultsByStatus(results, PlanValidationStatusMissing))
	extraPlans := len(v.comparator.FilterResultsByStatus(results, PlanValidationStatusExtra))

	log.Info().
		Int("total_results", len(results)).
		Int("missing_plans", missingPlans).
		Int("extra_plans", extraPlans).
		Msg("Plan comparison completed")

	return results
}

// handlePlanActions handles creation of plans based on validation results
func (v *PlansValidator) handlePlanActions(ctx context.Context, results []PlanValidationResult, report *ValidationReport) error {
	log.Info().
		Bool("auto_create", v.orbConfig.Validation.AutoCreateMissing).
		Msg("Handling plan actions")

	// Handle missing plans (creation)
	if v.orbConfig.Validation.AutoCreateMissing {
		missingResults := v.comparator.FilterResultsByStatus(results, PlanValidationStatusMissing)
		for _, result := range missingResults {
			if err := v.createPlan(ctx, result, report); err != nil {
				log.Error().
					Err(err).
					Str("plan_id", result.PlanID).
					Msg("Failed to create missing plan")
				// Continue with other plans even if one fails
			}
		}
	}

	return nil
}

// createPlan creates a missing plan in the external billing service
func (v *PlansValidator) createPlan(ctx context.Context, result PlanValidationResult, report *ValidationReport) error {
	planID := result.PlanID
	configPlan := result.ConfigPlan

	log.Info().
		Str("plan_id", planID).
		Msg("Creating missing plan")

	if configPlan == nil {
		return fmt.Errorf("config plan is nil for plan ID: %s", planID)
	}

	// Generate idempotency key for safe retries across multiple pods
	// Use a deterministic key based on the plan ID and plan-specific suffix
	// to ensure consistency across all pods and deployments
	idempotencyKey := fmt.Sprintf("plan-creation-%s-%s", planID, result.IdempotencyKeySuffix)

	// Create the plan via Orb API
	createdPlan, err := v.orbClient.CreatePlan(ctx, *configPlan, &idempotencyKey)
	if err != nil {
		// Check if this might be a race condition where another pod already created the plan
		// In that case, we should verify if the plan now exists before failing
		log.Warn().
			Err(err).
			Str("plan_id", planID).
			Str("idempotency_key", idempotencyKey).
			Msg("Plan creation failed, checking if plan was created by another pod")

		// Try to fetch the plan to see if it was created by another pod
		existingPlans, fetchErr := v.orbClient.ListAllPlans(ctx)
		if fetchErr != nil {
			log.Error().
				Err(fetchErr).
				Str("plan_id", planID).
				Msg("Failed to fetch plans after creation failure")
			return fmt.Errorf("failed to create plan via Orb API: %w", err)
		}

		// Check if the plan now exists (created by another pod)
		for _, existingPlan := range existingPlans {
			if existingPlan.ExternalPlanID == planID {
				log.Info().
					Str("plan_id", planID).
					Str("orb_plan_id", existingPlan.ID).
					Msg("Plan was created by another pod, continuing")
				report.CreatedPlans = append(report.CreatedPlans, planID)
				return nil
			}
		}

		// Plan still doesn't exist, this is a real error
		log.Error().
			Err(err).
			Str("plan_id", planID).
			Str("idempotency_key", idempotencyKey).
			Msg("Failed to create plan via Orb API and plan does not exist")
		return fmt.Errorf("failed to create plan via Orb API: %w", err)
	}

	log.Info().
		Str("plan_id", planID).
		Str("orb_plan_id", createdPlan.ID).
		Str("external_plan_id", createdPlan.ExternalPlanID).
		Msg("Successfully created plan via Orb API")

	report.CreatedPlans = append(report.CreatedPlans, planID)
	return nil
}

// HasValidationIssues determines if there are validation issues that need attention
func (v *PlansValidator) HasValidationIssues(report *ValidationReport) bool {
	if !report.Success {
		return true
	}

	// If configured to fail on warnings and there are warnings, report issues
	if v.orbConfig.Validation.FailOnWarnings && report.HasWarnings() {
		return true
	}

	return false
}

// ValidatePlans is a convenience function for billing plans validation
// It returns an error for logging purposes but does not block deployment
func ValidatePlans(ctx context.Context, orbConfig *orb_config.OrbConfig, orbClient orb.OrbClient) error {
	// Create validator with orb configuration
	validator := NewPlansValidator(orbConfig, orbClient)

	// Run validation with timeout
	validationCtx, cancel := context.WithTimeout(ctx, 30*time.Second)
	defer cancel()

	report, err := validator.ValidatePlans(validationCtx)
	if err != nil {
		return fmt.Errorf("plans validation failed: %w", err)
	}

	// Check if there are validation issues
	if validator.HasValidationIssues(report) {
		// Calculate summary statistics for error details
		missingPlans := len(validator.comparator.FilterResultsByStatus(report.PlanResults, PlanValidationStatusMissing))
		mismatchedPlans := len(validator.comparator.FilterResultsByStatus(report.PlanResults, PlanValidationStatusMismatch))

		// Log detailed report for debugging
		log.Error().
			Int("missing_plans", missingPlans).
			Int("mismatched_plans", mismatchedPlans).
			Msg("Plans validation failed - see details above")

		return fmt.Errorf("plans validation failed: %d missing plans, %d mismatched plans",
			missingPlans, mismatchedPlans)
	}

	// Calculate summary statistics for success logging
	validPlans := len(validator.comparator.FilterResultsByStatus(report.PlanResults, PlanValidationStatusValid))

	// Log success summary
	log.Info().
		Int("valid_plans", validPlans).
		Int("created_plans", len(report.CreatedPlans)).
		Msg("Plans validation summary")

	return nil
}
