package main

import (
	"encoding/base64"
	"net/http"
	"net/http/httptest"
	"os"
	"path/filepath"
	"testing"

	"github.com/gorilla/sessions"
)

// createTestConfig creates a test configuration with temporary secret files
func createTestConfig(t *testing.T) *Config {
	tmpDir := t.TempDir()
	signingSecretFile := filepath.Join(tmpDir, "signing_secret")
	encryptionSecretFile := filepath.Join(tmpDir, "encryption_secret")
	signingKey := "test-signing-key-32-bytes-long!!"
	encryptionKey := "test-encrypt-key-32-bytes-long!!"

	// Encode secrets as base64 URL-safe without padding (as expected by the main code)
	signingKeyBase64 := base64.RawURLEncoding.EncodeToString([]byte(signingKey))
	encryptionKeyBase64 := base64.RawURLEncoding.EncodeToString([]byte(encryptionKey))

	err := os.WriteFile(signingSecretFile, []byte(signingKeyBase64), 0o600)
	if err != nil {
		t.Fatalf("Failed to write signing secret file: %v", err)
	}

	err = os.WriteFile(encryptionSecretFile, []byte(encryptionKeyBase64), 0o600)
	if err != nil {
		t.Fatalf("Failed to write encryption secret file: %v", err)
	}

	return &Config{
		SessionSigningSecretKeyPath:    signingSecretFile,
		SessionEncryptionSecretKeyPath: encryptionSecretFile,
	}
}

func TestSetupSessionStore(t *testing.T) {
	config := createTestConfig(t)

	sessionManager, err := NewSessionManager(config)
	if err != nil {
		t.Fatalf("NewSessionManager failed: %v", err)
	}

	if sessionManager == nil {
		t.Fatal("sessionManager should not be nil after setup")
	}

	// Verify it has a store
	if sessionManager.store == nil {
		t.Fatal("sessionManager.store should not be nil")
	}

	// Verify it's a cookie store
	_, ok := sessionManager.store.(*sessions.CookieStore)
	if !ok {
		t.Fatal("sessionManager.store should be a CookieStore")
	}
}

func TestSessionMiddleware(t *testing.T) {
	config := createTestConfig(t)

	sessionManager, err := NewSessionManager(config)
	if err != nil {
		t.Fatalf("NewSessionManager failed: %v", err)
	}

	// Create a test handler that checks for session in context
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		session := GetSessionFromContext(r.Context())
		if session == nil {
			t.Error("session should be available in context")
			return
		}
		w.WriteHeader(http.StatusOK)
	})

	// Wrap with session middleware
	handler := sessionManager.Middleware()(testHandler)

	// Create test request
	req := httptest.NewRequest("GET", "/test", nil)
	w := httptest.NewRecorder()

	// Execute request
	handler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("expected status 200, got %d", w.Code)
	}
}

func TestSessionTokenFunctions(t *testing.T) {
	config := createTestConfig(t)

	sessionManager, err := NewSessionManager(config)
	if err != nil {
		t.Fatalf("NewSessionManager failed: %v", err)
	}

	// Create a new session
	req := httptest.NewRequest("GET", "/test", nil)
	session, err := sessionManager.GetSession(req)
	if err != nil {
		t.Fatalf("failed to create new session: %v", err)
	}

	// Test access token
	testToken := "test-access-token-12345"

	// Test setting token in session
	SetTokenInSession(session, testToken)

	// Test getting token from session
	retrievedToken := GetTokenFromSession(session)
	if retrievedToken != testToken {
		t.Errorf("expected token %s, got %s", testToken, retrievedToken)
	}

	// Test authentication check
	if !IsUserAuthenticated(session) {
		t.Error("user should be authenticated after setting token")
	}

	// Test clearing token from session
	ClearTokenFromSession(session)
	if IsUserAuthenticated(session) {
		t.Error("user should not be authenticated after clearing session")
	}

	retrievedToken = GetTokenFromSession(session)
	if retrievedToken != "" {
		t.Error("getTokenFromSession should return empty string after clearing session")
	}
}

func TestHealthCheckSkipsSession(t *testing.T) {
	config := createTestConfig(t)

	sessionManager, err := NewSessionManager(config)
	if err != nil {
		t.Fatalf("NewSessionManager failed: %v", err)
	}

	// Create a test handler that checks if session is in context
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		session := GetSessionFromContext(r.Context())
		if session != nil {
			t.Error("session should not be available in context for health check")
			return
		}
		w.WriteHeader(http.StatusOK)
	})

	// Wrap with session middleware
	handler := sessionManager.Middleware()(testHandler)

	// Create health check request
	req := httptest.NewRequest("GET", "/health", nil)
	w := httptest.NewRecorder()

	// Execute request
	handler.ServeHTTP(w, req)

	if w.Code != http.StatusOK {
		t.Errorf("expected status 200, got %d", w.Code)
	}
}
