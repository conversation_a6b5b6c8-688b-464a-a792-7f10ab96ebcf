package main

import (
	"crypto/rand"
	"crypto/sha256"
	"encoding/base64"
	"fmt"
	"net/http"
	"net/url"
	"strings"

	"github.com/rs/zerolog/log"
	"golang.org/x/oauth2"
)

// OAuthService defines the interface for OAuth operations
type OAuthService interface {
	// <PERSON>gin<PERSON>andler initiates the OAuth2 login flow with PKCE
	LoginHandler(w http.ResponseWriter, r *http.Request)
	// CallbackHandler handles the OAuth2 callback with PKCE
	CallbackHandler(w http.ResponseWriter, r *http.Request)
	// LogoutHandler handles user logout
	LogoutHandler(w http.ResponseWriter, r *http.Request)
	// AuthenticationMiddleware checks if user is authenticated and redirects to login if not
	AuthenticationMiddleware(next http.Handler) http.Handler
}

// oauthService implements the OAuthService interface
type oauthService struct {
	config    *oauth2.Config
	logoutURL *url.URL
	hostname  string
}

// NewOAuthService creates a new OAuth service with the given configuration
func NewOAuthService(config *Config) OAuthService {
	oauthConfig := &oauth2.Config{
		ClientID:     config.OAuthClientID,
		ClientSecret: config.OAuthClientSecret, // Can be empty for PKCE
		RedirectURL:  config.OAuthRedirectURL,
		Scopes:       config.OAuthScopes,
		Endpoint: oauth2.Endpoint{
			AuthURL:  config.OAuthAuthURL,
			TokenURL: config.OAuthTokenURL,
		},
	}

	logoutURL, err := url.Parse(config.OAuthLogoutURL)
	if err != nil {
		log.Error().Err(err).Msg("Failed to parse OAuth logout URL")
		logoutURL = &url.URL{Path: "/auth/login"}
	}

	if config.OAuthClientSecret == "" {
		log.Info().Msg("OAuth2 service initialized with PKCE (no client secret)")
	} else {
		log.Info().Msg("OAuth2 service initialized with client secret")
	}

	return &oauthService{
		config:    oauthConfig,
		logoutURL: logoutURL,
		hostname:  config.Hostname,
	}
}

// generateRandomState generates a random state string for OAuth2 CSRF protection
func generateRandomState() (string, error) {
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.EncodeToString(b), nil
}

// PKCE functions

// generateCodeVerifier generates a random code verifier for PKCE
func generateCodeVerifier() (string, error) {
	b := make([]byte, 32)
	_, err := rand.Read(b)
	if err != nil {
		return "", err
	}
	return base64.URLEncoding.WithPadding(base64.NoPadding).EncodeToString(b), nil
}

// generateCodeChallenge generates a code challenge from the verifier using SHA256
func generateCodeChallenge(verifier string) string {
	h := sha256.Sum256([]byte(verifier))
	return base64.URLEncoding.WithPadding(base64.NoPadding).EncodeToString(h[:])
}

// AuthenticationMiddleware checks if user is authenticated and redirects to login if not
func (s *oauthService) AuthenticationMiddleware(next http.Handler) http.Handler {
	return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		// Skip authentication for certain paths
		skipPaths := []string{
			"/health",
			"/auth/login",
			"/auth/callback",
			"/auth/logout",
			"/assets/",
			"/favicon.svg",
			"/manifest.json",
			"/agents/api/",
			"/agents/_app/",
		}
		for _, path := range skipPaths {
			if strings.HasPrefix(r.URL.Path, path) {
				next.ServeHTTP(w, r)
				return
			}
		}

		// Check if user is authenticated
		session := GetSessionFromContext(r.Context())
		if session == nil {
			log.Warn().Msgf("authenticationMiddleware: No session found for %s, redirecting to login", r.URL.Path)
			http.Redirect(w, r, "/auth/login", http.StatusFound)
			return
		}

		if !IsUserAuthenticated(session) {
			log.Warn().Msgf("authenticationMiddleware: User not authenticated for %s, redirecting to login", r.URL.Path)
			http.Redirect(w, r, "/auth/login", http.StatusFound)
			return
		}

		next.ServeHTTP(w, r)
	})
}

// getSchemeAndHost extracts scheme and host from request headers. It
// falls back to https://<hostname> if not found in headers.
// These are used for building redirect URLs to pass to auth central. Auth
// central validates the redirect urls so we don't need to validate them
// here.
// This is needed to support local development with http://localhost.
func (s *oauthService) getSchemeAndHost(r *http.Request) (scheme, host string) {
	scheme = r.Header.Get("X-Forwarded-Protocol")
	if scheme == "" {
		scheme = "https"
	}
	host = r.Header.Get("X-Forwarded-Hostname")
	if host == "" {
		host = s.hostname
	}
	return scheme, host
}

// OAuth handlers

// LoginHandler initiates the OAuth2 login flow with PKCE
func (s *oauthService) LoginHandler(w http.ResponseWriter, r *http.Request) {
	log.Info().Msg("Initiating OAuth login with PKCE")
	session := GetSessionFromContext(r.Context())
	if session == nil {
		http.Error(w, "Session not available", http.StatusInternalServerError)
		return
	}

	// Generate state for CSRF protection
	state, err := generateRandomState()
	if err != nil {
		log.Error().Err(err).Msg("Failed to generate OAuth state")
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}

	// Generate PKCE code verifier and challenge
	codeVerifier, err := generateCodeVerifier()
	if err != nil {
		log.Error().Err(err).Msg("Failed to generate PKCE code verifier")
		http.Error(w, "Internal server error", http.StatusInternalServerError)
		return
	}
	codeChallenge := generateCodeChallenge(codeVerifier)

	// Store state and code verifier in session
	session.Values["oauth_state"] = state
	session.Values["code_verifier"] = codeVerifier

	// Build redirect target from current request
	scheme, host := s.getSchemeAndHost(r)
	redirectURL := fmt.Sprintf("%s://%s/auth/callback", scheme, host)

	// Build authorization URL with PKCE parameters
	authURL := s.config.AuthCodeURL(state,
		oauth2.SetAuthURLParam("code_challenge", codeChallenge),
		oauth2.SetAuthURLParam("code_challenge_method", "S256"),
		oauth2.SetAuthURLParam("redirect_uri", redirectURL),
	)

	http.Redirect(w, r, authURL, http.StatusFound)
}

// CallbackHandler handles the OAuth2 callback with PKCE
func (s *oauthService) CallbackHandler(w http.ResponseWriter, r *http.Request) {
	session := GetSessionFromContext(r.Context())
	if session == nil {
		http.Error(w, "Session not available", http.StatusInternalServerError)
		return
	}

	// Verify state parameter
	state := r.URL.Query().Get("state")
	sessionState, ok := session.Values["oauth_state"].(string)
	if !ok || state != sessionState {
		log.Warn().Msg("OAuth state mismatch")
		http.Error(w, "Invalid state parameter", http.StatusBadRequest)
		return
	}

	// Get code verifier from session
	codeVerifier, ok := session.Values["code_verifier"].(string)
	if !ok {
		log.Warn().Msg("No code verifier found in session")
		http.Error(w, "Invalid session state", http.StatusBadRequest)
		return
	}

	// Clear the state and code verifier from session
	delete(session.Values, "oauth_state")
	delete(session.Values, "code_verifier")

	// Get authorization code
	code := r.URL.Query().Get("code")
	if code == "" {
		log.Warn().Msg("No authorization code received")
		http.Error(w, "No authorization code received", http.StatusBadRequest)
		return
	}

	// Exchange code for token with PKCE
	scheme, host := s.getSchemeAndHost(r)
	callbackURL := fmt.Sprintf("%s://%s/auth/callback", scheme, host)

	token, err := s.config.Exchange(r.Context(), code,
		oauth2.SetAuthURLParam("code_verifier", codeVerifier),
		oauth2.SetAuthURLParam("redirect_uri", callbackURL),
	)
	if err != nil {
		log.Error().Err(err).Msg("Failed to exchange code for token")
		http.Error(w, "Failed to exchange code for token", http.StatusInternalServerError)
		return
	}

	// Store only the access token in session (not user info)
	SetTokenInSession(session, token.AccessToken)

	log.Info().Msg("CallbackHandler: OAuth authentication with PKCE successful")

	redirectURL := fmt.Sprintf("%s://%s/", scheme, host)
	// Redirect to main application
	http.Redirect(w, r, redirectURL, http.StatusFound)
}

// LogoutHandler handles user logout
func (s *oauthService) LogoutHandler(w http.ResponseWriter, r *http.Request) {
	session := GetSessionFromContext(r.Context())
	if session != nil {
		ClearTokenFromSession(session)
	}

	log.Info().Msg("User logged out")

	// Build logout URL with required parameters
	scheme, host := s.getSchemeAndHost(r)
	redirectUrl := url.URL{
		Scheme: scheme,
		Host:   host,
		Path:   "/auth/login",
	}
	logoutURL := *s.logoutURL
	params := url.Values{}
	params.Set("client_id", s.config.ClientID)
	params.Set("redirect_uri", redirectUrl.String()) // Redirect back to login page
	logoutURL.RawQuery = params.Encode()

	// Redirect to auth-central logout
	http.Redirect(w, r, logoutURL.String(), http.StatusFound)
}
