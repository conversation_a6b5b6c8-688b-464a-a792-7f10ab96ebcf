load("//tools/bzl:go.bzl", "go_binary", "go_library", "go_oci_image", "go_test")

go_library(
    name = "web_rpc_proxy_lib",
    testonly = False,
    srcs = [
        "agents_app_proxy.go",
        "billing_service.go",
        "main.go",
        "oauth.go",
        "session.go",
    ],
    data = [
        "//clients/webapp:build",
        "//services/web_rpc_proxy/frontend",
    ],
    importpath = "github.com/augmentcode/augment/services/web_rpc_proxy/backend",
    deps = [
        "//base/cloud/iap:iap_go",
        "//base/feature_flags:feature_flags_go",
        "//base/go/secretstring:secretstring_go",
        "//base/logging:logging_go",
        "//base/proto/redact",
        "//services/auth/central/server:auth_entities_go_proto",
        "//services/auth/central/server:auth_go_grpc",
        "//services/lib/grpc/client:grpc_client_go",
        "//services/lib/grpc/connect_forward:connect_forward_go",
        "//services/lib/grpc/tls_config:grpc_tls_config_go",
        "//services/lib/request_context:request_context_go",
        "//services/lib/tokencache:tokencache_go",
        "//services/tenant_watcher:tenant_watcher_go_proto",
        "//services/tenant_watcher/client:client_go",
        "//services/token_exchange:auth_options_go_proto",
        "//services/token_exchange:token_exchange_go_proto",
        "//services/token_exchange:token_scopes_go_proto",
        "//services/token_exchange/client:client_go",
        "//services/web_rpc_proxy:web_rpc_proxy_go_connect",
        "//services/web_rpc_proxy:web_rpc_proxy_go_connect_proto",
        "@com_connectrpc_connect//:connect",
        "@com_github_gorilla_mux//:go_default_library",
        "@com_github_gorilla_sessions//:sessions",
        "@com_github_rs_zerolog//log",
        "@org_golang_google_grpc//:go_default_library",
        "@org_golang_google_grpc//codes:go_default_library",
        "@org_golang_google_grpc//metadata:go_default_library",
        "@org_golang_google_grpc//status:go_default_library",
        "@org_golang_google_protobuf//encoding/protojson:go_default_library",
        "@org_golang_google_protobuf//proto:go_default_library",
        "@org_golang_google_protobuf//reflect/protoreflect:go_default_library",
        "@org_golang_x_oauth2//:oauth2",
        "@org_uber_go_automaxprocs//:automaxprocs",
    ],
)

go_binary(
    name = "web_rpc_proxy",
    embed = [":web_rpc_proxy_lib"],
)

go_test(
    name = "web_rpc_proxy_test",
    srcs = ["session_test.go"],
    embed = [":web_rpc_proxy_lib"],
    deps = [
        "//services/lib/grpc/connect_forward:connect_forward_go",
        "@com_github_gorilla_sessions//:sessions",
    ],
)

go_oci_image(
    name = "image",
    package_name = package_name(),
    binary = ":web_rpc_proxy",
    visibility = ["//services/web_rpc_proxy:__subpackages__"],
)
