package main

import (
	"context"
	"fmt"

	"connectrpc.com/connect"

	authcentralproto "github.com/augmentcode/augment/services/auth/central/server/proto"
	connectforward "github.com/augmentcode/augment/services/lib/grpc/connect_forward"
)

// BillingServiceHandler implements the BillingService ConnectRPC interface
type BillingServiceHandler struct {
	forwarderFactory *connectforward.ForwarderFactory
}

// NewWebRpcProxyServiceHandler creates a new billing service handler
func NewWebRpcProxyServiceHandler(forwarderFactory *connectforward.ForwarderFactory,
) (*BillingServiceHandler, error) {
	return &BillingServiceHandler{
		forwarderFactory: forwarderFactory,
	}, nil
}

// GetAllPlans implements the GetAllPlans RPC method
func (h *BillingServiceHandler) GetAllOrbPlans(
	ctx context.Context,
	req *connect.Request[authcentralproto.GetAllOrbPlansRequest],
) (*connect.Response[authcentralproto.GetAllOrbPlansResponse], error) {
	resp := authcentralproto.GetAllOrbPlansResponse{}
	return connectforward.Forward(ctx, h.forwarderFactory, req, &resp, nil, nil)
}

// GetUserPlan implements the GetUserPlan RPC method
func (h *BillingServiceHandler) GetUserOrbPlanInfo(
	ctx context.Context,
	req *connect.Request[authcentralproto.GetUserOrbPlanInfoRequest],
) (*connect.Response[authcentralproto.GetUserOrbPlanInfoResponse], error) {
	resp := authcentralproto.GetUserOrbPlanInfoResponse{}
	return connectforward.Forward(ctx, h.forwarderFactory, req, &resp, func(ctx context.Context, userInfo *connectforward.ForwardUserInfo, req *authcentralproto.GetUserOrbPlanInfoRequest) (*connectforward.RouteInfo, error) {
		if req.UserId != "" {
			return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("user_id is not allowed"))
		}
		req.UserId = userInfo.UserID
		return nil, nil
	}, nil)
}

// PutUserOnPlan implements the PutUserOnPlan RPC method
func (h *BillingServiceHandler) PutUserOnPlan(
	ctx context.Context,
	req *connect.Request[authcentralproto.PutUserOnPlanRequest],
) (*connect.Response[authcentralproto.PutUserOnPlanResponse], error) {
	resp := authcentralproto.PutUserOnPlanResponse{}
	return connectforward.Forward(ctx, h.forwarderFactory, req, &resp, func(ctx context.Context, userInfo *connectforward.ForwardUserInfo, req *authcentralproto.PutUserOnPlanRequest) (*connectforward.RouteInfo, error) {
		if req.UserId != "" {
			return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("user_id is not allowed"))
		}
		req.UserId = userInfo.UserID
		return nil, nil
	}, nil)
}

// GetUserSubscriptionInfo implements the GetUserSubscriptionInfo RPC method
func (h *BillingServiceHandler) GetUserOrbSubscriptionInfo(
	ctx context.Context,
	req *connect.Request[authcentralproto.GetUserOrbSubscriptionInfoRequest],
) (*connect.Response[authcentralproto.GetUserOrbSubscriptionInfoResponse], error) {
	resp := authcentralproto.GetUserOrbSubscriptionInfoResponse{}
	return connectforward.Forward(ctx, h.forwarderFactory, req, &resp, func(ctx context.Context, userInfo *connectforward.ForwardUserInfo, req *authcentralproto.GetUserOrbSubscriptionInfoRequest) (*connectforward.RouteInfo, error) {
		if req.UserId != "" {
			return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("user_id is not allowed"))
		}
		req.UserId = userInfo.UserID
		return nil, nil
	}, nil)
}

// GetUserCreditsInfo implements the GetUserCreditsInfo RPC method
func (h *BillingServiceHandler) GetUserOrbCreditsInfo(
	ctx context.Context,
	req *connect.Request[authcentralproto.GetUserOrbCreditsInfoRequest],
) (*connect.Response[authcentralproto.GetUserOrbCreditsInfoResponse], error) {
	resp := authcentralproto.GetUserOrbCreditsInfoResponse{}
	return connectforward.Forward(ctx, h.forwarderFactory, req, &resp, func(ctx context.Context, userInfo *connectforward.ForwardUserInfo, req *authcentralproto.GetUserOrbCreditsInfoRequest) (*connectforward.RouteInfo, error) {
		if req.UserId != "" {
			return nil, connect.NewError(connect.CodeInvalidArgument, fmt.Errorf("user_id is not allowed"))
		}
		req.UserId = userInfo.UserID
		return nil, nil
	}, nil)
}
