package main

import (
	"context"
	"net/http"
	"net/http/httptest"
	"testing"

	"github.com/gorilla/sessions"
)

// createTestOAuthConfig creates a test OAuth configuration
func createTestOAuthConfig() *Config {
	return &Config{
		OAuthClientID:     "test-client-id",
		OAuthClientSecret: "test-client-secret",
		OAuthAuthURL:      "https://example.com/auth",
		OAuthTokenURL:     "https://example.com/token",
		OAuthRedirectURL:  "https://example.com/callback",
		OAuthScopes:       []string{"openid", "profile", "email"},
	}
}

func TestAuthenticationMiddleware(t *testing.T) {
	config := createTestOAuthConfig()
	service := NewOAuthService(config)

	// Test handler that should be called after authentication
	testHandler := http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		w.WriteHeader(http.StatusOK)
		w.Write([]byte("authenticated"))
	})

	// Wrap with authentication middleware
	handler := service.AuthenticationMiddleware(testHandler)

	t.Run("should skip authentication for health check", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/health", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("expected status 200 for health check, got %d", w.Code)
		}
		if w.Body.String() != "authenticated" {
			t.Errorf("expected 'authenticated' response for health check, got %s", w.Body.String())
		}
	})

	t.Run("should skip authentication for auth paths", func(t *testing.T) {
		authPaths := []string{"/auth/login", "/auth/callback", "/auth/logout", "/assets/test.js", "/favicon.svg", "/manifest.json"}

		for _, path := range authPaths {
			req := httptest.NewRequest("GET", path, nil)
			w := httptest.NewRecorder()

			handler.ServeHTTP(w, req)

			if w.Code != http.StatusOK {
				t.Errorf("expected status 200 for path %s, got %d", path, w.Code)
			}
		}
	})

	t.Run("should redirect to login when no session", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/protected", nil)
		w := httptest.NewRecorder()

		handler.ServeHTTP(w, req)

		if w.Code != http.StatusFound {
			t.Errorf("expected status 302 for protected path without session, got %d", w.Code)
		}

		location := w.Header().Get("Location")
		if location != "/auth/login" {
			t.Errorf("expected redirect to /auth/login, got %s", location)
		}
	})

	t.Run("should redirect to login when user not authenticated", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/protected", nil)

		// Add empty session to context
		session := &sessions.Session{Values: make(map[interface{}]interface{})}
		ctx := context.WithValue(req.Context(), "session", session)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()
		handler.ServeHTTP(w, req)

		if w.Code != http.StatusFound {
			t.Errorf("expected status 302 for protected path with unauthenticated session, got %d", w.Code)
		}

		location := w.Header().Get("Location")
		if location != "/auth/login" {
			t.Errorf("expected redirect to /auth/login, got %s", location)
		}
	})

	t.Run("should allow access when user is authenticated", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/protected", nil)

		// Add authenticated session to context
		session := &sessions.Session{Values: make(map[interface{}]interface{})}
		session.Values["authenticated"] = true
		session.Values["access_token"] = "test-token"
		ctx := context.WithValue(req.Context(), "session", session)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()
		handler.ServeHTTP(w, req)

		if w.Code != http.StatusOK {
			t.Errorf("expected status 200 for protected path with authenticated session, got %d", w.Code)
		}

		if w.Body.String() != "authenticated" {
			t.Errorf("expected 'authenticated' response, got %s", w.Body.String())
		}
	})
}

func TestLoginHandler(t *testing.T) {
	config := createTestOAuthConfig()
	service := NewOAuthService(config)

	t.Run("should return error when no session", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/auth/login", nil)
		w := httptest.NewRecorder()

		service.LoginHandler(w, req)

		if w.Code != http.StatusInternalServerError {
			t.Errorf("expected status 500 when no session, got %d", w.Code)
		}
	})

	t.Run("should redirect to OAuth provider when session exists", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/auth/login", nil)

		// Add session to context
		session := &sessions.Session{Values: make(map[interface{}]interface{})}
		ctx := context.WithValue(req.Context(), "session", session)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()
		service.LoginHandler(w, req)

		if w.Code != http.StatusFound {
			t.Errorf("expected status 302 for login redirect, got %d", w.Code)
		}

		location := w.Header().Get("Location")
		if location == "" {
			t.Error("expected redirect location to be set")
		}

		// Verify OAuth state and code verifier are stored in session
		if session.Values["oauth_state"] == nil {
			t.Error("oauth_state should be stored in session")
		}

		if session.Values["code_verifier"] == nil {
			t.Error("code_verifier should be stored in session")
		}
	})
}

func TestLogoutHandler(t *testing.T) {
	config := createTestOAuthConfig()
	service := NewOAuthService(config)

	t.Run("should return error when no session", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/auth/logout", nil)
		w := httptest.NewRecorder()

		service.LogoutHandler(w, req)

		if w.Code != http.StatusInternalServerError {
			t.Errorf("expected status 500 when no session, got %d", w.Code)
		}
	})

	t.Run("should clear session and redirect to login", func(t *testing.T) {
		req := httptest.NewRequest("GET", "/auth/logout", nil)

		// Add authenticated session to context
		session := &sessions.Session{Values: make(map[interface{}]interface{})}
		session.Values["authenticated"] = true
		session.Values["access_token"] = "test-token"
		ctx := context.WithValue(req.Context(), "session", session)
		req = req.WithContext(ctx)

		w := httptest.NewRecorder()
		service.LogoutHandler(w, req)

		if w.Code != http.StatusFound {
			t.Errorf("expected status 302 for logout redirect, got %d", w.Code)
		}

		location := w.Header().Get("Location")
		if location != "/auth/login" {
			t.Errorf("expected redirect to /auth/login, got %s", location)
		}

		// Verify session is cleared
		if session.Values["authenticated"] != nil {
			t.Error("authenticated should be cleared from session")
		}

		if session.Values["access_token"] != nil {
			t.Error("access_token should be cleared from session")
		}
	})
}
