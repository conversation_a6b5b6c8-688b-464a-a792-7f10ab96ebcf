package main

import (
	"context"
	"encoding/json"
	"fmt"
	"net/http"
	"os"
	"strings"
	"time"

	"connectrpc.com/connect"
	"github.com/gorilla/mux"
	"github.com/gorilla/sessions"
	"github.com/rs/zerolog/log"
	_ "go.uber.org/automaxprocs"
	"google.golang.org/grpc"
	"google.golang.org/grpc/metadata"

	"github.com/augmentcode/augment/base/cloud/iap"
	featureflags "github.com/augmentcode/augment/base/feature_flags"
	"github.com/augmentcode/augment/base/go/secretstring"
	"github.com/augmentcode/augment/base/logging"
	authcentralproto "github.com/augmentcode/augment/services/auth/central/server/proto"
	grpcclient "github.com/augmentcode/augment/services/lib/grpc/client"
	connectforwarder "github.com/augmentcode/augment/services/lib/grpc/connect_forward"
	tlsconfig "github.com/augmentcode/augment/services/lib/grpc/tls_config"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tokencache "github.com/augmentcode/augment/services/lib/tokencache"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	connectproto "github.com/augmentcode/augment/services/web_rpc_proxy/connectproto"
	webrpcproxyproto "github.com/augmentcode/augment/services/web_rpc_proxy/proto"
)

type Config struct {
	Port                   int                     `json:"port"`
	HttpsServerKey         string                  `json:"https_server_key"`
	HttpsServerCert        string                  `json:"https_server_cert"`
	ClientMtls             *tlsconfig.ClientConfig `json:"client_mtls"`
	IapAudience            string                  `json:"iap_audience"`
	IapJwtVerifierDisabled bool                    `json:"iap_jwt_verifier_disabled"`
	TenantWatcherGrpcUrl   string                  `json:"tenant_watcher_grpc_url"`
	GrpcDebugGrpcUrl       string                  `json:"grpc_debug_grpc_url"`
	TokenExchangeGrpcUrl   string                  `json:"token_exchange_grpc_url"`
	AuthCentralGrpcUrl     string                  `json:"auth_central_grpc_url"`

	FeatureFlagsSdkKeyPath      string `json:"feature_flags_sdk_key_path"`
	DynamicFeatureFlagsEndpoint string `json:"dynamic_feature_flags_endpoint"`

	// Map of cloud to internal domain suffix
	InternalDomainSuffixes map[string]string `json:"internal_domain_suffixes"`

	// Map of cloud to api domain for api proxy
	ApiDomainSuffixes map[string]string `json:"api_domain_suffixes"`

	// Used to help guide users to the right env (STAGING, PROD)
	Env              string            `json:"env"`
	IngressHostnames map[string]string `json:"ingress_hostnames"`

	// Session configuration
	SessionSigningSecretKeyPath    string `json:"session_signing_secret_key_path"`
	SessionEncryptionSecretKeyPath string `json:"session_encryption_secret_key_path"`

	// OAuth2 configuration
	OAuthClientID     string   `json:"oauth_client_id"`
	OAuthClientSecret string   `json:"oauth_client_secret,omitempty"` // Optional for PKCE
	OAuthAuthURL      string   `json:"oauth_auth_url"`
	OAuthTokenURL     string   `json:"oauth_token_url"`
	OAuthRedirectURL  string   `json:"oauth_redirect_url"`
	OAuthScopes       []string `json:"oauth_scopes"`
	OAuthLogoutURL    string   `json:"oauth_logout_url"`

	Hostname string `json:"hostname"`
}

func loadConfig(filename string) (*Config, error) {
	file, err := os.Open(filename)
	if err != nil {
		return nil, err
	}
	defer file.Close()
	var config Config
	if err := json.NewDecoder(file).Decode(&config); err != nil {
		return nil, err
	}
	log.Info().Msgf("Loaded config: %v", config)
	return &config, nil
}

func checkPath(r *http.Request) error {
	if r.URL.Path == "/" {
		return nil
	}
	if r.URL.Path[0] != '/' {
		return fmt.Errorf("path must start with /")
	}
	if strings.Contains(r.URL.Path[1:], "..") {
		return fmt.Errorf("path must not contain '..'")
	}
	return nil
}

func serveAssets(w http.ResponseWriter, r *http.Request) {
	log.Info().Msgf("Serving %s", r.URL.Path)
	err := checkPath(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}
	l := "services/web_rpc_proxy/frontend/dist/" + r.URL.Path[1:]
	http.ServeFile(w, r, l)
}

func serveIndex(w http.ResponseWriter, r *http.Request) {
	log.Info().Msgf("Serving index.html")
	http.ServeFile(w, r, "services/web_rpc_proxy/frontend/dist/index.html")
}

func serveAgentsAssets(w http.ResponseWriter, r *http.Request) {
	log.Info().Msgf("Serving agents asset: %s", r.URL.Path)
	err := checkPath(r)
	if err != nil {
		http.Error(w, err.Error(), http.StatusBadRequest)
		return
	}

	// Remove /agents prefix to get the asset path
	path := strings.TrimPrefix(r.URL.Path, "/agents")
	if path == "" || path == "/" {
		path = "/index.html"
	}

	// Set appropriate caching headers based on asset type
	if strings.HasPrefix(path, "/_app/") {
		// SvelteKit hashed assets - cache forever
		w.Header().Set("Cache-Control", "public, max-age=********, immutable")
	} else {
		// Non-hashed assets - cache for 1 hour
		w.Header().Set("Cache-Control", "public, max-age=3600")
	}

	filePath := "clients/webapp/dist" + path
	http.ServeFile(w, r, filePath)
}

func serveAgentsSPA(w http.ResponseWriter, r *http.Request) {
	log.Info().Msgf("Serving agents SPA fallback for: %s", r.URL.Path)
	// SPA fallback - serve index.html for client-side routing
	w.Header().Set("Cache-Control", "public, max-age=3600")
	http.ServeFile(w, r, "clients/webapp/dist/index.html")
}

func healthCheck(w http.ResponseWriter, r *http.Request) {
	w.WriteHeader(http.StatusOK)
	w.Write([]byte("ok"))
}

func iapVerificationMiddlewareFunc(config *Config) (func(http.Handler) http.Handler, error) {
	verifier, err := iap.New([]string{config.IapAudience})
	if err != nil {
		return nil, err
	}

	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Ignore for health checks
			if r.URL.Path == "/health" {
				next.ServeHTTP(w, r)
				return
			}

			// Get the IAP authentication headers
			iapToken := r.Header.Get("X-Goog-IAP-JWT-Assertion")

			email, err := verifier.Verify(iapToken)
			if err != nil {
				log.Warn().Msgf("Failed to verify IAP token: %v", err)
				http.Error(w, "Invalid IAP token", http.StatusUnauthorized)
				return
			}

			// Set the user's email in the request context
			ctx := context.WithValue(r.Context(), "userEmail", email)

			// Call the next handler in the chain
			next.ServeHTTP(w, r.WithContext(ctx))
		})
	}, nil
}

// getUserFromToken retrieves user information from auth-central using the access token
func getUserFromToken(ctx context.Context, authToken secretstring.SecretString, authServiceClient authcentralproto.AuthServiceClient, tokenExchangeClient tokenexchangeclient.TokenExchangeClient, tenantWatcherClient tenantwatcherclient.TenantWatcherClient) *connectforwarder.ForwardUserInfo {
	if authToken.Expose() == "" {
		log.Warn().Msg("getUserFromToken: Missing auth token")
		return nil
	}

	// Get service token for auth-central call
	serviceToken, err := tokenExchangeClient.GetSignedTokenForService(ctx, "", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_R})
	if err != nil {
		log.Error().Err(err).Msg("getUserFromToken: Failed to get service token for auth-central")
		return nil
	}

	// Create request context with service token
	requestCtx, err := requestcontext.FromGrpcContext(ctx)
	if err != nil {
		log.Error().Err(err).Msg("getUserFromToken: Failed to get request context")
		return nil
	}
	requestCtx.RequestSource = "web-rpc-proxy"
	requestCtx = requestCtx.WithAuthToken(serviceToken)

	// Add request context to gRPC metadata
	ctx = metadata.NewOutgoingContext(ctx, requestCtx.ToMetadata())

	// Call auth-central to get token info
	tokenInfoResp, err := authServiceClient.GetTokenInfo(ctx, &authcentralproto.GetTokenInfoRequest{
		Token:     authToken.Expose(),
		Requestor: "web_rpc_proxy",
	})
	if err != nil {
		log.Error().Err(err).Msg("getUserFromToken: Failed to get token info from auth-central")
		return nil
	}

	// Get tenant information from tenant watcher to fetch shard namespace
	tenant, err := tenantWatcherClient.GetTenantByID(ctx, tokenInfoResp.TenantId)
	if err != nil {
		log.Error().Err(err).Str("tenant_id", tokenInfoResp.TenantId).Msg("getUserFromToken: Failed to get tenant info from tenant watcher")
		return nil
	}

	return &connectforwarder.ForwardUserInfo{
		UserID:         tokenInfoResp.AugmentUserId,
		UserEmail:      tokenInfoResp.UserEmail,
		ShardNamespace: tenant.ShardNamespace,
		TenantID:       tokenInfoResp.TenantId,
	}
}

// getUserFromSession retrieves user information from the session token
func getUserFromSession(ctx context.Context, session *sessions.Session, authServiceClient authcentralproto.AuthServiceClient, tokenExchangeClient tokenexchangeclient.TokenExchangeClient, tenantWatcherClient tenantwatcherclient.TenantWatcherClient) *connectforwarder.ForwardUserInfo {
	if session == nil {
		return nil
	}
	accessToken := GetTokenFromSession(session)
	if accessToken == "" {
		return nil
	}
	return getUserFromToken(ctx, secretstring.New(accessToken), authServiceClient, tokenExchangeClient, tenantWatcherClient)
}

func main() {
	logging.SetupServerLogging()

	config, err := loadConfig("/config/config.json")
	if err != nil {
		log.Fatal().Msgf("Could not load config: %v", err)
	}

	ctx := context.Background()
	ctx, cancel := context.WithCancel(ctx)
	defer cancel()

	// Create client credentials for the central client.
	centralClientCreds, err := tlsconfig.GetClientTls(config.ClientMtls)
	if err != nil {
		log.Fatal().Err(err).Msgf("Error creating client credentials")
	}

	_, err = featureflags.NewFeatureFlagHandleFromFile(config.FeatureFlagsSdkKeyPath,
		config.DynamicFeatureFlagsEndpoint)
	if err != nil {
		log.Fatal().Err(err).Msg("Error creating feature flag handle")
	}

	// Setup session manager
	sessionManager, err := NewSessionManager(config)
	if err != nil {
		log.Fatal().Err(err).Msg("Failed to setup session manager")
	}

	// Setup OAuth service
	oauthService := NewOAuthService(config)

	// Create auth central client
	authCentralConn, err := grpcclient.New(config.AuthCentralGrpcUrl, centralClientCreds)
	if err != nil {
		log.Fatal().Msgf("Failed to connect to auth central: %v", err)
	}
	defer authCentralConn.Close()

	authServiceClient := authcentralproto.NewAuthServiceClient(authCentralConn)

	// Create token exchange client
	namespace := os.Getenv("POD_NAMESPACE")
	baseTokenExchangeClient, err := tokenexchangeclient.New(config.TokenExchangeGrpcUrl, namespace, centralClientCreds)
	if err != nil {
		log.Fatal().Msgf("Failed to create token exchange client: %v", err)
	}

	// Wrap with caching client (15 minute TTL)
	tokenExchangeClient := tokencache.NewBackendServiceTokenCachingClient(baseTokenExchangeClient, 15*time.Minute)

	// Create tenant watcher client
	tenantWatcherClient := tenantwatcherclient.New(config.TenantWatcherGrpcUrl,
		grpc.WithTransportCredentials(centralClientCreds),
	)

	// Create team management service handler
	forwarderFactory := connectforwarder.NewForwarderFactory()
	billingForwarderFactory := connectforwarder.NewUserForwarderFactory(tokenExchangeClient,
		authcentralproto.File_services_auth_central_server_auth_proto, "TeamManagementService",
		connectforwarder.NewSingleGrpcConnFactory(authCentralConn),
		"web_rpc_proxy", func(ctx context.Context, req *connect.Request[any]) *connectforwarder.ForwardUserInfo {
			// Try to get user from session first
			session := GetSessionFromContext(ctx)
			if session == nil {
				return nil
			}

			userInfo := getUserFromSession(ctx, session, authServiceClient, tokenExchangeClient, tenantWatcherClient)
			if userInfo != nil {
				return userInfo
			}

			log.Warn().Msg("UserInfo extractor: No user found in session, using nil")
			return nil
		})
	forwarderFactory.AddService(billingForwarderFactory)
	err = forwarderFactory.AddMethodsFromFileDescriptor(webrpcproxyproto.File_services_web_rpc_proxy_web_rpc_proxy_proto)
	if err != nil {
		log.Fatal().Msgf("Failed to add billing methods: %v", err)
	}
	billingHandler, err := NewWebRpcProxyServiceHandler(forwarderFactory)
	if err != nil {
		log.Fatal().Msgf("Failed to create billing service handler: %v", err)
	}
	agentsAppProxy := NewAgentsAppProxy(config, authServiceClient, tokenExchangeClient, tenantWatcherClient)

	r := mux.NewRouter()

	r.NotFoundHandler = http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
		log.Warn().Msgf("Not found: %s", r.URL.Path)
		http.Error(w, "Not Found", http.StatusNotFound)
	})
	r.Use(sessionManager.Middleware())
	// Keep auth middleware before file serving routes so that they are not accessible without auth
	r.Use(oauthService.AuthenticationMiddleware)
	if config.IapJwtVerifierDisabled {
		log.Info().Msg("IAP JWT verifier disabled")
	} else {
		log.Info().Msgf("IAP JWT verifier enabled with audience %s", config.IapAudience)
		middleware, err := iapVerificationMiddlewareFunc(config)
		if err != nil {
			log.Fatal().Msgf("Failed to create IAP JWT verifier: %v", err)
		}
		r.Use(middleware)
	}

	r.HandleFunc("/", serveIndex).Methods("GET")
	r.HandleFunc("/health", healthCheck).Methods("GET")
	r.HandleFunc("/assets/{path:.*}", serveAssets).Methods("GET")
	r.HandleFunc("/favicon.svg", serveAssets).Methods("GET")
	r.HandleFunc("/manifest.json", serveAssets).Methods("GET")

	// Agents webapp routes
	agentsRouter := r.PathPrefix("/agents").Subrouter()
	agentsRouter.HandleFunc("/_app/{path:.*}", serveAgentsAssets).Methods("GET") // SvelteKit hashed assets
	agentsRouter.HandleFunc("/index.html", serveAgentsAssets).Methods("GET")     // Explicit index.html
	agentsRouter.HandleFunc("/", serveAgentsAssets).Methods("GET")               // Root agents path with trailing slash
	agentsRouter.HandleFunc("", serveAgentsAssets).Methods("GET")                // Root agents path without trailing slash
	agentsRouter.HandleFunc("/{path:.*}", serveAgentsSPA).Methods("GET")         // SPA fallback for client-side routing - must be last

	// Agents app API proxy
	agentsRouter.HandleFunc("/api/{path:.*}", agentsAppProxy.handleAPIRequest).Methods("POST")

	// OAuth routes
	r.HandleFunc("/auth/login", oauthService.LoginHandler).Methods("GET")
	r.HandleFunc("/auth/callback", oauthService.CallbackHandler).Methods("GET")
	r.HandleFunc("/auth/logout", oauthService.LogoutHandler).Methods("GET", "POST")

	// Add ConnectRPC billing service routes
	billingPath, billingConnectHandler := connectproto.NewWebRpcProxyServiceHandler(billingHandler)
	r.PathPrefix(billingPath).Handler(billingConnectHandler)

	srv := &http.Server{
		Handler: r,
		Addr:    fmt.Sprintf(":%d", config.Port),
	}
	log.Info().Msgf("Listening on %d", config.Port)

	if err := srv.ListenAndServeTLS(config.HttpsServerCert, config.HttpsServerKey); err != nil {
		log.Fatal().Msgf("Failed to listen: %v", err)
	}
}
