package main

import (
	"fmt"
	"net/http"
	"net/http/httputil"
	"net/url"
	"time"

	"github.com/gorilla/mux"
	"github.com/rs/zerolog/log"
	"google.golang.org/grpc/metadata"

	authcentralproto "github.com/augmentcode/augment/services/auth/central/server/proto"
	requestcontext "github.com/augmentcode/augment/services/lib/request_context"
	tenantwatcherclient "github.com/augmentcode/augment/services/tenant_watcher/client"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
)

// AgentsAppProxy is an HTTP reverse proxy that forwards Agents app API requests to the tenant's API proxy.
type AgentsAppProxy struct {
	config              *Config
	authServiceClient   authcentralproto.AuthServiceClient
	tokenExchangeClient tokenexchangeclient.TokenExchangeClient
	tenantWatcherClient tenantwatcherclient.TenantWatcherClient
	reverseProxy        *httputil.ReverseProxy
}

// Creates a new AgentsAppProxy.
func NewAgentsAppProxy(config *Config, authServiceClient authcentralproto.AuthServiceClient, tokenExchangeClient tokenexchangeclient.TokenExchangeClient, tenantWatcherClient tenantwatcherclient.TenantWatcherClient) *AgentsAppProxy {
	p := &AgentsAppProxy{
		config:              config,
		authServiceClient:   authServiceClient,
		tokenExchangeClient: tokenExchangeClient,
		tenantWatcherClient: tenantWatcherClient,
	}
	p.reverseProxy = p.NewReverseProxy()
	return p
}

// Makes a reverse proxy that directs requests to the appropriate tenant API proxy.
func (p *AgentsAppProxy) NewReverseProxy() *httputil.ReverseProxy {
	return &httputil.ReverseProxy{
		// Negative value means to flush immediately after each write to the client
		// so streaming responses work.
		FlushInterval: -1 * time.Millisecond,
		Director: func(req *http.Request) {
			tenantBaseUrl := p.resolveTenantBaseUrl(req)
			baseUrl, err := url.Parse(tenantBaseUrl)
			if err != nil {
				log.Error().Err(err).Msg("ReverseProxy: Error parsing target URL")
				return
			}
			upstream := mux.Vars(req)["path"]
			req.URL.Scheme = baseUrl.Scheme
			req.URL.Host = baseUrl.Host
			req.Host = baseUrl.Host
			req.URL.Path = "/" + upstream

			authToken := p.resolveAuthToken(req)
			if authToken != "" {
				req.Header.Set("Authorization", "Bearer "+authToken)
			}
		},
		ErrorHandler: func(w http.ResponseWriter, r *http.Request, err error) {
			log.Error().Err(err).Msg("ReverseProxy: Error")
			http.Error(w, "Bad Gateway", http.StatusBadGateway)
		},
		ModifyResponse: func(resp *http.Response) error {
			// Do nothing so streaming responses work
			return nil
		},
	}
}

type AgentsAppUserInfo struct {
	ShardNamespace string
	TenantID       string
}

// Resolve the tenant base URL from the session and tenant info. This is meant
// to give us an api proxy URL that we can use to make requests to the tenant's API proxy.
func (p *AgentsAppProxy) resolveTenantBaseUrl(r *http.Request) string {
	session := GetSessionFromContext(r.Context())
	if session == nil {
		return ""
	}

	userInfo := p.getUserInfo(r)

	if userInfo == nil || userInfo.ShardNamespace == "" {
		return ""
	}

	tenant, err := p.tenantWatcherClient.GetTenantByID(r.Context(), userInfo.TenantID)
	if err != nil {
		log.Error().Err(err).Msg("resolveTenantBaseUrl: Failed to get tenant info from tenant watcher")
		return ""
	}

	suffix, ok := p.config.ApiDomainSuffixes[tenant.Cloud]
	if !ok {
		log.Error().Msgf("resolveTenantBaseUrl: No API domain for cloud %s", tenant.Cloud)
		return ""
	}

	return fmt.Sprintf("https://%s.%s/", tenant.ShardNamespace, suffix)
}

func (p *AgentsAppProxy) getUserInfo(r *http.Request) *AgentsAppUserInfo {
	session := GetSessionFromContext(r.Context())
	if session == nil {
		return nil
	}

	accessToken := GetTokenFromSession(session)
	if accessToken == "" {
		return nil
	}

	return p.getUserFromToken(accessToken, r)
}

func (p *AgentsAppProxy) getUserFromToken(accessToken string, r *http.Request) *AgentsAppUserInfo {
	// Get service token for auth-central call
	serviceToken, err := p.tokenExchangeClient.GetSignedTokenForService(r.Context(), "", []tokenscopesproto.Scope{tokenscopesproto.Scope_AUTH_R})
	if err != nil {
		log.Error().Err(err).Msg("getUserFromToken: Failed to get service token for auth-central")
		return nil
	}

	// Create request context with service token
	// Use a random request ID and session ID since this is a background operation
	requestCtx := requestcontext.New(
		requestcontext.NewRandomRequestId(),
		requestcontext.NewRandomRequestSessionId(),
		"web-rpc-proxy",
		serviceToken,
	)

	// Add request context to gRPC metadata
	ctx := metadata.NewOutgoingContext(r.Context(), requestCtx.ToMetadata())

	// Call auth-central to get token info
	tokenInfoResp, err := p.authServiceClient.GetTokenInfo(ctx, &authcentralproto.GetTokenInfoRequest{
		Token:     accessToken,
		Requestor: "web_rpc_proxy",
	})
	if err != nil {
		log.Error().Err(err).Msg("getUserFromToken: Failed to get token info from auth-central")
		return nil
	}

	// Get tenant information from tenant watcher to fetch shard namespace
	tenant, err := p.tenantWatcherClient.GetTenantByID(r.Context(), tokenInfoResp.TenantId)
	if err != nil {
		log.Error().Err(err).Str("tenant_id", tokenInfoResp.TenantId).Msg("getUserFromToken: Failed to get tenant info from tenant watcher")
		return nil
	}

	return &AgentsAppUserInfo{
		ShardNamespace: tenant.ShardNamespace,
		TenantID:       tokenInfoResp.TenantId,
	}
}

// Resolve the auth token from the session.
func (p *AgentsAppProxy) resolveAuthToken(r *http.Request) string {
	session := GetSessionFromContext(r.Context())
	if session == nil {
		return ""
	}

	accessToken := GetTokenFromSession(session)
	return accessToken
}

// Handle an API request by proxying it to the tenant's API proxy.
func (p *AgentsAppProxy) handleAPIRequest(w http.ResponseWriter, r *http.Request) {
	p.reverseProxy.ServeHTTP(w, r)
}
