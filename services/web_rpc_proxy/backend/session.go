package main

import (
	"context"
	"encoding/base64"
	"fmt"
	"net/http"
	"os"
	"strings"

	"github.com/gorilla/sessions"
	"github.com/rs/zerolog/log"
)

// SessionManager handles all session-related operations
type SessionManager struct {
	store sessions.Store
}

// NewSessionManager creates a new session manager with the given configuration
func NewSessionManager(config *Config) (*SessionManager, error) {
	store, err := createSessionStore(config)
	if err != nil {
		return nil, err
	}

	return &SessionManager{
		store: store,
	}, nil
}

// createSessionStore initializes the cookie-based session store
func createSessionStore(config *Config) (sessions.Store, error) {
	// Read session signing secret from file
	signingSecretBase64, err := os.ReadFile(config.SessionSigningSecretKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read session signing secret key from %s: %v", config.SessionSigningSecretKeyPath, err)
	}

	// Decode base64 URL-safe signing secret (without padding)
	signingSecretBytes, err := base64.RawURLEncoding.DecodeString(strings.TrimSpace(string(signingSecretBase64)))
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64 URL-safe signing secret: %v", err)
	}

	// Read session encryption secret from file
	encryptionSecretBase64, err := os.ReadFile(config.SessionEncryptionSecretKeyPath)
	if err != nil {
		return nil, fmt.Errorf("failed to read session encryption secret key from %s: %v", config.SessionEncryptionSecretKeyPath, err)
	}

	// Decode base64 URL-safe encryption secret (without padding)
	encryptionSecretBytes, err := base64.RawURLEncoding.DecodeString(strings.TrimSpace(string(encryptionSecretBase64)))
	if err != nil {
		return nil, fmt.Errorf("failed to decode base64 URL-safe encryption secret: %v", err)
	}

	// Create cookie store for sessions with both signing and encryption keys
	// The first key is used for authentication (signing), the second for encryption
	store := sessions.NewCookieStore(signingSecretBytes, encryptionSecretBytes)

	// Configure session options
	store.Options = &sessions.Options{
		Path:     "/",
		MaxAge:   86400 * 7, // 7 days
		HttpOnly: true,
		Secure:   true, // Set to true for HTTPS
		SameSite: http.SameSiteLaxMode,
	}

	log.Info().Msg("Session store initialized with encrypted cookies")
	return store, nil
}

// GetSession retrieves or creates a session for the request
func (sm *SessionManager) GetSession(r *http.Request) (*sessions.Session, error) {
	session, err := sm.store.Get(r, "web_rpc_proxy_session")
	if err != nil {
		log.Warn().Err(err).Msg("Failed to get session")
		// Continue with empty session
		session, _ = sm.store.New(r, "web_rpc_proxy_session")
	}
	return session, nil
}

// SaveSession saves the session to the response
func (sm *SessionManager) SaveSession(session *sessions.Session, r *http.Request, w http.ResponseWriter) error {
	return session.Save(r, w)
}

// Middleware creates the session middleware
func (sm *SessionManager) Middleware() func(http.Handler) http.Handler {
	return func(next http.Handler) http.Handler {
		return http.HandlerFunc(func(w http.ResponseWriter, r *http.Request) {
			// Skip session handling for health checks
			if r.URL.Path == "/health" {
				next.ServeHTTP(w, r)
				return
			}

			// Get or create session
			session, err := sm.GetSession(r)
			if err != nil {
				log.Error().Err(err).Msg("Failed to get session")
				http.Error(w, "Session error", http.StatusInternalServerError)
				return
			}

			// Add session to request context
			ctx := context.WithValue(r.Context(), "session", session)
			r = r.WithContext(ctx)

			// Create a custom ResponseWriter to save session after response
			sessionWriter := &sessionResponseWriter{
				ResponseWriter: w,
				request:        r,
				session:        session,
				sessionManager: sm,
			}

			next.ServeHTTP(sessionWriter, r)
		})
	}
}

// sessionResponseWriter wraps http.ResponseWriter to save session after response
type sessionResponseWriter struct {
	http.ResponseWriter
	request        *http.Request
	session        *sessions.Session
	sessionManager *SessionManager
	written        bool
}

func (sw *sessionResponseWriter) WriteHeader(code int) {
	if !sw.written {
		// Save session before writing response
		if err := sw.sessionManager.SaveSession(sw.session, sw.request, sw.ResponseWriter); err != nil {
			log.Error().Err(err).Msg("Failed to save session")
		}
		sw.written = true
	}
	sw.ResponseWriter.WriteHeader(code)
}

func (sw *sessionResponseWriter) Write(data []byte) (int, error) {
	if !sw.written {
		// Save session before writing response
		if err := sw.sessionManager.SaveSession(sw.session, sw.request, sw.ResponseWriter); err != nil {
			log.Error().Err(err).Msg("Failed to save session")
		}
		sw.written = true
	}
	return sw.ResponseWriter.Write(data)
}

// Implement http.Flusher interface
func (sw *sessionResponseWriter) Flush() {
	if !sw.written {
		// Save session before writing response
		if err := sw.sessionManager.SaveSession(sw.session, sw.request, sw.ResponseWriter); err != nil {
			log.Error().Err(err).Msg("Failed to save session")
		}
		sw.written = true
	}
	if f, ok := sw.ResponseWriter.(http.Flusher); ok {
		f.Flush()
	}
}

// Session utility functions

// GetSessionFromContext retrieves the session from the request context
func GetSessionFromContext(ctx context.Context) *sessions.Session {
	if session, ok := ctx.Value("session").(*sessions.Session); ok {
		return session
	}
	return nil
}

// SetTokenInSession stores the OAuth access token in the session
func SetTokenInSession(session *sessions.Session, accessToken string) {
	session.Values["access_token"] = accessToken
	session.Values["authenticated"] = true
}

// GetTokenFromSession retrieves the OAuth access token from the session
func GetTokenFromSession(session *sessions.Session) string {
	if session == nil {
		return ""
	}

	authenticated, ok := session.Values["authenticated"].(bool)
	if !ok || !authenticated {
		return ""
	}

	accessToken, _ := session.Values["access_token"].(string)
	return accessToken
}

// ClearTokenFromSession removes the OAuth access token from the session
func ClearTokenFromSession(session *sessions.Session) {
	delete(session.Values, "access_token")
	delete(session.Values, "authenticated")
	delete(session.Values, "oauth_state")
	delete(session.Values, "code_verifier")
}

// IsUserAuthenticated checks if the user is authenticated in the session
func IsUserAuthenticated(session *sessions.Session) bool {
	if session == nil {
		return false
	}
	authenticated, ok := session.Values["authenticated"].(bool)
	return ok && authenticated
}
