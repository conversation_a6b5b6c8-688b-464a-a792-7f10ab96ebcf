load("@rules_proto//proto:defs.bzl", "proto_library")
load("//tools/bzl:go.bzl", "go_connect_proto_library")
load("//tools/bzl:kubecfg.bzl", "kubecfg")
load("//tools/bzl:typescript.bzl", "ts_proto_library")

kubecfg(
    name = "kubecfg",
    src = "deploy.jsonnet",
    data = [
        "//services/web_rpc_proxy/backend:image",
    ],
    visibility = ["//services/deploy:__subpackages__"],
    deps = [
        "//deploy/common:cert-lib",
        "//deploy/common:cloud_info",
        "//deploy/common:config-map-lib",
        "//deploy/common:dynamic-feature-flags-lib",
        "//deploy/common:grpc-lib",
        "//deploy/common:lib",
        "//deploy/common:node-lib",
        "//deploy/common:telemetry-lib",
        "//deploy/gcp:gcp-lib",
        "//services/deploy:endpoints",
    ],
)

go_connect_proto_library(
    name = "web_rpc_proxy_go_connect",
    connectimportpath = "github.com/augmentcode/augment/services/web_rpc_proxy/connectproto",
    proto = ":web_rpc_proxy_proto",
    protoimportpath = "github.com/augmentcode/augment/services/web_rpc_proxy/proto",
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_go_grpc",
        "//services/lib/grpc/connect_forward:forward_options_go_proto",
    ],
)

proto_library(
    name = "web_rpc_proxy_proto",
    srcs = ["web_rpc_proxy.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_proto",
        "//services/lib/grpc/connect_forward:forward_options_proto",
        "@protobuf//:timestamp_proto",
    ],
)

ts_proto_library(
    name = "web_rpc_proxy_ts_proto",
    copy_files = True,
    node_modules = "//:node_modules",
    proto = ":web_rpc_proxy_proto",
    proto_srcs = ["web_rpc_proxy.proto"],
    visibility = ["//services:__subpackages__"],
    deps = [
        "//services/auth/central/server:auth_ts_proto",
    ],
)
