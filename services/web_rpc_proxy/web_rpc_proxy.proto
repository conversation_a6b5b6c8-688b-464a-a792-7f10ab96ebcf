syntax = "proto3";
package web_rpc_proxy;

import "services/auth/central/server/auth.proto";
import "services/lib/grpc/connect_forward/forward_options.proto";

// WebRpcProxyService provides a ConnectRPC interface for managing user billing plans
// This service acts as a proxy to the auth central service's TeamManagementService
service WebRpcProxyService {
  // Get all available billing plans
  rpc GetAllOrbPlans(auth.GetAllOrbPlansRequest) returns (auth.GetAllOrbPlansResponse) {
    option (forward_options.forward_method_name) = "auth.TeamManagementService/GetAllOrbPlans";
  }

  // Get current plan information for a user
  rpc GetUserOrbPlanInfo(auth.GetUserOrbPlanInfoRequest) returns (auth.GetUserOrbPlanInfoResponse) {
    option (forward_options.forward_method_name) = "auth.TeamManagementService/GetUserOrbPlanInfo";
  }

  // Put a user on a specific plan
  rpc PutUserOnPlan(auth.PutUserOnPlanRequest) returns (auth.PutUserOnPlanResponse) {
    option (forward_options.forward_method_name) = "auth.TeamManagementService/PutUserOnPlan";
  }

  // Get user subscription information
  rpc GetUserOrbSubscriptionInfo(auth.GetUserOrbSubscriptionInfoRequest) returns (auth.GetUserOrbSubscriptionInfoResponse) {
    option (forward_options.forward_method_name) = "auth.TeamManagementService/GetUserOrbSubscriptionInfo";
  }

  // Get user credits information
  rpc GetUserOrbCreditsInfo(auth.GetUserOrbCreditsInfoRequest) returns (auth.GetUserOrbCreditsInfoResponse) {
    option (forward_options.forward_method_name) = "auth.TeamManagementService/GetUserOrbCreditsInfo";
  }
}
