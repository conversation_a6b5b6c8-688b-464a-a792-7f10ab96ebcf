// K8S deployment file for the webapp
local certLib = import 'deploy/common/cert-lib.jsonnet';
local cloudInfo = import 'deploy/common/cloud_info.jsonnet';
local configMapLib = import 'deploy/common/config-map-lib.jsonnet';
local dynamicFeatureFlagsLib = import 'deploy/common/dynamic-feature-flags-lib.jsonnet';
local grpcLib = import 'deploy/common/grpc-lib.jsonnet';
local lib = import 'deploy/common/lib.jsonnet';
local nodeLib = import 'deploy/common/node-lib.jsonnet';
local telemetryLib = import 'deploy/common/telemetry-lib.jsonnet';
local gcpLib = import 'deploy/gcp/gcp-lib.jsonnet';
local endpoints = import 'services/deploy/endpoints.jsonnet';
function(env, namespace, cloud, namespace_config)
  assert cloudInfo.isLeadCluster(cloud);
  assert cloudInfo.isCentralNamespace(env, namespace, cloud);

  local appName = 'web-rpc-proxy';
  local backendConfig = gcpLib.createBackendConfig(app=appName,
                                                   cloud=cloud,
                                                   namespace=namespace,
                                                   healthCheck={
                                                     checkIntervalSec: 15,
                                                     port: 5000,
                                                     type: 'HTTPS',
                                                     requestPath: '/health',
                                                   },
                                                   timeoutSec=300,
                                                   securityPolicy='ingress-web',
                                                   iap=if env == 'DEV' then false else true);
  local clientCert = certLib.createCentralClientCert(
    name='%s-client-cert' % appName,
    namespace=namespace,
    appName=appName,
    env=env,
    dnsNames=grpcLib.grpcServiceNames(appName, namespace=namespace)
  );
  local dynamicFeatureFlags = dynamicFeatureFlagsLib.createLaunchDarklySecret(env=env, namespace=namespace, cloud=cloud, appName=appName);

  // Create service account for accessing GCP secrets
  local serviceAccount = gcpLib.createServiceAccount(app=appName, cloud=cloud, env=env, namespace=namespace, iam=true);

  // Session secrets for cookie security
  local sessionSigningSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='session-signing-secret',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );

  local sessionEncryptionSecret = gcpLib.mountSecretManagerSecret(
    env=env,
    cloud=cloud,
    namespace=namespace,
    appName=appName,
    purpose='session-encryption-secret',
    version={
      PROD: '1',
      STAGING: '1',
      DEV: 'latest',
    }[env],
    serviceAccount=serviceAccount,
  );
  local domainSuffix = cloudInfo[cloud].internalDomainSuffix;
  local ingressHostnames = {
    PROD: 'rpc-proxy.%s' % domainSuffix,
    STAGING: 'rpc-proxy.staging.%s' % domainSuffix,
    DEV: 'rpc-proxy.%s.%s' % [namespace, domainSuffix],
  };
  local ingressHostname = ingressHostnames[env];
  local ingressFacingCert = certLib.createPublicServerCert(name='%s-public-cert' % appName,
                                                           namespace=namespace,
                                                           appName=appName,
                                                           dnsNames=[ingressHostname],
                                                           volumeName='https-certs',
                                                           env=env);
  local service = {
    apiVersion: 'v1',
    kind: 'Service',
    metadata: {
      name: '%s-svc' % appName,
      namespace: namespace,
      annotations: {
        'cloud.google.com/backend-config': std.manifestJson({ default: backendConfig.metadata.name }),
        'cloud.google.com/app-protocols': std.manifestJson({ 'viewer-https': 'HTTPS' }),
      },
      labels: {
        app: appName,
      },
    },
    spec: {
      type: 'NodePort',
      selector: {
        app: appName,
      },
      ports: [
        {
          protocol: 'TCP',
          port: 443,
          name: 'viewer-https',
          targetPort: 'viewer-https',
        },
      ],
    },
  };
  // TODO the real webapp would be auth0 protected, but keep IAP for now
  local iapAudience = '/projects/%s/global/backendServices/%s' % [cloudInfo[cloud].projectNumber, if std.objectHas(namespace_config, 'iapAudience') then namespace_config.iapAudience else ''];
  local mtls = grpcLib.isMtls(env=env, namespace=namespace, namespace_config=namespace_config);
  local tenantWatcherGrpcUrl = endpoints.getTenantWatcherGrpcUrl(env=env, namespace=namespace, cloud=cloud);
  local tokenExchangeGrpcUrl = endpoints.getTokenExchangeGrpcUrl(env=env, namespace=namespace, cloud=cloud);
  local authCentralGrpcUrl = '%s:50051' % endpoints.getAuthCentralGrpcUrl(env=env, namespace=namespace, cloud=cloud);
  local authHostname = endpoints.get_auth_hostname(env, namespace, cloud);
  local webRpcProxyHostname = endpoints.getWebRpcProxyHostname(env, namespace, cloud);
  local configMap = configMapLib.createConfigMap(appName=appName, namespace=namespace, config={
    port: 5000,
    https_server_key: '/https-certs/tls.key',
    https_server_cert: '/https-certs/tls.crt',
    client_mtls: if mtls then clientCert.config else null,
    iap_audience: iapAudience,
    iap_jwt_verifier_disabled: namespace_config.flags.iapJwtVerifierDisabled,
    tenant_watcher_grpc_url: tenantWatcherGrpcUrl,
    token_exchange_grpc_url: tokenExchangeGrpcUrl,
    auth_central_grpc_url: authCentralGrpcUrl,
    internal_domain_suffixes: {
      [cloud]: cloudInfo[cloud].internalDomainSuffix
      for cloud in std.objectFields(cloudInfo)
    },
    api_domain_suffixes: {
      [cloud]: cloudInfo[cloud].apiDomain
      for cloud in std.objectFields(cloudInfo)
    },
    env: env,
    ingress_hostnames: ingressHostnames,
    feature_flags_sdk_key_path: dynamicFeatureFlags.secretsFilePath,
    dynamic_feature_flags_endpoint: if namespace_config.flags.useFakeFeatureFlags then 'http://fake-feature-flags-svc' else null,
    session_signing_secret_key_path: sessionSigningSecret.filePath,
    session_encryption_secret_key_path: sessionEncryptionSecret.filePath,
    oauth_client_id: 'web-rpc-proxy',
    oauth_auth_url: 'https://%s/authorize' % authHostname,
    oauth_token_url: 'https://%s/token' % authHostname,
    oauth_redirect_url: 'https://%s/auth/callback' % webRpcProxyHostname,
    oauth_scopes: [],
    oauth_logout_url: 'https://%s/logout' % authHostname,
    hostname: webRpcProxyHostname,
  });
  local container =
    {
      name: appName,
      target: {
        name: '//services/web_rpc_proxy/backend:image',
        dst: appName,
      },
      ports: [
        {
          containerPort: 5000,
          name: 'viewer-https',
        },
      ],
      volumeMounts: [
        configMap.volumeMountDef,
        clientCert.volumeMountDef,
        ingressFacingCert.volumeMountDef,
        dynamicFeatureFlags.volumeMountDef,
        sessionSigningSecret.volumeMountDef,
        sessionEncryptionSecret.volumeMountDef,
      ],
      // the environment variables that are passed to the server
      env: telemetryLib.telemetryEnv(appName, telemetryLib.collectorUri(env, namespace, cloud)) + dynamicFeatureFlags.env,
      readinessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 5,
        periodSeconds: 10,
      },
      livenessProbe: {
        httpGet: {
          scheme: 'HTTPS',
          path: '/health',
          port: 5000,
        },
        initialDelaySeconds: 15,
        periodSeconds: 20,
      },
      resources: {
        limits: {
          cpu: 0.1,
          memory: '512Mi',
        },
      },
    };
  local tolerations = nodeLib.tolerations(resource=null, env=env, cloud=cloud);
  local affinity = nodeLib.affinity(resource=null, env=env, cloud=cloud, appName=appName);
  local pod =
    {
      priorityClassName: cloudInfo.envToPriorityClass(env),
      affinity: affinity,
      tolerations: tolerations,
      serviceAccountName: serviceAccount.name,
      containers: [
        container,
      ],
      volumes: [
        clientCert.podVolumeDef,
        configMap.podVolumeDef,
        ingressFacingCert.podVolumeDef,
        dynamicFeatureFlags.podVolumeDef,
        sessionSigningSecret.podVolumeDef,
        sessionEncryptionSecret.podVolumeDef,
      ],
    };
  local frontendConfig = gcpLib.createFrontendConfig(app=appName, cloud=cloud, namespace=namespace);
  local ingressObjects = [
    {
      apiVersion: 'networking.k8s.io/v1',
      kind: 'Ingress',
      metadata: {
        annotations: {
          'kubernetes.io/ingress.class': 'gce',
          'cert-manager.io/cluster-issuer': certLib.getIngressIssuer(env),
          'kubernetes.io/ingress.allow-http': 'false',
          'networking.gke.io/v1beta1.FrontendConfig': frontendConfig.metadata.name,
        },
        labels: {
          app: appName,
        },
        name: '%s-ingress' % appName,
        namespace: namespace,
      },
      spec: {
        ingressClassName: 'gce',
        tls: [
          {
            secretName: '%s-ssl-cert' % appName,  // pragma: allowlist secret
            hosts: [ingressHostname],
          },
        ],
        rules: [
          {
            host: ingressHostname,
            http: {
              paths: [
                {
                  path: '/',
                  pathType: 'Prefix',
                  backend: {
                    service: {
                      name: service.metadata.name,
                      port: {
                        number: 443,
                      },
                    },
                  },
                },
              ],
            },
          },
        ],
      },
    },
    frontendConfig,
    backendConfig,
  ];

  local deployment = {
    apiVersion: 'apps/v1',
    kind: 'Deployment',
    metadata: {
      name: appName,
      namespace: namespace,
      labels: {
        app: appName,
      },
      annotations: {
        'reloader.stakater.com/search': 'true',
      },
    },
    spec: {
      minReadySeconds: if env == 'DEV' then 0 else 60,
      replicas: if env == 'DEV' then 1 else 2,
      strategy: {
        type: 'RollingUpdate',
        rollingUpdate: {
          maxSurge: 1,
          maxUnavailable: 0,
        },
      },
      selector: {
        matchLabels: {
          app: appName,
        },
      },
      template: {
        metadata: {
          labels: {
            app: appName,
          },
        },
        spec: pod,
      },
    },
  };
  lib.flatten([
    configMap.objects,
    service,
    serviceAccount.objects,
    ingressFacingCert.objects,
    deployment,
    ingressObjects,
    clientCert.objects,
    dynamicFeatureFlags.k8s_objects,
    sessionSigningSecret.objects,
    sessionEncryptionSecret.objects,
  ])
