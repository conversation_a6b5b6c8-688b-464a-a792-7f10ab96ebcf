import React, { useEffect, useState } from "react";
import {
  Card,
  Button,
  Form,
  Select,
  Alert,
  Spin,
  Typography,
  Row,
  Col,
  Divider,
  Tag,
  Space,
  Modal,
  message,
} from "antd";

// Type assertion to fix Card component issues
const AntCard = Card as any;
import { createPromiseClient } from "@connectrpc/connect";
import { createConnectTransport } from "@connectrpc/connect-web";
import { LayoutComponent } from "../lib/layout";
import { WebRpcProxyService } from "../../../web_rpc_proxy_connect";

import {
  GetAllOrbPlansRequest,
  GetUserOrbPlanInfoRequest,
  PutUserOnPlanRequest,
  GetUserSubscriptionInfoRequest,
  GetUserOrbCreditsInfoRequest,
  OrbPlanInfo,
  OrbPlanInfo_PlanType,
  OrbSubscriptionInfo_SubscriptionStatus,
  GetUserOrbPlanInfoResponse,
  GetAllOrbPlansResponse,
  GetUserOrbSubscriptionInfoResponse,
  GetUserOrbCreditsInfoResponse,
} from "../../../../auth/central/server/auth_pb";

const { Title, Text } = Typography;

// Utility functions for generating request IDs and session IDs
const generateRequestId = (): string => {
  return crypto.randomUUID();
};

const generateSessionId = (): string => {
  return crypto.randomUUID();
};

// Session ID is generated once per component instance and reused
let sessionId: string | null = null;

const getOrCreateSessionId = (): string => {
  if (!sessionId) {
    sessionId = generateSessionId();
    console.log("Generated new session ID:", sessionId);
  }
  return sessionId;
};

interface BillingState {
  plans: OrbPlanInfo[];
  currentPlan?: OrbPlanInfo;
  subscriptionInfo?: any;
  creditsInfo?: any;
  loading: boolean;
  error?: string;
}

const BillingPageComponent: React.FC = () => {
  const [state, setState] = useState<BillingState>({
    plans: [],
    loading: true,
  });
  const [form] = Form.useForm();
  const [changingPlan, setChangingPlan] = useState(false);
  const [selectedPlan, setSelectedPlan] = useState<string>("");

  // Create ConnectRPC client with request ID and session ID headers
  const transport = createConnectTransport({
    baseUrl: window.location.origin,
    useBinaryFormat: true,
    interceptors: [
      (next) => async (req) => {
        // Generate a new request ID for each request
        const requestId = generateRequestId();
        const currentSessionId = getOrCreateSessionId();

        // Add request ID and session ID headers
        req.header.set("x-request-id", requestId);
        req.header.set("x-request-session-id", currentSessionId);

        console.log(
          "Making request with ID:",
          requestId,
          "Session ID:",
          currentSessionId,
        );

        return await next(req);
      },
    ],
  });
  const client = createPromiseClient(WebRpcProxyService, transport);

  const loadBillingData = async () => {
    try {
      setState((prev) => ({ ...prev, loading: true, error: undefined }));

      // Load all plans
      const plansResponse: GetAllOrbPlansResponse = await client.getAllOrbPlans(
        new GetAllOrbPlansRequest(),
      );

      // Load current user plan
      const userPlanResponse: GetUserOrbPlanInfoResponse =
        await client.getUserOrbPlanInfo(new GetUserOrbPlanInfoRequest());

      // Load subscription info
      const subscriptionResponse: GetUserOrbSubscriptionInfoResponse =
        await client.getUserOrbSubscriptionInfo(
          new GetUserSubscriptionInfoRequest(),
        );

      // Load credits info
      const creditsResponse: GetUserOrbCreditsInfoResponse =
        await client.getUserOrbCreditsInfo(new GetUserOrbCreditsInfoRequest());

      setState((prev) => ({
        ...prev,
        plans: plansResponse.orbPlans || [],
        currentPlan:
          userPlanResponse.planInfo?.case === "orbPlanInfo"
            ? userPlanResponse.planInfo.value
            : undefined,
        subscriptionInfo: subscriptionResponse.orbSubscriptionInfo || undefined,
        creditsInfo: creditsResponse,
        loading: false,
      }));
    } catch (error) {
      console.error("Failed to load billing data:", error);
      setState((prev) => ({
        ...prev,
        loading: false,
        error: `Failed to load billing data: ${error}`,
      }));
    }
  };

  useEffect(() => {
    loadBillingData();
  }, []);

  const handlePlanChange = async (planId: string) => {
    try {
      setChangingPlan(true);

      const response = await client.putUserOnPlan(
        new PutUserOnPlanRequest({ planId }),
      );

      if (response.success) {
        message.success(response.message || "Plan changed successfully!");
        await loadBillingData(); // Reload data
        setSelectedPlan("");
      } else {
        message.error(response.message || "Failed to change plan");
      }
    } catch (error) {
      console.error("Failed to change plan:", error);
      message.error(`Failed to change plan: ${error}`);
    } finally {
      setChangingPlan(false);
    }
  };

  const confirmPlanChange = () => {
    if (!selectedPlan) return;

    const plan = (state.plans || []).find(
      (p) => p.externalPlanId === selectedPlan,
    );
    if (!plan) return;

    Modal.confirm({
      title: "Confirm Plan Change",
      content: `Are you sure you want to change to the ${plan.formattedPlanName} plan?`,
      onOk: () => handlePlanChange(selectedPlan),
    });
  };

  const getPlanTypeColor = (planType: OrbPlanInfo_PlanType): string => {
    switch (planType) {
      case OrbPlanInfo_PlanType.COMMUNITY:
        return "blue";
      case OrbPlanInfo_PlanType.TRIAL:
        return "orange";
      case OrbPlanInfo_PlanType.PAID:
        return "green";
      default:
        return "default";
    }
  };

  const getPlanTypeName = (planType: OrbPlanInfo_PlanType): string => {
    switch (planType) {
      case OrbPlanInfo_PlanType.COMMUNITY:
        return "Community";
      case OrbPlanInfo_PlanType.TRIAL:
        return "Trial";
      case OrbPlanInfo_PlanType.PAID:
        return "Paid";
      default:
        return "Unknown";
    }
  };

  const getSubscriptionStatusColor = (
    status: OrbSubscriptionInfo_SubscriptionStatus,
  ): string => {
    switch (status) {
      case OrbSubscriptionInfo_SubscriptionStatus.ACTIVE:
        return "success";
      case OrbSubscriptionInfo_SubscriptionStatus.UPCOMING:
        return "processing";
      case OrbSubscriptionInfo_SubscriptionStatus.ENDED:
        return "error";
      default:
        return "default";
    }
  };

  const getSubscriptionStatusName = (
    status: OrbSubscriptionInfo_SubscriptionStatus,
  ): string => {
    switch (status) {
      case OrbSubscriptionInfo_SubscriptionStatus.ACTIVE:
        return "Active";
      case OrbSubscriptionInfo_SubscriptionStatus.UPCOMING:
        return "Upcoming";
      case OrbSubscriptionInfo_SubscriptionStatus.ENDED:
        return "Ended";
      default:
        return "Unknown";
    }
  };

  if (state.loading) {
    return (
      <LayoutComponent
        selectedMenuKey="billing"
        breadcrumbs={[{ label: "Billing", link: "/billing" }]}
      >
        <div style={{ textAlign: "center", padding: "50px" }}>
          <Spin size="large" />
          <div style={{ marginTop: 16 }}>Loading billing information...</div>
        </div>
      </LayoutComponent>
    );
  }

  if (state.error) {
    return (
      <LayoutComponent
        selectedMenuKey="billing"
        breadcrumbs={[{ label: "Billing", link: "/billing" }]}
      >
        <Alert
          message="Error Loading Billing Data"
          description={state.error}
          type="error"
          showIcon
          action={
            <Button size="small" onClick={loadBillingData}>
              Retry
            </Button>
          }
        />
      </LayoutComponent>
    );
  }

  return (
    <LayoutComponent
      selectedMenuKey="billing"
      breadcrumbs={[{ label: "Billing", link: "/billing" }]}
    >
      <div style={{ padding: "24px" }}>
        <Title level={2}>Billing Management</Title>

        {/* Current Plan Section */}
        <AntCard title="Current Plan" style={{ marginBottom: 24 }}>
          {state.currentPlan ? (
            <Row gutter={16}>
              <Col span={12}>
                <Space direction="vertical" size="small">
                  <div>
                    <Text strong>Plan: </Text>
                    <Text>{state.currentPlan.formattedPlanName}</Text>
                    <Tag
                      color={getPlanTypeColor(state.currentPlan.planType)}
                      style={{ marginLeft: 8 }}
                    >
                      {getPlanTypeName(state.currentPlan.planType)}
                    </Tag>
                  </div>
                  <div>
                    <Text strong>Price per Seat: </Text>
                    <Text>{state.currentPlan.pricePerSeat}</Text>
                  </div>
                  <div>
                    <Text strong>Usage Units per Seat: </Text>
                    <Text>{state.currentPlan.usageUnitsPerSeat}</Text>
                  </div>
                  <div>
                    <Text strong>Max Seats: </Text>
                    <Text>{state.currentPlan.maxNumSeats}</Text>
                  </div>
                </Space>
              </Col>
              <Col span={12}>
                <Space direction="vertical" size="small">
                  <div>
                    <Text strong>Training Allowed: </Text>
                    <Tag
                      color={
                        state.currentPlan.trainingAllowed ? "green" : "red"
                      }
                    >
                      {state.currentPlan.trainingAllowed ? "Yes" : "No"}
                    </Tag>
                  </div>
                  <div>
                    <Text strong>Teams Allowed: </Text>
                    <Tag
                      color={state.currentPlan.teamsAllowed ? "green" : "red"}
                    >
                      {state.currentPlan.teamsAllowed ? "Yes" : "No"}
                    </Tag>
                  </div>
                  <div>
                    <Text strong>Additional Usage Available: </Text>
                    <Tag
                      color={
                        state.currentPlan.addUsageAvailable ? "green" : "red"
                      }
                    >
                      {state.currentPlan.addUsageAvailable ? "Yes" : "No"}
                    </Tag>
                  </div>
                  {state.currentPlan.addUsageAvailable && (
                    <div>
                      <Text strong>Additional Usage Cost: </Text>
                      <Text>{state.currentPlan.additionalUsageUnitCost}</Text>
                    </div>
                  )}
                </Space>
              </Col>
            </Row>
          ) : (
            <Text>No current plan information available</Text>
          )}
        </AntCard>

        {/* Plan Facts */}
        {state.currentPlan?.displayInfo?.planFacts &&
          state.currentPlan.displayInfo.planFacts.length > 0 && (
            <AntCard title="Plan Features" style={{ marginBottom: 24 }}>
              <ul>
                {state.currentPlan.displayInfo.planFacts.map((fact, index) => (
                  <li key={index}>{fact}</li>
                ))}
              </ul>
            </AntCard>
          )}

        {/* Subscription Info */}
        {state.subscriptionInfo && (
          <AntCard
            title="Subscription Information"
            style={{ marginBottom: 24 }}
          >
            {state.subscriptionInfo.case === "activeSubscription" && (
              <Row gutter={16}>
                <Col span={12}>
                  <Space direction="vertical" size="small">
                    <div>
                      <Text strong>Status: </Text>
                      <Tag
                        color={getSubscriptionStatusColor(
                          state.subscriptionInfo.value.subscriptionStatus,
                        )}
                      >
                        {getSubscriptionStatusName(
                          state.subscriptionInfo.value.subscriptionStatus,
                        )}
                      </Tag>
                    </div>
                    <div>
                      <Text strong>Seats: </Text>
                      <Text>{state.subscriptionInfo.value.seats}</Text>
                    </div>
                    <div>
                      <Text strong>Monthly Cost: </Text>
                      <Text>
                        {state.subscriptionInfo.value.monthlyTotalCost}
                      </Text>
                    </div>
                    <div>
                      <Text strong>Next Billing Amount: </Text>
                      <Text>
                        {state.subscriptionInfo.value.nextBillingCycleAmount}
                      </Text>
                    </div>
                  </Space>
                </Col>
                <Col span={12}>
                  <Space direction="vertical" size="small">
                    <div>
                      <Text strong>Billing Period End: </Text>
                      <Text>
                        {new Date(
                          state.subscriptionInfo.value.billingPeriodEndDateIso,
                        ).toLocaleDateString()}
                      </Text>
                    </div>
                    {state.subscriptionInfo.value.trialPeriodEndDateIso && (
                      <div>
                        <Text strong>Trial End: </Text>
                        <Text>
                          {new Date(
                            state.subscriptionInfo.value.trialPeriodEndDateIso,
                          ).toLocaleDateString()}
                        </Text>
                      </div>
                    )}
                    {state.subscriptionInfo.value.subscriptionEndDateIso && (
                      <div>
                        <Text strong>Subscription End: </Text>
                        <Text>
                          {new Date(
                            state.subscriptionInfo.value.subscriptionEndDateIso,
                          ).toLocaleDateString()}
                        </Text>
                      </div>
                    )}
                    {state.subscriptionInfo.value.portalUrl && (
                      <div>
                        <Button
                          type="link"
                          href={state.subscriptionInfo.value.portalUrl}
                          target="_blank"
                        >
                          Manage Subscription
                        </Button>
                      </div>
                    )}
                  </Space>
                </Col>
              </Row>
            )}
            {state.subscriptionInfo.case === "pendingSubscription" && (
              <Alert message="Subscription is pending activation" type="info" />
            )}
            {state.subscriptionInfo.case === "noSubscription" && (
              <Alert message="No active subscription" type="warning" />
            )}
          </AntCard>
        )}

        {/* Credits Info */}
        {state.creditsInfo && (
          <AntCard title="Usage Credits" style={{ marginBottom: 24 }}>
            <Row gutter={16}>
              <Col span={8}>
                <div style={{ textAlign: "center" }}>
                  <Title level={3} style={{ color: "#52c41a", margin: 0 }}>
                    {state.creditsInfo.usageUnitsAvailable}
                  </Title>
                  <Text type="secondary">Available</Text>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: "center" }}>
                  <Title level={3} style={{ color: "#1890ff", margin: 0 }}>
                    {state.creditsInfo.usageUnitsUsedThisBillingCycle}
                  </Title>
                  <Text type="secondary">Used This Cycle</Text>
                </div>
              </Col>
              <Col span={8}>
                <div style={{ textAlign: "center" }}>
                  <Title level={3} style={{ color: "#faad14", margin: 0 }}>
                    {state.creditsInfo.usageUnitsPending}
                  </Title>
                  <Text type="secondary">Pending</Text>
                </div>
              </Col>
            </Row>
          </AntCard>
        )}

        <Divider />

        {/* Change Plan Section */}
        <AntCard title="Change Plan">
          <Form form={form} layout="vertical" onFinish={confirmPlanChange}>
            <Form.Item
              label="Select New Plan"
              name="planId"
              rules={[{ required: true, message: "Please select a plan" }]}
            >
              <Select
                placeholder="Choose a plan"
                value={selectedPlan}
                onChange={setSelectedPlan}
                loading={changingPlan}
              >
                {(state.plans || [])
                  .filter(
                    (plan) =>
                      plan.externalPlanId !== state.currentPlan?.externalPlanId,
                  )
                  .sort(
                    (a, b) =>
                      (a.displayInfo?.sortOrder || 0) -
                      (b.displayInfo?.sortOrder || 0),
                  )
                  .map((plan) => (
                    <Select.Option
                      key={plan.externalPlanId}
                      value={plan.externalPlanId}
                    >
                      <div
                        style={{
                          display: "flex",
                          justifyContent: "space-between",
                          alignItems: "center",
                        }}
                      >
                        <span>{plan.formattedPlanName}</span>
                        <Space>
                          <Tag color={getPlanTypeColor(plan.planType)}>
                            {getPlanTypeName(plan.planType)}
                          </Tag>
                          <Text type="secondary">{plan.pricePerSeat}</Text>
                        </Space>
                      </div>
                    </Select.Option>
                  ))}
              </Select>
            </Form.Item>

            {selectedPlan && (
              <AntCard
                size="small"
                style={{ marginBottom: 16, backgroundColor: "#f6ffed" }}
              >
                {(() => {
                  const plan = (state.plans || []).find(
                    (p) => p.externalPlanId === selectedPlan,
                  );
                  if (!plan) return null;
                  return (
                    <div>
                      <Title level={5} style={{ margin: 0, marginBottom: 8 }}>
                        {plan.formattedPlanName} Details
                      </Title>
                      <Row gutter={16}>
                        <Col span={12}>
                          <Space direction="vertical" size="small">
                            <div>
                              <Text strong>Price per Seat:</Text>{" "}
                              {plan.pricePerSeat}
                            </div>
                            <div>
                              <Text strong>Usage Units per Seat:</Text>{" "}
                              {plan.usageUnitsPerSeat}
                            </div>
                            <div>
                              <Text strong>Max Seats:</Text> {plan.maxNumSeats}
                            </div>
                          </Space>
                        </Col>
                        <Col span={12}>
                          <Space direction="vertical" size="small">
                            <div>
                              <Text strong>Training:</Text>{" "}
                              <Tag
                                color={plan.trainingAllowed ? "green" : "red"}
                              >
                                {plan.trainingAllowed
                                  ? "Allowed"
                                  : "Not Allowed"}
                              </Tag>
                            </div>
                            <div>
                              <Text strong>Teams:</Text>{" "}
                              <Tag color={plan.teamsAllowed ? "green" : "red"}>
                                {plan.teamsAllowed ? "Allowed" : "Not Allowed"}
                              </Tag>
                            </div>
                            {plan.addUsageAvailable && (
                              <div>
                                <Text strong>Additional Usage Cost:</Text>{" "}
                                {plan.additionalUsageUnitCost}
                              </div>
                            )}
                          </Space>
                        </Col>
                      </Row>
                      {plan.displayInfo?.planFacts &&
                        plan.displayInfo.planFacts.length > 0 && (
                          <div style={{ marginTop: 12 }}>
                            <Text strong>Features:</Text>
                            <ul
                              style={{ margin: "4px 0 0 0", paddingLeft: 20 }}
                            >
                              {plan.displayInfo.planFacts.map((fact, index) => (
                                <li key={index}>{fact}</li>
                              ))}
                            </ul>
                          </div>
                        )}
                    </div>
                  );
                })()}
              </AntCard>
            )}

            <Form.Item>
              <Button
                type="primary"
                htmlType="submit"
                loading={changingPlan}
                disabled={!selectedPlan}
              >
                Change Plan
              </Button>
            </Form.Item>
          </Form>
        </AntCard>
      </div>
    </LayoutComponent>
  );
};

export default BillingPageComponent;
