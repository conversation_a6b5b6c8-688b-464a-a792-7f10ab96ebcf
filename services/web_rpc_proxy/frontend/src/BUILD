load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@npm//services/web_rpc_proxy/frontend:eslint/package_json.bzl", eslint_bin = "bin")

ASSET_PATTERNS = [
    "*.svg",
    "*.css",
]

SRC_PATTERNS = [
    "*.tsx",
    "routes/*.tsx",
    "lib/*.ts",
    "lib/*.tsx",
]

js_library(
    name = "src",
    srcs = glob(ASSET_PATTERNS + SRC_PATTERNS),
    visibility = ["//services/web_rpc_proxy/frontend:__subpackages__"],
    deps = [
        "//services/web_rpc_proxy/frontend:node_modules/antd",
        "//services/web_rpc_proxy/frontend:node_modules/axios",
        "//services/web_rpc_proxy/frontend:node_modules/react",
        "//services/web_rpc_proxy/frontend:node_modules/react-dom",
        "//services/web_rpc_proxy/frontend:node_modules/react-router-dom",
    ],
)

eslint_bin.eslint_test(
    name = "eslint_test",
    args = ["{}/{}".format(
        package_name(),
        p,
    ) for p in SRC_PATTERNS],
    data = [
        "//services/web_rpc_proxy/frontend:eslintrc_cjs",
        "//services/web_rpc_proxy/frontend:node_modules/@typescript-eslint/eslint-plugin",
        "//services/web_rpc_proxy/frontend:node_modules/@typescript-eslint/parser",
        "//services/web_rpc_proxy/frontend:node_modules/eslint-plugin-react-hooks",
        "//services/web_rpc_proxy/frontend:node_modules/react",
        "//services/web_rpc_proxy/frontend:package_json",
    ] + glob(SRC_PATTERNS),
)
