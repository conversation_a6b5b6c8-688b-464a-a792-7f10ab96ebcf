import axios from "axios";

export type SupportUrl = {
  name: string;
  url: string;
};

export type TenantSupportDetails = {
  tenant: Tenant;
  support_urls?: SupportUrl[];
};

export type Tenant = {
  name: string;
  id: string;
  shard_namespace: string;
  cloud: string;
  auth_configuration: {
    domain: string;
  };
  config: {
    configs: {
      [key: string]: string;
    };
  };
};

export async function getTenants(): Promise<TenantSupportDetails[]> {
  const { data: response }: { data: TenantSupportDetails[] } =
    await axios.get(`/api/tenants`);

  response.sort((a: TenantSupportDetails, b: TenantSupportDetails) =>
    a.tenant.name.localeCompare(b.tenant.name),
  );

  return response;
}
