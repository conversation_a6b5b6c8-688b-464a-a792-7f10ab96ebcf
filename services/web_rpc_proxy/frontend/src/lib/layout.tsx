// react component containing the overall layout of the pages
import React from "react";
import { Breadcrumb, Layout, Menu, theme } from "antd";
import { Link } from "react-router-dom";

const { <PERSON><PERSON>, Footer, Content } = Layout;

type BreadcrumbItem = {
  label: string;
  link: string;
};

type Probs = {
  // key of the menu item to select
  selectedMenuKey?: string;
  // react children nodes to display
  children?: React.ReactNode;
  // breadcrumb items to display
  breadcrumbs: BreadcrumbItem[];
};

export const LayoutComponent = ({
  children,
  selectedMenuKey,
  breadcrumbs,
}: Probs) => {
  const {
    token: { colorBgContainer },
  } = theme.useToken();
  let defaultSelectedKeys: Array<string> = ["home"];
  if (selectedMenuKey) {
    defaultSelectedKeys = [selectedMenuKey];
  }

  return (
    <Layout className="layout">
      <Header>
        <div className="logo" />
        <Menu
          theme="dark"
          mode="horizontal"
          defaultSelectedKeys={defaultSelectedKeys}
          items={[
            {
              key: "home",
              label: <Link to={"/"}>Tenants</Link>,
            },
          ]}
        />
      </Header>
      <Content style={{ padding: "0 50px" }}>
        <Breadcrumb style={{ margin: "16px 0" }}>
          <Breadcrumb.Item>
            <Link to={"/"}>Home</Link>
          </Breadcrumb.Item>
          {breadcrumbs.map((b) => {
            return (
              <Breadcrumb.Item key={b.label}>
                <Link to={b.link}>{b.label}</Link>
              </Breadcrumb.Item>
            );
          })}
        </Breadcrumb>
        <div
          className="site-layout-content"
          style={{ background: colorBgContainer }}
        >
          {children}
        </div>
      </Content>
      <Footer style={{ textAlign: "center" }}>Augment Code - Internal</Footer>
    </Layout>
  );
};

function App() {
  const children: any[] = [];
  return <LayoutComponent children={children} breadcrumbs={[]} />;
}
export default App;
