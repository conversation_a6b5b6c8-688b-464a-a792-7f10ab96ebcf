import React from "react";
import ReactDOM from "react-dom/client";
import "./index.css";
import { createBrowserRouter, RouterProvider } from "react-router-dom";
import BillingPageComponent from "./routes/billing";
import ErrorPage from "./error-page";
// sets up the different routes
const router = createBrowserRouter([
  {
    path: "/",
    element: <BillingPageComponent />,
    errorElement: <ErrorPage />,
  },
]);
const root = ReactDOM.createRoot(document.getElementById("root")!);
root.render(
  <React.StrictMode>
    <RouterProvider router={router} />
  </React.StrictMode>,
);
