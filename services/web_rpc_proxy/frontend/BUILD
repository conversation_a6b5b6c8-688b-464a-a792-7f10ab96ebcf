load("@aspect_rules_js//js:defs.bzl", "js_library")
load("@aspect_rules_ts//ts:defs.bzl", "ts_config")
load("@npm//:defs.bzl", "npm_link_all_packages")
load("@npm//services/web_rpc_proxy/frontend:vite/package_json.bzl", vite_bin = "bin")

npm_link_all_packages()

COMMON = [
    ":index.html",
    ":vite.config.ts",
    ":package.json",
    ":tsconfig",
    "//services/web_rpc_proxy/frontend/src",
    "//services/web_rpc_proxy/frontend/public",
]

ALL_MODULES = [
    ":node_modules",
]

BUILD_MODULES = [
    ":node_modules/@vitejs/plugin-react",
    ":node_modules/eslint-plugin-react-hooks",
    ":node_modules/react-dom",
    ":node_modules/vite",
    ":node_modules/react",
    ":node_modules/typescript",
    ":node_modules/axios",
    ":node_modules/antd",
    ":node_modules/react-json-view",
    ":node_modules/@connectrpc/connect",
    ":node_modules/@connectrpc/connect-web",
    "//services/token_exchange:token_exchange_ts_proto",
    "//services/tenant_watcher:tenant_watcher_ts_proto",
    "//services/auth/central/server:auth_ts_proto",
    "//services/auth/central/server:auth_entities_ts_proto",
    "//services/web_rpc_proxy:web_rpc_proxy_ts_proto",
]

vite_bin.vite(
    name = "frontend",
    srcs = BUILD_MODULES + COMMON,
    args = ["build"],
    chdir = package_name(),
    out_dirs = ["dist"],
    visibility = ["//services/web_rpc_proxy:__subpackages__"],
)

vite_bin.vite_binary(
    name = "start",
    chdir = package_name(),
    data = ALL_MODULES + COMMON,
)

ts_config(
    name = "tsconfig",
    src = "tsconfig.json",
    visibility = ["//services/web_rpc_proxy/frontend:__subpackages__"],
)

js_library(
    name = "package_json",
    srcs = ["package.json"],
    visibility = ["//services/web_rpc_proxy/frontend:__subpackages__"],
)

js_library(
    name = "eslintrc_cjs",
    srcs = [".eslintrc.cjs"],
    visibility = ["//services/web_rpc_proxy/frontend:__subpackages__"],
)
