import { useState } from "react";
import { useQuery, useQueryClient } from "@tanstack/react-query";
import { Box, Heading, Text, Flex } from "@radix-ui/themes";
import DeletionTable from "../components/Deletions/DeletionTable";
import DeletionSubmissionForm from "../components/Deletions/DeletionSubmissionForm";
import DeletionFiltersComponent, {
  type DeletionFilters,
} from "../components/Deletions/DeletionFilters";
import {
  DeletionsResponseSchema,
  DeletionDetailsResponseSchema,
  type DeletionsResponse,
  type Task,
} from "../schemas/deletions";
import { ErrorResponseSchema } from "../schemas/common";

export default function DeletionsPage() {
  const [nextPageToken, setNextPageToken] = useState("");
  const [appliedFilters, setAppliedFilters] = useState<DeletionFilters>({
    userId: "",
    userEmail: "",
    status: "",
  });
  const queryClient = useQueryClient();

  const fetchDeletions = async (
    pageToken: string = "",
    filterParams: DeletionFilters = { userId: "", userEmail: "", status: "" },
  ): Promise<DeletionsResponse> => {
    const params = new URLSearchParams({
      pageSize: "50",
    });

    if (pageToken) {
      params.append("pageToken", pageToken);
    }
    if (filterParams.userId) {
      params.append("userId", filterParams.userId);
    }
    if (filterParams.userEmail) {
      params.append("userEmail", filterParams.userEmail);
    }
    if (filterParams.status) {
      params.append("status", filterParams.status);
    }

    const response = await fetch(`/api/deletions?${params.toString()}`);
    const data = await response.json();

    if (!response.ok || data.error) {
      // Validate error response with Zod
      const errorData = ErrorResponseSchema.safeParse(data);
      const errorMessage = errorData.success
        ? errorData.data.error
        : `Failed to fetch deletions: ${response.statusText}`;
      throw new Error(errorMessage);
    }

    // Validate successful response with Zod
    return DeletionsResponseSchema.parse(data);
  };

  const fetchDeletionDetails = async (deletionId: string): Promise<Task[]> => {
    const response = await fetch(`/api/deletions/${deletionId}`);
    const data = await response.json();

    if (!response.ok || data.error) {
      const errorData = ErrorResponseSchema.safeParse(data);
      const errorMessage = errorData.success
        ? errorData.data.error
        : `Failed to fetch deletion details: ${response.statusText}`;
      throw new Error(errorMessage);
    }

    const validatedData = DeletionDetailsResponseSchema.parse(data);
    return validatedData.tasks;
  };

  const { data, isLoading, isError, error, isFetching, refetch } =
    useQuery<DeletionsResponse>({
      queryKey: ["deletions", nextPageToken, appliedFilters],
      queryFn: () => fetchDeletions(nextPageToken, appliedFilters),
      enabled: true,
      staleTime: 60000, // 1 minute
    });

  const handleLoadMore = () => {
    if (data?.nextPageToken) {
      setNextPageToken(data.nextPageToken);
    }
  };

  const handleFilterSubmit = (newFilters: DeletionFilters) => {
    setAppliedFilters(newFilters);
    setNextPageToken("");
    refetch();
  };

  const handleExpandDeletion = async (deletionId: string): Promise<Task[]> => {
    return fetchDeletionDetails(deletionId);
  };

  const handleSubmissionSuccess = () => {
    // Invalidate and refetch the deletions query to show the new deletion
    queryClient.invalidateQueries({ queryKey: ["deletions"] });
    // Reset pagination to show the latest deletions
    setNextPageToken("");
  };

  const handleRetryTask = async (deletionId: string, taskId: string) => {
    const reason = prompt(
      "Please provide a reason for retrying this task:",
      "",
    );

    if (!reason || reason.trim() === "") {
      // User cancelled or provided empty reason
      return;
    }

    try {
      const response = await fetch("/api/deletions/retry-task", {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          deletionId,
          taskId,
          reason: reason.trim(),
        }),
      });

      if (!response.ok) {
        const errorData = await response.json();
        throw new Error(errorData.error || "Failed to retry task");
      }

      // Invalidate and refetch the deletion details to show updated status
      queryClient.invalidateQueries({
        queryKey: ["deletion-tasks", deletionId],
      });
      // Also invalidate the main deletions list in case the deletion status changed
      queryClient.invalidateQueries({ queryKey: ["deletions"] });
    } catch (error) {
      console.error("Failed to retry task:", error);
      // You might want to show a toast notification here
      alert(
        `Failed to retry task: ${
          error instanceof Error ? error.message : "Unknown error"
        }`,
      );
    }
  };

  if (isError) {
    return (
      <Box p="4">
        <Heading size="6" mb="4">
          Deletions
        </Heading>
        <Box
          p="4"
          style={{
            backgroundColor: "var(--red-2)",
            border: "1px solid var(--red-6)",
            borderRadius: "var(--radius-2)",
          }}
        >
          <Text color="red" weight="bold">
            Error loading deletions
          </Text>
          <Text as="div" size="2" mt="2">
            {error instanceof Error
              ? error.message
              : "An unknown error occurred"}
          </Text>
        </Box>
      </Box>
    );
  }

  return (
    <Box p="4">
      <Flex justify="between" align="center" mb="4">
        <Heading size="6">Deletions</Heading>
        <DeletionSubmissionForm onSubmissionSuccess={handleSubmissionSuccess} />
      </Flex>

      <DeletionFiltersComponent
        initialFilters={appliedFilters}
        onSubmit={handleFilterSubmit}
        isLoading={isLoading || isFetching}
      />

      {appliedFilters &&
        (appliedFilters.userId ||
          appliedFilters.userEmail ||
          appliedFilters.status) && (
          <Box mb="4">
            <Text size="2" color="gray">
              Active filters:{" "}
              {appliedFilters.userId && (
                <Text weight="bold">User ID: {appliedFilters.userId} </Text>
              )}
              {appliedFilters.userEmail && (
                <Text weight="bold">Email: {appliedFilters.userEmail} </Text>
              )}
              {appliedFilters.status && (
                <Text weight="bold">Status: {appliedFilters.status}</Text>
              )}
            </Text>
          </Box>
        )}

      {isLoading && !data ? (
        <Box py="8">
          <Text>Loading deletions...</Text>
        </Box>
      ) : (
        <DeletionTable
          deletions={data?.deletions || []}
          isLoading={isFetching}
          hasMoreResults={!!data?.nextPageToken}
          onLoadMore={handleLoadMore}
          onExpandDeletion={handleExpandDeletion}
          onRetryTask={handleRetryTask}
        />
      )}
    </Box>
  );
}
