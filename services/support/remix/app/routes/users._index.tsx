import { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import { Box, Heading, Text } from "@radix-ui/themes";
import UserSearchInput from "../components/Users/<USER>";
import UserTable from "../components/Users/<USER>";
import {
  UserQueryParamsSchema,
  UsersResponseSchema,
  type UsersResponse,
} from "../schemas/users";
import { ErrorResponseSchema } from "../schemas/common";

export default function UsersPage() {
  const [searchValue, setSearchValue] = useState("");
  const [currentSearch, setCurrentSearch] = useState("");
  const [nextPageToken, setNextPageToken] = useState("");

  const fetchUsers = async (
    searchString: string,
    pageToken: string = "",
  ): Promise<UsersResponse> => {
    // Validate query parameters with Zod
    const queryParams = UserQueryParamsSchema.parse({
      searchString,
      pageToken,
      pageSize: 200,
    });

    const params = new URLSearchParams();
    if (queryParams.searchString)
      params.append("searchString", queryParams.searchString);
    if (queryParams.pageToken)
      params.append("pageToken", queryParams.pageToken);
    params.append("pageSize", queryParams.pageSize.toString());

    const response = await fetch(`/api/users?${params.toString()}`);
    const data = await response.json();

    if (!response.ok || data.error) {
      // Validate error response with Zod
      const errorData = ErrorResponseSchema.safeParse(data);
      const errorMessage = errorData.success
        ? errorData.data.error
        : `Failed to fetch users: ${response.statusText}`;
      throw new Error(errorMessage);
    }

    // Validate successful response with Zod
    return UsersResponseSchema.parse(data);
  };

  const { data, isLoading, isError, error, refetch, isFetching } =
    useQuery<UsersResponse>({
      queryKey: ["users", currentSearch, nextPageToken],
      queryFn: () => fetchUsers(currentSearch, nextPageToken),
      enabled: true,
      staleTime: 60000, // 1 minute
    });

  const handleSearch = (value: string) => {
    setCurrentSearch(value);
    setNextPageToken("");
    refetch();
  };

  const handleLoadMore = () => {
    if (data?.nextPageToken) {
      setNextPageToken(data.nextPageToken);
    }
  };

  const handleUserRemovalSuccess = (userId: string, tenantId: string) => {
    // The toast notification is already handled in TenantRemovalButton
    // This callback can be used for additional actions if needed
    console.log(`User ${userId} successfully removed from tenant ${tenantId}`);
  };

  return (
    <Box py="4">
      <Heading size="6" mb="4">
        Users
      </Heading>

      <Box mb="4">
        <UserSearchInput
          placeholder="Search users by email or ID..."
          searchValue={searchValue}
          onSearchValueChange={setSearchValue}
          onSubmit={handleSearch}
          isLoading={isLoading || isFetching}
        />
        {currentSearch && (
          <Box mt="2">
            <Text size="2" color="gray">
              Current search: <Text weight="bold">{currentSearch}</Text>
            </Text>
          </Box>
        )}
      </Box>

      {isError && (
        <Box
          py="4"
          style={{
            backgroundColor: "var(--red-3)",
            padding: "16px",
            borderRadius: "6px",
            marginBottom: "16px",
          }}
        >
          <Text color="red" weight="bold">
            Error: {error instanceof Error ? error.message : "Unknown error"}
          </Text>
        </Box>
      )}

      <UserTable
        users={data?.users || []}
        isLoading={isLoading || isFetching}
        hasMoreResults={!!data?.nextPageToken}
        onLoadMore={handleLoadMore}
        onUserRemovalSuccess={handleUserRemovalSuccess}
      />
    </Box>
  );
}
