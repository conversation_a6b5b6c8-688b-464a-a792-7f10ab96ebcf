import { LoaderFunctionArgs } from "@remix-run/router";
import { logger } from "@augment-internal/logging";
import { getShredderAdminClient } from "../.server/grpc/shredder-admin";
import {
  DeletionQueryParamsSchema,
  type Deletion,
  type DeletionsResponse,
} from "../schemas/deletions";
import type { ErrorResponse } from "../schemas/common";
import {
  mapDeletionRequestType,
  mapDeletionStatus,
  mapStatusToProto,
  handleShredderError,
} from "../utils/deletion-utils";
import { Scope } from "~services/token_exchange/token_scopes_pb";
import { exchangeIAPToken } from "app/utils/grpc-utils";
import type { PartialMessage } from "@bufbuild/protobuf";
import type { ListDeletionsRequest } from "~services/shredder/shredder_admin_pb";

export const loader = async ({ request }: LoaderFunctionArgs) => {
  const url = new URL(request.url);

  const queryParamsResult = DeletionQueryParamsSchema.safeParse({
    pageSize: url.searchParams.get("pageSize") || "50",
    pageToken: url.searchParams.get("pageToken") || "",
    userId: url.searchParams.get("userId") || "",
    userEmail: url.searchParams.get("userEmail") || "",
    status: url.searchParams.get("status") || "",
  });

  if (!queryParamsResult.success) {
    logger.warn("Invalid query parameters for deletions list", {
      error: queryParamsResult.error.message,
      params: {
        pageSize: url.searchParams.get("pageSize"),
        pageToken: url.searchParams.get("pageToken"),
        userId: url.searchParams.get("userId"),
        userEmail: url.searchParams.get("userEmail"),
        status: url.searchParams.get("status"),
      },
    });
    const errorResponse: ErrorResponse = {
      error: queryParamsResult.error.message,
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const {
    pageSize,
    pageToken,
    userId: filterUserId,
    userEmail: filterUserEmail,
    status: filterStatus,
  } = queryParamsResult.data;

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  const signedToken = await exchangeIAPToken({
    iapToken: IAPJWT,
    scopes: [Scope.SHREDDER_R], // Read access to shredder
  });

  try {
    const shredderClient = getShredderAdminClient();

    // Build the request with optional filters
    const request: PartialMessage<ListDeletionsRequest> = {
      pageSize,
      pageToken,
    };
    if (filterUserId) {
      request.userId = filterUserId;
    }
    if (filterUserEmail) {
      request.userEmail = filterUserEmail;
    }
    if (filterStatus) {
      const protoStatus = mapStatusToProto(filterStatus);
      if (protoStatus !== undefined) {
        request.status = protoStatus;
      }
    }

    const response = await shredderClient.listDeletions(request, {
      headers: {
        Authorization: `Bearer ${signedToken}`,
      },
    });

    const deletions: Deletion[] = response.deletions.map((deletion) => ({
      deletionId: deletion.deletionId,
      status: mapDeletionStatus(deletion.status),
      userId: deletion.request?.userId || "",
      userEmail: deletion.userEmail,
      requestType: mapDeletionRequestType(deletion.request),
      reason: deletion.request?.reason || "",
      createdAt: deletion.createdAt?.toDate().toISOString() || "",
      updatedAt: deletion.updatedAt?.toDate().toISOString(),
      completedAt: deletion.completedAt?.toDate().toISOString(),
    }));

    const successResponse: DeletionsResponse = {
      deletions,
      nextPageToken: response.nextPageToken,
    };

    return Response.json(successResponse);
  } catch (error) {
    return handleShredderError(error, "fetch deletions");
  }
};
