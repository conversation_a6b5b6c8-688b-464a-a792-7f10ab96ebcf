import { ActionFunctionArgs } from "@remix-run/node";
import { ConnectError } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";
import { getAuthCentralClient } from "../.server/grpc/auth-central";
import {
  DeleteSuspensionRequestSchema,
  CreateSuspensionRequestSchema,
} from "../schemas/users";
import type { ErrorResponse } from "../schemas/common";
import {
  exchangeIAPToken,
  connectCodeToHttpStatus,
} from "app/utils/grpc-utils";
import { Scope } from "~services/token_exchange/token_scopes_pb";

/**
 * API endpoint for managing user suspensions
 *
 * POST /api/users/{userId}/suspensions - Create a new suspension
 * DELETE /api/users/{userId}/suspensions - Delete existing suspensions
 *
 * Authentication: Requires X-Goog-IAP-JWT-Assertion header
 *
 * POST Request body:
 * {
 *   "suspension_type": 1, // Integer enum value (1=API_ABUSE, 2=FREE_TRIAL_ABUSE, etc.)
 *   "evidence": "Reason for suspension",
 *   "tenant_id": "optional_tenant_id" // defaults to empty string
 * }
 *
 * POST Success Response (200):
 * {
 *   "suspensionId": "generated_suspension_id",
 *   "tokensDeleted": 5
 * }
 *
 * DELETE Request body:
 * {
 *   "suspension_ids": ["suspension_id_1", "suspension_id_2"],
 *   "tenant_id": "optional_tenant_id" // defaults to empty string
 * }
 *
 * DELETE Success Response (200):
 * {
 *   "suspensionsDeleted": 2,
 * }
 *
 * Error Response:
 * {
 *   "error": "Error message"
 * }
 *
 * Error codes:
 * - 400: Invalid request body or missing user ID
 * - 401: Authentication required
 * - 403: Permission denied
 * - 404: User or suspensions not found
 * - 405: Method not allowed
 * - 500: Internal server error
 */

export async function action({ request, params }: ActionFunctionArgs) {
  const { userId } = params;

  if (!userId) {
    const errorResponse: ErrorResponse = {
      error: "User ID is required",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  if (request.method !== "DELETE" && request.method !== "POST") {
    const errorResponse: ErrorResponse = {
      error: "Method not allowed",
    };
    return Response.json(errorResponse, { status: 405 });
  }

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  if (!IAPJWT) {
    const errorResponse: ErrorResponse = {
      error: "Authentication required",
    };
    return Response.json(errorResponse, { status: 401 });
  }

  // Handle POST request for creating suspension
  if (request.method === "POST") {
    return handleCreateSuspension(request, userId, IAPJWT);
  }

  // Handle DELETE request for deleting suspensions
  return handleDeleteSuspensions(request, userId, IAPJWT);
}

async function handleCreateSuspension(
  request: Request,
  userId: string,
  IAPJWT: string,
) {
  // Parse and validate request body
  let suspensionType: number;
  let evidence: string;
  let tenantId: string;
  try {
    const requestData = await request.json().catch(() => ({}));
    const validatedData = CreateSuspensionRequestSchema.parse(requestData);
    suspensionType = validatedData.suspension_type;
    evidence = validatedData.evidence;
    tenantId = validatedData.tenant_id;
  } catch {
    const errorResponse: ErrorResponse = {
      error:
        "Invalid request body. Expected: { suspension_type: number, evidence: string, tenant_id?: string }",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  // Get signed token for authentication with write access
  const signedToken = await exchangeIAPToken({
    iapToken: IAPJWT,
    scopes: [Scope.AUTH_RW],
  });

  // Create suspension
  try {
    const response = await getAuthCentralClient().createUserSuspension(
      {
        userId,
        tenantId,
        suspensionType, // Now properly typed as number
        evidence,
      },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    logger.info(
      `Successfully created suspension ${response.suspensionId} for user ${userId}`,
      { suspensionType, evidence },
    );

    return Response.json(
      {
        suspensionId: response.suspensionId,
        tokensDeleted: response.tokensDeleted,
      },
      { status: 200 },
    );
  } catch (error) {
    if (error instanceof ConnectError) {
      logger.error(
        `Failed to create suspension for user ${userId}: ${error.message}`,
        { suspensionType, evidence },
      );
      const errorResponse: ErrorResponse = {
        error: error.message,
      };
      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    logger.error(
      `Unexpected error creating suspension for user ${userId}:`,
      error,
    );
    const errorResponse: ErrorResponse = {
      error: "Internal server error",
    };
    return Response.json(errorResponse, { status: 500 });
  }
}

async function handleDeleteSuspensions(
  request: Request,
  userId: string,
  IAPJWT: string,
) {
  // Parse and validate request body
  let suspension_ids: string[];
  let tenant_id: string;
  try {
    const requestData = await request.json().catch(() => ({}));
    const validatedData = DeleteSuspensionRequestSchema.parse(requestData);
    suspension_ids = validatedData.suspension_ids;
    tenant_id = validatedData.tenant_id;
  } catch {
    const errorResponse: ErrorResponse = {
      error:
        "Invalid request body. Expected: { suspension_ids: string[], tenant_id?: string }",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  // Get signed token for authentication with write access
  const signedToken = await exchangeIAPToken({
    iapToken: IAPJWT,
    scopes: [Scope.AUTH_RW],
  });

  // Delete suspensions
  try {
    const response = await getAuthCentralClient().deleteUserSuspensions(
      {
        userId,
        tenantId: tenant_id,
        suspensionIds: suspension_ids,
      },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    logger.info(
      `Successfully deleted ${response.suspensionsDeleted} suspensions for user ${userId}`,
      { suspensionIds: suspension_ids },
    );

    return Response.json(
      {
        suspensionsDeleted: response.suspensionsDeleted,
      },
      { status: 200 },
    );
  } catch (error) {
    // Handle errors from deleteUserSuspensions
    if (error instanceof ConnectError) {
      logger.error(
        `DeleteUserSuspensions error: ${error.code} - ${error.message}`,
        {
          userId,
          suspensionIds: suspension_ids,
          errorCode: error.code,
        },
      );

      const errorResponse: ErrorResponse = {
        error: error.message,
      };
      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    // For non-ConnectError errors, return a generic server error
    logger.error(`Unexpected error in deleteUserSuspensions: ${error}`, {
      userId,
      suspensionIds: suspension_ids,
    });
    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred while deleting suspensions",
    };
    return Response.json(errorResponse, { status: 500 });
  }
}
