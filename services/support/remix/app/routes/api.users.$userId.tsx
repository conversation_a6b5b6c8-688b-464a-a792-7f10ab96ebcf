import { LoaderFunctionArgs } from "@remix-run/router";
import { ConnectError } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";
import { getAuthCentralClient } from "../.server/grpc/auth-central";
import { TenantWatcherCachingClient } from "../.server/grpc/tenant-watcher";
import { exchangeIAPToken, connectCodeToHttpStatus } from "../utils/grpc-utils";
import { getSuspensionTypeName } from "../utils/suspension-utils";
import { type User } from "../schemas/users";
import type { ErrorResponse } from "../schemas/common";

/* eslint-disable import/no-unresolved */
import { Scope } from "~services/token_exchange/token_scopes_pb";
import { TenantTier } from "~services/tenant_watcher/tenant_watcher_pb";
import { CustomerUiRole } from "~services/auth/central/server/auth_entities_pb";
/* eslint-enable import/no-unresolved */

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { userId } = params;

  if (!userId) {
    const errorResponse: ErrorResponse = {
      error: "User ID is required",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const tenantWatcher = TenantWatcherCachingClient.getInstance();
  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  // Exchange IAP token for a service token with AUTH_R scope
  const signedToken = await exchangeIAPToken({
    iapToken: IAPJWT,
    scopes: [Scope.AUTH_R],
  });

  try {
    // Get user by ID
    const response = await getAuthCentralClient().getUsers(
      {
        searchString: userId, // Search by user ID
        pageToken: "",
        pageSize: 1,
      },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    // Check if user was found
    const foundUser = response.users.find((user) => user.id === userId);
    if (!foundUser) {
      const errorResponse: ErrorResponse = {
        error: "User not found",
      };
      return Response.json(errorResponse, { status: 404 });
    }

    // Transform the user to a more frontend-friendly format
    const user: User = {
      id: foundUser.id,
      email: foundUser.email,
      tenants: foundUser.tenants,
      createdAt: foundUser.createdAt?.toDate().toISOString(),
      suspensions: foundUser.suspensions.map((suspension) => ({
        suspensionId: suspension.suspensionId,
        suspensionType: getSuspensionTypeName(suspension.suspensionType),
        evidence: suspension.evidence,
        createdTime: suspension.createdTime?.toDate().toISOString(),
      })),
      suspensionExempt: foundUser.suspensionExempt,
      tenantsWithName: [],
      isAdmin: false,
    };

    // Check if user is admin. Only check if 1 tenant.
    if (user.tenants.length == 1) {
      try {
        const userOnTenantResponse =
          await getAuthCentralClient().getUserOnTenant(
            {
              userId: user.id,
              tenantId: user.tenants[0],
            },
            {
              headers: {
                Authorization: `Bearer ${signedToken}`,
              },
            },
          );
        // Check if user has admin role
        user.isAdmin = userOnTenantResponse.customerUiRoles.includes(
          CustomerUiRole.ADMIN,
        );
      } catch (error) {
        logger.warn(`Failed to get admin status for user ${user.id}:`, error);
        user.isAdmin = false;
      }
    }

    // Populate tenant information
    for (const tenantId of user.tenants) {
      const tenant = await tenantWatcher.tenantFor(tenantId);
      if (tenant) {
        user.tenantsWithName.push({
          id: tenant.id,
          name: tenant.name,
          tier: tenant.tier,
        });
      } else {
        user.tenantsWithName.push({
          id: tenantId,
          name: tenantId,
          tier: TenantTier.TENANT_TIER_UNKNOWN,
        });
      }
    }

    return Response.json(user);
  } catch (error) {
    // Handle errors from getUsers
    if (error instanceof ConnectError) {
      logger.error(`GetUser error: ${error.code} - ${error.message}`);

      const errorResponse: ErrorResponse = {
        error: error.message,
      };
      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    // For non-ConnectError errors, return a generic server error
    logger.error(`Unexpected error in getUser: ${error}`);
    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred while fetching user",
    };
    return Response.json(errorResponse, { status: 500 });
  }
};
