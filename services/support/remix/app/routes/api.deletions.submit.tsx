import { ActionFunctionArgs } from "@remix-run/router";
import { logger } from "@augment-internal/logging";
import { getShredderAdminClient } from "../.server/grpc/shredder-admin";
import { handleShredderError } from "../utils/deletion-utils";
import { exchangeIAPToken } from "../utils/grpc-utils";
import type { ErrorResponse } from "../schemas/common";
import { z } from "zod";
import { Scope } from "~services/token_exchange/token_scopes_pb";
import {
  EnqueueUserDataDeletionRequest,
  AccountDeletionRequest,
  ContentDeletionRequest,
  GdprCcpaDeletionRequest,
  VanguardContentDeletionRequest,
} from "~services/shredder/shredder_pb";

// Schema for the submission request
const SubmitDeletionRequestSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  reason: z.string().min(1, "Reason is required"),
  requestType: z.enum([
    "ACCOUNT_DELETION",
    "CONTENT_DELETION",
    "GDPR_CCPA_DELETION",
    "VANGUARD_CONTENT_DELETION",
  ]),
  over90Minutes: z.boolean().optional(),
  userEmail: z.string().min(1, "User Email is required"),
});

type SubmitDeletionRequest = z.infer<typeof SubmitDeletionRequestSchema>;

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method !== "POST") {
    const errorResponse: ErrorResponse = {
      error: "Method not allowed",
    };
    return Response.json(errorResponse, { status: 405 });
  }

  let requestData: SubmitDeletionRequest;
  try {
    const formData = await request.formData();
    const body = {
      userId: formData.get("userId") as string,
      reason: formData.get("reason") as string,
      requestType: formData.get("requestType") as string,
      over90Minutes: formData.get("over90Minutes") === "true",
      userEmail: formData.get("userEmail") as string,
    };

    requestData = SubmitDeletionRequestSchema.parse(body);
  } catch (error) {
    logger.warn("Invalid request body for deletion submission", {
      error: error instanceof Error ? error.message : String(error),
    });
    const errorResponse: ErrorResponse = {
      error: "Invalid request body",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const { userId, reason, requestType, userEmail } = requestData;

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  const signedToken = await exchangeIAPToken({
    iapToken: IAPJWT,
    scopes: [Scope.SHREDDER_RW, Scope.AUTH_R],
  });

  try {
    const shredderClient = getShredderAdminClient();

    // Create the appropriate request type based on the requestType
    let deletionRequest: EnqueueUserDataDeletionRequest;

    switch (requestType) {
      case "ACCOUNT_DELETION":
        deletionRequest = new EnqueueUserDataDeletionRequest({
          userId,
          reason,
          request: {
            case: "accountDeletion",
            value: new AccountDeletionRequest(),
          },
          userEmail,
        });
        break;
      case "CONTENT_DELETION":
        deletionRequest = new EnqueueUserDataDeletionRequest({
          userId,
          reason,
          request: {
            case: "contentDeletion",
            value: new ContentDeletionRequest(),
          },
          userEmail,
        });
        break;
      case "GDPR_CCPA_DELETION":
        deletionRequest = new EnqueueUserDataDeletionRequest({
          userId,
          reason,
          request: {
            case: "gdprCcpaDeletion",
            value: new GdprCcpaDeletionRequest(),
          },
          userEmail,
        });
        break;
      case "VANGUARD_CONTENT_DELETION":
        deletionRequest = new EnqueueUserDataDeletionRequest({
          userId,
          reason,
          request: {
            case: "vanguardContentDeletion",
            value: new VanguardContentDeletionRequest(),
          },
          userEmail,
        });
        break;
      default: {
        const errorResponse: ErrorResponse = {
          error: "Invalid request type",
        };
        return Response.json(errorResponse, { status: 400 });
      }
    }

    const response = await shredderClient.enqueueUserDataDeletion(
      deletionRequest,
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    logger.info("Successfully submitted deletion request", {
      userId,
      requestType,
      deletionId: response.deletion?.deletionId,
    });

    return Response.json({
      success: true,
      deletionId: response.deletion?.deletionId,
      message: "Deletion request submitted successfully",
    });
  } catch (error) {
    logger.error("Failed to submit deletion request", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      userId,
      requestType,
    });
    return handleShredderError(error, "submit deletion request");
  }
};
