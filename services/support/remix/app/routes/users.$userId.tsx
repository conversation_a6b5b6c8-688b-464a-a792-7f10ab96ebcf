import { use<PERSON><PERSON><PERSON>, <PERSON> } from "@remix-run/react";
import { useQuery } from "@tanstack/react-query";
import { Box, Heading, Text, Flex, Card, Button } from "@radix-ui/themes";
import { ArrowLeftIcon } from "@radix-ui/react-icons";
import { type User } from "../schemas/users";
import { ErrorResponseSchema } from "../schemas/common";
import {
  ExemptionButton,
  SuspendUserButton,
} from "../components/Users/<USER>";
import UserOverviewCard from "../components/Users/<USER>";
import UserSuspensions from "../components/Users/<USER>";
import UserBillingInfo, {
  isEnterpriseTenant,
} from "../components/Users/<USER>";
import UserSubscriptionInfo from "../components/Users/<USER>";
import ManageBilling from "../components/Users/<USER>";

export default function UserDetailPage() {
  const { userId } = useParams();

  // Fetch individual user data
  const {
    data: user,
    isLoading,
    isError,
    error,
  } = useQuery<User>({
    queryKey: ["user", userId],
    queryFn: async () => {
      if (!userId) throw new Error("User ID is required");

      const response = await fetch(`/api/users/${userId}`);
      const data = await response.json();

      if (!response.ok || data.error) {
        const errorData = ErrorResponseSchema.safeParse(data);
        const errorMessage = errorData.success
          ? errorData.data.error
          : `Failed to fetch user: ${response.statusText}`;
        throw new Error(errorMessage);
      }

      return data;
    },
    enabled: !!userId,
    staleTime: 60000, // 1 minute
  });

  if (isLoading) {
    return (
      <Box py="4">
        <Text>Loading user details...</Text>
      </Box>
    );
  }

  if (isError || !user) {
    return (
      <Box py="4">
        <Flex direction="column" gap="4">
          <Link to="/users">
            <Button variant="soft">
              <ArrowLeftIcon /> Back to Users
            </Button>
          </Link>
          <Box
            py="4"
            style={{
              backgroundColor: "var(--red-3)",
              padding: "16px",
              borderRadius: "6px",
            }}
          >
            <Text color="red" weight="bold">
              Error: {error instanceof Error ? error.message : "User not found"}
            </Text>
          </Box>
        </Flex>
      </Box>
    );
  }

  return (
    <Box py="4">
      {/* Header with back button */}
      <Flex align="center" gap="3" mb="4">
        <Link to="/users">
          <Button variant="soft">
            <ArrowLeftIcon /> Back to Users
          </Button>
        </Link>
        <Heading size="6">User Details</Heading>
      </Flex>

      {/* User Overview Card */}
      <UserOverviewCard user={user} />

      {/* Suspensions */}
      <UserSuspensions user={user} />

      {/* Billing Information */}
      <Card size="2" mb="4">
        <Heading size="4" mb="3">
          Billing Information
        </Heading>
        <UserBillingInfo
          user={user}
          tenantId={user.tenantsWithName?.[0]?.id || ""}
        />
      </Card>

      {/* Subscription Information - Only for non-enterprise tenants */}
      {user.tenantsWithName?.[0]?.id &&
        !isEnterpriseTenant(user, user.tenantsWithName?.[0]?.id || "") && (
          <Card size="2" mb="4">
            <Heading size="4" mb="3">
              Subscription Information
            </Heading>
            <UserSubscriptionInfo
              user={user}
              tenantId={user.tenantsWithName?.[0]?.id}
            />
          </Card>
        )}

      {/* Manage User */}
      <Card size="2">
        <Heading size="4" mb="3">
          Manage User
        </Heading>
        <Flex direction="column" gap="3">
          <Box>
            <Text as="div" weight="bold" size="2" mb="2">
              Suspensions
            </Text>
            <Flex gap="2" wrap="wrap">
              <ExemptionButton user={user} />
              <SuspendUserButton user={user} />
            </Flex>
          </Box>
          {user.tenantsWithName?.[0]?.id &&
            !isEnterpriseTenant(user, user.tenantsWithName?.[0]?.id || "") && (
              <Box>
                <Text as="div" weight="bold" size="2" mb="2">
                  Billing
                </Text>
                <ManageBilling
                  user={user}
                  tenantId={user.tenantsWithName?.[0]?.id}
                />
              </Box>
            )}
        </Flex>
      </Card>
    </Box>
  );
}
