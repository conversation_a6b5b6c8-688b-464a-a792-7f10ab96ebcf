import { ActionFunctionArgs } from "@remix-run/router";
import { logger } from "@augment-internal/logging";
import { getShredderAdminClient } from "../.server/grpc/shredder-admin";
import { handleShredderError } from "../utils/deletion-utils";
import { exchangeIAPToken } from "../utils/grpc-utils";
import type { ErrorResponse } from "../schemas/common";
import { z } from "zod";
import { Scope } from "~services/token_exchange/token_scopes_pb";
import { RetryTaskRequest } from "~services/shredder/shredder_admin_pb";

// Schema for the retry task request
const RetryTaskRequestSchema = z.object({
  deletionId: z.string().min(1, "Deletion ID is required"),
  taskId: z.string().min(1, "Task ID is required"),
  reason: z.string().min(1, "Reason is required"),
});

type RetryTaskRequestType = z.infer<typeof RetryTaskRequestSchema>;

export const action = async ({ request }: ActionFunctionArgs) => {
  if (request.method !== "POST") {
    const errorResponse: ErrorResponse = {
      error: "Method not allowed",
    };
    return Response.json(errorResponse, { status: 405 });
  }

  let requestData: RetryTaskRequestType;
  try {
    const body = await request.json();
    requestData = RetryTaskRequestSchema.parse(body);
  } catch (error) {
    logger.warn("Invalid request body for task retry", {
      error: error instanceof Error ? error.message : String(error),
    });
    const errorResponse: ErrorResponse = {
      error: "Invalid request body",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const { deletionId, taskId, reason } = requestData;

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  const signedToken = await exchangeIAPToken({
    iapToken: IAPJWT,
    scopes: [Scope.SHREDDER_RW],
  });

  try {
    const shredderClient = getShredderAdminClient();

    const retryRequest = new RetryTaskRequest({
      deletionId,
      taskId,
      reason,
    });

    await shredderClient.retryTask(retryRequest, {
      headers: {
        Authorization: `Bearer ${signedToken}`,
      },
    });

    logger.info("Successfully updated task status", {
      deletionId,
      taskId,
      reason,
    });

    return Response.json({
      success: true,
      message: "Task retry initiated successfully",
    });
  } catch (error) {
    logger.error("Failed to retry task", {
      error: error instanceof Error ? error.message : String(error),
      stack: error instanceof Error ? error.stack : undefined,
      deletionId,
      taskId,
      reason,
    });
    return handleShredderError(error, "retry task");
  }
};
