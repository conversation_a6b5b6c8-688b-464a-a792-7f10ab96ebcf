import { ActionFunctionArgs } from "@remix-run/node";
import { ConnectError } from "@connectrpc/connect";
import { getAuthCentralClient } from "../.server/grpc/auth-central";
import { UpdateExemptionRequestSchema } from "../schemas/users";
import type { ErrorResponse } from "../schemas/common";
import { logger } from "@augment-internal/logging";
import { Scope } from "~services/token_exchange/token_scopes_pb";
import { exchangeIAPToken, connectCodeToHttpStatus } from "../utils/grpc-utils";

export const action = async ({ request, params }: ActionFunctionArgs) => {
  if (request.method !== "PATCH") {
    const errorResponse: ErrorResponse = {
      error: "Method not allowed",
    };
    return Response.json(errorResponse, { status: 405 });
  }

  const { userId } = params;

  if (!userId) {
    const errorResponse: ErrorResponse = {
      error: "User ID is required",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  if (!IAPJWT) {
    const errorResponse: ErrorResponse = {
      error: "Authentication required",
    };
    return Response.json(errorResponse, { status: 401 });
  }

  // Parse and validate request body
  let exempt: boolean;
  let reason: string;
  try {
    const requestData = await request.json().catch(() => ({}));
    const validatedData = UpdateExemptionRequestSchema.parse(requestData);
    exempt = validatedData.exempt;
    reason = validatedData.reason;
  } catch {
    const errorResponse: ErrorResponse = {
      error: "Invalid request body",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  // Get signed token for authentication with write access
  const signedToken = await exchangeIAPToken({
    iapToken: IAPJWT,
    scopes: [Scope.AUTH_RW],
  });

  // Update suspension exemption
  try {
    const response = await getAuthCentralClient().updateSuspensionExemption(
      {
        userId,
        // Note: tenantId is not required for this operation as it's a user-level setting
        tenantId: "",
        exempt,
        reason,
      },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    logger.info(
      `Successfully updated exemption status for user ${userId} to ${exempt}`,
    );

    return Response.json(
      {
        message: "Exemption status updated successfully",
        userId: response.userId,
        exempt: response.exempt,
      },
      { status: 200 },
    );
  } catch (error) {
    // Handle errors from updateSuspensionExemption
    if (error instanceof ConnectError) {
      logger.error(
        `UpdateSuspensionExemption error: ${error.code} - ${error.message}`,
      );

      const errorResponse: ErrorResponse = {
        error: error.message,
      };

      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    // For non-ConnectError errors, return a generic server error
    logger.error(`Unexpected error in updateSuspensionExemption: ${error}`);
    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred while updating exemption status",
    };
    return Response.json(errorResponse, { status: 500 });
  }
};
