import { LoaderFunctionArgs } from "@remix-run/router";
import { logger } from "@augment-internal/logging";
import { getShredderAdminClient } from "../.server/grpc/shredder-admin";
import {
  TaskStatus,
  type DeletionDetailsResponse,
  type Task,
  type TaskDetails,
} from "../schemas/deletions";
import type { ErrorResponse } from "../schemas/common";
import {
  mapDeletionRequestType,
  mapDeletionStatus,
  handleShredderError,
} from "../utils/deletion-utils";
import type { TaskDetails as ProtoTaskDetails } from "~services/shredder/shredder_admin_pb";
import { exchangeIAPToken } from "app/utils/grpc-utils";
import { Scope } from "~services/token_exchange/token_scopes_pb";

function mapTaskStatus(
  status: number,
): "STATUS_UNKNOWN" | "BLOCKED" | "PENDING" | "COMPLETED" | "FAILED" {
  const mapped = TaskStatus.fromEnumValue(status);
  if (
    mapped === "BLOCKED" ||
    mapped === "PENDING" ||
    mapped === "COMPLETED" ||
    mapped === "FAILED"
  ) {
    return mapped;
  }
  return "STATUS_UNKNOWN";
}

function mapTaskDetails(details: ProtoTaskDetails | undefined): TaskDetails {
  const task = details?.task;
  if (!task || !task.case) {
    return {
      taskType: "Unknown Task",
    };
  }

  switch (task.case) {
    case "analyticsForgetUser":
      return {
        taskType: "Analytics Forget User",
      };
    case "authCentralForgetUser":
      return {
        taskType: "Auth Central Forget User",
      };
    case "authCentralDeleteAccount":
      return {
        taskType: "Auth Central Delete Account",
      };
    case "vanguardExportDeleteBlobs":
      return {
        taskType: "Vanguard Export Delete Blobs",
      };
    case "vanguardExportDeleteRequestData":
      return {
        taskType: "Vanguard Export Delete Request Data",
      };
    case "contentManagerDeleteBlobs":
      return {
        taskType: "Content Manager Delete Blobs",
      };
    case "settingsDeleteUserSettings":
      return {
        taskType: "Settings Delete User Settings",
      };
    case "remoteAgentsDeleteData":
      return {
        taskType: "Remote Agents Delete Data",
      };
    default:
      return {
        taskType: "Unknown Task",
      };
  }
}

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { deletionId } = params;

  if (!deletionId) {
    logger.warn("Deletion details request missing deletionId parameter");
    const errorResponse: ErrorResponse = {
      error: "Deletion ID is required",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  const signedToken = await exchangeIAPToken({
    iapToken: IAPJWT,
    scopes: [Scope.SHREDDER_R], // Read access to shredder
  });

  try {
    const shredderClient = getShredderAdminClient();
    const response = await shredderClient.getDeletion(
      {
        deletionId,
      },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    if (!response.deletion) {
      logger.warn("Deletion not found", { deletionId });
      const errorResponse: ErrorResponse = {
        error: "Deletion not found",
      };
      return Response.json(errorResponse, { status: 404 });
    }

    const tasks: Task[] = response.tasks.map((task) => {
      return {
        taskId: task.taskId,
        status: mapTaskStatus(task.status),
        startedAt: task.startedAt?.toDate().toISOString(),
        updatedAt: task.updatedAt?.toDate().toISOString(),
        completedAt: task.completedAt?.toDate().toISOString(),
        details: mapTaskDetails(task.details),
        statusDetail: task.statusDetail,
      };
    });

    const successResponse: DeletionDetailsResponse = {
      deletion: {
        deletionId: response.deletion.deletionId,
        status: mapDeletionStatus(response.deletion.status),
        userId: response.deletion.request?.userId || "",
        userEmail: response.deletion.userEmail,
        requestType: mapDeletionRequestType(response.deletion.request),
        reason: response.deletion.request?.reason || "",
        createdAt: response.deletion.createdAt?.toDate().toISOString() || "",
        updatedAt: response.deletion.updatedAt?.toDate().toISOString(),
        completedAt: response.deletion.completedAt?.toDate().toISOString(),
        tasks,
      },
      tasks,
    };

    return Response.json(successResponse);
  } catch (error) {
    return handleShredderError(error, "fetch deletion details");
  }
};
