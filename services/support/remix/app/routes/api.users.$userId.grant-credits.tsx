import { ActionFunctionArgs } from "@remix-run/node";
import { ConnectError } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";
import { getTeamManagementClient } from "../.server/grpc/team-management";
import { exchangeIAPToken, connectCodeToHttpStatus } from "../utils/grpc-utils";
import { Scope } from "~services/token_exchange/token_scopes_pb";
import type { ErrorResponse } from "../schemas/common";
import { GrantCreditsRequestSchema } from "../schemas/users";
import { randomUUID } from "crypto";

export const action = async ({ request, params }: ActionFunctionArgs) => {
  const { userId } = params;

  if (!userId) {
    const errorResponse: ErrorResponse = {
      error: "User ID is required",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  if (request.method !== "POST") {
    const errorResponse: ErrorResponse = {
      error: "Method not allowed",
    };
    return Response.json(errorResponse, { status: 405 });
  }

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  if (!IAPJWT) {
    const errorResponse: ErrorResponse = {
      error: "Authentication required",
    };
    return Response.json(errorResponse, { status: 401 });
  }

  // Parse and validate request body
  let requestBody;
  try {
    requestBody = await request.json();
  } catch {
    const errorResponse: ErrorResponse = {
      error: "Invalid JSON in request body",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const validationResult = GrantCreditsRequestSchema.safeParse(requestBody);
  if (!validationResult.success) {
    const errorResponse: ErrorResponse = {
      error: `Validation failed: ${validationResult.error.issues
        .map((issue) => `${issue.path.join(".")}: ${issue.message}`)
        .join(", ")}`,
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const { num_credits, reason } = validationResult.data;

  // Get signed token for authentication with AUTH_RW scope (required for GrantFreeCreditsToUsers)
  const signedToken = await exchangeIAPToken({
    iapToken: IAPJWT,
    scopes: [Scope.AUTH_RW], // AUTH_RW scope required for granting credits
  });

  try {
    const teamManagementClient = getTeamManagementClient();

    // Generate random idempotency key for this request
    const idempotencyKey = randomUUID().slice(0, 16);
    const response = await teamManagementClient.grantFreeCreditsToUsers(
      {
        userCredits: [
          {
            userId,
            numUserMessages: BigInt(num_credits),
          },
        ],
        idempotencyKey,
        reason,
      },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    // Check if the grant was successful
    if (response.results.length === 0) {
      const errorResponse: ErrorResponse = {
        error: "No results returned from credit grant operation",
      };
      return Response.json(errorResponse, { status: 500 });
    }

    const result = response.results[0];
    if (result.status !== 1) {
      // STATUS_SUCCESS = 1
      const errorResponse: ErrorResponse = {
        error: result.errorMessage || "Credit grant failed with unknown error",
      };
      return Response.json(errorResponse, { status: 400 });
    }

    logger.info("Successfully granted credits to user", {
      userId,
      numUserMessages: num_credits,
      reason,
      idempotencyKey,
    });

    return Response.json({
      success: true,
      userId: result.userId,
      creditsGranted: num_credits,
      reason,
    });
  } catch (error) {
    logger.error("Failed to grant credits to user", error, {
      userId,
      numUserMessages: num_credits,
      reason,
    });

    if (error instanceof ConnectError) {
      const errorResponse: ErrorResponse = {
        error: error.message,
      };
      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    const errorResponse: ErrorResponse = {
      error: "An unexpected error occurred while granting credits",
    };
    return Response.json(errorResponse, { status: 500 });
  }
};
