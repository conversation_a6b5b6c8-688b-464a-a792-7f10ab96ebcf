import { LoaderFunctionArgs } from "@remix-run/router";
import { ConnectError } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";
import { getAuthCentralClient } from "../.server/grpc/auth-central";
import { exchangeIAPToken, connectCodeToHttpStatus } from "../utils/grpc-utils";
import { Scope } from "~services/token_exchange/token_scopes_pb";
import type { ErrorResponse } from "../schemas/common";

export const loader = async ({ request, params }: LoaderFunctionArgs) => {
  const { userId } = params;
  const url = new URL(request.url);
  const tenantId = url.searchParams.get("tenantId");

  if (!userId) {
    const errorResponse: ErrorResponse = {
      error: "User ID is required",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  if (!tenantId) {
    const errorResponse: ErrorResponse = {
      error: "Tenant ID is required",
    };
    return Response.json(errorResponse, { status: 400 });
  }

  const IAPJWT = request.headers.get("X-Goog-IAP-JWT-Assertion") ?? "";

  if (!IAPJWT) {
    const errorResponse: ErrorResponse = {
      error: "Authentication required",
    };
    return Response.json(errorResponse, { status: 401 });
  }

  // Get signed token for authentication
  const signedToken = await exchangeIAPToken({
    iapToken: IAPJWT,
    scopes: [Scope.AUTH_R], // Read access for billing info
    tenantId: tenantId,
  });

  try {
    const authCentralClient = getAuthCentralClient();
    const billingInfo = await authCentralClient.getUserBillingInfo(
      {
        userId,
        tenantId,
      },
      {
        headers: {
          Authorization: `Bearer ${signedToken}`,
        },
      },
    );

    return Response.json({
      userId: billingInfo.userId,
      email: billingInfo.email,
      isSelfServeTeam: billingInfo.isSelfServeTeam,
      orbCustomerId: billingInfo.orbCustomerId,
      orbSubscriptionId: billingInfo.orbSubscriptionId,
      stripeCustomerId: billingInfo.stripeCustomerId,
    });
  } catch (error) {
    logger.error("Failed to fetch user billing info", error, {
      userId,
      tenantId,
    });

    if (error instanceof ConnectError) {
      const errorResponse: ErrorResponse = {
        error: `Failed to fetch billing info: ${error.message}`,
      };
      return Response.json(errorResponse, {
        status: connectCodeToHttpStatus(error.code),
      });
    }

    const errorResponse: ErrorResponse = {
      error: "Internal server error",
    };
    return Response.json(errorResponse, { status: 500 });
  }
};
