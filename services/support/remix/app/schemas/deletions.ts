import { z } from "zod";
import { toConstObject } from "../utils/object";
import {
  Deletion_Status,
  Task_Status,
} from "~services/shredder/shredder_admin_pb";

/**
 * Schemas and types for deletion management functionality.
 * Handles validation and type safety for deletion requests, tasks, and responses.
 */

// Convert proto enums to const objects using the utility
export const DeletionStatus = toConstObject(Deletion_Status);
export type DeletionStatus = toConstObject.infer<typeof DeletionStatus>;

export const TaskStatus = toConstObject(Task_Status);
export type TaskStatus = toConstObject.infer<typeof TaskStatus>;

// Create Zod schemas that validate the string values derived from proto enums
export const DeletionStatusSchema = z.enum([
  DeletionStatus.STATUS_UNKNOWN,
  DeletionStatus.PENDING,
  DeletionStatus.COMPLETED,
  DeletionStatus.FAILED,
] as const);

export const TaskStatusSchema = z.enum([
  TaskStatus.STATUS_UNKNOWN,
  TaskStatus.BLOCKED,
  TaskStatus.PENDING,
  TaskStatus.COMPLETED,
  TaskStatus.FAILED,
] as const);

// Schema for task details - simplified for display purposes
export const TaskDetailsSchema = z.object({
  taskType: z.string(),
  description: z.string().optional(),
});

export type TaskDetails = z.infer<typeof TaskDetailsSchema>;

// Schema for individual task data
export const TaskSchema = z.object({
  taskId: z.string(),
  status: TaskStatusSchema,
  startedAt: z.string().datetime().optional(),
  updatedAt: z.string().datetime().optional(),
  completedAt: z.string().datetime().optional(),
  details: TaskDetailsSchema,
  statusDetail: z.string().optional(),
});

export type Task = z.infer<typeof TaskSchema>;

// Schema for deletion request types
export const DeletionRequestTypeSchema = z.enum([
  "CONTENT_DELETION",
  "ACCOUNT_DELETION",
  "GDPR_CCPA_DELETION",
  "VANGUARD_CONTENT_DELETION",
  "UNKNOWN",
]);

export type DeletionRequestType = z.infer<typeof DeletionRequestTypeSchema>;

// Schema for individual deletion data
export const DeletionSchema = z.object({
  deletionId: z.string(),
  status: DeletionStatusSchema,
  userId: z.string(),
  userEmail: z.string().email(),
  requestType: DeletionRequestTypeSchema,
  reason: z.string(),
  createdAt: z.string().datetime(),
  updatedAt: z.string().datetime().optional(),
  completedAt: z.string().datetime().optional(),
  tasks: z.array(TaskSchema).optional(), // Only populated when expanded
});

export type Deletion = z.infer<typeof DeletionSchema>;

// Schema for query parameters
export const DeletionQueryParamsSchema = z.object({
  pageSize: z.coerce
    .number()
    .int()
    .min(1)
    .max(500)
    .default(50)
    .describe("Number of deletions to return per page (1-500)"),
  pageToken: z.string().default(""),
  userId: z.string().default(""),
  userEmail: z.string().default(""),
  status: z.string().default(""),
});

export type DeletionQueryParams = z.infer<typeof DeletionQueryParamsSchema>;

// Schema for successful list response
export const DeletionsResponseSchema = z.object({
  deletions: z.array(DeletionSchema),
  nextPageToken: z.string(),
});

export type DeletionsResponse = z.infer<typeof DeletionsResponseSchema>;

// Schema for successful get deletion response
export const DeletionDetailsResponseSchema = z.object({
  deletion: DeletionSchema,
  tasks: z.array(TaskSchema),
});

export type DeletionDetailsResponse = z.infer<
  typeof DeletionDetailsResponseSchema
>;
