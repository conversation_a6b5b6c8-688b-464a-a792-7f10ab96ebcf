import React, { useState } from "react";
import { useQuery } from "@tanstack/react-query";
import {
  Table,
  Box,
  Text,
  Flex,
  Badge,
  <PERSON><PERSON>,
  Card,
  Heading,
} from "@radix-ui/themes";
import { ChevronDownIcon, ChevronRightIcon, ReloadIcon } from "@radix-ui/react-icons";
import { type Deletion, type Task } from "../../schemas/deletions";

interface DeletionTableProps {
  deletions: Deletion[];
  isLoading: boolean;
  hasMoreResults: boolean;
  onLoadMore: () => void;
  onExpandDeletion: (deletionId: string) => Promise<Task[]>;
  onRetryTask: (deletionId: string, taskId: string) => Promise<void>;
}

// Custom hook to fetch tasks for a specific deletion
function useDeletionTasks(
  deletionId: string,
  enabled: boolean,
  onExpandDeletion: (deletionId: string) => Promise<Task[]>,
) {
  return useQuery<Task[]>({
    queryKey: ["deletion-tasks", deletionId],
    queryFn: () => onExpandDeletion(deletionId),
    enabled,
    staleTime: 30000, // 30 seconds - refresh more frequently than main deletions
    refetchInterval: 30000, // Auto-refresh every 30 seconds when expanded
  });
}

// Component to render tasks for a specific deletion
function DeletionTasks({
  deletionId,
  isExpanded,
  onExpandDeletion,
  getStatusBadge,
  onRetryTask,
}: {
  deletionId: string;
  isExpanded: boolean;
  onExpandDeletion: (deletionId: string) => Promise<Task[]>;
  getStatusBadge: (status: string) => React.ReactNode;
  onRetryTask: (deletionId: string, taskId: string) => Promise<void>;
}) {
  const {
    data: tasks,
    isLoading,
    error,
  } = useDeletionTasks(deletionId, isExpanded, onExpandDeletion);

  if (!isExpanded) return null;

  if (isLoading) {
    return <Text size="2">Loading tasks...</Text>;
  }

  if (error) {
    return (
      <Text size="2" color="red">
        Failed to load tasks
      </Text>
    );
  }

  if (!tasks || tasks.length === 0) {
    return <Text size="2">No tasks available</Text>;
  }

  return (
    <Flex direction="column" gap="2">
      {tasks.map((task) => (
        <Box
          key={task.taskId}
          style={{
            borderLeft: "2px solid var(--blue-9)",
            paddingLeft: "8px",
          }}
        >
          <Flex justify="between" align="center" mb="1">
            <Flex direction="column" gap="1">
              <Text as="div" weight="bold" size="2">
                {task.details.taskType}
              </Text>
              <Text as="div" size="1">
                Task ID: {task.taskId}
              </Text>
            </Flex>
            <Flex align="center" gap="2">
              {getStatusBadge(task.status)}
              {task.status === "FAILED" && (
                <Button
                  size="1"
                  variant="soft"
                  color="blue"
                  onClick={() => onRetryTask(deletionId, task.taskId)}
                  title="Retry task"
                >
                  <ReloadIcon />
                </Button>
              )}
            </Flex>
          </Flex>
          {task.details.description && (
            <Text as="div" size="2" mb="1">
              {task.details.description}
            </Text>
          )}
          <Flex gap="4">
            {task.startedAt && (
              <Text as="div" size="1">
                Started: {new Date(task.startedAt).toLocaleString()}
              </Text>
            )}
            {task.completedAt && (
              <Text as="div" size="1">
                Completed: {new Date(task.completedAt).toLocaleString()}
              </Text>
            )}
          </Flex>
          <Flex gap="4">
            {task.statusDetail && (
              <Text as="div" size="1">
                Detail: {task.statusDetail}
              </Text>
            )}
          </Flex>
        </Box>
      ))}
    </Flex>
  );
}

export default function DeletionTable({
  deletions,
  isLoading,
  hasMoreResults,
  onLoadMore,
  onExpandDeletion,
  onRetryTask,
}: DeletionTableProps) {
  const [expandedRows, setExpandedRows] = useState<Record<string, boolean>>({});

  const toggleRow = (deletionId: string) => {
    setExpandedRows((prev) => ({
      ...prev,
      [deletionId]: !prev[deletionId],
    }));
  };

  const getStatusBadge = (status: string) => {
    switch (status) {
      case "COMPLETED":
        return <Badge color="green">Completed</Badge>;
      case "PENDING":
        return <Badge color="blue">Pending</Badge>;
      case "FAILED":
        return <Badge color="red">Failed</Badge>;
      case "BLOCKED":
        return <Badge color="orange">Blocked</Badge>;
      default:
        return <Badge color="gray">Unknown</Badge>;
    }
  };

  const getRequestTypeBadge = (requestType: string) => {
    switch (requestType) {
      case "CONTENT_DELETION":
        return <Badge color="blue">Content Deletion</Badge>;
      case "ACCOUNT_DELETION":
        return <Badge color="cyan">Account Deletion</Badge>;
      case "GDPR_CCPA_DELETION":
        return <Badge color="purple">GDPR/CCPA Deletion</Badge>;
      case "VANGUARD_CONTENT_DELETION":
        return <Badge color="orange">Vanguard Content Deletion</Badge>;
      default:
        return <Badge color="gray">Unknown</Badge>;
    }
  };

  if (deletions.length === 0 && !isLoading) {
    return (
      <Box py="4">
        <Text>No deletions found.</Text>
      </Box>
    );
  }

  return (
    <Box>
      <Table.Root variant="surface">
        <Table.Header>
          <Table.Row>
            <Table.ColumnHeaderCell width="40px"></Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>User ID</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Deletion ID</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Request Type</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Status</Table.ColumnHeaderCell>
            <Table.ColumnHeaderCell>Created At</Table.ColumnHeaderCell>
          </Table.Row>
        </Table.Header>

        <Table.Body>
          {deletions.map((deletion) => (
            <React.Fragment key={deletion.deletionId}>
              <Table.Row>
                <Table.Cell>
                  <Button
                    variant="ghost"
                    onClick={() => toggleRow(deletion.deletionId)}
                    aria-label={
                      expandedRows[deletion.deletionId]
                        ? "Collapse row"
                        : "Expand row"
                    }
                  >
                    {expandedRows[deletion.deletionId] ? (
                      <ChevronDownIcon />
                    ) : (
                      <ChevronRightIcon />
                    )}
                  </Button>
                </Table.Cell>
                <Table.Cell>
                  <Text size="2" as="span">
                    {deletion.userId}
                  </Text>
                </Table.Cell>
                <Table.Cell>
                  <Text size="2" as="span">
                    {deletion.deletionId}
                  </Text>
                </Table.Cell>
                <Table.Cell>
                  {getRequestTypeBadge(deletion.requestType)}
                </Table.Cell>
                <Table.Cell>{getStatusBadge(deletion.status)}</Table.Cell>
                <Table.Cell>
                  <Text size="2">
                    {new Date(deletion.createdAt).toLocaleString()}
                  </Text>
                </Table.Cell>
              </Table.Row>
              {expandedRows[deletion.deletionId] && (
                <Table.Row>
                  <Table.Cell colSpan={6}>
                    <Card size="1">
                      <Flex direction="column" gap="3">
                        <Box>
                          <Heading size="3">Deletion Details</Heading>
                        </Box>
                        <Box>
                          <Text as="div" weight="bold" size="2">
                            User Email
                          </Text>
                          <Text as="div" size="2">
                            {deletion.userEmail}
                          </Text>
                        </Box>
                        <Box>
                          <Text as="div" weight="bold" size="2">
                            Reason
                          </Text>
                          <Text as="div" size="2">
                            {deletion.reason}
                          </Text>
                        </Box>
                        <Flex gap="4">
                          <Box>
                            <Text as="div" weight="bold" size="2">
                              Created At
                            </Text>
                            <Text as="div" size="2">
                              {new Date(deletion.createdAt).toLocaleString()}
                            </Text>
                          </Box>
                          {deletion.updatedAt && (
                            <Box>
                              <Text as="div" weight="bold" size="2">
                                Updated At
                              </Text>
                              <Text as="div" size="2">
                                {new Date(deletion.updatedAt).toLocaleString()}
                              </Text>
                            </Box>
                          )}
                          {deletion.completedAt && (
                            <Box>
                              <Text as="div" weight="bold" size="2">
                                Completed At
                              </Text>
                              <Text as="div" size="2">
                                {new Date(
                                  deletion.completedAt,
                                ).toLocaleString()}
                              </Text>
                            </Box>
                          )}
                        </Flex>

                        <Box>
                          <Text as="div" weight="bold" size="2" mb="2">
                            Tasks
                          </Text>
                          <DeletionTasks
                            deletionId={deletion.deletionId}
                            isExpanded={expandedRows[deletion.deletionId]}
                            onExpandDeletion={onExpandDeletion}
                            getStatusBadge={getStatusBadge}
                            onRetryTask={onRetryTask}
                          />
                        </Box>
                      </Flex>
                    </Card>
                  </Table.Cell>
                </Table.Row>
              )}
            </React.Fragment>
          ))}
        </Table.Body>
      </Table.Root>

      {hasMoreResults && (
        <Flex justify="center" mt="4">
          <Button onClick={onLoadMore} disabled={isLoading}>
            {isLoading ? "Loading..." : "Load More"}
          </Button>
        </Flex>
      )}
    </Box>
  );
}
