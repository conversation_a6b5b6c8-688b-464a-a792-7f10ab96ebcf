import React, { useCallback, useState, useEffect } from "react";
import { MagnifyingGlassIcon } from "@radix-ui/react-icons";
import { Flex, Box, TextField, Button, Select, Text } from "@radix-ui/themes";

export interface DeletionFilters {
  userId: string;
  userEmail: string;
  status: string;
}

interface DeletionFiltersProps {
  initialFilters: DeletionFilters;
  onSubmit: (filters: DeletionFilters) => void;
  isLoading?: boolean;
}

export default function DeletionFiltersComponent({
  initialFilters,
  onSubmit,
  isLoading,
}: DeletionFiltersProps) {
  const [filters, setFilters] = useState<DeletionFilters>(initialFilters);

  // Update internal state when initialFilters change
  useEffect(() => {
    setFilters(initialFilters);
  }, [initialFilters]);
  const handleSubmit = useCallback(
    (e: React.FormEvent) => {
      e.preventDefault();
      if (onSubmit && filters) {
        onSubmit(filters);
      }
    },
    [onSubmit, filters],
  );

  const handleFilterChange = useCallback(
    (field: keyof DeletionFilters, value: string) => {
      setFilters((prev) => ({
        ...prev,
        [field]: value,
      }));
    },
    [],
  );

  const handleClearFilters = useCallback(() => {
    const clearedFilters = {
      userId: "",
      userEmail: "",
      status: "",
    };
    setFilters(clearedFilters);
    if (onSubmit) {
      onSubmit(clearedFilters);
    }
  }, [onSubmit]);

  const hasActiveFilters =
    filters && (filters.userId || filters.userEmail || filters.status);

  return (
    <form onSubmit={handleSubmit}>
      <Box mb="4">
        <Text size="3" weight="bold" mb="3" as="div">
          Filter Deletions
        </Text>

        <Flex gap="3" direction="column">
          {/* First row: User ID and User Email */}
          <Flex gap="3" align="end">
            <Box flexGrow="1">
              <Text size="2" mb="1" as="div">
                User ID
              </Text>
              <TextField.Root
                placeholder="Enter user ID..."
                value={filters?.userId || ""}
                onChange={(e) => handleFilterChange("userId", e.target.value)}
              />
            </Box>

            <Box flexGrow="1">
              <Text size="2" mb="1" as="div">
                User Email
              </Text>
              <TextField.Root
                placeholder="Enter user email..."
                value={filters?.userEmail || ""}
                onChange={(e) =>
                  handleFilterChange("userEmail", e.target.value)
                }
              />
            </Box>

            <Box>
              <Text size="2" mb="1" as="div">
                Status
              </Text>
              <Select.Root
                value={filters?.status || ""}
                onValueChange={(value) => handleFilterChange("status", value)}
              >
                <Select.Trigger placeholder="All statuses" />
                <Select.Content>
                  <Select.Item value="PENDING">Pending</Select.Item>
                  <Select.Item value="COMPLETED">Completed</Select.Item>
                  <Select.Item value="FAILED">Failed</Select.Item>
                </Select.Content>
              </Select.Root>
            </Box>
          </Flex>

          {/* Second row: Action buttons */}
          <Flex gap="3" align="center">
            <Button type="submit" disabled={isLoading}>
              <MagnifyingGlassIcon height="16" width="16" />
              {isLoading ? "Searching..." : "Apply Filters"}
            </Button>

            {hasActiveFilters && (
              <Button
                type="button"
                variant="soft"
                onClick={handleClearFilters}
                disabled={isLoading}
              >
                Clear Filters
              </Button>
            )}
          </Flex>
        </Flex>
      </Box>
    </form>
  );
}
