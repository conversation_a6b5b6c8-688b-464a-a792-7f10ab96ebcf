import React, { useState } from "react";
import {
  Box,
  Button,
  Dialog,
  Flex,
  Text,
  TextField,
  Select,
  TextArea,
  Callout,
  Checkbox,
} from "@radix-ui/themes";
import {
  InfoCircledIcon,
  ExclamationTriangleIcon,
  PlusIcon,
} from "@radix-ui/react-icons";
import { useToast } from "../ui/Toast";

interface DeletionSubmissionFormProps {
  onSubmissionSuccess?: (deletionId: string) => void;
}

type RequestType =
  | "ACCOUNT_DELETION"
  | "CONTENT_DELETION"
  | "GDPR_CCPA_DELETION"
  | "VANGUARD_CONTENT_DELETION";

interface ValidationErrors {
  userId?: string;
  userEmail?: string;
  reason?: string;
  over90Minutes?: string;
}

// Reusable form field component
interface FormFieldProps {
  label: string;
  error?: string;
  required?: boolean;
  children: React.ReactNode;
}

const FormField: React.FC<FormFieldProps> = ({
  label,
  error,
  required,
  children,
}) => (
  <Box>
    <Text
      as="label"
      size="2"
      weight="bold"
      style={{ display: "block", marginBottom: "4px" }}
    >
      {label} {required && "*"}
    </Text>
    {children}
    {error && (
      <Text size="1" color="red" style={{ display: "block", marginTop: "2px" }}>
        {error}
      </Text>
    )}
  </Box>
);

export default function DeletionSubmissionForm({
  onSubmissionSuccess,
}: DeletionSubmissionFormProps) {
  const [isOpen, setIsOpen] = useState(false);
  const [userId, setUserId] = useState("");
  const [userEmail, setUserEmail] = useState("");
  const [reason, setReason] = useState("");
  const [requestType, setRequestType] =
    useState<RequestType>("CONTENT_DELETION");
  const [over90Minutes, setOver90Minutes] = useState(false);
  const [validationErrors, setValidationErrors] = useState<ValidationErrors>(
    {},
  );
  const [isSubmitting, setIsSubmitting] = useState(false);

  const toast = useToast();

  const validateForm = () => {
    const errors: ValidationErrors = {};

    if (!userId.trim()) errors.userId = "User ID is required";
    if (!userEmail.trim()) errors.userEmail = "User Email is required";
    if (!reason.trim()) errors.reason = "Reason is required";
    if (shouldShow90MinuteCheckbox && !over90Minutes) {
      errors.over90Minutes =
        "You must confirm that it's been >90 minutes since the user requested deletion";
    }

    setValidationErrors(errors);
    return Object.keys(errors).length === 0;
  };

  const handleFormSubmit = async (e: React.FormEvent) => {
    e.preventDefault();

    if (!validateForm()) {
      return;
    }

    setIsSubmitting(true);

    try {
      const formData = new FormData();
      formData.append("userId", userId);
      formData.append("userEmail", userEmail);
      formData.append("requestType", requestType);
      formData.append("reason", reason);
      formData.append("over90Minutes", over90Minutes.toString());

      const response = await fetch("/api/deletions/submit", {
        method: "POST",
        body: formData,
      });

      const data = await response.json();

      if (response.ok && data.success) {
        toast.success({
          title: "Deletion Request Submitted",
          description: `Deletion ID: ${data.deletionId}`,
        });

        if (onSubmissionSuccess) {
          onSubmissionSuccess(data.deletionId);
        }

        handleReset();
        setIsOpen(false);
      } else {
        toast.error({
          title: "Submission Failed",
          description: data.error || "An unknown error occurred.",
        });
      }
    } catch {
      toast.error({
        title: "Submission Failed",
        description: "Network error occurred. Please try again.",
      });
    } finally {
      setIsSubmitting(false);
    }
  };

  const handleReset = () => {
    setUserId("");
    setUserEmail("");
    setReason("");
    setRequestType("CONTENT_DELETION");
    setOver90Minutes(false);
    setValidationErrors({});
  };

  const handleOpenChange = (open: boolean) => {
    setIsOpen(open);
    if (!open) {
      setTimeout(handleReset, 200);
    }
  };

  const shouldShow90MinuteCheckbox =
    requestType === "VANGUARD_CONTENT_DELETION" ||
    requestType === "GDPR_CCPA_DELETION";

  return (
    <Dialog.Root open={isOpen} onOpenChange={handleOpenChange}>
      <Dialog.Trigger>
        <Button>
          <PlusIcon />
          Submit New Deletion Request
        </Button>
      </Dialog.Trigger>

      <Dialog.Content style={{ maxWidth: "500px" }}>
        <Dialog.Title>Submit New Deletion Request</Dialog.Title>
        <Dialog.Description size="2" mb="4">
          Create a new deletion request in the shredder system.
        </Dialog.Description>

        {Object.keys(validationErrors).length > 0 && (
          <Callout.Root color="red" mb="4">
            <Callout.Icon>
              <ExclamationTriangleIcon />
            </Callout.Icon>
            <Callout.Text>{Object.values(validationErrors)[0]}</Callout.Text>
          </Callout.Root>
        )}

        <form onSubmit={handleFormSubmit}>
          <Flex direction="column" gap="3">
            <FormField label="User ID" error={validationErrors.userId} required>
              <TextField.Root
                id="userId"
                placeholder="Enter user ID"
                value={userId}
                onChange={(e) => setUserId(e.target.value)}
                disabled={isSubmitting}
                style={{ marginTop: "4px" }}
              />
            </FormField>

            <FormField
              label="User Email"
              error={validationErrors.userEmail}
              required
            >
              <TextField.Root
                id="userEmail"
                placeholder="Enter user email"
                value={userEmail}
                onChange={(e) => setUserEmail(e.target.value)}
                disabled={isSubmitting}
                style={{ marginTop: "4px" }}
              />
            </FormField>

            <FormField label="Request Type" required>
              <Select.Root
                value={requestType}
                onValueChange={(value) => setRequestType(value as RequestType)}
                disabled={isSubmitting}
              >
                <Select.Trigger />
                <Select.Content>
                  <Select.Item value="CONTENT_DELETION">
                    Content Deletion
                  </Select.Item>
                  <Select.Item value="ACCOUNT_DELETION">
                    Account Deletion
                  </Select.Item>
                  <Select.Item value="GDPR_CCPA_DELETION">
                    GDPR/CCPA Deletion
                  </Select.Item>
                  <Select.Item value="VANGUARD_CONTENT_DELETION">
                    Vanguard Content Deletion
                  </Select.Item>
                </Select.Content>
              </Select.Root>
            </FormField>

            <FormField label="Reason" error={validationErrors.reason} required>
              <TextArea
                id="reason"
                placeholder="Enter reason for deletion request"
                value={reason}
                onChange={(e) => setReason(e.target.value)}
                disabled={isSubmitting}
                rows={3}
                style={{ marginTop: "4px" }}
              />
            </FormField>

            {shouldShow90MinuteCheckbox && (
              <Box
                mt="3"
                p="3"
                style={{
                  backgroundColor: "var(--gray-2)",
                  borderRadius: "6px",
                }}
              >
                <Text
                  as="label"
                  size="2"
                  weight="medium"
                  style={{ display: "flex", alignItems: "center" }}
                >
                  <Checkbox
                    checked={over90Minutes}
                    onCheckedChange={(checked) =>
                      setOver90Minutes(checked === true)
                    }
                    disabled={isSubmitting}
                  />
                  <Text ml="2">
                    It has been &gt;90 minutes since the user requested deletion
                  </Text>
                </Text>
                <Text
                  size="1"
                  color="gray"
                  mt="1"
                  style={{ display: "block", marginLeft: "24px" }}
                >
                  Required for vanguard export data deletion
                </Text>
              </Box>
            )}

            <Flex gap="2" justify="end" mt="4">
              <Dialog.Close>
                <Button type="button" variant="soft" color="gray">
                  Cancel
                </Button>
              </Dialog.Close>
              <Button
                type="submit"
                disabled={
                  isSubmitting ||
                  !userId.trim() ||
                  !userEmail.trim() ||
                  !reason.trim() ||
                  (shouldShow90MinuteCheckbox && !over90Minutes)
                }
              >
                {isSubmitting ? "Submitting..." : "Submit Request"}
              </Button>
            </Flex>
          </Flex>
        </form>

        <Callout.Root mt="4">
          <Callout.Icon>
            <InfoCircledIcon />
          </Callout.Icon>
          <Callout.Text>
            <Text size="2">
              <strong>Important:</strong> This will create a new deletion
              request in the shredder system. Make sure you have the correct
              user ID and a clear reason for the deletion.
            </Text>
          </Callout.Text>
        </Callout.Root>
      </Dialog.Content>
    </Dialog.Root>
  );
}
