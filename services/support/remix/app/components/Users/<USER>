import { TextField, TextArea, Select, Checkbox } from "@radix-ui/themes";

// Shared reason field component
export const ReasonField = <T extends { reason: string }>({
  formData,
  setFormData,
}: {
  formData: T;
  setFormData: (data: T) => void;
}) => (
  <div>
    <label htmlFor="reason" style={{ display: "block", marginBottom: "8px" }}>
      Reason *
    </label>
    <TextArea
      id="reason"
      value={formData.reason}
      onChange={(e) => setFormData({ ...formData, reason: e.target.value })}
      placeholder="Enter the reason for this action. Please include link to Pylon, Slack, or other supporting documentation. This reason is audit logged."
      required
      rows={3}
      maxLength={500}
    />
  </div>
);

// Shared select field component
export interface SelectOption {
  value: string | number;
  label: string;
}

export const SelectField = <T, K extends keyof T>({
  formData,
  setFormData,
  fieldName,
  label,
  placeholder,
  options,
  required = false,
}: {
  formData: T;
  setFormData: (data: T) => void;
  fieldName: K;
  label: string;
  placeholder?: string;
  options: SelectOption[];
  required?: boolean;
}) => (
  <div>
    <label
      htmlFor={String(fieldName)}
      style={{ display: "block", marginBottom: "8px" }}
    >
      {label} {required && "*"}
    </label>
    <Select.Root
      value={String(formData[fieldName])}
      onValueChange={(value) => {
        const parsedValue = options.find(
          (opt) => String(opt.value) === value,
        )?.value;
        setFormData({ ...formData, [fieldName]: parsedValue });
      }}
    >
      <Select.Trigger placeholder={placeholder} />
      <Select.Content>
        {options.map((option) => (
          <Select.Item key={String(option.value)} value={String(option.value)}>
            {option.label}
          </Select.Item>
        ))}
      </Select.Content>
    </Select.Root>
  </div>
);

// Shared number field component
export const NumberField = <T, K extends keyof T>({
  formData,
  setFormData,
  fieldName,
  label,
  placeholder,
  min,
  max,
  required = false,
}: {
  formData: T;
  setFormData: (data: T) => void;
  fieldName: K;
  label: string;
  placeholder?: string;
  min?: number;
  max?: number;
  required?: boolean;
}) => (
  <div>
    <label
      htmlFor={String(fieldName)}
      style={{ display: "block", marginBottom: "8px" }}
    >
      {label} {required && "*"}
    </label>
    <TextField.Root
      id={String(fieldName)}
      type="number"
      value={String(formData[fieldName])}
      onChange={(e) => {
        const value = parseInt(e.target.value, 10);
        setFormData({ ...formData, [fieldName]: isNaN(value) ? 0 : value });
      }}
      placeholder={placeholder}
      min={min}
      max={max}
      required={required}
    />
  </div>
);

// Shared checkbox field component
export const CheckboxField = <T, K extends keyof T>({
  formData,
  setFormData,
  fieldName,
  label,
}: {
  formData: T;
  setFormData: (data: T) => void;
  fieldName: K;
  label: string;
}) => (
  <label style={{ display: "flex", alignItems: "center", gap: "8px" }}>
    <Checkbox
      checked={!!formData[fieldName]}
      onCheckedChange={(checked) =>
        setFormData({ ...formData, [fieldName]: !!checked })
      }
    />
    {label}
  </label>
);
