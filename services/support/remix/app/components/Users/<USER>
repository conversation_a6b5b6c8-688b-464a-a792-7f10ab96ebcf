import { useState } from "react";
import { useMutation, useQueryClient } from "@tanstack/react-query";
import { Button, Dialog, Flex, Text, Box } from "@radix-ui/themes";
import { Cross2Icon } from "@radix-ui/react-icons";
import { useToast } from "../ui/Toast";
import { type User, type UserSuspension } from "../../schemas/users";
import { ErrorResponseSchema } from "../../schemas/common";

interface SuspensionDeleteButtonProps {
  user: User;
  suspension: UserSuspension;
}

export default function SuspensionDeleteButton({
  user,
  suspension,
}: SuspensionDeleteButtonProps) {
  const [isConfirmDialogOpen, setIsConfirmDialogOpen] = useState(false);
  const queryClient = useQueryClient();
  const toast = useToast();

  const deleteSuspensionMutation = useMutation({
    mutationFn: async (): Promise<void> => {
      const response = await fetch(`/api/users/${user.id}/suspensions`, {
        method: "DELETE",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify({
          suspension_ids: [suspension.suspensionId],
          tenant_id: "",
        }),
      });

      const data = await response.json();

      if (!response.ok) {
        const errorData = ErrorResponseSchema.safeParse(data);
        const errorMessage = errorData.success
          ? errorData.data.error
          : `Failed to delete suspension: ${response.statusText}`;
        throw new Error(errorMessage);
      }
    },
    onSuccess: () => {
      // Show success toast
      toast.success({
        title: "Suspension Deleted",
        description: `Suspension ${suspension.suspensionType} has been successfully deleted for ${user.email}`,
        duration: 5000,
      });

      // Invalidate and refetch users query to update the UI
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["user", user.id] });

      // Close the dialog
      setIsConfirmDialogOpen(false);
    },
    onError: (error) => {
      console.error(
        `Failed to delete suspension ${suspension.suspensionId} for user ${user.id}:`,
        error,
      );
      // Keep the dialog open so user can see the error and try again
    },
  });

  const handleDeleteClick = () => {
    setIsConfirmDialogOpen(true);
  };

  const handleConfirmDeletion = () => {
    deleteSuspensionMutation.mutate();
  };

  return (
    <>
      <Button
        size="1"
        variant="ghost"
        color="red"
        onClick={handleDeleteClick}
        disabled={deleteSuspensionMutation.isPending}
        aria-label={`Delete suspension ${suspension.suspensionType}`}
      >
        <Cross2Icon />
      </Button>

      {/* Confirmation Dialog */}
      <Dialog.Root
        open={isConfirmDialogOpen}
        onOpenChange={setIsConfirmDialogOpen}
      >
        <Dialog.Content style={{ maxWidth: 450 }}>
          <Dialog.Title>Delete Suspension</Dialog.Title>
          <Dialog.Description size="2" mb="4">
            Are you sure you want to delete this suspension? This action cannot
            be undone.
          </Dialog.Description>

          <Box mb="4">
            <Flex direction="column" gap="2">
              <Text size="2" weight="bold">
                User:
              </Text>
              <Text size="2">{user.email}</Text>

              <Text size="2" weight="bold">
                Suspension Type:
              </Text>
              <Text size="2">{suspension.suspensionType}</Text>

              <Text size="2" weight="bold">
                Evidence:
              </Text>
              <Text size="2">{suspension.evidence}</Text>

              <Text size="2" weight="bold">
                Created:
              </Text>
              <Text size="2">
                {suspension.createdTime
                  ? new Date(suspension.createdTime).toLocaleString()
                  : "N/A"}
              </Text>
            </Flex>
          </Box>

          {deleteSuspensionMutation.error && (
            <Box
              mb="4"
              p="3"
              style={{
                backgroundColor: "var(--red-3)",
                borderRadius: "6px",
              }}
            >
              <Text size="2" color="red" weight="bold">
                Error: {deleteSuspensionMutation.error.message}
              </Text>
            </Box>
          )}

          <Flex gap="3" mt="4" justify="end">
            <Dialog.Close>
              <Button variant="soft" color="gray">
                Cancel
              </Button>
            </Dialog.Close>
            <Button
              color="red"
              onClick={handleConfirmDeletion}
              disabled={deleteSuspensionMutation.isPending}
            >
              {deleteSuspensionMutation.isPending
                ? "Deleting..."
                : "Delete Suspension"}
            </Button>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>
    </>
  );
}
