import { Box, Text, Flex, Badge, Card, Heading } from "@radix-ui/themes";
import { type User } from "../../schemas/users";
import SuspensionDeleteButton from "./SuspensionDeleteButton";

interface UserSuspensionsProps {
  user: User;
}

export default function UserSuspensions({ user }: UserSuspensionsProps) {
  return (
    <Card size="2" mb="4">
      <Heading size="4" mb="3">
        Suspensions
      </Heading>
      {user.suspensions && user.suspensions.length > 0 ? (
        <Flex direction="column" gap="3">
          {user.suspensions.map((suspension) => (
            <Box
              key={suspension.suspensionId}
              p="3"
              style={{
                backgroundColor: "var(--red-3)",
                borderRadius: "6px",
                border: "1px solid var(--red-6)",
              }}
            >
              <Flex direction="column" gap="2">
                {/* Header row with type and actions */}
                <Flex align="center" justify="between">
                  <Flex align="center" gap="2">
                    <Badge color="red" size="2">
                      {suspension.suspensionType
                        .replace("USER_SUSPENSION_TYPE_", "")
                        .replace(/_/g, " ")}
                    </Badge>
                    <Text size="1" color="gray">
                      {suspension.createdTime
                        ? new Date(suspension.createdTime).toLocaleDateString()
                        : "N/A"}
                    </Text>
                  </Flex>
                  <SuspensionDeleteButton user={user} suspension={suspension} />
                </Flex>

                {/* Evidence row */}
                {suspension.evidence && (
                  <Box>
                    <Text size="1" color="gray" weight="bold">
                      Evidence:
                    </Text>
                    <Text size="2" style={{ fontStyle: "italic" }}>
                      &quot;{suspension.evidence}&quot;
                    </Text>
                  </Box>
                )}
              </Flex>
            </Box>
          ))}
        </Flex>
      ) : (
        <Text size="2" color="gray">
          No active suspensions
        </Text>
      )}
    </Card>
  );
}
