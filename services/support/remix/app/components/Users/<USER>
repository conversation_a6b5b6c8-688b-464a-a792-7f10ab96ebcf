import { Button } from "@radix-ui/themes";

export interface BasicUserActionButtonProps {
  text: string;
  color: "orange" | "blue" | "red" | "green";
  icon: React.ComponentType;
  onClick: () => void;
}

export default function BasicUserActionButton({
  text,
  color,
  icon: IconComponent,
  onClick,
}: BasicUserActionButtonProps) {
  return (
    <Button color={color} size="2" variant="soft" onClick={onClick}>
      <IconComponent />
      {text}
    </Button>
  );
}
