import { Box, Text, Flex, Badge, Card, Heading } from "@radix-ui/themes";
import { type User } from "../../schemas/users";
import TenantRemovalButton from "./TenantRemovalButton";

interface UserOverviewCardProps {
  user: User;
}

export default function UserOverviewCard({ user }: UserOverviewCardProps) {
  return (
    <Card size="2" mb="4">
      <Flex direction="column" gap="3">
        <Flex align="center" gap="3">
          <Heading size="4">{user.email}</Heading>
          {user.suspensionExempt ? (
            <Badge color="blue" size="2">
              Exempt
            </Badge>
          ) : user.suspensions && user.suspensions.length > 0 ? (
            <Badge color="orange" size="2">
              Suspended
            </Badge>
          ) : (
            <Badge color="green" size="2">
              Active
            </Badge>
          )}
        </Flex>

        <Flex gap="4" wrap="wrap">
          <Box>
            <Text as="div" weight="bold" size="2">
              User ID
            </Text>
            <Text as="div" size="2">
              {user.id}
            </Text>
          </Box>

          <Box>
            <Text as="div" weight="bold" size="2">
              Created At
            </Text>
            <Text as="div" size="2">
              {user.createdAt
                ? new Date(user.createdAt).toLocaleString()
                : "N/A"}
            </Text>
          </Box>

          {user.isAdmin && (
            <Box>
              <Text as="div" weight="bold" size="2">
                Role
              </Text>
              <Badge color="blue" size="2">
                Admin
              </Badge>
            </Box>
          )}
        </Flex>

        {/* Tenants */}
        <Box>
          <Text as="div" weight="bold" size="2" mb="2">
            Tenants
          </Text>
          <Flex gap="2" wrap="wrap">
            {user.tenantsWithName.map((tenant) => (
              <Flex key={tenant.id} align="center" gap="1">
                <Badge size="1">{tenant.name}</Badge>
                <TenantRemovalButton user={user} tenantId={tenant.id} />
              </Flex>
            ))}
          </Flex>
        </Box>
      </Flex>
    </Card>
  );
}
