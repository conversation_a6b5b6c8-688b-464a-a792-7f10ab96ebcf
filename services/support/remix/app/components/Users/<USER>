import { Di<PERSON>, But<PERSON>, Text, Flex, Box } from "@radix-ui/themes";
import { useState, ReactNode } from "react";
import type { User } from "../../schemas/users";

interface ModalConfig {
  title: string;
  description: string;
}

interface FormContentProps<TFormData> {
  formData: TFormData;
  setFormData: (data: TFormData) => void;
}

interface UserActionModalProps<
  TFormData = Record<string, string | number | boolean | null>,
> {
  trigger: (open: () => void) => ReactNode;
  user: User;
  modalConfig: ModalConfig;
  form: {
    initialData: TFormData;
    validate: (formData: TFormData) => boolean;
    content: ReactNode | ((props: FormContentProps<TFormData>) => ReactNode);
  };
  isLoading: boolean;
  error: Error | null;
  onConfirm: (formData: TFormData) => Promise<void> | void;
}

export default function UserActionModal<
  TFormData = Record<string, string | number | boolean | null>,
>({
  trigger,
  user,
  modalConfig,
  form,
  onConfirm,
  isLoading,
  error,
}: UserActionModalProps<TFormData>) {
  const [isDialogOpen, setIsDialogOpen] = useState(false);
  const [formData, setFormData] = useState<TFormData>(form.initialData);

  // Check if form is valid
  const isFormValid = form.validate(formData);

  const handleOpen = () => {
    setIsDialogOpen(true);
  };

  const handleClose = () => {
    // Reset form data when closing
    setFormData(form.initialData);
    setIsDialogOpen(false);
  };

  const handleConfirm = async () => {
    if (onConfirm) {
      try {
        await onConfirm(formData);
        handleClose(); // only close on success
      } catch (error) {
        console.error("Failed to confirm action:", error);
      }
    }
  };

  return (
    <>
      {trigger(handleOpen)}

      <Dialog.Root open={isDialogOpen} onOpenChange={setIsDialogOpen}>
        <Dialog.Content style={{ maxWidth: 500 }}>
          <Dialog.Title>{modalConfig.title}</Dialog.Title>
          <Dialog.Description size="2" mb="4">
            {modalConfig.description}
          </Dialog.Description>

          <Box mb="4">
            <Text size="2" weight="bold">
              User:
            </Text>
            <Text size="2">{user.email}</Text>
          </Box>

          <Box mb="4">
            {typeof form.content === "function"
              ? form.content({ formData, setFormData })
              : form.content}
          </Box>

          {/* Error display */}
          {error && (
            <Box
              mb="4"
              p="3"
              style={{
                backgroundColor: "var(--red-3)",
                borderRadius: "6px",
              }}
            >
              <Text size="2" color="red" weight="bold">
                Error: {error.message}
              </Text>
            </Box>
          )}

          <Flex gap="3" mt="4" justify="end">
            <Button
              variant="soft"
              color="gray"
              onClick={handleClose}
              disabled={isLoading}
            >
              Cancel
            </Button>
            <Button
              color="blue"
              onClick={handleConfirm}
              disabled={!isFormValid || isLoading}
              loading={isLoading}
            >
              {isLoading ? "Processing..." : "Confirm"}
            </Button>
          </Flex>
        </Dialog.Content>
      </Dialog.Root>
    </>
  );
}
