import { useQuery } from "@tanstack/react-query";
import { Box, Text, Flex, Spinner, Badge } from "@radix-ui/themes";
import type { User } from "../../schemas/users";
import {
  SubscriptionApiResponseSchema,
  type SubscriptionApiResponse,
} from "../../schemas/users";
import { OrbSubscriptionInfo_SubscriptionStatus } from "~services/auth/central/server/auth_pb";

interface UserSubscriptionInfoProps {
  user: User;
  tenantId: string;
}

// Converts subscription status to a human-readable string
function getStatusDisplayText(
  status: SubscriptionApiResponse["subscription"]["status"],
): string {
  if (typeof status === "string") {
    return status.toUpperCase();
  }

  // Handle enum values
  switch (status) {
    case OrbSubscriptionInfo_SubscriptionStatus.ACTIVE:
      return "ACTIVE";
    case OrbSubscriptionInfo_SubscriptionStatus.ENDED:
      return "ENDED";
    case OrbSubscriptionInfo_SubscriptionStatus.UPCOMING:
      return "UPCOMING";
    case OrbSubscriptionInfo_SubscriptionStatus.UNKNOWN:
    default:
      return "UNKNOWN";
  }
}

function getStatusColor(
  status: SubscriptionApiResponse["subscription"]["status"],
): "green" | "red" | "orange" {
  if (status === OrbSubscriptionInfo_SubscriptionStatus.ACTIVE) {
    return "green";
  }
  if (status === OrbSubscriptionInfo_SubscriptionStatus.ENDED) {
    return "red";
  }
  return "orange";
}

export default function UserSubscriptionInfo({
  user,
  tenantId,
}: UserSubscriptionInfoProps) {
  // Query to fetch subscription info
  const {
    data: subscriptionData,
    isLoading,
    error,
  } = useQuery<SubscriptionApiResponse>({
    queryKey: ["subscription", user.id, tenantId],
    queryFn: async () => {
      const response = await fetch(
        `/api/users/${user.id}/tenants/${tenantId}/subscription`,
      );
      if (!response.ok) {
        throw new Error("Failed to fetch subscription info");
      }
      const data = await response.json();
      return SubscriptionApiResponseSchema.parse(data);
    },
    enabled: !!user.id && !!tenantId,
    staleTime: 300000, // 5 minutes
  });

  if (isLoading) {
    return (
      <Flex align="center" gap="2">
        <Spinner size="1" />
        <Text size="2">Loading subscription info...</Text>
      </Flex>
    );
  }

  if (error) {
    return (
      <Text size="2" color="red">
        Failed to load subscription info
      </Text>
    );
  }

  if (!subscriptionData) {
    return (
      <Text size="2" color="gray">
        No subscription found
      </Text>
    );
  }

  const { subscription, credits } = subscriptionData;

  return (
    <>
      <Flex direction="column" gap="3">
        {/* Status and basic info */}
        <Flex gap="4" wrap="wrap">
          <Box>
            <Text as="div" weight="bold" size="2">
              Status
            </Text>
            <Badge color={getStatusColor(subscription.status)}>
              {getStatusDisplayText(subscription.status)}
            </Badge>
          </Box>
          <Box>
            <Text as="div" weight="bold" size="2">
              Plan ID
            </Text>
            <Text as="div" size="2" style={{ fontFamily: "monospace" }}>
              {subscription.externalPlanId}
            </Text>
          </Box>
          {subscription.status ===
            OrbSubscriptionInfo_SubscriptionStatus.ACTIVE && (
            <Box>
              <Text as="div" weight="bold" size="2">
                Seats
              </Text>
              <Text as="div" size="2">
                {subscription.seats}
              </Text>
            </Box>
          )}
        </Flex>

        {/* Active subscription details */}
        {subscription.status ===
          OrbSubscriptionInfo_SubscriptionStatus.ACTIVE && (
          <Flex gap="4" wrap="wrap">
            {subscription.billingPeriodEndDate && (
              <Box>
                <Text as="div" weight="bold" size="2">
                  Billing Period End
                </Text>
                <Text as="div" size="2">
                  {new Date(
                    subscription.billingPeriodEndDate,
                  ).toLocaleDateString()}
                </Text>
              </Box>
            )}
            <Box>
              <Text as="div" weight="bold" size="2">
                Usage Available
              </Text>
              <Text as="div" size="2">
                {credits.usageUnitsAvailable}
              </Text>
            </Box>
            <Box>
              <Text as="div" weight="bold" size="2">
                Usage Used
              </Text>
              <Text as="div" size="2">
                {credits.usageUnitsUsedThisBillingCycle}
              </Text>
            </Box>
            {subscription.nextBillingCycleAmount && (
              <Box>
                <Text as="div" weight="bold" size="2">
                  Next Billing Amount
                </Text>
                <Text as="div" size="2">
                  ${subscription.nextBillingCycleAmount}
                </Text>
              </Box>
            )}
          </Flex>
        )}
      </Flex>
    </>
  );
}
