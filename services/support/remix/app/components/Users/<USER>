import { useQuery } from "@tanstack/react-query";
import { Flex } from "@radix-ui/themes";
import type { User } from "../../schemas/users";
import {
  SubscriptionApiResponseSchema,
  type SubscriptionApiResponse,
} from "../../schemas/users";
import { OrbSubscriptionInfo_SubscriptionStatus } from "~services/auth/central/server/auth_pb";
import {
  CancelSubscriptionButton,
  GrantCreditsButton,
} from "./ManageUserActions";

interface ManageBillingProps {
  user: User;
  tenantId: string;
}

export default function ManageBilling({ user, tenantId }: ManageBillingProps) {
  // Query to fetch subscription info (reuse the cached data from UserSubscriptionInfo)
  const { data: subscriptionData } = useQuery<SubscriptionApiResponse>({
    queryKey: ["subscription", user.id, tenantId],
    queryFn: async () => {
      const response = await fetch(
        `/api/users/${user.id}/tenants/${tenantId}/subscription`,
      );
      if (!response.ok) {
        throw new Error("Failed to fetch subscription info");
      }
      const data = await response.json();
      return SubscriptionApiResponseSchema.parse(data);
    },
    enabled: !!user.id && !!tenantId,
    staleTime: 300000, // 5 minutes
  });

  // Only show management actions if we have subscription data
  if (!subscriptionData) {
    return (
      <Flex gap="2" wrap="wrap">
        <GrantCreditsButton user={user} tenantId={tenantId} />
      </Flex>
    );
  }

  const { subscription } = subscriptionData;

  return (
    <Flex gap="2" wrap="wrap">
      <GrantCreditsButton user={user} tenantId={tenantId} />
      {subscription.status ===
        OrbSubscriptionInfo_SubscriptionStatus.ACTIVE && (
        <CancelSubscriptionButton user={user} tenantId={tenantId} />
      )}
      {/* Future management actions can be added here */}
    </Flex>
  );
}
