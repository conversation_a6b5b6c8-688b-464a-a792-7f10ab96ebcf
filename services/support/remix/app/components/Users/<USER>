import { useQuery } from "@tanstack/react-query";
import { Box, Text, Flex, Spinner, Link } from "@radix-ui/themes";
import type { User } from "../../schemas/users";
import { TenantTier } from "~services/tenant_watcher/tenant_watcher_pb";

interface UserBillingInfoProps {
  user: User;
  tenantId: string;
}

export function isEnterpriseTenant(user: User, tenantId: string): boolean {
  const tenant = user.tenantsWithName?.find((t) => t.id === tenantId);
  const tier = tenant?.tier;
  // Check for TenantTier.ENTERPRISE enum value
  return tier === TenantTier.ENTERPRISE;
}

function getAccountType(
  user: User,
  tenantId: string,
  isSelfServeTeam: boolean,
): string {
  const tenant = user.tenantsWithName?.find((t) => t.id === tenantId);
  const tier = tenant?.tier;

  if (tier === TenantTier.ENTERPRISE) {
    return "Enterprise User";
  } else if (tier === TenantTier.COMMUNITY) {
    return "Community User";
  } else if (isSelfServeTeam) {
    return "Self-Serve Team";
  } else {
    return "Individual User";
  }
}

interface BillingInfoResponse {
  userId: string;
  email: string;
  isSelfServeTeam: boolean;
  orbCustomerId: string;
  orbSubscriptionId: string;
  stripeCustomerId: string;
}

export default function UserBillingInfo({
  user,
  tenantId,
}: UserBillingInfoProps) {
  const {
    data: billingInfo,
    isLoading,
    error,
  } = useQuery<BillingInfoResponse>({
    queryKey: ["userBillingInfo", user.id, tenantId],
    queryFn: async () => {
      const response = await fetch(
        `/api/users/${user.id}/billing?tenantId=${tenantId}`,
      );
      if (!response.ok) {
        throw new Error(`Failed to fetch billing info: ${response.statusText}`);
      }
      return response.json();
    },
    enabled: !!user.id && !!tenantId && user.tenants.length === 1,
    staleTime: 600000, // 10 minute
  });

  if (isLoading) {
    return (
      <Flex align="center" gap="2">
        <Spinner size="1" />
        <Text size="2">Loading billing info...</Text>
      </Flex>
    );
  }

  if (error) {
    return (
      <Text size="2" color="red">
        Failed to load billing info
      </Text>
    );
  }

  if (!billingInfo) {
    return null;
  }

  return (
    <Flex gap="4" wrap="wrap">
      {billingInfo.stripeCustomerId && (
        <Box>
          <Text as="div" weight="bold" size="2">
            Stripe Customer ID
          </Text>
          <Link
            href={`https://dashboard.stripe.com/customers/${billingInfo.stripeCustomerId}`}
            target="_blank"
            rel="noopener noreferrer"
            size="2"
          >
            {billingInfo.stripeCustomerId}
          </Link>
        </Box>
      )}
      {billingInfo.orbCustomerId && (
        <Box>
          <Text as="div" weight="bold" size="2">
            Orb Customer ID
          </Text>
          <Link
            href={`https://app.withorb.com/customers/${billingInfo.orbCustomerId}`}
            target="_blank"
            rel="noopener noreferrer"
            size="2"
          >
            {billingInfo.orbCustomerId}
          </Link>
        </Box>
      )}
      {billingInfo.orbSubscriptionId && (
        <Box>
          <Text as="div" weight="bold" size="2">
            Orb Subscription ID
          </Text>
          <Link
            href={`https://app.withorb.com/subscriptions/${billingInfo.orbSubscriptionId}`}
            target="_blank"
            rel="noopener noreferrer"
            size="2"
          >
            {billingInfo.orbSubscriptionId}
          </Link>
        </Box>
      )}
      <Box>
        <Text as="div" weight="bold" size="2">
          Account Type
        </Text>
        <Text as="div" size="2">
          {getAccountType(user, tenantId, billingInfo.isSelfServeTeam)}
        </Text>
      </Box>
    </Flex>
  );
}
