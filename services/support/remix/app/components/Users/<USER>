import { useMutation, useQueryClient } from "@tanstack/react-query";
import {
  Cross1Icon,
  ExclamationTriangleIcon,
  PlusIcon,
} from "@radix-ui/react-icons";
import UserActionModal from "./UserActionModal";
import BasicUserActionButton from "./BasicUserActionButton";
import { useToast } from "app/components/ui/Toast";
import type { User } from "../../schemas/users";
import { UserSuspensionType } from "~services/auth/central/server/auth_entities_pb";
import { createFormValidator } from "../../utils/validation";

import {
  ReasonField,
  SelectField,
  NumberField,
  CheckboxField,
  type SelectOption,
} from "./FormFields";
import {
  cancelSubscription,
  suspendUser,
  updateExemption,
  grantCredits,
  type CancelSubscriptionFormData,
  type SuspendUserFormData,
  type UpdateExemptionFormData,
  type GrantCreditsFormData,
} from "../../mutations";

export function CancelSubscriptionButton({
  user,
  tenantId,
}: {
  user: User;
  tenantId: string;
}) {
  const queryClient = useQueryClient();
  const toast = useToast();

  const cancelMutation = useMutation(
    cancelSubscription(user, tenantId, queryClient, toast),
  );

  return (
    <UserActionModal<CancelSubscriptionFormData>
      trigger={(open) => (
        <BasicUserActionButton
          text="Cancel Subscription"
          color="orange"
          icon={Cross1Icon}
          onClick={open}
        />
      )}
      user={user}
      modalConfig={{
        title: "Confirm Subscription Cancellation",
        description:
          "Are you sure you want to cancel this user's subscription? This action cannot be undone. Cancellation by default is scheduled for the end of the current billing period.",
      }}
      form={{
        initialData: {
          cancelImmediately: false,
          reason: "",
        },
        validate: createFormValidator<CancelSubscriptionFormData>(),
        content: ({ formData, setFormData }) => (
          <div
            style={{ display: "flex", flexDirection: "column", gap: "16px" }}
          >
            <CheckboxField
              formData={formData}
              setFormData={setFormData}
              fieldName="cancelImmediately"
              label="Cancel immediately (instead of at end of billing period)"
            />
            <ReasonField formData={formData} setFormData={setFormData} />
          </div>
        ),
      }}
      isLoading={cancelMutation.isPending}
      error={cancelMutation.error}
      onConfirm={async (formData) => {
        return cancelMutation.mutateAsync(formData);
      }}
    />
  );
}

// Available suspension types (excluding UNKNOWN)
const SUSPENSION_TYPES: SelectOption[] = [
  { value: UserSuspensionType.API_ABUSE, label: "API Abuse" },
  { value: UserSuspensionType.FREE_TRIAL_ABUSE, label: "Free Trial Abuse" },
  { value: UserSuspensionType.COMMUNITY_ABUSE, label: "Community Abuse" },
  { value: UserSuspensionType.DISPOSABLE_EMAIL, label: "Disposable Email" },
  { value: UserSuspensionType.PAYMENT_FRAUD, label: "PaymentFraud" },
];

export function SuspendUserButton({ user }: { user: User }) {
  const queryClient = useQueryClient();
  const toast = useToast();

  const suspendMutation = useMutation(suspendUser(user, queryClient, toast));

  return (
    <UserActionModal<SuspendUserFormData>
      trigger={(open) => (
        <BasicUserActionButton
          text="Suspend User"
          color="orange"
          icon={ExclamationTriangleIcon}
          onClick={open}
        />
      )}
      user={user}
      modalConfig={{
        title: "Suspend User",
        description:
          "Suspend this user. This action will restrict the user's access.",
      }}
      form={{
        initialData: {
          suspensionType: UserSuspensionType.API_ABUSE,
          reason: "",
        },
        validate: createFormValidator<SuspendUserFormData>(
          (formData) => formData.suspensionType !== undefined,
        ),
        content: ({ formData, setFormData }) => (
          <div
            style={{ display: "flex", flexDirection: "column", gap: "16px" }}
          >
            <SelectField
              formData={formData}
              setFormData={setFormData}
              fieldName="suspensionType"
              label="Suspension Type"
              placeholder="Select suspension type"
              options={SUSPENSION_TYPES}
              required
            />

            <ReasonField formData={formData} setFormData={setFormData} />
          </div>
        ),
      }}
      isLoading={suspendMutation.isPending}
      error={suspendMutation.error}
      onConfirm={async (formData) => {
        return suspendMutation.mutateAsync(formData);
      }}
    />
  );
}

export function ExemptionButton({ user }: { user: User }) {
  const queryClient = useQueryClient();
  const toast = useToast();

  const isCurrentlyExempt = user.suspensionExempt || false;
  const targetExemptStatus = !isCurrentlyExempt;
  const buttonText = isCurrentlyExempt
    ? "Clear Suspension Exemption"
    : "Exempt From Suspensions";
  const buttonColor = isCurrentlyExempt ? "orange" : "blue";
  const actionText = isCurrentlyExempt ? "remove exemption from" : "exempt";
  const dialogTitle = isCurrentlyExempt ? "Remove Exemption" : "Exempt User";

  const exemptionMutation = useMutation(
    updateExemption(user, targetExemptStatus, queryClient, toast),
  );

  return (
    <UserActionModal<UpdateExemptionFormData>
      trigger={(open) => (
        <BasicUserActionButton
          text={buttonText}
          color={buttonColor}
          icon={ExclamationTriangleIcon}
          onClick={open}
        />
      )}
      user={user}
      modalConfig={{
        title: dialogTitle,
        description: `Are you sure you want to ${actionText} this user from suspensions?`,
      }}
      form={{
        initialData: {
          reason: "",
        },
        validate: createFormValidator<UpdateExemptionFormData>(),
        content: ({ formData, setFormData }) => (
          <ReasonField formData={formData} setFormData={setFormData} />
        ),
      }}
      isLoading={exemptionMutation.isPending}
      error={exemptionMutation.error}
      onConfirm={async (formData) => {
        return exemptionMutation.mutateAsync(formData);
      }}
    />
  );
}

export function GrantCreditsButton({
  user,
  tenantId,
}: {
  user: User;
  tenantId?: string;
}) {
  const queryClient = useQueryClient();
  const toast = useToast();

  const grantCreditsMutation = useMutation(
    grantCredits(user, tenantId, queryClient, toast),
  );

  return (
    <UserActionModal<GrantCreditsFormData>
      trigger={(open) => (
        <BasicUserActionButton
          text="Grant Credits"
          color="blue"
          icon={PlusIcon}
          onClick={open}
        />
      )}
      user={user}
      modalConfig={{
        title: "Grant Credits",
        description:
          "Grant credits to this user. This will add to their current credit balance.",
      }}
      form={{
        initialData: {
          numCredits: 0,
          reason: "",
        },
        validate: createFormValidator<GrantCreditsFormData>(
          (formData) => formData.numCredits > 0 && formData.numCredits <= 1000,
        ),
        content: ({ formData, setFormData }) => (
          <div
            style={{ display: "flex", flexDirection: "column", gap: "16px" }}
          >
            <NumberField
              formData={formData}
              setFormData={setFormData}
              fieldName="numCredits"
              label="Number of Credits"
              placeholder="Enter number of credits to grant"
              min={1}
              max={1000}
              required
            />
            <ReasonField formData={formData} setFormData={setFormData} />
          </div>
        ),
      }}
      isLoading={grantCreditsMutation.isPending}
      error={grantCreditsMutation.error}
      onConfirm={async (formData) => {
        return grantCreditsMutation.mutateAsync(formData);
      }}
    />
  );
}
