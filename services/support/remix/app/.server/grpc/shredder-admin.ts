import { createGrpcTransport } from "@connectrpc/connect-node";
import { type CallOptions, createClient } from "@connectrpc/connect";
import type { PartialMessage } from "@bufbuild/protobuf";
import { logger } from "@augment-internal/logging";

import { Config } from "../config";
// eslint-disable-next-line import/no-unresolved
import { ShredderAdmin } from "~services/shredder/shredder_admin_connect";
import type {
  ListDeletionsRequest,
  ListDeletionsResponse,
  GetDeletionRequest,
  GetDeletionResponse,
  EnqueueUserDataDeletionResponse,
  RetryTaskRequest,
  RetryTaskResponse,
} from "~services/shredder/shredder_admin_pb";
import type { EnqueueUserDataDeletionRequest } from "~services/shredder/shredder_pb";

class ShredderAdminClient {
  constructor(
    private readonly client = createClient(
      ShredderAdmin,
      createGrpcTransport(
        Config.createGrpcTransportOptions(Config.SHREDDER_ENDPOINT || ""),
      ),
    ),
  ) {
    this.listDeletions = this.client.listDeletions.bind(this.client);
    this.getDeletion = this.client.getDeletion.bind(this.client);
    this.enqueueUserDataDeletion = this.client.enqueueUserDataDeletion.bind(
      this.client,
    );
    this.retryTask = this.client.retryTask.bind(this.client);
  }

  listDeletions: (
    request: PartialMessage<ListDeletionsRequest>,
    options?: CallOptions,
  ) => Promise<ListDeletionsResponse>;

  getDeletion: (
    request: PartialMessage<GetDeletionRequest>,
    options?: CallOptions,
  ) => Promise<GetDeletionResponse>;

  enqueueUserDataDeletion: (
    request: PartialMessage<EnqueueUserDataDeletionRequest>,
    options?: CallOptions,
  ) => Promise<EnqueueUserDataDeletionResponse>;

  retryTask: (
    request: PartialMessage<RetryTaskRequest>,
    options?: CallOptions,
  ) => Promise<RetryTaskResponse>;
}

let client: ShredderAdminClient;
/**
 * lazy initializes the shredder admin client. This is to avoid creating a new client for each request.
 * It also makes testing easier by not initializing on module load.
 * @returns ShredderAdminClient
 */
export function getShredderAdminClient() {
  if (!client) {
    if (!Config.SHREDDER_ENDPOINT) {
      throw new Error("SHREDDER_ENDPOINT is not defined");
    }
    client = new ShredderAdminClient();
    logger.info(`Created shredder admin client: ${Config.SHREDDER_ENDPOINT}`);
  }
  return client;
}
