import { mutationOptions } from "./mutationOptions";
import { ErrorResponseSchema } from "../schemas/common";
import type { User } from "../schemas/users";
import type { QueryClient } from "@tanstack/react-query";
import type { useToast } from "../components/ui/Toast";

type ToastContextValue = ReturnType<typeof useToast>;

export interface CancelSubscriptionFormData {
  cancelImmediately: boolean;
  reason: string;
}

export const cancelSubscription = (
  user: User,
  tenantId: string,
  queryClient: QueryClient,
  toast: ToastContextValue,
) =>
  mutationOptions({
    mutationFn: async (formData: CancelSubscriptionFormData) => {
      const response = await fetch(
        `/api/users/${user.id}/tenants/${tenantId}/cancel-subscription`,
        {
          method: "POST",
          headers: { "Content-Type": "application/json" },
          body: JSON.stringify({
            cancel_immediately: formData.cancelImmediately,
            reason: formData.reason,
          }),
        },
      );

      const data = await response.json();
      if (!response.ok) {
        const errorData = ErrorResponseSchema.safeParse(data);
        const errorMessage = errorData.success
          ? errorData.data.error
          : `Failed to cancel subscription: ${response.statusText}`;
        throw new Error(errorMessage);
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ["subscription", user.id, tenantId],
      });
      queryClient.invalidateQueries({ queryKey: ["user", user.id] });
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success({
        title: "Subscription Cancelled",
        description: `Subscription for ${user.email} has been cancelled`,
      });
    },
  });
