import { mutationOptions } from "./mutationOptions";
import { ErrorResponseSchema } from "../schemas/common";
import type { User } from "../schemas/users";
import type { QueryClient } from "@tanstack/react-query";
import type { useToast } from "../components/ui/Toast";

type ToastContextValue = ReturnType<typeof useToast>;

export interface GrantCreditsFormData {
  numCredits: number;
  reason: string;
}

export const grantCredits = (
  user: User,
  tenantId: string | undefined,
  queryClient: QueryClient,
  toast: ToastContextValue,
) =>
  mutationOptions({
    mutationFn: async (formData: GrantCreditsFormData) => {
      const response = await fetch(`/api/users/${user.id}/grant-credits`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          num_credits: formData.numCredits,
          reason: formData.reason.trim(),
        }),
      });

      const data = await response.json();
      if (!response.ok) {
        const errorData = ErrorResponseSchema.safeParse(data);
        const errorMessage = errorData.success
          ? errorData.data.error
          : `Failed to grant credits: ${response.statusText}`;
        throw new Error(errorMessage);
      }
      return data;
    },
    onSuccess: (_, formData) => {
      queryClient.invalidateQueries({
        queryKey: ["subscription", user.id, tenantId || ""],
      });
      queryClient.invalidateQueries({ queryKey: ["user", user.id] });
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success({
        title: "Credits Granted Successfully",
        description: `Granted ${formData.numCredits} credits to ${user.email}`,
      });
    },
  });
