import type { DefaultError, UseMutationOptions } from "@tanstack/react-query";

/**
 * Mutation Options Creator
 * Creates consistent mutation options with proper typing
 */
export function mutationOptions<
  TData = unknown,
  TError = DefaultError,
  TVariables = void,
  TContext = unknown,
>(
  options: UseMutationOptions<TData, TError, TVariables, TContext>,
): UseMutationOptions<TData, TError, TVariables, TContext> {
  return options;
}
