import { mutationOptions } from "./mutationOptions";
import { ErrorResponseSchema } from "../schemas/common";
import type { User } from "../schemas/users";
import type { QueryClient } from "@tanstack/react-query";
import type { useToast } from "../components/ui/Toast";

type ToastContextValue = ReturnType<typeof useToast>;

export interface UpdateExemptionFormData {
  reason: string;
}

export const updateExemption = (
  user: User,
  targetExemptStatus: boolean,
  queryClient: QueryClient,
  toast: ToastContextValue,
) =>
  mutationOptions({
    mutationFn: async (formData: UpdateExemptionFormData) => {
      const response = await fetch(`/api/users/${user.id}/exemption`, {
        method: "PATCH",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          exempt: targetExemptStatus,
          reason: formData.reason,
        }),
      });

      const data = await response.json();
      if (!response.ok) {
        const errorData = ErrorResponseSchema.safeParse(data);
        const errorMessage = errorData.success
          ? errorData.data.error
          : `Failed to update exemption: ${response.statusText}`;
        throw new Error(errorMessage);
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["users"] });
      queryClient.invalidateQueries({ queryKey: ["user", user.id] });
      const action = targetExemptStatus ? "exempted" : "unexempted";
      toast.success({
        title: `User ${action}`,
        description: `${user.email} has been successfully ${action} from suspensions`,
      });
    },
  });
