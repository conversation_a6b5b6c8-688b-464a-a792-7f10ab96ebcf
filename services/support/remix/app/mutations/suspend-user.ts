import { mutationOptions } from "./mutationOptions";
import { ErrorResponseSchema } from "../schemas/common";
import type { User } from "../schemas/users";
import type { QueryClient } from "@tanstack/react-query";
import type { useToast } from "../components/ui/Toast";

type ToastContextValue = ReturnType<typeof useToast>;

export interface SuspendUserFormData {
  suspensionType: number;
  reason: string;
}

export const suspendUser = (
  user: User,
  queryClient: QueryClient,
  toast: ToastContextValue,
) =>
  mutationOptions({
    mutationFn: async (formData: SuspendUserFormData) => {
      const response = await fetch(`/api/users/${user.id}/suspensions`, {
        method: "POST",
        headers: { "Content-Type": "application/json" },
        body: JSON.stringify({
          suspension_type: formData.suspensionType,
          evidence: formData.reason.trim(),
          tenant_id: user.tenantsWithName?.[0]?.id || "",
        }),
      });

      const data = await response.json();
      if (!response.ok) {
        const errorData = ErrorResponseSchema.safeParse(data);
        const errorMessage = errorData.success
          ? errorData.data.error
          : `Failed to suspend user: ${response.statusText}`;
        throw new Error(errorMessage);
      }
      return data;
    },
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: ["user", user.id] });
      queryClient.invalidateQueries({ queryKey: ["users"] });
      toast.success({
        title: "User Suspended",
        description: `${user.email} has been suspended`,
      });
    },
  });
