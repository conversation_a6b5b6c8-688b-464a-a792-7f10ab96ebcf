/**
 * Validation utilities for user action forms
 */

/**
 * Validates that a reason field is not empty
 */
export const validateReason = (reason: string): boolean => {
  return reason.trim().length > 0;
};

/**
 * Creates a form validator that always checks the reason field
 * and optionally runs additional validation
 */
export const createFormValidator = <T extends { reason: string }>(
  additionalValidation?: (formData: T) => boolean,
) => {
  return (formData: T): boolean => {
    if (!validateReason(formData.reason)) return false;
    return additionalValidation ? additionalValidation(formData) : true;
  };
};
