/* eslint-disable import/no-unresolved */
import { UserSuspensionType } from "~services/auth/central/server/auth_entities_pb";
/* eslint-enable import/no-unresolved */

/**
 * Converts a UserSuspensionType enum value to a human-readable string.
 * @param suspensionType The suspension type enum value
 * @returns A human-readable string representation of the suspension type
 */
export function getSuspensionTypeName(
  suspensionType: UserSuspensionType,
): string {
  switch (suspensionType) {
    case UserSuspensionType.UNKNOWN:
      return "Unknown";
    case UserSuspensionType.API_ABUSE:
      return "API Abuse";
    case UserSuspensionType.FREE_TRIAL_ABUSE:
      return "Free Trial Abuse";
    case UserSuspensionType.COMMUNITY_ABUSE:
      return "Community Abuse";
    case UserSuspensionType.DISPOSABLE_EMAIL:
      return "Disposable Email";
    case UserSuspensionType.PAYMENT_FRAUD:
      return "Payment Fraud";
    default:
      return "Unknown";
  }
}
