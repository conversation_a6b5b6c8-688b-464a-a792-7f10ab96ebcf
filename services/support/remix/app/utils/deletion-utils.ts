import { ConnectError } from "@connectrpc/connect";
import { logger } from "@augment-internal/logging";
import { connectCodeToHttpStatus } from "./grpc-utils";
import { DeletionStatus, type DeletionRequestType } from "../schemas/deletions";
import type { ErrorResponse } from "../schemas/common";
import type { EnqueueUserDataDeletionRequest } from "~services/shredder/shredder_pb";
import { Deletion_Status } from "~services/shredder/shredder_admin_pb";

/**
 * Shared utilities for deletion management functionality.
 */

// Helper function to map proto deletion request type to our enum
export function mapDeletionRequestType(
  request: EnqueueUserDataDeletionRequest | undefined,
): DeletionRequestType {
  if (!request || !request.request) return "UNKNOWN";

  switch (request.request.case) {
    case "contentDeletion":
      return "CONTENT_DELETION";
    case "accountDeletion":
      return "ACCOUNT_DELETION";
    case "gdprCcpaDeletion":
      return "GDPR_CCPA_DELETION";
    case "vanguardContentDeletion":
      return "VANGUARD_CONTENT_DELETION";
    default:
      return "UNKNOWN";
  }
}

// Helper function to map proto deletion status to our enum
export function mapDeletionStatus(
  status: number,
): "STATUS_UNKNOWN" | "PENDING" | "COMPLETED" | "FAILED" {
  const mapped = DeletionStatus.fromEnumValue(status);
  if (mapped === "PENDING" || mapped === "COMPLETED" || mapped === "FAILED") {
    return mapped;
  }
  return "STATUS_UNKNOWN";
}

// Helper function to map our status enum back to proto enum
export function mapStatusToProto(status: string): Deletion_Status | undefined {
  switch (status) {
    case DeletionStatus.PENDING:
      return Deletion_Status.PENDING;
    case DeletionStatus.COMPLETED:
      return Deletion_Status.COMPLETED;
    case DeletionStatus.FAILED:
      return Deletion_Status.FAILED;
    case DeletionStatus.STATUS_UNKNOWN:
      return Deletion_Status.STATUS_UNKNOWN;
    default:
      return undefined;
  }
}

/**
 * Handles shredder API errors with consistent error responses.
 */
export function handleShredderError(
  error: unknown,
  operation: string,
): Response {
  if (error instanceof ConnectError) {
    logger.error(`Failed to ${operation}`, {
      operation,
      errorCode: error.code,
      errorMessage: error.message,
      connectError: true,
    });

    const httpStatus = connectCodeToHttpStatus(error.code);
    const errorResponse: ErrorResponse = {
      error: `Failed to ${operation}: ${error.message}`,
    };
    return Response.json(errorResponse, { status: httpStatus });
  }

  logger.error(`Failed to ${operation} with unexpected error`, {
    operation,
    error: error instanceof Error ? error.message : String(error),
    stack: error instanceof Error ? error.stack : undefined,
    connectError: false,
  });

  const errorResponse: ErrorResponse = {
    error: "Internal server error",
  };
  return Response.json(errorResponse, { status: 500 });
}
