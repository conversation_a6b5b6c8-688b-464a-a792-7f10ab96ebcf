import { Descriptions, Divider } from "antd";
import {
  ViewedContentEvent,
  RequestData,
  showTimeStamp,
} from "../../lib/requests";
import { WarningComponent } from "./Warning";
import { BlobLink } from "../../lib/blob_components";

function ViewedContentEventComponent({ doc }: { doc: ViewedContentEvent }) {
  if (doc.visibleContent !== undefined) {
    return <pre className="language-plaintext">{doc.visibleContent}</pre>;
  } else {
    return <WarningComponent />;
  }
}

function ViewedContent2Html(
  viewedContents: ViewedContentEvent[],
  tenantName: string,
) {
  return (
    <div>
      {viewedContents.map((d, idx) => {
        // Convert timestamp to Date object using helper function
        const timestamp = showTimeStamp(new Date(d.timestamp));
        const lineCount = d.lineEnd - d.lineStart + 1;

        return (
          <div key={idx}>
            <Divider></Divider>
            <Descriptions bordered column={3}>
              <Descriptions.Item label="File">
                <BlobLink
                  blobName={d.fileBlobName}
                  filePath={d.path}
                  tenantName={tenantName}
                />
              </Descriptions.Item>
              <Descriptions.Item label="Line Range">
                {d.lineStart} ~ {d.lineEnd} ({lineCount} lines)
              </Descriptions.Item>
              <Descriptions.Item label="Character Range">
                {d.charStart} ~ {d.charEnd} ({d.visibleContent.length} chars)
              </Descriptions.Item>
              <Descriptions.Item label="Timestamp (Pacific Time)">
                {timestamp}
              </Descriptions.Item>
            </Descriptions>
            <ViewedContentEventComponent doc={d} />
          </div>
        );
      })}
    </div>
  );
}

export function ViewedContentComponent({
  requestData,
  tenantName,
}: {
  requestData: RequestData;
  tenantName: string;
}) {
  // Check for viewed content in completion host requests
  if (
    requestData.completionHostRequest?.recencyInfo?.viewedContents !==
      undefined &&
    requestData.completionHostRequest.recencyInfo.viewedContents.length > 0
  ) {
    const viewedContents =
      requestData.completionHostRequest.recencyInfo.viewedContents;
    return ViewedContent2Html(viewedContents, tenantName);
  }

  return <WarningComponent />;
}
