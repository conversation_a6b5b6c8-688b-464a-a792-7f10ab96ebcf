local monitoringLib = import 'deploy/gcp/monitoring-lib.jsonnet';
function(cloud)
  local shredderDeletionTasksErrorSpec = {
    displayName: 'Shredder Deletion Tasks Errors',
    conditionPrometheusQueryLanguage: {
      duration: '0s',
      evaluationInterval: '30s',
      labels: { severity: 'warning' },
      // we check if the metric increased in the last minute or if it exists now but didn't exist 2 minute ago (corresponds to a recent change)
      // Prometheus states that the value of a newly created counter is always 0, regardless of what the actual first reported value is.
      // So when we "increment" a counter that wasn't explicitly initialized before, it won't trigger the first `increase` check.
      // The second check filters all counters that existed 2 minutes ago, leaving only counters that were just created.
      query: |||
        increase(au_shredder_task_latency_count{"status"="FAILED"}[2m]) > 0 or
        (au_shredder_task_latency_count{"status"="FAILED"} unless au_shredder_task_latency_count{"status"="FAILED"} offset 2m)
      |||,
    },
  };

  [
    monitoringLib.alertPolicy(
      cloud,
      shredderDeletionTasksErrorSpec,
      'shredder-deletion-tasks-errors',
      'Shredder deletion tasks returned errors',
      team='insights',
    ),
  ]
