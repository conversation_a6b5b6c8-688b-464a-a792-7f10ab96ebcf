package tasks

import (
	"context"
	"errors"
	"testing"

	authclient "github.com/augmentcode/augment/services/auth/central/client"
	auth_entities "github.com/augmentcode/augment/services/auth/central/server/auth_entities_proto"
	authpb "github.com/augmentcode/augment/services/auth/central/server/proto"
	pb "github.com/augmentcode/augment/services/shredder/admin_proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// Test constants for auth central delete account tests
const (
	testAuthUserID    = "auth_user123"
	testAuthUserEmail = "<EMAIL>"
	testAuthToken     = "auth-test-token"
	testAuthTenantID  = "auth_tenant123"
)

func createAuthCentralDeleteAccountTestExecutor() (*AuthCentralDeleteAccountExecutor, *authclient.MockAuthClient, *mockTeamManagementClient, *tokenexchangeclient.MockTokenExchangeClient) {
	mockAuth := authclient.NewMockAuthClient()
	mockTeamMgmt := &mockTeamManagementClient{}
	mockTokenExchange := tokenexchangeclient.NewMockTokenExchangeClient()

	executor := NewAuthCentralDeleteAccountExecutor(
		mockAuth,
		mockTeamMgmt,
		mockTokenExchange,
	)

	return executor, mockAuth, mockTeamMgmt, mockTokenExchange
}

func createAuthCentralDeleteAccountTaskDetails() *pb.TaskDetails {
	return &pb.TaskDetails{
		Task: &pb.TaskDetails_AuthCentralDeleteAccount{
			AuthCentralDeleteAccount: &pb.AuthCentralDeleteAccountTask{},
		},
	}
}

func TestNewAuthCentralDeleteAccountExecutor(t *testing.T) {
	executor, _, _, _ := createAuthCentralDeleteAccountTestExecutor()
	require.NotNil(t, executor)
}

func TestAuthCentralDeleteAccountExecute_InvalidTaskDetails(t *testing.T) {
	executor, _, _, _ := createAuthCentralDeleteAccountTestExecutor()

	_, err := executor.Execute(context.Background(), testAuthUserID, testAuthUserEmail, nil)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")

	// Test with wrong task details type
	wrongDetails := &pb.TaskDetails{
		Task: &pb.TaskDetails_ContentManagerDeleteBlobs{
			ContentManagerDeleteBlobs: &pb.ContentManagerDeleteBlobsTask{},
		},
	}
	_, err = executor.Execute(context.Background(), testAuthUserID, testAuthUserEmail, wrongDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")
}

func TestAuthCentralDeleteAccountExecute_TokenExchangeFailure(t *testing.T) {
	executor, _, _, mockTokenExchange := createAuthCentralDeleteAccountTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return("", errors.New("token exchange failed"))

	taskDetails := createAuthCentralDeleteAccountTaskDetails()
	_, err := executor.Execute(context.Background(), testAuthUserID, testAuthUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to get request context")

	mockTokenExchange.AssertExpectations(t)
}

func TestAuthCentralDeleteAccountExecute_GetUserFailure(t *testing.T) {
	executor, mockAuth, _, mockTokenExchange := createAuthCentralDeleteAccountTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testAuthToken, nil)

	mockAuth.On("GetUser", mock.Anything, mock.Anything, testAuthUserID, (*string)(nil)).
		Return((*auth_entities.User)(nil), errors.New("get user failed"))

	taskDetails := createAuthCentralDeleteAccountTaskDetails()
	_, err := executor.Execute(context.Background(), testAuthUserID, testAuthUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to get user")

	mockTokenExchange.AssertExpectations(t)
	mockAuth.AssertExpectations(t)
}

func TestAuthCentralDeleteAccountExecute_UserInMultipleTenants(t *testing.T) {
	executor, mockAuth, _, mockTokenExchange := createAuthCentralDeleteAccountTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testAuthToken, nil)

	user := &auth_entities.User{
		Id:      testAuthUserID,
		Email:   testAuthUserEmail,
		Tenants: []string{"tenant1", "tenant2"}, // Multiple tenants
	}
	mockAuth.On("GetUser", mock.Anything, mock.Anything, testAuthUserID, (*string)(nil)).
		Return(user, nil)

	taskDetails := createAuthCentralDeleteAccountTaskDetails()
	_, err := executor.Execute(context.Background(), testAuthUserID, testAuthUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "user is in multiple tenants")

	mockTokenExchange.AssertExpectations(t)
	mockAuth.AssertExpectations(t)
}

func TestAuthCentralDeleteAccountExecute_Success(t *testing.T) {
	executor, mockAuth, mockTeamMgmt, mockTokenExchange := createAuthCentralDeleteAccountTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testAuthToken, nil)

	user := &auth_entities.User{
		Id:      testAuthUserID,
		Email:   testAuthUserEmail,
		Tenants: []string{testAuthTenantID},
	}
	mockAuth.On("GetUser", mock.Anything, mock.Anything, testAuthUserID, (*string)(nil)).
		Return(user, nil)

	mockTeamMgmt.On("DeleteAccount", mock.Anything, mock.Anything, testAuthUserID, testAuthTenantID).
		Return(&authpb.DeleteAccountResponse{DeletionType: authpb.AccountDeletionType_ACCOUNT_DELETION_TYPE_INDIVIDUAL}, nil)

	taskDetails := createAuthCentralDeleteAccountTaskDetails()
	statusDetail, err := executor.Execute(context.Background(), testAuthUserID, testAuthUserEmail, taskDetails)
	require.NoError(t, err)
	require.Equal(t, "Successfully deleted account for user: ACCOUNT_DELETION_TYPE_INDIVIDUAL", statusDetail)

	mockTokenExchange.AssertExpectations(t)
	mockAuth.AssertExpectations(t)
	mockTeamMgmt.AssertExpectations(t)
}
