package tasks

import (
	"context"
	"errors"
	"testing"
	"time"

	ricentralpb "github.com/augmentcode/augment/services/request_insight/central/proto"
	pb "github.com/augmentcode/augment/services/shredder/admin_proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func createVanguardExportDeleteRequestDataTestExecutor() (*VanguardExportDeleteRequestDataExecutor, *mockRequestInsightCentralClient, *tokenexchangeclient.MockTokenExchangeClient) {
	mockRequestInsightCentral := &mockRequestInsightCentralClient{}
	mockTokenExchange := tokenexchangeclient.NewMockTokenExchangeClient()

	executor := NewVanguardExportDeleteRequestDataExecutor(
		mockRequestInsightCentral,
		mockTokenExchange,
	)

	return executor, mockRequestInsightCentral, mockTokenExchange
}

func createVanguardExportDeleteRequestDataTaskDetails() *pb.TaskDetails {
	return &pb.TaskDetails{
		Task: &pb.TaskDetails_VanguardExportDeleteRequestData{
			VanguardExportDeleteRequestData: &pb.VanguardExportDeleteRequestDataTask{},
		},
	}
}

func TestNewVanguardExportDeleteRequestDataExecutor(t *testing.T) {
	executor, _, _ := createVanguardExportDeleteRequestDataTestExecutor()
	require.NotNil(t, executor)
}

func TestVanguardExportDeleteRequestDataExecute_InvalidTaskDetails(t *testing.T) {
	executor, _, _ := createVanguardExportDeleteRequestDataTestExecutor()

	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, nil)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")

	// Test with wrong task details type
	wrongDetails := &pb.TaskDetails{
		Task: &pb.TaskDetails_AnalyticsForgetUser{
			AnalyticsForgetUser: &pb.AnalyticsForgetUserTask{},
		},
	}
	_, err = executor.Execute(context.Background(), testUserID, testUserEmail, wrongDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")
}

func TestVanguardExportDeleteRequestDataExecute_TokenExchangeFailure(t *testing.T) {
	executor, _, mockTokenExchange := createVanguardExportDeleteRequestDataTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", []tokenscopesproto.Scope{tokenscopesproto.Scope_REQUEST_ADMIN}).
		Return("", errors.New("token exchange failed"))

	taskDetails := createVanguardExportDeleteRequestDataTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to get service token")

	mockTokenExchange.AssertExpectations(t)
}

func TestVanguardExportDeleteRequestDataExecute_DeleteEventsFailure(t *testing.T) {
	executor, mockRequestInsightCentral, mockTokenExchange := createVanguardExportDeleteRequestDataTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", []tokenscopesproto.Scope{tokenscopesproto.Scope_REQUEST_ADMIN}).
		Return(testToken, nil)

	mockRequestInsightCentral.
		On("DeleteEventsForUser", mock.Anything, mock.Anything, testUserID, (*time.Time)(nil), mock.Anything).
		Return((*ricentralpb.DeleteEventsForUserResponse)(nil), errors.New("delete events failed"))

	taskDetails := createVanguardExportDeleteRequestDataTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err)

	mockTokenExchange.AssertExpectations(t)
	mockRequestInsightCentral.AssertExpectations(t)
}

func TestVanguardExportDeleteRequestDataExecute_FailedEventDeletions(t *testing.T) {
	executor, mockRequestInsightCentral, mockTokenExchange := createVanguardExportDeleteRequestDataTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", []tokenscopesproto.Scope{tokenscopesproto.Scope_REQUEST_ADMIN}).
		Return(testToken, nil)

	response := &ricentralpb.DeleteEventsForUserResponse{
		NumSuccessfulEventDeletions: 10,
		NumFailedEventDeletions:     5, // Some failed deletions
		SearchDeletionStatus:        ricentralpb.DeleteEventsForUserResponse_SUCCESS,
		NumDeletedSearchRows:        3,
	}

	mockRequestInsightCentral.
		On("DeleteEventsForUser", mock.Anything, mock.Anything, testUserID, (*time.Time)(nil), mock.Anything).
		Return(response, nil)

	taskDetails := createVanguardExportDeleteRequestDataTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to delete 5 request events")

	mockTokenExchange.AssertExpectations(t)
	mockRequestInsightCentral.AssertExpectations(t)
}

func TestVanguardExportDeleteRequestDataExecute_SearchDeletionFailure(t *testing.T) {
	executor, mockRequestInsightCentral, mockTokenExchange := createVanguardExportDeleteRequestDataTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", []tokenscopesproto.Scope{tokenscopesproto.Scope_REQUEST_ADMIN}).
		Return(testToken, nil)

	response := &ricentralpb.DeleteEventsForUserResponse{
		NumSuccessfulEventDeletions: 15,
		NumFailedEventDeletions:     0,                                               // No failed event deletions
		SearchDeletionStatus:        ricentralpb.DeleteEventsForUserResponse_FAILURE, // But search deletion failed
		NumDeletedSearchRows:        0,
	}

	mockRequestInsightCentral.
		On("DeleteEventsForUser", mock.Anything, mock.Anything, testUserID, (*time.Time)(nil), mock.Anything).
		Return(response, nil)

	taskDetails := createVanguardExportDeleteRequestDataTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to delete search rows")

	mockTokenExchange.AssertExpectations(t)
	mockRequestInsightCentral.AssertExpectations(t)
}

func TestVanguardExportDeleteRequestDataExecute_Success(t *testing.T) {
	executor, mockRequestInsightCentral, mockTokenExchange := createVanguardExportDeleteRequestDataTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", []tokenscopesproto.Scope{tokenscopesproto.Scope_REQUEST_ADMIN}).
		Return(testToken, nil)

	response := &ricentralpb.DeleteEventsForUserResponse{
		NumSuccessfulEventDeletions: 25,
		NumFailedEventDeletions:     0,
		SearchDeletionStatus:        ricentralpb.DeleteEventsForUserResponse_SUCCESS,
		NumDeletedSearchRows:        8,
	}

	mockRequestInsightCentral.
		On("DeleteEventsForUser", mock.Anything, mock.Anything, testUserID, (*time.Time)(nil), mock.Anything).
		Return(response, nil)

	taskDetails := createVanguardExportDeleteRequestDataTaskDetails()
	statusDetail, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.NoError(t, err)
	require.Equal(t, "Deleted 25 events and 8 search rows", statusDetail)

	mockTokenExchange.AssertExpectations(t)
	mockRequestInsightCentral.AssertExpectations(t)
}

func TestVanguardExportDeleteRequestDataExecute_SearchDeletionNotAttempted(t *testing.T) {
	executor, mockRequestInsightCentral, mockTokenExchange := createVanguardExportDeleteRequestDataTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", []tokenscopesproto.Scope{tokenscopesproto.Scope_REQUEST_ADMIN}).
		Return(testToken, nil)

	response := &ricentralpb.DeleteEventsForUserResponse{
		NumSuccessfulEventDeletions: 10,
		NumFailedEventDeletions:     0,
		SearchDeletionStatus:        ricentralpb.DeleteEventsForUserResponse_NOT_ATTEMPTED,
		NumDeletedSearchRows:        0,
	}

	mockRequestInsightCentral.
		On("DeleteEventsForUser", mock.Anything, mock.Anything, testUserID, (*time.Time)(nil), mock.Anything).
		Return(response, nil)

	taskDetails := createVanguardExportDeleteRequestDataTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to delete search rows")

	mockTokenExchange.AssertExpectations(t)
	mockRequestInsightCentral.AssertExpectations(t)
}
