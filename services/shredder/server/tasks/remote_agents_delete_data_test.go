package tasks

import (
	"context"
	"errors"
	"testing"

	remoteagentsclient "github.com/augmentcode/augment/services/remote_agents/client"
	pb "github.com/augmentcode/augment/services/shredder/admin_proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func createRemoteAgentsDeleteDataTestExecutor(tenantIDs []string) (*RemoteAgentsDeleteDataExecutor, *remoteagentsclient.MockRemoteAgentsClient, *mockAnalyticsClient, *tokenexchangeclient.MockTokenExchangeClient) {
	mockRemoteAgents := remoteagentsclient.NewMockRemoteAgentsClient()
	mockRemoteAgents.On("Close").Return(nil).Maybe()
	mockAnalytics := &mockAnalyticsClient{}
	mockTenantCache := newMockTenantCache(tenantIDs)
	mockTokenExchange := tokenexchangeclient.NewMockTokenExchangeClient()
	mockRemoteAgentsFactory := &mockShardedClientFactory[remoteagentsclient.RemoteAgentsClient]{}
	mockRemoteAgentsFactory.On("GetClientForTenant", mock.Anything, mock.Anything).
		Return(mockRemoteAgents, nil).Maybe()

	executor := NewRemoteAgentsDeleteDataExecutor(
		mockRemoteAgentsFactory,
		mockTokenExchange,
		mockTenantCache,
		mockAnalytics,
	)

	return executor, mockRemoteAgents, mockAnalytics, mockTokenExchange
}

func createRemoteAgentsDeleteDataTestTaskDetails() *pb.TaskDetails {
	return &pb.TaskDetails{
		Task: &pb.TaskDetails_RemoteAgentsDeleteData{
			RemoteAgentsDeleteData: &pb.RemoteAgentsDeleteDataTask{},
		},
	}
}

// Helper function to set up common mock expectations
func setupRemoteAgentsDeleteDataBasicMocks(mockTokenExchange *tokenexchangeclient.MockTokenExchangeClient, mockAnalytics *mockAnalyticsClient, tenantIDs []string, tenantNamespaces []string) {
	// Mock service token for general operations
	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testToken, nil)

	// Mock tenant-specific tokens
	for i, tenantID := range tenantIDs {
		mockTokenExchange.On("GetSignedTokenForServiceWithNamespace", mock.Anything, tenantID, tenantNamespaces[i], mock.Anything).
			Return(tenantToken, nil)
	}

	// Mock tenant history
	mockAnalytics.On("GetUserTenantHistory", mock.Anything, testUserID, mock.Anything).
		Return(tenantIDs, nil)
}

func TestRemoteAgentsDeleteDataExecutor_Execute_Success(t *testing.T) {
	tenantIDs := []string{enterpriseTenantID, professionalTenantID}
	executor, mockRemoteAgents, mockAnalytics, mockTokenExchange := createRemoteAgentsDeleteDataTestExecutor(tenantIDs)
	taskDetails := createRemoteAgentsDeleteDataTestTaskDetails()

	tenantNamespaces := []string{enterpriseNamespace, professionalNamespace}
	setupRemoteAgentsDeleteDataBasicMocks(mockTokenExchange, mockAnalytics, tenantIDs, tenantNamespaces)

	// Mock successful deletion for each tenant
	mockRemoteAgents.On("DeleteAllAgentsForUser", mock.Anything, mock.Anything, testUserID).
		Return(int32(3), nil).Once() // 3 agents deleted for first tenant
	mockRemoteAgents.On("DeleteAllAgentsForUser", mock.Anything, mock.Anything, testUserID).
		Return(int32(2), nil).Once() // 2 agents deleted for second tenant

	statusDetail, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)

	require.NoError(t, err)
	require.Equal(t, "Deleted 5 agents across 2 tenants", statusDetail)
	mockRemoteAgents.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
	mockTokenExchange.AssertExpectations(t)
}

func TestRemoteAgentsDeleteDataExecutor_Execute_InvalidTaskDetails(t *testing.T) {
	executor, _, _, _ := createRemoteAgentsDeleteDataTestExecutor(nil)

	// Create task details without RemoteAgentsDeleteData
	invalidTaskDetails := &pb.TaskDetails{
		Task: &pb.TaskDetails_AnalyticsForgetUser{
			AnalyticsForgetUser: &pb.AnalyticsForgetUserTask{},
		},
	}

	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, invalidTaskDetails)

	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")
}

func TestRemoteAgentsDeleteDataExecutor_Execute_GetTenantHistoryError(t *testing.T) {
	executor, _, mockAnalytics, mockTokenExchange := createRemoteAgentsDeleteDataTestExecutor(nil)
	taskDetails := createRemoteAgentsDeleteDataTestTaskDetails()

	// Mock service token for general operations
	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testToken, nil)

	// Mock tenant history failure
	mockAnalytics.On("GetUserTenantHistory", mock.Anything, testUserID, mock.Anything).
		Return([]string{}, errors.New("analytics service unavailable"))

	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)

	require.Error(t, err)
	require.Contains(t, err.Error(), "analytics service unavailable")
	mockAnalytics.AssertExpectations(t)
	mockTokenExchange.AssertExpectations(t)
}

func TestRemoteAgentsDeleteDataExecutor_Execute_DeleteAgentsError(t *testing.T) {
	tenantIDs := []string{enterpriseTenantID}
	executor, mockRemoteAgents, mockAnalytics, mockTokenExchange := createRemoteAgentsDeleteDataTestExecutor(tenantIDs)
	taskDetails := createRemoteAgentsDeleteDataTestTaskDetails()

	tenantNamespaces := []string{enterpriseNamespace}
	setupRemoteAgentsDeleteDataBasicMocks(mockTokenExchange, mockAnalytics, tenantIDs, tenantNamespaces)

	// Mock deletion failure
	mockRemoteAgents.On("DeleteAllAgentsForUser", mock.Anything, mock.Anything, testUserID).
		Return(int32(0), errors.New("remote agents service unavailable"))

	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)

	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to delete all agents for user")
	require.Contains(t, err.Error(), "remote agents service unavailable")
	mockRemoteAgents.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
	mockTokenExchange.AssertExpectations(t)
}
