package tasks

import (
	"context"
	"errors"
	"testing"

	settingsclient "github.com/augmentcode/augment/services/settings/client"
	settingsproto "github.com/augmentcode/augment/services/settings/proto"
	pb "github.com/augmentcode/augment/services/shredder/admin_proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

func createSettingsDeleteUserSettingsTestExecutor(tenantIDs []string) (*SettingsDeleteUserSettingsExecutor, *settingsclient.MockSettingsClient, *tokenexchangeclient.MockTokenExchangeClient, *mockAnalyticsClient) {
	mockSettings := settingsclient.NewMockSettingsClient()
	mockSettings.On("Close").Return(nil).Maybe()
	mockTokenExchange := tokenexchangeclient.NewMockTokenExchangeClient()
	mockTenantCache := newMockTenantCache(tenantIDs)
	mockAnalytics := &mockAnalyticsClient{}
	mockSettingsFactory := &mockShardedClientFactory[settingsclient.SettingsClient]{}
	mockSettingsFactory.On("GetClientForTenant", mock.Anything, mock.Anything).
		Return(mockSettings, nil).Maybe()

	executor := NewSettingsDeleteUserSettingsExecutor(
		mockSettingsFactory,
		mockTokenExchange,
		mockTenantCache,
		mockAnalytics,
	)

	return executor, mockSettings, mockTokenExchange, mockAnalytics
}

func createSettingsDeleteUserSettingsTestTaskDetails() *pb.TaskDetails {
	return &pb.TaskDetails{
		Task: &pb.TaskDetails_SettingsDeleteUserSettings{
			SettingsDeleteUserSettings: &pb.SettingsDeleteUserSettings{},
		},
	}
}

// Helper function to set up common mock expectations
func setupSettingsDeleteUserSettingsBasicMocks(mockTokenExchange *tokenexchangeclient.MockTokenExchangeClient, mockAnalytics *mockAnalyticsClient, tenantIDs []string, tenantNamespaces []string) {
	// Mock service token calls - one for getTenantHistoryForUser + one for each tenant
	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testToken, nil)

	for i, tenantID := range tenantIDs {
		mockTokenExchange.On("GetSignedTokenForServiceWithNamespace", mock.Anything, tenantID, tenantNamespaces[i], mock.Anything).
			Return(tenantToken, nil)
	}

	// Mock tenant history
	mockAnalytics.On("GetUserTenantHistory", mock.Anything, testUserID, mock.Anything).
		Return(tenantIDs, nil)
}

func TestSettingsDeleteUserSettingsExecutor_Execute_Success(t *testing.T) {
	tenantIDs := []string{enterpriseTenantID, professionalTenantID}
	executor, mockSettings, mockTokenExchange, mockAnalytics := createSettingsDeleteUserSettingsTestExecutor(tenantIDs)
	taskDetails := createSettingsDeleteUserSettingsTestTaskDetails()

	tenantNamespaces := []string{enterpriseNamespace, professionalNamespace}
	setupSettingsDeleteUserSettingsBasicMocks(mockTokenExchange, mockAnalytics, tenantIDs, tenantNamespaces)

	// Mock successful deletion for each tenant
	userIDPtr := testUserID
	numSettingsPtr := int32(2)
	mockSettings.On("DeleteUserSettings", mock.Anything, mock.Anything, &userIDPtr).
		Return(&settingsproto.DeleteUserSettingsResponse{NumSettingsDeleted: &numSettingsPtr}, nil).Twice() // Called once for each tenant

	statusDetail, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)

	require.NoError(t, err)
	require.Equal(t, "Deleted ~4 settings for user in 2 tenants", statusDetail)
	mockSettings.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
	mockTokenExchange.AssertExpectations(t)
}

func TestSettingsDeleteUserSettingsExecutor_Execute_InvalidTaskDetails(t *testing.T) {
	executor, _, _, _ := createSettingsDeleteUserSettingsTestExecutor(nil)

	// Test with nil task details
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, nil)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")

	// Test with wrong task details type
	wrongDetails := &pb.TaskDetails{
		Task: &pb.TaskDetails_AnalyticsForgetUser{
			AnalyticsForgetUser: &pb.AnalyticsForgetUserTask{},
		},
	}
	_, err = executor.Execute(context.Background(), testUserID, testUserEmail, wrongDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")
}

func TestSettingsDeleteUserSettingsExecutor_Execute_GetTenantHistoryError(t *testing.T) {
	executor, _, mockTokenExchange, mockAnalytics := createSettingsDeleteUserSettingsTestExecutor(nil)
	taskDetails := createSettingsDeleteUserSettingsTestTaskDetails()

	// Mock service token for general operations
	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testToken, nil)

	// Mock tenant history failure
	mockAnalytics.On("GetUserTenantHistory", mock.Anything, testUserID, mock.Anything).
		Return([]string{}, errors.New("analytics service unavailable"))

	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)

	require.Error(t, err)
	require.Contains(t, err.Error(), "analytics service unavailable")
	mockAnalytics.AssertExpectations(t)
	mockTokenExchange.AssertExpectations(t)
}

func TestSettingsDeleteUserSettingsExecutor_Execute_GetTokenError(t *testing.T) {
	tenantIDs := []string{enterpriseTenantID}
	executor, _, mockTokenExchange, mockAnalytics := createSettingsDeleteUserSettingsTestExecutor(tenantIDs)
	taskDetails := createSettingsDeleteUserSettingsTestTaskDetails()

	tenantNamespaces := []string{enterpriseNamespace}

	// Mock service token for getTenantHistoryForUser (succeeds)
	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testToken, nil).Once()

	// Mock tenant history success
	mockAnalytics.On("GetUserTenantHistory", mock.Anything, testUserID, mock.Anything).
		Return(tenantIDs, nil)

	// Mock token exchange failure for tenant processing
	mockTokenExchange.On("GetSignedTokenForServiceWithNamespace", mock.Anything, tenantIDs[0], tenantNamespaces[0], mock.Anything).
		Return("", errors.New("token exchange service unavailable")).Once()

	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)

	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to get request context")
	require.Contains(t, err.Error(), "token exchange service unavailable")
	mockAnalytics.AssertExpectations(t)
	mockTokenExchange.AssertExpectations(t)
}

func TestSettingsDeleteUserSettingsExecutor_Execute_DeleteUserSettingsError(t *testing.T) {
	tenantIDs := []string{enterpriseTenantID}
	executor, mockSettings, mockTokenExchange, mockAnalytics := createSettingsDeleteUserSettingsTestExecutor(tenantIDs)
	taskDetails := createSettingsDeleteUserSettingsTestTaskDetails()

	tenantNamespaces := []string{enterpriseNamespace}
	setupSettingsDeleteUserSettingsBasicMocks(mockTokenExchange, mockAnalytics, tenantIDs, tenantNamespaces)

	// Mock deletion failure
	userIDPtr := testUserID
	mockSettings.On("DeleteUserSettings", mock.Anything, mock.Anything, &userIDPtr).
		Return((*settingsproto.DeleteUserSettingsResponse)(nil), errors.New("settings service unavailable"))

	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)

	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to delete user settings")
	require.Contains(t, err.Error(), "settings service unavailable")
	mockSettings.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
	mockTokenExchange.AssertExpectations(t)
}
