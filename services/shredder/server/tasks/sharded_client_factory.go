package tasks

import (
	"context"
	"fmt"

	twclient "github.com/augmentcode/augment/services/tenant_watcher/client"
)

type ShardedClientFactory[T any] interface {
	// These clients can be cached, so callers should NOT close them.
	GetClientForTenant(ctx context.Context, tenantID string) (T, error)

	Close()
}

type shardedClientFactoryImpl[T any] struct {
	tenantCache           twclient.TenantCacheSync
	endpointTemplate      string
	newClientFromEndpoint func(endpoint string) (T, error)
	closeFunc             func(T)

	clientCache map[string]T
}

func NewShardedClientFactory[T any](
	tenantCache twclient.TenantCacheSync,
	endpointTemplate string,
	newClientFromEndpoint func(endpoint string) (T, error),
	closeFunc func(T),
) ShardedClientFactory[T] {
	return &shardedClientFactoryImpl[T]{
		tenantCache:           tenantCache,
		endpointTemplate:      endpointTemplate,
		newClientFromEndpoint: newClientFromEndpoint,
		closeFunc:             closeFunc,
		clientCache:           make(map[string]T),
	}
}

func (f *shardedClientFactoryImpl[T]) GetClientForTenant(ctx context.Context, tenantID string) (T, error) {
	tenant, err := f.tenantCache.GetTenant(ctx, tenantID)
	if err != nil {
		var nilClient T
		return nilClient, fmt.Errorf("failed to get tenant %s: %w", tenantID, err)
	}

	shardNamespace := tenant.ShardNamespace
	if client, ok := f.clientCache[shardNamespace]; ok {
		return client, nil
	}

	endpoint := fmt.Sprintf(f.endpointTemplate, shardNamespace)
	client, err := f.newClientFromEndpoint(endpoint)
	if err == nil {
		f.clientCache[shardNamespace] = client
	}
	return client, err
}

func (f *shardedClientFactoryImpl[T]) Close() {
	for _, client := range f.clientCache {
		f.closeFunc(client)
	}
}
