package tasks

import (
	"context"
	"errors"
	"testing"

	authclient "github.com/augmentcode/augment/services/auth/central/client"
	pb "github.com/augmentcode/augment/services/shredder/admin_proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
)

// Test constants for auth central forget user tests
const (
	testForgetUserID    = "forget_user123"
	testForgetUserEmail = "<EMAIL>"
	testForgetToken     = "forget-test-token"
)

func createAuthCentralForgetUserTestExecutor() (*AuthCentralForgetUserExecutor, *authclient.MockAuthClient, *tokenexchangeclient.MockTokenExchangeClient) {
	mockAuth := authclient.NewMockAuthClient()
	mockTokenExchange := tokenexchangeclient.NewMockTokenExchangeClient()

	executor := NewAuthCentralForgetUserExecutor(
		mockAuth,
		mockTokenExchange,
	)

	return executor, mockAuth, mockTokenExchange
}

func createAuthCentralForgetUserTaskDetails() *pb.TaskDetails {
	return &pb.TaskDetails{
		Task: &pb.TaskDetails_AuthCentralForgetUser{
			AuthCentralForgetUser: &pb.AuthCentralForgetUserTask{},
		},
	}
}

func TestAuthCentralForgetUserExecute_Success(t *testing.T) {
	executor, mockAuth, mockTokenExchange := createAuthCentralForgetUserTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testForgetToken, nil)

	mockAuth.On("ForgetUser", mock.Anything, mock.Anything, testForgetUserID).
		Return(nil)

	taskDetails := createAuthCentralForgetUserTaskDetails()
	statusDetail, err := executor.Execute(context.Background(), testForgetUserID, testForgetUserEmail, taskDetails)
	require.NoError(t, err)
	require.Equal(t, "Successfully forgot user", statusDetail)

	mockTokenExchange.AssertExpectations(t)
	mockAuth.AssertExpectations(t)
}

func TestAuthCentralForgetUserExecute_InvalidTaskDetails(t *testing.T) {
	executor, _, _ := createAuthCentralForgetUserTestExecutor()

	_, err := executor.Execute(context.Background(), testForgetUserID, testForgetUserEmail, nil)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")

	// Test with wrong task details type
	wrongDetails := &pb.TaskDetails{
		Task: &pb.TaskDetails_ContentManagerDeleteBlobs{
			ContentManagerDeleteBlobs: &pb.ContentManagerDeleteBlobsTask{},
		},
	}
	_, err = executor.Execute(context.Background(), testForgetUserID, testForgetUserEmail, wrongDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")
}

func TestAuthCentralForgetUserExecute_TokenExchangeFailure(t *testing.T) {
	executor, _, mockTokenExchange := createAuthCentralForgetUserTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return("", errors.New("token exchange failed"))

	taskDetails := createAuthCentralForgetUserTaskDetails()
	_, err := executor.Execute(context.Background(), testForgetUserID, testForgetUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to get request context")

	mockTokenExchange.AssertExpectations(t)
}

func TestAuthCentralForgetUserExecute_ForgetUserFailure(t *testing.T) {
	executor, mockAuth, mockTokenExchange := createAuthCentralForgetUserTestExecutor()

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testForgetToken, nil)

	mockAuth.On("ForgetUser", mock.Anything, mock.Anything, testForgetUserID).
		Return(errors.New("forget user failed"))

	taskDetails := createAuthCentralForgetUserTaskDetails()
	_, err := executor.Execute(context.Background(), testForgetUserID, testForgetUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to forget user")

	mockTokenExchange.AssertExpectations(t)
	mockAuth.AssertExpectations(t)
}
