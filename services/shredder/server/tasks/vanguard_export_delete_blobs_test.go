package tasks

import (
	"context"
	"errors"
	"testing"

	contentmanagerclient "github.com/augmentcode/augment/services/content_manager/client"
	contentmanagerproto "github.com/augmentcode/augment/services/content_manager/proto"
	ricentralpb "github.com/augmentcode/augment/services/request_insight/central/proto"
	pb "github.com/augmentcode/augment/services/shredder/admin_proto"
	tokenexchangeclient "github.com/augmentcode/augment/services/token_exchange/client"
	tokenscopesproto "github.com/augmentcode/augment/services/token_exchange/token_scopes_proto"
	"github.com/stretchr/testify/mock"
	"github.com/stretchr/testify/require"
	"google.golang.org/protobuf/types/known/timestamppb"
)

func createVanguardExportDeleteBlobsTestExecutor(tenantIDs []string) (*VanguardExportDeleteBlobsExecutor, *mockContentManagerClient, *mockRequestInsightCentralClient, *mockAnalyticsClient, *tokenexchangeclient.MockTokenExchangeClient) {
	mockContentManager := &mockContentManagerClient{}
	mockContentManager.On("Close").Return().Maybe()
	mockRequestInsightCentral := &mockRequestInsightCentralClient{}
	mockAnalytics := &mockAnalyticsClient{}
	mockTenantCache := newMockTenantCache(tenantIDs)
	mockTokenExchange := tokenexchangeclient.NewMockTokenExchangeClient()
	mockContentManagerFactory := &mockShardedClientFactory[contentmanagerclient.ContentManagerClient]{}
	mockContentManagerFactory.On("GetClientForTenant", mock.Anything, mock.Anything).
		Return(mockContentManager, nil).Maybe()

	executor := NewVanguardExportDeleteBlobsExecutor(
		mockContentManagerFactory,
		mockRequestInsightCentral,
		mockAnalytics,
		mockTenantCache,
		mockTokenExchange,
	)

	return executor, mockContentManager, mockRequestInsightCentral, mockAnalytics, mockTokenExchange
}

func createVanguardExportDeleteBlobsTestTaskDetails() *pb.TaskDetails {
	return &pb.TaskDetails{
		Task: &pb.TaskDetails_VanguardExportDeleteBlobs{
			VanguardExportDeleteBlobs: &pb.VanguardExportDeleteBlobsTask{},
		},
	}
}

// Helper function to set up common mock expectations (with admin token for deletion)
func setupVanguardExportDeleteBlobsBasicMocks(mockTokenExchange *tokenexchangeclient.MockTokenExchangeClient, mockAnalytics *mockAnalyticsClient, tenantIDs []string, tenantNamespaces []string) {
	// Mock service token for general operations
	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.MatchedBy(func(scopes []tokenscopesproto.Scope) bool {
		return len(scopes) == 1 && scopes[0] == tokenscopesproto.Scope_REQUEST_R
	})).Return(testToken, nil)

	// Mock user tenant history
	mockAnalytics.On("GetUserTenantHistory", mock.Anything, testUserID, mock.Anything).
		Return(tenantIDs, nil)

	// Mock content admin token for blob retrieval
	for i, tenantID := range tenantIDs {
		mockTokenExchange.On("GetSignedTokenForServiceWithNamespace", mock.Anything, tenantID, tenantNamespaces[i], mock.MatchedBy(func(scopes []tokenscopesproto.Scope) bool {
			return len(scopes) == 1 && scopes[0] == tokenscopesproto.Scope_CONTENT_ADMIN
		})).
			Return(tenantToken, nil)
	}

	// Mock admin token for GCS blob deletion
	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.MatchedBy(func(scopes []tokenscopesproto.Scope) bool {
		return len(scopes) == 1 &&
			scopes[0] == tokenscopesproto.Scope_REQUEST_ADMIN
	})).Return(testToken, nil)
}

func TestNewVanguardExportDeleteBlobsExecutor(t *testing.T) {
	executor, _, _, _, _ := createVanguardExportDeleteBlobsTestExecutor(nil)
	require.NotNil(t, executor)
}

func TestVanguardExportDeleteBlobsExecute_InvalidTaskDetails(t *testing.T) {
	executor, _, _, _, _ := createVanguardExportDeleteBlobsTestExecutor(nil)

	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, nil)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")

	// Test with wrong task details type
	wrongDetails := &pb.TaskDetails{
		Task: &pb.TaskDetails_AnalyticsForgetUser{
			AnalyticsForgetUser: &pb.AnalyticsForgetUserTask{},
		},
	}
	_, err = executor.Execute(context.Background(), testUserID, testUserEmail, wrongDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "invalid task details")
}

func TestVanguardExportDeleteBlobsExecute_TokenExchangeFailure(t *testing.T) {
	executor, _, _, _, mockTokenExchange := createVanguardExportDeleteBlobsTestExecutor(nil)

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return("", errors.New("token exchange failed"))

	taskDetails := createVanguardExportDeleteBlobsTestTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to get request context")

	mockTokenExchange.AssertExpectations(t)
}

func TestVanguardExportDeleteBlobsExecute_AnalyticsFailure(t *testing.T) {
	executor, _, _, mockAnalytics, mockTokenExchange := createVanguardExportDeleteBlobsTestExecutor(nil)

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testToken, nil)

	mockAnalytics.On("GetUserTenantHistory", mock.Anything, testUserID, mock.Anything).
		Return([]string{}, errors.New("analytics failed"))

	taskDetails := createVanguardExportDeleteBlobsTestTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to get user tenant history")

	mockTokenExchange.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
}

func TestVanguardExportDeleteBlobsExecute_NoTenantHistory(t *testing.T) {
	executor, _, _, mockAnalytics, mockTokenExchange := createVanguardExportDeleteBlobsTestExecutor(nil)

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testToken, nil)

	mockAnalytics.On("GetUserTenantHistory", mock.Anything, testUserID, mock.Anything).
		Return([]string{}, nil)

	taskDetails := createVanguardExportDeleteBlobsTestTaskDetails()
	statusDetail, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.NoError(t, err)
	require.Equal(t, "No community tenants found for vanguard exported blobs deletion", statusDetail)

	mockTokenExchange.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
}

func TestVanguardExportDeleteBlobsExecute_NonCommunityTenantSkipped(t *testing.T) {
	tenantIDs := []string{enterpriseTenantID}
	executor, _, _, mockAnalytics, mockTokenExchange := createVanguardExportDeleteBlobsTestExecutor(tenantIDs)

	mockTokenExchange.On("GetSignedTokenForService", mock.Anything, "", mock.Anything).
		Return(testToken, nil)

	mockAnalytics.On("GetUserTenantHistory", mock.Anything, testUserID, mock.Anything).
		Return(tenantIDs, nil)

	taskDetails := createVanguardExportDeleteBlobsTestTaskDetails()
	statusDetail, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.NoError(t, err)
	require.Equal(t, "No community tenants found for vanguard exported blobs deletion", statusDetail)

	mockTokenExchange.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
}

func TestVanguardExportDeleteBlobsExecute_Success(t *testing.T) {
	tenantIDs := []string{communityTenantID}
	executor, mockContentManager, mockRequestInsightCentral, mockAnalytics, mockTokenExchange := createVanguardExportDeleteBlobsTestExecutor(tenantIDs)

	tenantNamespaces := []string{communityNamespace}
	setupVanguardExportDeleteBlobsBasicMocks(mockTokenExchange, mockAnalytics, tenantIDs, tenantNamespaces)

	// Create test blobs covering all success scenarios: deleted and not found (no errors)
	testBlobs := []*contentmanagerproto.UploadInfo{
		{
			BlobName: "deleted-blob",
			Time:     &timestamppb.Timestamp{Seconds: 1640995200, Nanos: 0},
		},
		{
			BlobName: "not-found-blob",
			Time:     &timestamppb.Timestamp{Seconds: 1640995100, Nanos: 0},
		},
	}

	mockContentManager.On("GetUserBlobs", mock.Anything, testUserEmail, communityTenantID, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(testBlobs, nil)

	expectedRequests := []*ricentralpb.DeleteGcsExportedBlobsRequest{
		{TenantId: communityTenantID, BlobName: "deleted-blob"},
		{TenantId: communityTenantID, BlobName: "not-found-blob"},
	}

	// Responses with deleted and not found (no errors)
	expectedResponses := []*ricentralpb.DeleteGcsExportedBlobsResponse{
		{BlobName: "deleted-blob", Found: true, Deleted: true, ErrorMessage: ""},
		{BlobName: "not-found-blob", Found: false, Deleted: false, ErrorMessage: ""},
	}

	mockRequestInsightCentral.On("DeleteGcsExportedBlobs", mock.Anything, mock.Anything, expectedRequests).
		Return(expectedResponses, nil)

	taskDetails := createVanguardExportDeleteBlobsTestTaskDetails()
	statusDetail, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.NoError(t, err) // Should succeed when no errors occur
	require.Equal(t, "Deleted 1 blobs and could not find 1 blobs across 1 tenants", statusDetail)

	mockTokenExchange.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
	mockContentManager.AssertExpectations(t)
	mockRequestInsightCentral.AssertExpectations(t)
}

func TestVanguardExportDeleteBlobsExecute_GetUserBlobsFailure(t *testing.T) {
	tenantIDs := []string{communityTenantID}
	executor, mockContentManager, _, mockAnalytics, mockTokenExchange := createVanguardExportDeleteBlobsTestExecutor(tenantIDs)

	tenantNamespaces := []string{communityNamespace}
	setupVanguardExportDeleteBlobsBasicMocks(mockTokenExchange, mockAnalytics, tenantIDs, tenantNamespaces)

	// Mock GetUserBlobs to return an error
	mockContentManager.On("GetUserBlobs", mock.Anything, testUserEmail, communityTenantID, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return([]*contentmanagerproto.UploadInfo{}, errors.New("failed to get user blobs"))

	taskDetails := createVanguardExportDeleteBlobsTestTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to process blobs")

	// Should have called token exchange twice: once for tenant history and once for admin context
	mockTokenExchange.AssertNumberOfCalls(t, "GetSignedTokenForService", 2)
	mockTokenExchange.AssertNumberOfCalls(t, "GetSignedTokenForServiceWithNamespace", 1)

	mockAnalytics.AssertExpectations(t)
	mockContentManager.AssertExpectations(t)
}

func TestVanguardExportDeleteBlobsExecute_DeleteGcsExportedBlobsFailure(t *testing.T) {
	tenantIDs := []string{communityTenantID}
	executor, mockContentManager, mockRequestInsightCentral, mockAnalytics, mockTokenExchange := createVanguardExportDeleteBlobsTestExecutor(tenantIDs)

	tenantNamespaces := []string{communityNamespace}
	setupVanguardExportDeleteBlobsBasicMocks(mockTokenExchange, mockAnalytics, tenantIDs, tenantNamespaces)

	// Create test blobs
	testBlobs := []*contentmanagerproto.UploadInfo{
		{
			BlobName: "test-blob-1",
			Time:     &timestamppb.Timestamp{Seconds: 1640995200, Nanos: 0},
		},
	}

	mockContentManager.On("GetUserBlobs", mock.Anything, testUserEmail, communityTenantID, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(testBlobs, nil)

	// Mock DeleteGcsExportedBlobs to return an error
	mockRequestInsightCentral.On("DeleteGcsExportedBlobs", mock.Anything, mock.Anything, mock.Anything).
		Return([]*ricentralpb.DeleteGcsExportedBlobsResponse{}, errors.New("GCS deletion failed"))

	taskDetails := createVanguardExportDeleteBlobsTestTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err)
	require.Contains(t, err.Error(), "failed to delete GCS exported blobs")

	mockTokenExchange.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
	mockContentManager.AssertExpectations(t)
	mockRequestInsightCentral.AssertExpectations(t)
}

func TestVanguardExportDeleteBlobsExecute_WithDuplicateBlobs(t *testing.T) {
	tenantIDs := []string{communityTenantID}
	executor, mockContentManager, mockRequestInsightCentral, mockAnalytics, mockTokenExchange := createVanguardExportDeleteBlobsTestExecutor(tenantIDs)

	tenantNamespaces := []string{communityNamespace}
	setupVanguardExportDeleteBlobsBasicMocks(mockTokenExchange, mockAnalytics, tenantIDs, tenantNamespaces)

	// Create test blobs with duplicates (same blob name and timestamp)
	testBlobs := []*contentmanagerproto.UploadInfo{
		{
			BlobName: "blob-1",
			Time:     &timestamppb.Timestamp{Seconds: 1640995200, Nanos: 0},
		},
		{
			BlobName: "blob-2",
			Time:     &timestamppb.Timestamp{Seconds: 1640995100, Nanos: 0},
		},
		{
			BlobName: "blob-1", // Duplicate blob name and timestamp
			Time:     &timestamppb.Timestamp{Seconds: 1640995200, Nanos: 0},
		},
		{
			BlobName: "blob-3",
			Time:     &timestamppb.Timestamp{Seconds: 1640995000, Nanos: 0},
		},
	}

	mockContentManager.On("GetUserBlobs", mock.Anything, testUserEmail, communityTenantID, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(testBlobs, nil)

	// Expected deletion requests (only unique blobs - 3 blobs, not 4)
	expectedRequests := []*ricentralpb.DeleteGcsExportedBlobsRequest{
		{TenantId: communityTenantID, BlobName: "blob-1"},
		{TenantId: communityTenantID, BlobName: "blob-2"},
		{TenantId: communityTenantID, BlobName: "blob-3"},
	}

	expectedResponses := []*ricentralpb.DeleteGcsExportedBlobsResponse{
		{BlobName: "blob-1", Found: true, Deleted: true, ErrorMessage: ""},
		{BlobName: "blob-2", Found: true, Deleted: true, ErrorMessage: ""},
		{BlobName: "blob-3", Found: true, Deleted: true, ErrorMessage: ""},
	}

	mockRequestInsightCentral.On("DeleteGcsExportedBlobs", mock.Anything, mock.Anything, expectedRequests).
		Return(expectedResponses, nil)

	taskDetails := createVanguardExportDeleteBlobsTestTaskDetails()
	statusDetail, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.NoError(t, err)
	require.Equal(t, "Deleted 3 blobs and could not find 0 blobs across 1 tenants", statusDetail)

	mockTokenExchange.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
	mockContentManager.AssertExpectations(t)
	mockRequestInsightCentral.AssertExpectations(t)
}

func TestVanguardExportDeleteBlobsExecute_WithErrors(t *testing.T) {
	tenantIDs := []string{communityTenantID}
	executor, mockContentManager, mockRequestInsightCentral, mockAnalytics, mockTokenExchange := createVanguardExportDeleteBlobsTestExecutor(tenantIDs)

	tenantNamespaces := []string{communityNamespace}
	setupVanguardExportDeleteBlobsBasicMocks(mockTokenExchange, mockAnalytics, tenantIDs, tenantNamespaces)

	// Create test blobs covering all scenarios: deleted, not found, and error
	testBlobs := []*contentmanagerproto.UploadInfo{
		{
			BlobName: "deleted-blob",
			Time:     &timestamppb.Timestamp{Seconds: 1640995200, Nanos: 0},
		},
		{
			BlobName: "not-found-blob",
			Time:     &timestamppb.Timestamp{Seconds: 1640995100, Nanos: 0},
		},
		{
			BlobName: "error-blob",
			Time:     &timestamppb.Timestamp{Seconds: 1640995000, Nanos: 0},
		},
	}

	mockContentManager.On("GetUserBlobs", mock.Anything, testUserEmail, communityTenantID, mock.Anything, mock.Anything, mock.Anything, mock.Anything).
		Return(testBlobs, nil)

	expectedRequests := []*ricentralpb.DeleteGcsExportedBlobsRequest{
		{TenantId: communityTenantID, BlobName: "deleted-blob"},
		{TenantId: communityTenantID, BlobName: "not-found-blob"},
		{TenantId: communityTenantID, BlobName: "error-blob"},
	}

	// Mixed responses: deleted, not found, and error
	expectedResponses := []*ricentralpb.DeleteGcsExportedBlobsResponse{
		{BlobName: "deleted-blob", Found: true, Deleted: true, ErrorMessage: ""},
		{BlobName: "not-found-blob", Found: false, Deleted: false, ErrorMessage: ""},
		{BlobName: "error-blob", Found: false, Deleted: false, ErrorMessage: "GCS operation failed"},
	}

	mockRequestInsightCentral.On("DeleteGcsExportedBlobs", mock.Anything, mock.Anything, expectedRequests).
		Return(expectedResponses, nil)

	taskDetails := createVanguardExportDeleteBlobsTestTaskDetails()
	_, err := executor.Execute(context.Background(), testUserID, testUserEmail, taskDetails)
	require.Error(t, err) // Should fail when some deletions have errors
	require.Contains(t, err.Error(), "failed to delete 1 blobs")

	mockTokenExchange.AssertExpectations(t)
	mockAnalytics.AssertExpectations(t)
	mockContentManager.AssertExpectations(t)
	mockRequestInsightCentral.AssertExpectations(t)
}
