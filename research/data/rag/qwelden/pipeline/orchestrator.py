"""Pipeline orchestrator for the hindsight dataset processing."""

import json
import hashlib
from pathlib import Path
from typing import Dict, List, Optional, NamedTuple, Callable
import traceback
from abc import ABC, abstractmethod

from research.data.rag.qwelden.pipeline.pipeline_config import (
    PipelineConfigLoader,
    PipelineRunMetadata,
)
from research.data.rag.qwelden.pipeline.filter_jsonl_data import filter_stage
from research.data.rag.qwelden.pipeline.partition_jsonl_to_parquet import (
    partition_stage,
)

# =============================================================================
# CONSTANTS
# =============================================================================

# Stage name constants
STAGE_FILTER = "filter"
STAGE_PARTITION = "partition"
STAGE_RETRIEVAL = "retrieval"
STAGE_MIXDATA = "mixdata"
STAGE_PROMPT = "prompt"
STAGE_INDEXED_DATASET = "indexed_dataset"

# Stage execution order
STAGE_ORDER = [
    STAGE_FILTER,
    STAGE_PARTITION,
    STAGE_RETRIEVAL,
    STAGE_MIXDATA,
    STAGE_PROMPT,
    STAGE_INDEXED_DATASET,
]

# File patterns for completion checks
COMPLETION_PATTERNS = {
    STAGE_FILTER: "*.jsonl.zst",
    STAGE_PARTITION: "*.parquet",
    STAGE_RETRIEVAL: "*.parquet",
    STAGE_MIXDATA: "*.parquet",
    STAGE_PROMPT: "*.parquet",
    STAGE_INDEXED_DATASET: "*",
}


# =============================================================================
# CORE DATA STRUCTURES
# =============================================================================


class StageExecution(NamedTuple):
    """Represents a stage that needs to be executed."""

    stage_name: str
    config_name: str

    @property
    def identifier(self) -> str:
        """Get a unique identifier for this stage execution."""
        return f"{self.stage_name}:{self.config_name}"

    @property
    def display_name(self) -> str:
        """Get a human-readable display name for this stage execution."""
        if self.stage_name in [STAGE_FILTER, STAGE_PARTITION, STAGE_RETRIEVAL]:
            return f"{self.stage_name} (data source: {self.config_name})"
        elif self.stage_name == STAGE_MIXDATA:
            return f"{self.stage_name} (mixdata config: {self.config_name})"
        elif self.stage_name in [STAGE_PROMPT, STAGE_INDEXED_DATASET]:
            return f"{self.stage_name} (prompt config: {self.config_name})"
        else:
            return f"{self.stage_name} ({self.config_name})"


class StageExecutionResult:
    """Result of executing a pipeline stage."""

    def __init__(self, success: bool, error_message: Optional[str] = None):
        self.success = success
        self.error_message = error_message

    @classmethod
    def success_result(cls) -> "StageExecutionResult":
        """Create a successful result."""
        return cls(success=True)

    @classmethod
    def error_result(cls, error_message: str) -> "StageExecutionResult":
        """Create an error result."""
        return cls(success=False, error_message=error_message)


class PipelineAnalysis:
    """Results of pipeline analysis showing what stages need to run."""

    def __init__(self):
        self.stages_to_run: List[StageExecution] = []
        self.completed_stages: List[StageExecution] = []

    def add_stage_to_run(self, stage: StageExecution):
        """Add a stage that needs to run."""
        self.stages_to_run.append(stage)

    def add_completed_stage(self, stage: StageExecution):
        """Add a stage that is already completed."""
        self.completed_stages.append(stage)

    @property
    def has_work_to_do(self) -> bool:
        """Check if there are any stages that need to run."""
        return len(self.stages_to_run) > 0

    def get_stages_by_name(self, stage_name: str) -> List[StageExecution]:
        """Get all stages to run with the given name."""
        return [stage for stage in self.stages_to_run if stage.stage_name == stage_name]


# =============================================================================
# STAGE EXECUTORS
# =============================================================================


class StageExecutor(ABC):
    """Abstract base class for stage executors."""

    def __init__(self, orchestrator: "PipelineOrchestrator"):
        self.orchestrator = orchestrator
        self.config = orchestrator.config
        self.dry_run = orchestrator.dry_run

    @abstractmethod
    def execute(self, stage: StageExecution) -> StageExecutionResult:
        """Execute the stage and return the result."""
        pass

    def _safe_execute(
        self, stage: StageExecution, execution_func: Callable[[], None]
    ) -> StageExecutionResult:
        """Safely execute a stage function with error handling."""
        if self.dry_run:
            return StageExecutionResult.success_result()

        try:
            execution_func()
            return StageExecutionResult.success_result()
        except Exception as e:
            error_msg = f"Error in {stage.stage_name} stage: {e}"
            print(error_msg)
            traceback.print_exc()
            return StageExecutionResult.error_result(error_msg)


class FilterStageExecutor(StageExecutor):
    """Executor for the filter stage."""

    def execute(self, stage: StageExecution) -> StageExecutionResult:
        if stage.config_name not in self.config.dataset_configs:
            return StageExecutionResult.error_result(
                f"Dataset config '{stage.config_name}' not found"
            )

        dataset_config = self.config.dataset_configs[stage.config_name]
        output_path = self.orchestrator._get_stage_output_path(
            stage.config_name, STAGE_FILTER
        )

        def run_filter():
            filter_stage(
                base_dir=self.orchestrator.base_dir,
                input_datasets=dataset_config.input_datasets,
                output_dir=output_path,
                filter_config=dataset_config.filters,
            )

        return self._safe_execute(stage, run_filter)


class PartitionStageExecutor(StageExecutor):
    """Executor for the partition stage."""

    def execute(self, stage: StageExecution) -> StageExecutionResult:
        input_path = self.orchestrator._get_stage_output_path(
            stage.config_name, STAGE_FILTER
        )
        output_path = self.orchestrator._get_stage_output_path(
            stage.config_name, STAGE_PARTITION
        )

        def run_partition():
            partition_stage(input_dir=str(input_path), output_dir=str(output_path))

        return self._safe_execute(stage, run_partition)


class RetrievalStageExecutor(StageExecutor):
    """Executor for the retrieval stage."""

    def execute(self, stage: StageExecution) -> StageExecutionResult:
        if stage.config_name not in self.config.dataset_configs:
            return StageExecutionResult.error_result(
                f"Dataset config '{stage.config_name}' not found"
            )

        dataset_config = self.config.dataset_configs[stage.config_name]

        input_path = self.orchestrator._get_stage_output_path(
            stage.config_name, STAGE_PARTITION
        )
        output_path = self.orchestrator._get_stage_output_path(
            stage.config_name, STAGE_RETRIEVAL
        )

        def run_retrieval():
            import research.data.rag.qwelden.pipeline.qwelden_hindsight_20241229 as hs

            hs.retrieval_stage(
                input_dir=input_path,
                output_dir=output_path,
                tenant_name=self.config.tenant_name,
                use_hindsight_ground_truth=dataset_config.use_hindsight_ground_truth,
                num_retrieved_chunks=dataset_config.num_retrieved_chunks,
                test_mode=self.config.test_mode,
            )

        return self._safe_execute(stage, run_retrieval)


class MixdataStageExecutor(StageExecutor):
    """Executor for the mixdata stage."""

    def execute(self, stage: StageExecution) -> StageExecutionResult:
        if stage.config_name not in self.config.mixdata_configs:
            return StageExecutionResult.error_result(
                f"Mixdata config '{stage.config_name}' not found"
            )

        mixdata_config = self.config.mixdata_configs[stage.config_name]

        def input_path_fn(dataset_name):
            return self.orchestrator._get_stage_output_path(
                dataset_name, STAGE_RETRIEVAL
            )

        output_path = self.orchestrator._get_stage_output_path(
            stage.config_name, STAGE_MIXDATA
        )

        def run_mixdata():
            import research.data.rag.qwelden.pipeline.qwelden_hindsight_20241229 as hs

            hs.mixdata_stage(
                input_dir_fn=input_path_fn,
                output_dir=output_path,
                mixdata_config=mixdata_config,
            )

        return self._safe_execute(stage, run_mixdata)


class PromptStageExecutor(StageExecutor):
    """Executor for the prompt stage."""

    def execute(self, stage: "StageExecution") -> StageExecutionResult:
        if stage.config_name not in self.config.prompt_configs:
            return StageExecutionResult.error_result(
                f"Prompt config '{stage.config_name}' not found"
            )

        prompt_config = self.config.prompt_configs[stage.config_name]

        # Ensure the mixdata config that this prompt config references exists
        if prompt_config.mixdata_config_name not in self.config.mixdata_configs:
            return StageExecutionResult.error_result(
                f"Mixdata config '{prompt_config.mixdata_config_name}' not found"
            )

        input_path = self.orchestrator._get_stage_output_path(
            prompt_config.mixdata_config_name, STAGE_MIXDATA
        )
        output_path = self.orchestrator._get_stage_output_path(
            stage.config_name, STAGE_PROMPT
        )

        def run_prompt():
            import research.data.rag.qwelden.pipeline.qwelden_hindsight_20241229 as hs

            hs.prompt_stage(
                input_dir=input_path,
                output_dir=output_path,
                prompt_config=prompt_config,
            )

        return self._safe_execute(stage, run_prompt)


class IndexedDatasetStageExecutor(StageExecutor):
    """Executor for the indexed dataset stage."""

    def execute(self, stage: "StageExecution") -> StageExecutionResult:
        if stage.config_name not in self.config.prompt_configs:
            return StageExecutionResult.error_result(
                f"Prompt config '{stage.config_name}' not found"
            )

        prompt_config = self.config.prompt_configs[stage.config_name]

        input_path = self.orchestrator._get_stage_output_path(
            stage.config_name, STAGE_PROMPT
        )
        output_path = self.orchestrator._get_stage_output_path(
            stage.config_name, STAGE_INDEXED_DATASET
        )

        def run_indexed_dataset():
            import research.data.rag.qwelden.pipeline.qwelden_hindsight_20241229 as hs

            hs.indexed_dataset_stage(
                input_dir=input_path,
                output_dir=output_path,
                random_seed=prompt_config.random_seed,
            )

        return self._safe_execute(stage, run_indexed_dataset)


# =============================================================================
# PIPELINE ORCHESTRATOR
# =============================================================================


class PipelineOrchestrator:
    """Orchestrates the execution of the hindsight dataset pipeline."""

    def __init__(
        self,
        config_path: Path,
        output_dir: str,
        dry_run: bool = False,
        stages_to_run: Optional[List[str]] = None,
    ):
        """Initialize the orchestrator.

        Args:
            config_path: Path to the YAML configuration file
            output_dir: Output directory for pipeline results
            dry_run: If True, only show what would be executed without running
            stages_to_run: If specified, only run these stages (default: run all stages)
        """
        self.config_path = config_path
        self.config = PipelineConfigLoader.load_from_file(config_path)
        self.dry_run = dry_run
        self.stages_to_run = set(stages_to_run) if stages_to_run else None
        self.output_dir = output_dir

        # Validate configuration
        errors = PipelineConfigLoader.validate_config(self.config)
        if errors:
            raise ValueError("Configuration validation failed:\n" + "\n".join(errors))

        # Set up paths
        self.base_dir = Path(self.config.base_dir)
        # Always use the provided output directory
        self.pipeline_dir = Path(self.output_dir) / self.config.pipeline_name
        self.metadata_dir = self.pipeline_dir / ".pipeline_metadata"
        self.metadata_dir.mkdir(parents=True, exist_ok=True)

        # Initialize run metadata
        self.run_metadata = PipelineRunMetadata(
            config_path=str(config_path),
            config_hash=self._compute_config_hash(),
        )

        # Check if we need to wipe previous results due to config changes
        self._should_wipe_results = self._check_config_changes()

        # Initialize stage executors
        self.stage_executors: Dict[str, StageExecutor] = {
            STAGE_FILTER: FilterStageExecutor(self),
            STAGE_PARTITION: PartitionStageExecutor(self),
            STAGE_RETRIEVAL: RetrievalStageExecutor(self),
            STAGE_MIXDATA: MixdataStageExecutor(self),
            STAGE_PROMPT: PromptStageExecutor(self),
            STAGE_INDEXED_DATASET: IndexedDatasetStageExecutor(self),
        }

    def _compute_config_hash(self) -> str:
        """Compute a hash of the configuration for change detection."""
        with open(self.config_path, "rb") as f:
            return hashlib.sha256(f.read()).hexdigest()

    def _check_config_changes(self) -> bool:
        """Check if the configuration has changed since the last run.

        Only checks configs for stages that were specified in the previous config.
        If any previous stage's config has changed, asks user to wipe results.

        Returns:
            True if we should wipe results, False otherwise
        """
        last_config_path = self.metadata_dir / "last_config.yaml"

        if not last_config_path.exists():
            print("Warning: No previous config found.")
            return False  # No previous config, no need to wipe

        try:
            old_config = PipelineConfigLoader.load_from_file(last_config_path)
        except Exception as e:
            print(f"Warning: Could not load previous config ({e}). Requiring wipe.")
            return True

        if old_config.test_mode != self.config.test_mode:
            print("Warning: Test mode has changed. Requiring wipe.")
            return True

        # Check which stages were specified in the previous config and compare their configs
        changed_configs = []

        # Check dataset configs (used by filter, partition, retrieval stages)
        for dataset_name, dataset_config in self.config.dataset_configs.items():
            for stage_name in [STAGE_FILTER, STAGE_PARTITION, STAGE_RETRIEVAL]:
                old_dataset_config = old_config.dataset_configs.get(dataset_name)
                if old_dataset_config != dataset_config:
                    changed_configs.append(f"{stage_name} (dataset: {dataset_name})")

        # Check mixdata configs (used by mixdata stage)
        if self.config.mixdata_configs:
            for mixdata_name, mixdata_config in self.config.mixdata_configs.items():
                old_mixdata_config = old_config.mixdata_configs.get(mixdata_name)
                if old_mixdata_config != mixdata_config:
                    changed_configs.append(f"{STAGE_MIXDATA} (mixdata: {mixdata_name})")

        # Check prompt configs (used by prompt and indexed_dataset stages)
        if self.config.prompt_configs:
            for prompt_name, prompt_config in self.config.prompt_configs.items():
                for stage_name in [STAGE_PROMPT, STAGE_INDEXED_DATASET]:
                    old_prompt_config = old_config.prompt_configs.get(prompt_name)
                    if old_prompt_config != prompt_config:
                        changed_configs.append(f"{stage_name} (prompt: {prompt_name})")

        # If no previous stages have changed configs, no need to wipe
        if not changed_configs:
            return False  # No previous stages have changed configs, no need to wipe

        # Some previous stages have changed configs, ask user
        print("\nConfiguration has changed for the following previous stages:")
        for stage_info in changed_configs:
            print(f"  - {stage_info}")

        if self.dry_run:
            print(
                "[DRY RUN] Would wipe all previous results and start fresh (will not actually run)"
            )
            return True
        else:
            response = input(
                "\nConfiguration changed. This will wipe all previous results and start fresh. `i` will ignore this safety check, only recommended if you know what you are doing. Continue? (y/n/i): "
            )
            if response.lower() not in ["y", "i"]:
                print("Pipeline execution cancelled.")
                exit(1)
            if response.lower() == "i":
                print("Ignoring config change check. Proceeding at your own risk.")
                return False

            return True  # Config changed, wipe results

    def _wipe_pipeline_results(self):
        """Wipe all pipeline stage results and metadata to start fresh."""
        if self.pipeline_dir.exists():
            if not self.dry_run:
                print(f"Wiping pipeline results and metadata in {self.pipeline_dir}")
                import shutil

                shutil.rmtree(self.pipeline_dir)
                self.metadata_dir.mkdir(parents=True, exist_ok=True)
            else:
                print(
                    f"[DRY RUN] Would wipe pipeline results and metadata in {self.pipeline_dir}"
                )

    def _save_current_config(self):
        """Save the current configuration as the last used config."""
        if not self.dry_run:
            last_config_path = self.metadata_dir / "last_config.yaml"
            with open(self.config_path, "r") as src, open(last_config_path, "w") as dst:
                dst.write(src.read())

    def _get_stage_output_path(self, config_name: str, stage: str) -> Path:
        """Get the output path for a specific stage and data source.

        Directory structure:
        base_dir/pipelines/pipeline_name/stage/config_name/

        For example:
        /mnt/efs/augment/user/pranay/hindsight/pipelines/vanguard_2025/filter/vanguard_permissive_hindsight/
        /mnt/efs/augment/user/pranay/hindsight/pipelines/vanguard_2025/mixdata/mix_empty10/
        /mnt/efs/augment/user/pranay/hindsight/pipelines/vanguard_2025/prompt/mix_empty15_no_dropout/
        """
        return self.pipeline_dir / stage / config_name

    def _is_stage_complete(self, config_name: str, stage: str) -> bool:
        """Check if a stage has already been completed."""
        if self._should_wipe_results and self.dry_run:
            return False  # If we should wipe and it's a dry run, nothing is complete

        if stage not in STAGE_ORDER:
            raise ValueError(f"Unknown stage: {stage}")

        output_path = self._get_stage_output_path(config_name, stage)
        pattern = COMPLETION_PATTERNS[stage]
        return output_path.exists() and any(output_path.glob(pattern))

    def _execute_stage(self, stage: StageExecution) -> bool:
        """Execute a single stage.

        Args:
            stage: StageExecution object containing stage details

        Returns:
            True if successful, False otherwise
        """
        print(f"\n{'[DRY RUN] ' if self.dry_run else ''}Executing {stage.display_name}")

        executor = self.stage_executors.get(stage.stage_name)
        if not executor:
            print(f"Unknown stage: {stage.stage_name}")
            return False

        result = executor.execute(stage)
        if not result.success:
            print(f"Error: {result.error_message}")

        return result.success

    def _save_run_metadata(self):
        """Save metadata (config already saved earlier)."""
        if not self.dry_run:
            metadata = {
                "config_path": str(self.config_path),
                "config_hash": self.run_metadata.config_hash,
            }

            current_run_path = self.metadata_dir / "last_run.json"
            with open(current_run_path, "w") as f:
                json.dump(metadata, f, indent=2)

    def _analyze_pipeline(self) -> PipelineAnalysis:
        """Analyze the pipeline and determine what stages need to run.

        Returns:
            PipelineAnalysis object containing stages to run and completed stages
        """
        analysis = PipelineAnalysis()

        # Process each data source through individual stages
        for dataset_config_name in self.config.dataset_configs.keys():
            print(f"\nDataset: {dataset_config_name}")

            # Check filter, partition, and retrieval stages
            for stage_name in [STAGE_FILTER, STAGE_PARTITION, STAGE_RETRIEVAL]:
                if self.stages_to_run and stage_name not in self.stages_to_run:
                    continue
                stage = StageExecution(
                    stage_name=stage_name, config_name=dataset_config_name
                )
                if self._is_stage_complete(dataset_config_name, stage_name):
                    print(f"  - {stage_name}: already complete")
                    analysis.add_completed_stage(stage)
                else:
                    print(f"  - {stage_name}: needs to run")
                    analysis.add_stage_to_run(stage)

        # Process mixdata configurations
        if self.config.mixdata_configs:
            print("\nMixdata configurations:")
            for mixdata_config_name in self.config.mixdata_configs.keys():
                print(f"\n{mixdata_config_name}:")

                # Check mixdata stage
                if not self.stages_to_run or STAGE_MIXDATA in self.stages_to_run:
                    stage = StageExecution(
                        stage_name=STAGE_MIXDATA, config_name=mixdata_config_name
                    )
                    if self._is_stage_complete(mixdata_config_name, STAGE_MIXDATA):
                        print(f"  - {STAGE_MIXDATA}: already complete")
                        analysis.add_completed_stage(stage)
                    else:
                        print(f"  - {STAGE_MIXDATA}: needs to run")
                        analysis.add_stage_to_run(stage)

        # Process prompt configurations
        if self.config.prompt_configs:
            print("\nPrompt configurations:")
            for prompt_config_name in self.config.prompt_configs.keys():
                print(f"\n{prompt_config_name}:")

                # Check prompt stage
                for stage_name in [STAGE_PROMPT, STAGE_INDEXED_DATASET]:
                    if not self.stages_to_run or stage_name in self.stages_to_run:
                        stage = StageExecution(
                            stage_name=stage_name, config_name=prompt_config_name
                        )
                        if self._is_stage_complete(prompt_config_name, stage_name):
                            print(f"  - {stage_name}: already complete")
                            analysis.add_completed_stage(stage)
                        else:
                            print(f"  - {stage_name}: needs to run")
                            analysis.add_stage_to_run(stage)

        return analysis

    def _execute_pipeline(self, analysis: PipelineAnalysis) -> bool:
        """Execute the pipeline stages in order.

        Args:
            analysis: PipelineAnalysis object containing stages to run

        Returns:
            True if all stages completed successfully
        """
        for stage_name in STAGE_ORDER:
            # Find all instances of this stage that need to run
            stage_instances = analysis.get_stages_by_name(stage_name)

            if not stage_instances:
                continue

            # Execute each instance
            for stage in stage_instances:
                success = self._execute_stage(stage)

                if not success:
                    print(f"\nStage {stage.stage_name} failed. Stopping pipeline.")
                    return False

        return True

    def run(self) -> bool:
        """Run the complete pipeline.

        Returns:
            True if all stages completed successfully, False otherwise
        """
        print(f"Configuration: {self.config_path}")
        print(f"Pipeline directory: {self.pipeline_dir}")

        if self.stages_to_run:
            print(f"Running only stages: {', '.join(sorted(self.stages_to_run))}")
        else:
            print("Running all stages")

        # Wipe results if configuration changed
        if self._should_wipe_results:
            self._wipe_pipeline_results()

        # Save current config immediately after setup
        self._save_current_config()
        self._save_run_metadata()

        # Analyze what needs to be done
        print("\n=== Pipeline Analysis ===")
        analysis = self._analyze_pipeline()

        if not analysis.has_work_to_do:
            print("\nAll stages are already complete.")
            return True

        # Confirm execution plan
        if not self.dry_run:
            response = input("\nProceed with execution? (y/n): ")
            if response.lower() != "y":
                print("Pipeline execution cancelled.")
                return False

        # Execute stages
        print("\n=== Pipeline Execution ===")
        success = self._execute_pipeline(analysis)

        return success
