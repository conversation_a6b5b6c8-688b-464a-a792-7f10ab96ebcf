# Hindsight Dataset Pipeline

This directory contains scripts for processing and preparing data for the Qwelden model training pipeline. The pipeline processes hindsight data (user interactions with the model) to create datasets for training and evaluation.

## Overview

The pipeline processes data through several stages:
1. **Filter**: Apply various filters to raw JSONL data
2. **Partition**: Convert filtered JSONL to partitioned Parquet files
3. **Retrieval**: Perform dense retrieval and target generation
   - Uses `use_hindsight_ground_truth` flag to determine behavior
   - Generates retrieved chunks from line and signature retriever, edit events and recency chunks
4. **Mixdata**: Combine multiple data sources with specified percentages
5. **Prompt**: Generate prompts and target tokens with configurable parameters
   - Converts mixdata output to formatted prompts
   - Creates indexed datasets for training
   - Supports multiple prompt configurations per mixdata config
6. **Indexed Dataset**: Create final indexed dataset for training
   - Combines prompt outputs into a single indexed dataset

## Usage

### Basic Usage

```bash
python research/data/rag/qwelden/pipeline/run_pipeline.py config.yaml --output-dir /path/to/output
```

### Dry Run (Preview)

```bash
python research/data/rag/qwelden/pipeline/run_pipeline.py config.yaml --output-dir /path/to/output --dry-run
```

### Run Specific Stages

```bash
python research/data/rag/qwelden/pipeline/run_pipeline.py config.yaml --output-dir /path/to/output --stages filter,partition,retrieval
python research/data/rag/qwelden/pipeline/run_pipeline.py config.yaml --output-dir /path/to/output --stages prompt
```


## New Pipeline System
- `run_pipeline.py`: Main orchestrator script that runs the complete pipeline from YAML config
- `schema.py`: Configuration schema and validation
- `orchestrator.py`: Pipeline execution

- `filter_jsonl_data.py`: Contains filter stage function that filters hindsight data based on various criteria
- `partition_jsonl_to_parquet.py`: Contains partition stage function that converts filtered jsonl file to parquet with ZSTD compression and partitions by user_id
- `qwelden_hindsight_20241229.py`: Contains stage functions for retrieval, mixdata, prompt, and indexed dataset generation

## Configuration Format

### Key Configuration Parameters

- `base_dir`: Base directory for input hindsight data (e.g., `/mnt/efs/augment/user/pranay/hindsight`)
- `pipeline_name`: Name for this pipeline run (used in output paths)
- `--output-dir` (required command-line argument): Output directory where all pipeline outputs will be written

### Directory Structure

The pipeline outputs will be written to the directory specified by the --output-dir command line argument:
```
output_dir/pipeline_name/
├── filter/
│   ├── data_source1/
│   └── data_source2/
├── partition/
│   ├── data_source1/
│   └── data_source2/
├── retrieval/
│   ├── data_source1/
│   └── data_source2/
├── mixdata/
│   ├── mix_name1/
│   └── mix_name2/
├── prompt/
│   ├── prompt_name1/
│   └── prompt_name2/
└── indexed_dataset/
    ├── prompt_name1/
    └── prompt_name2/
```

### Data Source Configuration

Each data source defines:
- `input_datasets`: List of input dataset names
- `filters`: Filters to apply during the filter stage
  - `model`: Model name to filter by
  - `license_filter`: Apply license filtering
  - `edit_filter`: Keep only events with edit events
  - `skip_token_filter`: Filter out skip tokens
  - `empty_completion_filter`: Filter out empty completions
  - `limit_filter`: Limit number of events
- `use_hindsight_ground_truth`: Whether to use ground truth in target generation

### Mixdata Configuration

Defines how to combine multiple data sources:
- `dataset_percentages`: Dictionary mapping dataset names to their percentages (null = main dataset, uses all samples)
- `random_seed`: Random seed for sampling

### Prompt Configuration

Defines how to generate prompts from mixdata:
- `mixdata_config`: Name of the mixdata config to use as input
- `max_content_len`: Maximum content length for prompts
- `input_fraction`: Fraction of content to use for input
- `prefix_fraction`: Fraction of input to use for prefix
- `max_path_tokens`: Maximum tokens for file path
- `max_dense_signature_tokens`: Maximum tokens for dense signature retrieval
- `max_recency_retriever_tokens`: Maximum tokens for recency retrieval
- `max_diff_retriever_tokens`: Maximum tokens for diff retrieval
- `include_diff_retriever`: Whether to include diff retriever
- `max_target_tokens`: Maximum tokens for target

## Example Configurations

See the `configs/` directory for examples:
- `vanguard_permissive_test.yaml`: Basic configuration example
- `vanguard_permissive.yaml`: Full configuration example

### Specifying Output Directory

The output directory is now a required command-line argument:

```bash
python research/data/rag/qwelden/pipeline/run_pipeline.py config.yaml --output-dir /mnt/efs/augment/user/pranay/custom_output
```

This will create outputs at `/mnt/efs/augment/user/pranay/custom_output/pipeline_name/` where `pipeline_name` is specified in your configuration file.

## Pipeline Metadata

The pipeline creates a `.pipeline_metadata/` directory containing metadata about the pipeline run.

## Integration with Existing Scripts

The pipeline orchestrator calls functions directly from:
- `filter_jsonl_data.py`: `filter_stage()` for the filter stage
- `partition_jsonl_to_parquet.py`: `partition_stage()` for the partition stage
- `qwelden_hindsight_20241229.py`:
  - `retrieval_stage()` for retrieval and target generation
  - `mixdata_stage()` for mixdata stage
  - `prompt_stage()` for prompt generation and indexed dataset creation
  - `indexed_dataset_stage()` for final indexed dataset creation
