"""Data pipeline to process hindsight parquet files.

The pipeline is consist of 4 stages:
1. Retrieve line and signature chunks, recency chunks, and diff chunks. Also truncate ground truth to target.
2. Mixup different subsets of data into a single dataset.
3. Generate prompts from the mixdata.
4. Create final indexed dataset for training or eval.
"""

import functools
import os
import time
from dataclasses import dataclass
from pathlib import Path
from typing import Callable, Optional

import pyspark.sql.dataframe as sparkDF

from base.tokenizers.qwen25coder_tokenizer import Qwen25CoderTokenizer
from research.core.utils_for_log import time_string
from research.data.rag import hindsight_common
from research.data.rag.common import create_pad_pack_tokens_fn
from research.data.rag.qwelden.pipeline.pipeline_config import (
    MixdataConfig,
    PromptConfig,
)
from research.data.spark import k8s_session
from research.data.spark.pipelines.stages.common import export_indexed_dataset_helper
from research.data.spark.pipelines.utils import map_parquet
from research.data.spark.pipelines.utils.map_parquet import (
    allow_unused_args,
    passthrough_feature,
)
from research.data.utils.spark_utils import repartition_and_shuffle
from research.determined_utils import bucket_for_augment_fs_path

TASK_INFO = "task_info"

# Config for StarEthanol. StarEthanol is used for line chunk retrieval.
SETHANOL_CONFIG = dict(
    chunker={
        "name": "transform_smart_line_level",
        "max_chunk_chars": 768,
        "max_headers": 3,
    },
    document_formatter={
        "name": "base:ethanol6-embedding-with-path-key",
        "max_tokens": 999,
        "tokenizer": "starcoder",
    },
    query_formatter={
        "name": "base:ethanol6.16.1-query-embedding",
        "max_tokens": 1023,
        "tokenizer": "starcoder",
    },
    scorer={
        "name": "dense_scorer_v2_fbwd",
        "checkpoint_path": "/mnt/efs/augment/checkpoints/michiel/retriever/ethanol/stareth_2000s_128docactual_smartnoheader",
    },
    max_num_chunks_per_file=512,
)

# Config for Methanol. Methanol is used for signature chunk retrieval.
METHANOL_CONFIG = dict(
    scorer={
        "checkpoint_path": "/mnt/efs/augment/checkpoints/menthol/methanol_0416.4_1250/global_step1250/",
        "name": "dense_scorer_v2_fbwd_neox",
    },
    chunker={
        "name": "signature",
    },
    document_formatter={
        "add_path": False,
        "name": "simple_document",
        "max_tokens": 999,
        "tokenizer_name": "StarCoderTokenizer",
    },
    query_formatter={
        "add_path": True,
        "add_suffix": True,
        "max_tokens": 1023,
        "name": "ethanol6_query",
        "tokenizer_name": "StarCoderTokenizer",
    },
    max_num_chunks_per_file=512,
)


def _get_uri_path(dir: Path) -> str:
    bucket, path = bucket_for_augment_fs_path(dir)
    return f"gs://{bucket}/{path}"


def retrieval_stage(
    input_dir: Path,
    output_dir: Path,
    tenant_name: str,
    use_hindsight_ground_truth: bool,
    num_retrieved_chunks: int = 32,
    test_mode: bool = False,
):
    """Process the partitioned data with retrieval and integrated target generation.

    Args:
        base_dir: Base directory for hindsight data
        pipeline_name: Name of the pipeline
        data_source: Name of the data source
        tenant_name: Tenant name
        use_hindsight_ground_truth: Whether to use ground truth (True) or model output (False) as target.
    """
    start_time = time.time()

    spark = k8s_session(
        name=f"retrieval-parquet-{tenant_name}",
        max_workers=96,
        conf={
            "spark.executor.pyspark.memory": "256G",
            "spark.executor.memory": "110G",
            "spark.executor.cores": "1",
            "spark.task.cpus": "1",
            "spark.sql.parquet.columnarReaderBatchSize": "16",
        },
        gpu_type=["h100"],
        ephemeral_storage_gb=110,
    )

    # Uncomment this to run on local.
    # spark = get_session()

    input_url = _get_uri_path(input_dir)
    output_url = _get_uri_path(output_dir)

    data = spark.read.parquet(
        *map_parquet.list_files(spark, input_url, suffix="parquet", include_path=True)
    )
    input_count = data.count()
    print(f"{time_string()} Input {input_url} has {input_count} rows.")
    # data.printSchema()

    def make_retrieval_with_target_fn():
        def _create_retriever(config):
            from research.eval.harness.factories import create_retriever
            from research.retrieval.retrieval_database import RetrievalDatabase

            retrieval_database = create_retriever(config)
            assert isinstance(retrieval_database, RetrievalDatabase)
            return retrieval_database

        return hindsight_common.create_retrieval_processor()(
            retrieval_database_factories={
                "dense_retriever": lambda: _create_retriever(SETHANOL_CONFIG),
                "dense_signature": lambda: _create_retriever(METHANOL_CONFIG),
            },
            num_retrieved_chunks=num_retrieved_chunks,
            tenant_name=tenant_name,
            use_hindsight_ground_truth=use_hindsight_ground_truth,
            test_mode=test_mode,
        )

    _ = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                hindsight_common.deserialize_hindsight_problem,
                hindsight_common.FunctionWrapper(make_fn=make_retrieval_with_target_fn),
            ]
        ),
        input_path=input_url,
        output_path=output_url,
        timeout=3600 * 5,
        batch_size=128,
        task_info_location=os.path.join(output_dir, TASK_INFO),
        ignore_error=False,
        allow_resume=True,
    )

    data = spark.read.parquet(
        *map_parquet.list_files(spark, output_url, suffix="parquet", include_path=True)
    )
    data.printSchema()

    output_count = data.count()
    print(
        f"{time_string()} Output {output_url} has {output_count} rows. Retained {output_count}/{input_count} of the input."
    )

    spark.stop()

    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")


@dataclass
class DatasetInfo:
    """Encapsulates all information about a dataset during mixdata processing."""

    name: str
    dataframe: sparkDF.DataFrame
    num_samples: int
    target_samples: Optional[int] = None
    sampling_fraction: Optional[float] = None
    sampled_dataframe: Optional[sparkDF.DataFrame] = None


def mixdata_stage(
    input_dir_fn: Callable[[str], Path], output_dir: Path, mixdata_config: MixdataConfig
):
    """Mixup different subsets of data into a single dataset using pipeline configuration.

    Args:
        input_dir_fn: Function to get input directory for a dataset
        output_dir: Output directory for the mixed dataset
        mixdata_config: MixdataConfig object with mixdata configuration
    """
    start_time = time.time()
    spark = k8s_session(
        name="mixdata-parquet",
        max_workers=16,
        conf={
            "spark.executor.memory": "128G",
            "spark.executor.cores": "8",
            "spark.task.cpus": "1",
            "spark.sql.parquet.columnarReaderBatchSize": "20",
        },
        ephemeral_storage_gb=128,
    )

    # 1. Load all dataframes and count samples
    dataset_infos: dict[str, DatasetInfo] = {}

    for dataset_name in mixdata_config.dataset_names:
        input_url = _get_uri_path(input_dir_fn(dataset_name))
        df = spark.read.parquet(os.path.join(input_url, "*zstd.parquet"))
        samples = df.count()
        print(f"{time_string()} Input {input_url} has {samples} rows.")

        dataset_infos[dataset_name] = DatasetInfo(
            name=dataset_name,
            dataframe=df,
            num_samples=samples,
        )

    # 2. Calculate target samples for each dataset
    remaining_percentage = 1.0 - mixdata_config.total_specified_percentage
    assert (
        remaining_percentage > 0
    ), f"Insufficient remaining percentage for main dataset. Got {remaining_percentage:.2f}"

    total_main_samples = sum(
        dataset_infos[d].num_samples for d in mixdata_config.main_datasets
    )
    assert (
        total_main_samples > 0
    ), f"Insufficient samples for main datasets. Found {total_main_samples}"
    target_final_dataset_size = int(total_main_samples / remaining_percentage)

    # For datasets with specified percentage, calculate target samples. For main datasets, use all available samples.
    for dataset_name, percentage in mixdata_config.dataset_percentages.items():
        dataset_info = dataset_infos[dataset_name]
        if percentage is not None:
            # Calculate target samples based on final dataset size
            dataset_info.target_samples = int(target_final_dataset_size * percentage)
        else:
            # Main dataset gets all available samples
            dataset_info.target_samples = dataset_info.num_samples

        print(
            f"{time_string()} Target samples for {dataset_name}: Taking {dataset_info.target_samples}/{dataset_info.num_samples}={dataset_info.target_samples/dataset_info.num_samples:.1%} samples from dataset, which is {dataset_info.target_samples}/{target_final_dataset_size}={dataset_info.target_samples/target_final_dataset_size:.1%} of final dataset"
        )
        assert (
            dataset_info.num_samples >= dataset_info.target_samples
        ), f"Insufficient samples for {dataset_name}. Found {dataset_info.num_samples} but need {dataset_info.target_samples}"

    # 3. Calculate sampling fraction for each dataset
    for dataset_info in dataset_infos.values():
        assert (
            dataset_info.target_samples is not None
        ), f"Target samples not set for {dataset_info.name}"
        sampling_fraction = min(
            1.0, dataset_info.target_samples / dataset_info.num_samples
        )
        dataset_info.sampling_fraction = sampling_fraction

    # 4. Sample from each dataset and add new column
    sampled_dataframes = []
    for dataset_name in mixdata_config.dataset_names:
        dataset_info = dataset_infos[dataset_name]
        assert (
            dataset_info.sampling_fraction is not None
        ), f"Sampling fraction not set for {dataset_info.name}"

        # Sample does not always return the exact number of samples requested.
        sampled_dataframe = dataset_info.dataframe.sample(
            withReplacement=False,
            fraction=dataset_info.sampling_fraction,
            seed=mixdata_config.random_seed,
        )
        print(
            f"{time_string()} Sampling {dataset_info.sampling_fraction:.1%} of {dataset_info.name} ({dataset_info.num_samples} -> {dataset_info.target_samples}), got {sampled_dataframe.count()} samples."
        )
        dataset_info.sampled_dataframe = sampled_dataframe
        sampled_dataframes.append(sampled_dataframe)

    union_df = functools.reduce(
        lambda df1, df2: df1.unionByName(df2, allowMissingColumns=True),
        sampled_dataframes,
    )
    total_samples = union_df.count()

    # 5. Assert that we got the right number of samples
    for dataset_name, percentage in mixdata_config.dataset_percentages.items():
        dataset_info = dataset_infos[dataset_name]
        assert (
            dataset_info.target_samples is not None
        ), f"Target samples not set for {dataset_info.name}"
        target_samples = dataset_info.target_samples
        assert (
            dataset_info.sampled_dataframe is not None
        ), f"Sampled dataframe is None for {dataset_name}"
        actual_samples = dataset_info.sampled_dataframe.count()

        if percentage is None:
            assert (
                actual_samples == target_samples
            ), f"{time_string()} Failed to get all samples for main dataset {dataset_name}. Actually got {actual_samples} out of {target_samples} samples."
        else:
            actual_ratio = actual_samples / total_samples
            expected_ratio = percentage
            assert (
                abs(actual_ratio - expected_ratio) < 0.05
            ), f"{time_string()} Actual ratio {actual_ratio:.3f} is too far from expected ratio {expected_ratio:.3f} for {dataset_name}. Actually got {actual_samples=} out of {target_samples=}. Final dataset has {total_samples=} samples."

    # Write output
    output_url = _get_uri_path(output_dir)
    assert union_df is not None
    union_df.write.mode("overwrite").parquet(output_url)
    union_df = spark.read.parquet(output_url)
    print(f"{time_string()} Union has {union_df.count()} rows in {output_url}.")
    spark.stop()
    print(f"{time_string()} Time elapsed: {(time.time() - start_time)/60:.1f} mins.")


def prompt_stage(input_dir: Path, output_dir: Path, prompt_config: PromptConfig):
    """Generate the prompts and target tokens.

    Args:
        input_dir: Directory containing mixdata parquet files
        output_dir: Directory to write prompt outputs
        prompt_config: PromptConfig object with prompt generation parameters
    """

    input_url = _get_uri_path(input_dir)
    output_url = _get_uri_path(output_dir)

    start_time = time.time()
    spark = k8s_session(
        name="prompts-parquet",
        max_workers=16,
        conf={
            "spark.executor.memory": "128G",
            "spark.executor.cores": "8",
            "spark.task.cpus": "1",
            "spark.sql.parquet.columnarReaderBatchSize": "20",
        },
        ephemeral_storage_gb=128,
    )

    tokenizer = Qwen25CoderTokenizer()
    config = hindsight_common.EnderDataPromptFormattingConfig(
        max_content_len=prompt_config.max_content_len,
        input_fraction=prompt_config.input_fraction,
        prefix_fraction=prompt_config.prefix_fraction,
        max_path_tokens=prompt_config.max_path_tokens,
        max_dense_signature_tokens=prompt_config.max_dense_signature_tokens,
        max_recency_retriever_tokens=prompt_config.max_recency_retriever_tokens,
        max_diff_retriever_tokens=prompt_config.max_diff_retriever_tokens,
        max_recency_retriever_viewed_content_tokens=prompt_config.max_recency_retriever_viewed_content_tokens,
        include_diff_retriever=prompt_config.include_diff_retriever,
        max_target_tokens=prompt_config.max_target_tokens,
    )

    _ = map_parquet.apply_pandas(
        spark,
        map_parquet.chain_processors(
            [
                passthrough_feature()(
                    allow_unused_args()(
                        hindsight_common.generate_ender_prompt_fn(
                            tokenizer=tokenizer,
                            config=config,
                        )
                    )
                ),
                allow_unused_args()(
                    hindsight_common.add_target_to_prompt_tokens_fn(
                        tokenizer=tokenizer,
                        config=config,
                    )
                ),
                passthrough_feature()(
                    allow_unused_args()(
                        create_pad_pack_tokens_fn(
                            seq_len=config.max_content_len,
                            tokenizer=tokenizer,
                        )
                    )
                ),
            ]
        ),
        input_path=input_url,
        output_path=output_url,
        task_info_location=os.path.join(output_url, TASK_INFO),
        ignore_error=False,
        timeout=3600,
        batch_size=128,
    )

    spark.stop()
    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")


def indexed_dataset_stage(input_dir: Path, output_dir: Path, random_seed: int = 42):
    """Export the prompts to indexed dataset.

    Args:
        input_dir: Directory containing prompt parquet files
        output_dir: Directory to write indexed dataset
        random_seed: Random seed for shuffling
    """

    start_time = time.time()
    spark = k8s_session(
        name="indexed-dataset",
        max_workers=16,
        conf={
            "spark.executor.memory": "128G",
            "spark.executor.cores": "8",
            "spark.task.cpus": "1",
            "spark.sql.parquet.columnarReaderBatchSize": "20",
        },
        ephemeral_storage_gb=128,
    )

    input_url = _get_uri_path(input_dir)
    tokenizer = Qwen25CoderTokenizer()

    df = spark.read.parquet(
        *map_parquet.list_files(spark, input_url, suffix="parquet", include_path=True)
    ).select("prompt_tokens")
    df = repartition_and_shuffle(random_seed, df)
    print(f"{time_string()} Generated {df.count()} rows in {input_url}.")

    export_indexed_dataset_helper(
        df,
        vocab_size=tokenizer.vocab_size,
        samples_column="prompt_tokens",
        num_validation_samples=0,
        indexed_dataset_path=output_dir,
        filter_by_langs=None,
    )

    print(f"{time_string()} Final Dataset generated: {output_dir}")

    spark.stop()
    total_time = time.time() - start_time
    print(f"{time_string()} Total time: {total_time / 60:.1f} mins.")
