"""Schema definitions for the hindsight dataset pipeline configuration."""

from dataclasses import dataclass, field
from typing import Dict, List, Optional, Any
from pathlib import Path
import yaml

from research.data.rag.qwelden.pipeline.filter_jsonl_data import FilterConfig


@dataclass
class DatasetConfig:
    """Configuration for a single data source."""

    name: str
    """Unique name for this data source (e.g., 'hindsight', 'skip', 'empty')."""

    input_datasets: List[str]
    """List of input dataset names to process."""

    filters: FilterConfig
    """Filters to apply during filter_jsonl_data stage."""

    use_hindsight_ground_truth: bool
    """Whether to use ground truth in target generation during retrieval."""

    num_retrieved_chunks: int = 32
    """Number of retrieved chunks to keep during retrieval."""


@dataclass
class MixdataConfig:
    """Configuration for the mixdata stage."""

    dataset_percentages: Dict[str, Optional[float]]
    """Dataset names mapped to their percentages. None means main dataset."""

    random_seed: int = 42
    """Random seed for sampling."""

    @property
    def dataset_names(self) -> List[str]:
        """Returns list of dataset names."""
        return list(self.dataset_percentages.keys())

    @property
    def main_datasets(self) -> List[str]:
        """Returns the main datasets (those with percentage=None)."""
        return [name for name, pct in self.dataset_percentages.items() if pct is None]

    @property
    def total_specified_percentage(self) -> float:
        """Returns the sum of all specified percentages."""
        return sum(
            value for value in self.dataset_percentages.values() if value is not None
        )


@dataclass
class PromptConfig:
    """Configuration for the prompt stage."""

    mixdata_config_name: str
    """Name of the mixdata config to use as input."""

    max_content_len: int
    """Maximum content length for prompts."""

    input_fraction: float
    """Fraction of content to use for input."""

    prefix_fraction: float
    """Fraction of input to use for prefix."""

    max_path_tokens: int
    """Maximum tokens for file path."""

    max_dense_signature_tokens: int
    """Maximum tokens for dense signature retrieval."""

    max_recency_retriever_tokens: int
    """Maximum tokens for recency retrieval."""

    max_recency_retriever_viewed_content_tokens: int
    """Maximum tokens for recency retrieval of viewed content."""

    max_diff_retriever_tokens: int
    """Maximum tokens for diff retrieval."""

    include_diff_retriever: bool
    """Whether to include diff retriever."""

    max_target_tokens: int
    """Maximum tokens for target."""

    random_seed: int = 42
    """Random seed for sampling."""


@dataclass
class PipelineConfig:
    """Complete pipeline configuration."""

    version: str
    """Configuration version for compatibility checking."""

    pipeline_name: str
    """Name for this pipeline run (used in output paths)."""

    base_dir: str
    """Base directory for hindsight data (e.g., /mnt/efs/augment/user/pranay/hindsight)."""

    tenant_name: str
    """Tenant name (e.g., 'i0-vanguard0', 'dogfood-shard')."""

    dataset_configs: Dict[str, DatasetConfig]
    """Configuration for each data source."""

    mixdata_configs: Dict[str, MixdataConfig]
    """Configuration for mixdata combinations."""

    prompt_configs: Dict[str, PromptConfig]
    """Configuration for prompt generation."""

    output_dir: Optional[str] = None
    """DEPRECATED: Use --output-dir command line argument instead. This field is ignored."""

    metadata: Dict[str, Any] = field(default_factory=dict)
    """Additional metadata (e.g., description, author)."""

    test_mode: bool = False
    """Whether to run in test mode (limited indexing.)."""


class PipelineConfigLoader:
    """Loads and validates pipeline configuration from YAML."""

    @staticmethod
    def load_from_file(config_path: Path) -> PipelineConfig:
        """Load pipeline configuration from a YAML file."""
        with open(config_path, "r") as f:
            data = yaml.safe_load(f)

        return PipelineConfigLoader._parse_config(data)

    @staticmethod
    def _parse_config(data: Dict[str, Any]) -> PipelineConfig:
        """Parse configuration dictionary into PipelineConfig object."""
        # Parse data sources
        dataset_configs = {}
        for name, dataset in data.get("dataset_configs", {}).items():
            # Parse filters
            filter_data = dataset.get("filters", {})
            filters = FilterConfig(
                model=filter_data.get("model"),
                license_filter=filter_data.get("license_filter", False),
                edit_events_percentage=filter_data.get("edit_events_percentage", None),
                skip_token_filter=filter_data.get("skip_token_filter", False),
                empty_completion_filter=filter_data.get(
                    "empty_completion_filter", False
                ),
                limit_filter=filter_data.get("limit_filter"),
            )

            dataset_configs[name] = DatasetConfig(
                name=name,
                input_datasets=dataset["input_datasets"],
                filters=filters,
                use_hindsight_ground_truth=dataset["use_hindsight_ground_truth"],
            )

        # Parse mixdata configs
        mixdata_configs = {}
        for name, mixdata in data.get("mixdata_configs", {}).items():
            mixdata_configs[name] = MixdataConfig(
                dataset_percentages=mixdata["dataset_percentages"],
                random_seed=mixdata.get("random_seed", 42),
            )

        # Parse prompt configs
        prompt_configs = {}
        for name, prompt_data in data.get("prompt_configs", {}).items():
            prompt_configs[name] = PromptConfig(
                mixdata_config_name=prompt_data["mixdata_config_name"],
                max_content_len=prompt_data["max_content_len"],
                input_fraction=prompt_data["input_fraction"],
                prefix_fraction=prompt_data["prefix_fraction"],
                max_path_tokens=prompt_data["max_path_tokens"],
                max_dense_signature_tokens=prompt_data["max_dense_signature_tokens"],
                max_recency_retriever_tokens=prompt_data[
                    "max_recency_retriever_tokens"
                ],
                max_recency_retriever_viewed_content_tokens=prompt_data[
                    "max_recency_retriever_viewed_content_tokens"
                ],
                max_diff_retriever_tokens=prompt_data["max_diff_retriever_tokens"],
                include_diff_retriever=prompt_data["include_diff_retriever"],
                max_target_tokens=prompt_data["max_target_tokens"],
                random_seed=prompt_data.get("random_seed", 42),
            )

        return PipelineConfig(
            version=data["version"],
            pipeline_name=data["pipeline_name"],
            base_dir=data["base_dir"],
            output_dir=data.get("output_dir"),
            tenant_name=data["tenant_name"],
            dataset_configs=dataset_configs,
            mixdata_configs=mixdata_configs,
            prompt_configs=prompt_configs,
            metadata=data.get("metadata", {}),
            test_mode=data.get("test_mode", False),
        )

    @staticmethod
    def validate_config(config: PipelineConfig) -> List[str]:
        """Validate the configuration and return any errors."""
        errors = []

        # Check that mixdata datasets exist and validate percentages
        if config.mixdata_configs:
            for mix_name, mix_config in config.mixdata_configs.items():
                total_percentage = 0
                main_dataset_count = 0

                for dataset_name, percentage in mix_config.dataset_percentages.items():
                    # Check that the dataset exists in data_sources
                    if dataset_name not in config.dataset_configs:
                        errors.append(
                            f"Mixdata config '{mix_name}' references non-existent dataset '{dataset_name}'"
                        )

                    # Count percentages and main datasets
                    if percentage is not None:
                        total_percentage += percentage
                    else:
                        main_dataset_count += 1

                if total_percentage >= 1.0:
                    errors.append(
                        f"Mixdata config '{mix_name}' has total percentage >= 100%: {total_percentage}"
                    )
                if main_dataset_count == 0:
                    errors.append(
                        f"Mixdata config '{mix_name}' has no main dataset (percentage=None)"
                    )

        # Check that prompt configs reference valid mixdata configs
        if config.prompt_configs:
            for prompt_name, prompt_config in config.prompt_configs.items():
                if prompt_config.mixdata_config_name not in config.mixdata_configs:
                    errors.append(
                        f"Prompt config '{prompt_name}' references non-existent mixdata config '{prompt_config.mixdata_config_name}'"
                    )

        return errors


@dataclass
class PipelineRunMetadata:
    """Metadata for a pipeline run."""

    config_path: str
    """Path to the configuration file used."""

    config_hash: str
    """Hash of the configuration for change detection."""

    def to_dict(self) -> Dict[str, Any]:
        """Convert to dictionary for serialization."""
        return {
            "config_path": self.config_path,
            "config_hash": self.config_hash,
        }
