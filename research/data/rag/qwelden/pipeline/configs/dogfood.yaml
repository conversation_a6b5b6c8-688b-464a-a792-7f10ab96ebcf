# Example configuration for Vanguard permissive mix dataset pipeline with varying empty percentages
version: "1.0"

# Base directory for hindsight data. All paths are relative to this directory
# TODO: change before you run
base_dir: "/mnt/efs/augment/user/pranay/hindsight"

# Pipeline name (used in output paths)
pipeline_name: "dogfood_groundtruth_2024-11-01_2025-01-14"


test_mode: false

# Tenant name
tenant_name: "dogfood-shard"

# Metadata about this configuration
metadata:
  description: "Dogfood mix dataset with percentage variations"
  author: "mike"
  created_date: "2025-07-10"

# Data sources configuration. This impacts the filter, partition, and retrieval stages
dataset_configs:
  dogfood_hindsight:
    input_datasets:
      - "2024-11-01_2024-11-14/dogfood-shard"
      - "2024-11-15_2024-11-30/dogfood-shard"
      - "2024-12-01_2024-12-14/dogfood-shard"
      - "2024-12-15_2024-12-31/dogfood-shard"
      - "2025-02-15_2025-02-28/dogfood-shard"
    filters:
      model: null # Keep all models as we are using hindsight ground truth
      license_filter: false # Dogfood data is only for eval
      edit_filter: false
      skip_token_filter: true
      empty_completion_filter: true
      # limit_filter: 100
    use_hindsight_ground_truth: true

  # dogfood_skip:
  #   input_datasets:
  #     - "skip_2024-11-01_2025-01-14/0_50000/dogfood-shard"
  #   filters:
  #     model: "qweldenv1-1-14b"
  #     license_filter: true
  #     edit_filter: true
  #     skip_token_filter: false  # Keep only skip tokens
  #     empty_completion_filter: true
  #     # limit_filter: 20
  #   use_hindsight_ground_truth: false

  # vanguard_permissive_empty:
  #   input_datasets:
  #     - "empty_2024-11-01_2025-01-14/0_50000/i0-vanguard0"
  #     - "empty_2024-11-01_2025-01-14/50000_100000/i0-vanguard0"
  #     - "empty_2024-11-01_2025-01-14/100000_150000/i0-vanguard0"
  #     - "empty_2024-11-01_2025-01-14/150000_200000/i0-vanguard0"
  #     - "empty_2024-11-01_2025-01-14/200000_300000/i0-vanguard0"
  #   filters:
  #     model: "qweldenv1-1-14b"
  #     license_filter: true
  #     edit_filter: true
  #     skip_token_filter: true
  #     empty_completion_filter: false  # Keep only empty completions
  #     # limit_filter: 25
  #   use_hindsight_ground_truth: false

# Multiple mixdata configurations with different empty percentages. This impacts the mixdata stage
mixdata_configs:
  # mix_empty10:
  #   dataset_percentages:
  #     vanguard_permissive_hindsight: null  # main dataset
  #     vanguard_permissive_empty: 0.1      # 10% empty
  #     vanguard_permissive_skip: 0.034     # 3.4% skip

  pure-groundtruth:
    dataset_percentages:
      dogfood_hindsight: null  # main dataset
      # vanguard_permissive_empty: 0.15     # 15% empty
      # vanguard_permissive_skip: 0.034     # 3.4% skip

  # mix_empty20:
  #   dataset_percentages:
  #     vanguard_permissive_hindsight: null  # main dataset
  #     vanguard_permissive_empty: 0.2      # 20% empty
  #     vanguard_permissive_skip: 0.034     # 3.4% skip

# Prompt configurations. This impacts the prompt and indexed dataset stage
prompt_configs:
  8352b_1024s_1024r_2048d:
    mixdata_config_name: "pure-groundtruth"
    max_content_len: 8352
    input_fraction: 0.25  # 4/16
    prefix_fraction: 0.75  # 3/4
    max_path_tokens: 50
    max_dense_signature_tokens: 1024
    max_recency_retriever_tokens: 1024
    max_diff_retriever_tokens: 2048
    include_diff_retriever: true
    max_target_tokens: 256

  # mix_empty15_6304b_1024s_0r_0d:
  #   mixdata_config_name: "mix_empty15"
  #   max_content_len: 6304
  #   input_fraction: 0.33  # 4/12
  #   prefix_fraction: 0.75  # 3/4
  #   max_path_tokens: 50
  #   max_dense_signature_tokens: 1024
  #   max_recency_retriever_tokens: 0
  #   max_diff_retriever_tokens: 0
  #   include_diff_retriever: false
  #   max_target_tokens: 256
