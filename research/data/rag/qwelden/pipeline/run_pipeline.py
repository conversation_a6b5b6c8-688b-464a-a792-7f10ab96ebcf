"""Command-line interface for running the hindsight dataset pipeline."""

import argparse
import sys
from pathlib import Path
import warnings
from research.data.rag.qwelden.pipeline.orchestrator import PipelineOrchestrator

warnings.filterwarnings("ignore")


def main():
    """Main entry point for the pipeline CLI."""
    parser = argparse.ArgumentParser(
        description="Run the hindsight dataset pipeline",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Run pipeline with a configuration file and output directory
  python research/data/rag/qwelden/pipeline/run_pipeline.py config.yaml --output-dir /path/to/output

  # Dry run to see what would be executed
  python research/data/rag/qwelden/pipeline/run_pipeline.py config.yaml --output-dir /path/to/output --dry-run

  # Run only specific stages
  python research/data/rag/qwelden/pipeline/run_pipeline.py config.yaml --output-dir /path/to/output --stages filter partition

  # Run only retrieval stage
  python research/data/rag/qwelden/pipeline/run_pipeline.py config.yaml --output-dir /path/to/output --stages retrieval

Available stages: filter, partition, retrieval, mixdata, prompt, indexed_dataset

Note: If the configuration file has changed since the last run, the pipeline
will automatically ask if you want to wipe previous results and start fresh.
        """,
    )

    parser.add_argument("config", type=str, help="Path to the YAML configuration file")

    parser.add_argument(
        "--output-dir",
        type=str,
        required=True,
        help="Output directory for pipeline results (required)",
    )

    parser.add_argument(
        "--dry-run",
        action="store_true",
        help="Show what would be executed without actually running",
    )

    parser.add_argument(
        "--stages",
        nargs="+",
        choices=[
            "filter",
            "partition",
            "retrieval",
            "mixdata",
            "prompt",
            "indexed_dataset",
        ],
        help="Run only the specified stages (default: run all stages)",
    )

    args = parser.parse_args()

    # Validate config path
    config_path = Path(args.config)
    if not config_path.exists():
        print(f"Error: Configuration file not found: {config_path}")
        sys.exit(1)

    try:
        # Create and run orchestrator
        orchestrator = PipelineOrchestrator(
            config_path=config_path,
            output_dir=args.output_dir,
            dry_run=args.dry_run,
            stages_to_run=args.stages,
        )

        success = orchestrator.run()
        sys.exit(0 if success else 1)

    except Exception as e:
        print(f"Error: {e}")
        sys.exit(1)


if __name__ == "__main__":
    main()
