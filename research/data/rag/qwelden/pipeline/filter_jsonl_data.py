"""
Filter hindsight data for Qwen training. The filtering is applied in the following order:

1. Model filtering: Keep only events for a specific model. If None, will not filter by model.
2. License filtering: Keep only events that are licensed. If False, will not filter by license.
3. Edit events filtering: Keep only events that have edit events. If False, will not filter by edit events.
4. Skip token filtering: Filter out events where the completion response contains a <|skip|> token.
5. Empty completion filtering: Filter out events where the completion response is empty.
6. Limit filtering: Limit the number of events to keep. If None, will not limit the number of events.
"""

import json
import sys
from datetime import datetime
from pathlib import Path
from typing import List, Dict, Optional
from dataclasses import dataclass
import pandas as pd
from google.cloud import storage
from collections import Counter
import glob
import random

from research.core.utils_for_file import read_jsonl_zst, write_jsonl_zst

random.seed(42)


@dataclass
class FilterConfig:
    """Configuration for data filtering during the filter stage."""

    model: Optional[str]
    """Model name to filter by (e.g., 'qweldenv1_14b')."""

    license_filter: bool
    """Whether to apply license filtering."""

    edit_events_percentage: Optional[float]
    """Percentage of data to keep that have edit events. If None, will keep them all."""

    skip_token_filter: bool
    """Whether to apply skip token filtering."""

    empty_completion_filter: bool
    """Whether to apply empty completion filtering."""

    limit_filter: Optional[int]
    """Maximum number of records to keep (for testing/sampling)."""


# License filter constants
LICENSE_BUCKET = "gcp-us1-spark-data"
LICENSE_NON_PERMISSIVE_PREFIX = (
    "shared/vanguard/license_filter/non_permissive_rids/completion_host_request/"
)
LICENSE_PERMISSIVE_PREFIX = (
    "shared/vanguard/license_filter/permissive_rids/completion_host_request/"
)


@dataclass
class FilterStats:
    initial_count: int
    model_stats: Optional[Dict[str, int]] = None
    license_stats: Optional[Dict[str, int]] = None
    edit_events_stats: Optional[Dict[str, int]] = None
    skip_token_stats: Optional[Dict[str, int]] = None
    empty_completion_stats: Optional[Dict[str, int]] = None
    limit_stats: Optional[Dict[str, int]] = None
    final_count: int = 0

    def record_filter_stats(
        self, filter_name: str, before_count: int, after_count: int
    ) -> None:
        stats = {
            "total_before": before_count,
            "total_after": after_count,
            "total_filtered": before_count - after_count,
        }

        if filter_name == "model":
            self.model_stats = stats
        elif filter_name == "license":
            self.license_stats = stats
        elif filter_name == "edit_events":
            self.edit_events_stats = stats
        elif filter_name == "skip_token":
            self.skip_token_stats = stats
        elif filter_name == "empty_completion":
            self.empty_completion_stats = stats
        elif filter_name == "limit":
            self.limit_stats = stats


def apply_model_filter(data: List[dict], model: str) -> List[dict]:
    """Filter to keep only events for a specific model."""
    return [
        result for result in data if model in result["completion"]["response"]["model"]
    ]


def apply_license_filter(data: List[dict]) -> List[dict]:
    """Filter to keep only events that are licensed."""

    client = storage.Client()
    bucket = client.bucket(LICENSE_BUCKET)

    def read_parquet(paths: list[str]) -> pd.DataFrame:
        """Reads parquet data from paths using pandas."""
        dfs = [pd.read_parquet(file) for file in paths]
        return pd.concat(dfs, ignore_index=True)

    def get_all_parquet_files(prefix: str, gs: bool = True) -> list[str]:
        """Gets all parquet files from GCS path.

        Args:
            prefix: Path prefix within the bucket
        Returns:
            List of full GCS paths to parquet files
        """
        if gs:
            # List all blobs in the bucket with the given prefix
            blobs = bucket.list_blobs(prefix=prefix)

            # Filter for .parquet files and construct full paths
            parquet_files = [
                f"gs://{LICENSE_BUCKET}/{blob.name}"
                for blob in blobs
                if blob.name.endswith(".zstd.parquet")
            ]
        else:
            parquet_files = glob.glob(f"{prefix}/*.zstd.parquet")

        return sorted(parquet_files)

    def get_request_ids_from_parquet(prefix: str, gs: bool = True) -> set[str]:
        """Gets all request IDs from parquet files in GCS path."""
        parquet_files = get_all_parquet_files(prefix, gs)
        df = read_parquet(parquet_files)
        request_ids = set(df["request_id"])
        return request_ids

    # remove non-permissive requests from data
    non_permissive_requests = get_request_ids_from_parquet(
        LICENSE_NON_PERMISSIVE_PREFIX, gs=True
    )
    permissive_requests = get_request_ids_from_parquet(
        LICENSE_PERMISSIVE_PREFIX, gs=True
    )

    request_ids = set(result["completion"]["request_id"] for result in data)
    neither = request_ids - permissive_requests - non_permissive_requests
    if len(neither) > 0:
        print(
            f"Warning: {len(neither)} requests were not in the permissive list or the non-permissive list. Removing them to be safe."
        )

    data = [
        result
        for result in data
        if result["completion"]["request_id"] not in non_permissive_requests
        and result["completion"]["request_id"] in permissive_requests
    ]

    return data


def apply_edit_events_filter(data: List[dict], percentage: float | None) -> List[dict]:
    """Filter to keep only events that have edit events."""
    if percentage is None:
        return data
    total = len(data)
    with_edit_events = [
        result for result in data if result["completion"]["request"]["edit_events"]
    ]
    without_edit_events = [
        result for result in data if not result["completion"]["request"]["edit_events"]
    ]
    if percentage == 1.0:
        return with_edit_events
    if percentage == 0.0:
        return without_edit_events
    with_edit_events_count = len(with_edit_events)
    without_edit_events_count = len(without_edit_events)
    total_target = min(total, with_edit_events_count / percentage)

    with_edit_events_target_count = int(total_target * percentage)
    without_edit_events_target_count = int(total_target - with_edit_events_target_count)

    with_edit_events_target = random.sample(
        with_edit_events, with_edit_events_target_count
    )
    without_edit_events_target = random.sample(
        without_edit_events, without_edit_events_target_count
    )

    dropped_edit_events = []
    # total is always bounded by with edit events
    # case 1: there are more with edit events than the target, we need to drop some edit events for some datapoints
    # case 2: there are more without edit events than the target, we need to add some datapoints
    if with_edit_events_count > with_edit_events_target_count:
        kept_edit_events = []
        # sample data and drop edit events
        sample_indices = set(
            random.sample(
                range(with_edit_events_count),
                with_edit_events_count - with_edit_events_target_count,
            )
        )
        for i in range(with_edit_events_count):
            if i in sample_indices:
                with_edit_events[i]["completion"]["request"]["edit_events"] = []
                dropped_edit_events.append(with_edit_events[i])
            else:
                kept_edit_events.append(with_edit_events[i])
        with_edit_events_target = kept_edit_events
    del with_edit_events
    if without_edit_events_count < without_edit_events_target_count:
        without_edit_events.extend(
            dropped_edit_events[
                : without_edit_events_target_count - without_edit_events_count
            ]
        )
    elif without_edit_events_count > without_edit_events_target_count:
        without_edit_events = without_edit_events[:without_edit_events_target_count]
    return with_edit_events_target + without_edit_events_target


def apply_skip_token_filter(data: List[dict]) -> List[dict]:
    """Filter to keep only events that don't have a <|skip|> token in the response."""
    return [
        result
        for result in data
        if len(result["completion"]["response"]["skipped_suffix"]) == 0
        and len(result["completion"]["response"]["suffix_replacement_text"]) == 0
    ]


def apply_empty_completion_filter(data: List[dict]) -> List[dict]:
    """Filter to keep only events that have a non-empty completion response."""
    return [
        result
        for result in data
        if len(result["completion"]["response"]["token_ids"]) > 0
    ]


def construct_source_files(base_dir: Path, input_datasets: List[str]) -> List[Path]:
    jsonl_files = []
    for input_dataset in input_datasets:
        jsonl_files.append(base_dir / input_dataset / "data.jsonl.zst")
        assert jsonl_files[-1].exists()
    return jsonl_files


def load_jsonl_files(jsonl_files: List[Path]) -> List[dict]:
    """Load and combine all JSONL files for given input_datasets and vendor."""
    results = []
    for file in jsonl_files:
        print(f"Reading {file}")
        results.extend(read_jsonl_zst(file))

    return results


def save_metadata(
    metadata_path: Path,
    source_files: List[Path],
    stats: FilterStats,
    filters_applied: Dict[str, bool],
    command: str,
    data: List[dict],
    model: Optional[str] = None,
) -> None:
    """Save metadata about the processing and filtering."""
    metadata = {
        "initial_count": stats.initial_count,
        "final_count": stats.final_count,
        "source_files": [str(f) for f in source_files],
        "command": command,
        "filters": {
            "model": {
                "applied": filters_applied["model"],
                "model_name": model if model else "N/A",
                **(stats.model_stats or {}),
            },
            "edit_events_percentage": {
                "percentage": filters_applied["edit_events_percentage"],
                **(stats.edit_events_stats or {}),
            },
            "skip_token": {
                "applied": filters_applied["skip_token"],
                **(stats.skip_token_stats or {}),
            },
            "empty_completion": {
                "applied": filters_applied["empty_completion"],
                **(stats.empty_completion_stats or {}),
            },
            "license": {
                "applied": filters_applied["license"],
                **(stats.license_stats or {}),
            },
            "limit": {
                "applied": filters_applied["limit"],
                **(stats.limit_stats or {}),
            },
        },
    }

    # Model breakdown
    metadata["model_breakdown"] = Counter(
        result["completion"]["response"]["model"] for result in data
    )

    # Get top 10 most popular file extension breakdown and count
    metadata["file_breakdown"] = Counter(
        Path(result["completion"]["request"]["path"]).suffix for result in data
    )
    metadata["file_breakdown"] = dict(metadata["file_breakdown"].most_common(10))

    # Date breakdown, ordered by date
    metadata["date_breakdown"] = Counter(
        datetime.fromtimestamp(result["completion"]["request"]["timestamp"]).date()
        for result in data
    )
    metadata["date_breakdown"] = dict(
        sorted(metadata["date_breakdown"].items(), key=lambda item: item[0])
    )
    metadata["date_breakdown"] = {
        date.isoformat(): count for date, count in metadata["date_breakdown"].items()
    }

    with open(metadata_path, "w") as f:
        json.dump(metadata, f, indent=2)


def filter_stage(
    base_dir: Path,
    input_datasets: List[str],
    output_dir: Path,
    filter_config: FilterConfig,
) -> None:
    """Process and filter data, saving results and metadata."""
    output_dir.mkdir(parents=True, exist_ok=True)

    # Load all data
    source_files = construct_source_files(base_dir, input_datasets)
    data = load_jsonl_files(source_files)
    stats = FilterStats(initial_count=len(data))

    # Apply filters in specified order
    if filter_config.model:
        before_count = len(data)
        data = apply_model_filter(data, filter_config.model)
        stats.record_filter_stats("model", before_count, len(data))

    if filter_config.edit_events_percentage:
        before_count = len(data)
        data = apply_edit_events_filter(data, filter_config.edit_events_percentage)
        stats.record_filter_stats("edit_events", before_count, len(data))

    if filter_config.skip_token_filter:
        before_count = len(data)
        data = apply_skip_token_filter(data)
        stats.record_filter_stats("skip_token", before_count, len(data))

    if filter_config.empty_completion_filter:
        before_count = len(data)
        data = apply_empty_completion_filter(data)
        stats.record_filter_stats("empty_completion", before_count, len(data))

    if filter_config.license_filter:
        before_count = len(data)
        data = apply_license_filter(data)
        stats.record_filter_stats("license", before_count, len(data))

    if filter_config.limit_filter:
        before_count = len(data)
        if filter_config.limit_filter < len(data):
            data = random.sample(data, filter_config.limit_filter)
        stats.record_filter_stats("limit", before_count, len(data))

    stats.final_count = len(data)

    # Save metadata
    filters_applied = {
        "model": filter_config.model is not None,
        "edit_events_percentage": filter_config.edit_events_percentage,
        "skip_token": filter_config.skip_token_filter,
        "empty_completion": filter_config.empty_completion_filter,
        "license": filter_config.license_filter,
        "limit": filter_config.limit_filter is not None,
    }
    command = f"python {' '.join(sys.argv)}"  # Get the exact command that was run
    save_metadata(
        output_dir / "metadata.json",
        source_files,
        stats,
        filters_applied,
        command,
        data,
        filter_config.model,
    )

    # Save request IDs
    request_ids = set(result["completion"]["request_id"] for result in data)
    with open(output_dir / "request_ids.json", "w") as f:
        json.dump(list(request_ids), f)

    # Save the dataset
    output_file = output_dir / "data.jsonl.zst"
    print(f"Writing {len(data)} records to {output_file}")
    write_jsonl_zst(output_file, data)

    print(f"Saved all files to {output_dir}")
