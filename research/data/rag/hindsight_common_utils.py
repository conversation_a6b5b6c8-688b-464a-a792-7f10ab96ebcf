from base.datasets import completion
from base.prompt_format.chunk_origin import Chunk<PERSON>rig<PERSON>
from base.prompt_format.common import PromptChunk
from base.prompt_format.recency_info import ReplacementText, ViewedContentEvent
from base.prompt_format_completion.recency_utils import (
    deduplicate_viewed_content_against_replacements,
    filter_stale_viewed_content,
    get_latest_file_timestamps,
    limit_viewed_content_chunk_size,
)
from research.core.recency_info import convert_from_datasets_recency_info


def recent_chunk_to_prompt_chunk(
    recent_change: ReplacementText,
) -> PromptChunk:
    """Converts a recent chunk to a prompt chunk."""
    return PromptChunk(
        blob_name=recent_change.blob_name,
        path=recent_change.path,
        text=recent_change.replacement_text,
        char_start=recent_change.char_start,
        char_end=recent_change.char_end,
        origin=ChunkOrigin.RECENCY_RETRIEVER.value,
    )


def viewed_content_to_prompt_chunk(
    viewed_content: ViewedContentEvent,
) -> PromptChunk:
    """Converts a viewed content event to a prompt chunk."""
    return PromptChunk(
        blob_name=viewed_content.file_blob_name,
        path=viewed_content.path,
        text=viewed_content.visible_content,
        char_start=viewed_content.char_start,
        char_end=viewed_content.char_end,
        origin=ChunkOrigin.RECENCY_RETRIEVER_VIEWED_CONTENT.value,
    )


def process_recency_chunks(
    num_retrieved_chunks: int,
    request: completion.CompletionRequest,
) -> list[PromptChunk]:
    if request.recency_info is None:
        return []

    recency_info = convert_from_datasets_recency_info(request.recency_info)
    recent_changes = recency_info.recent_changes

    file_timestamps = get_latest_file_timestamps(recent_changes)

    filtered_viewed_contents = filter_stale_viewed_content(
        recency_info.viewed_contents, file_timestamps
    )

    truncated_viewed_contents = limit_viewed_content_chunk_size(
        filtered_viewed_contents, max_chunk_size=1024
    )

    deduped_viewed_contents = deduplicate_viewed_content_against_replacements(
        truncated_viewed_contents, recent_changes
    )

    # Unsure if we want to limit to [:10] here, but service code in `recency_retriever.py` is, so we will for now.
    recent_changes_chunks = [
        recent_chunk_to_prompt_chunk(recent_change=recent_change)
        for recent_change in recent_changes[:10]
    ]

    viewed_content_chunks = [
        viewed_content_to_prompt_chunk(viewed_content=viewed_content)
        for viewed_content in deduped_viewed_contents[:10]
    ]

    results = viewed_content_chunks + recent_changes_chunks
    results = results[:num_retrieved_chunks]
    return results
