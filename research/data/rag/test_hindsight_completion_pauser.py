#!/usr/bin/env python3
"""Unit tests for HindsightCompletionPauser.

This test file focuses on meaningful test cases that verify:
1. Happy path scenarios with different languages and pause patterns
2. Edge cases and error handling
3. Correct pause span boundaries and concatenation
"""

import datetime
import unittest
from typing import List

from base.datasets.completion import (
    CompletionDatum,
    CompletionRequest,
    CompletionResponse,
)
from base.datasets.hindsight_completion import HindsightCompletionDatum
from base.ranges.range_types import <PERSON><PERSON><PERSON><PERSON><PERSON>
from research.data.rag.hindsight_completion_pauser import (
    HindsightCompletionPauser,
    PauseSpanResult,
)


class TestHindsightCompletionPauser(unittest.TestCase):
    """Test cases for HindsightCompletionPauser."""

    def setUp(self):
        """Set up test fixtures."""
        self.pauser = HindsightCompletionPauser()

    def _create_test_datum(
        self, prefix: str, ground_truth: str, suffix: str, file_path: str = "test.py"
    ) -> HindsightCompletionDatum:
        """Create a test HindsightCompletionDatum."""
        return HindsightCompletionDatum(
            ground_truth=ground_truth,
            completion=CompletionDatum(
                request_id="test_request",
                user_id="test_user",
                request=CompletionRequest(
                    prefix=prefix,
                    suffix=suffix,
                    path=file_path,
                    blob_names=[],
                    output_len=0,
                    timestamp=datetime.datetime.now(),
                ),
                response=CompletionResponse(
                    text="dummy",
                    model="test-model",
                    skipped_suffix="",
                    suffix_replacement_text="",
                    unknown_blob_names=[],
                    retrieved_chunks=[],
                    timestamp=datetime.datetime.now(),
                    tokens=["dummy"],
                    token_log_probs=[-0.5],
                    prompt_tokens=["prompt"],
                    prompt_token_ids=[1],
                    token_ids=[2],
                ),
            ),
        )

    def _get_pause_substrings(
        self, ground_truth: str, pause_spans: List[CharRange]
    ) -> List[str]:
        """Extract substrings from ground truth based on pause spans."""
        return [ground_truth[span.start : span.stop] for span in pause_spans]

    def _concatenate_pause_spans(
        self, ground_truth: str, pause_spans: List[CharRange]
    ) -> str:
        """Concatenate all pause span substrings."""
        return "".join(self._get_pause_substrings(ground_truth, pause_spans))

    # ========== HAPPY PATH TEST CASES ==========

    def test_python_one_line_completion(self):
        """Test Python with a one-line complete ground truth."""
        prefix = "def calculate_sum(a, b):\n    "
        ground_truth = "return a + b"
        suffix = "\n\nprint('done')"

        datum = self._create_test_datum(prefix, ground_truth, suffix, "test.py")
        result = self.pauser.compute_pause_spans(datum)

        self.assertFalse(result.fail_to_use_parser)
        self.assertFalse(result.has_parsing_errors_in_middle)
        self.assertEqual(len(result.pause_spans), 1)

        # Assert the pause span's substring is exactly what we expect
        pause_substr = self._get_pause_substrings(ground_truth, result.pause_spans)
        self.assertEqual(pause_substr[0], "return a + b")

        # Assert concatenated pause spans form the ground truth
        self.assertEqual(
            self._concatenate_pause_spans(ground_truth, result.pause_spans),
            ground_truth,
        )

    def test_golang_multi_line_single_pause(self):
        """Test Go with several lines that fit in one pause."""
        prefix = "package main\n\nfunc fibonacci(n int) int {\n    "
        ground_truth = """if n <= 1 {
        return n
    }
    return fibonacci(n-1) + fibonacci(n-2)"""
        suffix = "\n}\n\nfunc main() {}"

        datum = self._create_test_datum(prefix, ground_truth, suffix, "test.go")
        result = self.pauser.compute_pause_spans(datum)

        self.assertFalse(result.fail_to_use_parser)
        self.assertFalse(result.has_parsing_errors_in_middle)
        self.assertEqual(len(result.pause_spans), 1)

        # Assert the pause span covers the entire ground truth
        pause_substr = self._get_pause_substrings(ground_truth, result.pause_spans)
        self.assertEqual(pause_substr[0], ground_truth)

        # Assert concatenated pause spans form the ground truth
        self.assertEqual(
            self._concatenate_pause_spans(ground_truth, result.pause_spans),
            ground_truth,
        )

    def test_java_multi_line_multiple_pauses(self):
        """Test Java with multi-line ground truth that needs several pauses."""
        prefix = "public class DataProcessor {\n    "
        ground_truth = """private List<String> data;
    private boolean processed;

    public DataProcessor(List<String> data) {
        this.data = data;
        this.processed = false;
    }

    public void process() {
        if (data == null || data.isEmpty()) {
            throw new IllegalArgumentException("No data provided");
        }
        for (String item : data) {
            System.out.println("Processing: " + item);
        }
        this.processed = true;
    }"""
        suffix = "\n}\n\n// End of class"

        datum = self._create_test_datum(
            prefix, ground_truth, suffix, "DataProcessor.java"
        )
        result = self.pauser.compute_pause_spans(datum)

        self.assertFalse(result.fail_to_use_parser)
        self.assertFalse(result.has_parsing_errors_in_middle)
        # Should have multiple pause spans for this longer code
        self.assertGreater(len(result.pause_spans), 1)

        # Verify spans are ordered and non-overlapping
        for i in range(len(result.pause_spans) - 1):
            self.assertLessEqual(
                result.pause_spans[i].stop, result.pause_spans[i + 1].start
            )

        # Assert concatenated pause spans form the ground truth
        self.assertEqual(
            self._concatenate_pause_spans(ground_truth, result.pause_spans),
            ground_truth,
        )

    def test_javascript_with_comments_and_functions(self):
        """Test JavaScript with comments and function definitions."""
        prefix = "// Main processing module\n"
        ground_truth = """/**
 * Process array of data items
 * @param {Array} data - Input data array
 * @returns {Array} Processed results
 */
function processData(data) {
    // Filter and transform data
    const result = data
        .filter(item => item != null)
        .map(item => item * 2);

    return result;
}

// Export the function
export { processData };"""
        suffix = "\n\n// Usage example\nconst data = [1, 2, 3];"

        datum = self._create_test_datum(prefix, ground_truth, suffix, "process.js")
        result = self.pauser.compute_pause_spans(datum)

        self.assertFalse(result.fail_to_use_parser)
        self.assertFalse(result.has_parsing_errors_in_middle)
        self.assertGreater(len(result.pause_spans), 0)

        # Assert concatenated pause spans form the ground truth
        self.assertEqual(
            self._concatenate_pause_spans(ground_truth, result.pause_spans),
            ground_truth,
        )

    # ========== EDGE CASES AND FAILURES ==========

    def test_unsupported_file_dockerfile(self):
        """Test unsupported file type (Dockerfile) - should pause at complete lines."""
        prefix = "FROM python:3.9-slim\n\n"
        ground_truth = """WORKDIR /app
COPY requirements.txt .
RUN pip install -r requirements.txt
COPY . .
CMD ["python", "app.py"]"""
        suffix = "\n\nEXPOSE 8080"

        datum = self._create_test_datum(prefix, ground_truth, suffix, "Dockerfile")
        result = self.pauser.compute_pause_spans(datum)

        self.assertTrue(result.fail_to_use_parser)
        self.assertGreater(len(result.pause_spans), 0)

        # For unsupported languages, should pause at line boundaries
        pause_substrs = self._get_pause_substrings(ground_truth, result.pause_spans)
        for pause_substr in pause_substrs:
            # Each pause should end at a newline or end of ground truth
            if pause_substr != ground_truth and not pause_substr.endswith("\n"):
                # Check if it's the last pause that ends at the end of ground truth
                last_pause = result.pause_spans[-1]
                self.assertEqual(last_pause.stop, len(ground_truth))

    def test_syntax_error_missing_closing_paren(self):
        """Test syntax error - missing closing parenthesis."""
        prefix = "def broken_function(\n    x, y\n    # missing closing parenthesis"
        ground_truth = "\n    return x + y"
        suffix = "\n\nprint('done')"

        datum = self._create_test_datum(prefix, ground_truth, suffix, "test.py")
        result = self.pauser.compute_pause_spans(datum)

        self.assertFalse(result.fail_to_use_parser)
        # Should handle parsing errors gracefully
        self.assertIsNotNone(result.debug_info)
        if result.debug_info:
            self.assertTrue(result.debug_info.required_prefix_skipping)
            self.assertGreater(result.debug_info.num_prefix_lines_skipped, 0)

        # Should still generate pause spans
        self.assertGreater(len(result.pause_spans), 0)

    def test_syntax_error_missing_colon(self):
        """Test syntax error - missing colon in if statement."""
        prefix = "def process():\n    "
        ground_truth = "if condition\n        return True"
        suffix = "\n    return False"

        datum = self._create_test_datum(prefix, ground_truth, suffix, "test.py")
        result = self.pauser.compute_pause_spans(datum)

        self.assertFalse(result.fail_to_use_parser)
        # Should detect parsing errors in the middle range
        self.assertTrue(result.has_parsing_errors_in_middle)
        # Should still generate pause spans
        self.assertGreater(len(result.pause_spans), 0)

    def test_syntax_error_incomplete_string(self):
        """Test syntax error - unclosed string literal."""
        prefix = "def get_message():\n    "
        ground_truth = 'message = "Hello, world\n    return message'
        suffix = "\n\nprint(get_message())"

        datum = self._create_test_datum(prefix, ground_truth, suffix, "test.py")
        result = self.pauser.compute_pause_spans(datum)

        self.assertFalse(result.fail_to_use_parser)
        # Should handle parsing errors
        self.assertTrue(result.has_parsing_errors_in_middle)
        self.assertGreater(len(result.pause_spans), 0)

    def test_syntax_error_unmatched_bracket(self):
        """Test syntax error - unmatched bracket in list comprehension."""
        prefix = "def filter_data(items):\n    "
        ground_truth = "result = [x for x in items if x > 0\n    return result"
        suffix = "\n\ndata = filter_data([1, -2, 3])"

        datum = self._create_test_datum(prefix, ground_truth, suffix, "test.py")
        result = self.pauser.compute_pause_spans(datum)

        self.assertFalse(result.fail_to_use_parser)
        self.assertTrue(result.has_parsing_errors_in_middle)
        self.assertGreater(len(result.pause_spans), 0)

    def test_consecutive_newlines_for_unsupported_file(self):
        """Test consecutive newlines in unsupported file type."""
        prefix = "def calculate_sum(a, b):\n\n\n    "
        ground_truth = "a=a+1\n\n\n  b=b+1\n\n\n    ret a + b\n\n\n"
        suffix = "print('done')"

        datum = self._create_test_datum(prefix, ground_truth, suffix, "FILE")
        self.pauser = HindsightCompletionPauser(max_first_span_size=8)
        result = self.pauser.compute_pause_spans(datum)
        for span in result.pause_spans:
            print(f"={ground_truth[span.start : span.stop]}=")
        self.assertEqual(len(result.pause_spans), 3)
        first_pause_text = ground_truth[: result.pause_spans[0].stop]
        assert first_pause_text == "a=a+1"
        second_pause_text = ground_truth[
            result.pause_spans[1].start : result.pause_spans[1].stop
        ]
        assert second_pause_text == "\n\n\n  b=b+1"
        third_pause_text = ground_truth[
            result.pause_spans[2].start : result.pause_spans[2].stop
        ]
        assert third_pause_text == "\n\n\n    ret a + b\n\n\n"
        self.assertTrue(result.fail_to_use_parser)
        self.assertEqual(
            self._concatenate_pause_spans(ground_truth, result.pause_spans),
            ground_truth,
        )


if __name__ == "__main__":
    unittest.main()
