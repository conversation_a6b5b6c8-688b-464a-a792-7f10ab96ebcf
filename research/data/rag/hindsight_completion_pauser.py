"""Hindsight completion pause span detection module.

This module formalizes the pause span computation logic for hindsight training data,
identifying syntactically meaningful pause points in code completions.
"""

import logging
from dataclasses import dataclass
from pathlib import Path
from typing import List, Optional

from base.datasets.hindsight_completion import HindsightCompletionDatum
from base.ranges.range_types import By<PERSON><PERSON><PERSON><PERSON>, Char<PERSON><PERSON><PERSON>
from base.static_analysis.common import guess_lang_from_fp, LanguageID
from base.static_analysis.parsing import TsParsedFile
from research.fim.fim_sampling import CSTFimSampler
import tree_sitter as ts


@dataclass
class PauseDebugInfo:
    """Debug information about pause span computation.

    Attributes:
        required_prefix_skipping: Whether prefix lines were skipped to avoid parsing errors
        num_prefix_lines_skipped: Number of lines removed from the prefix
        num_prefix_chars_skipped: Number of characters removed from the prefix
    """

    required_prefix_skipping: bool
    num_prefix_lines_skipped: int
    num_prefix_chars_skipped: int


@dataclass
class PauseSpanResult:
    """Result of pause span computation for a hindsight completion.

    Attributes:
        pause_spans: List of character ranges representing syntactic pause points
        has_parsing_errors_in_middle: True if tree-sitter found parsing errors in the middle region
        unspported_language: True if the language is not supported for parsing
        debug_info: Debug information about the pause span computation
    """

    pause_spans: List[CharRange]
    has_parsing_errors_in_middle: bool
    fail_to_use_parser: bool = False
    debug_info: Optional[PauseDebugInfo] = None

    @property
    def num_pauses(self) -> int:
        """Number of pause spans computed."""
        return len(self.pause_spans)

    @property
    def first_pause_span(self) -> Optional[CharRange]:
        """Get the first pause span if available."""
        return self.pause_spans[0] if self.pause_spans else None

    @property
    def last_pause_span(self) -> Optional[CharRange]:
        """Get the last pause span if available."""
        return self.pause_spans[-1] if self.pause_spans else None


@dataclass
class HindsightCompletionPauser:
    """Computes pause spans for hindsight completion data.

    This class identifies natural pause points in code completions based on
    tree-sitter parsing of the full file content, enabling models to learn when to stop generation
    at syntactically meaningful boundaries.
    """

    max_first_span_size: int = 180
    min_size_to_pause: int = 4
    additional_suffix_lines: int = 4

    def __post_init__(self):
        self._fim_sampler = CSTFimSampler(
            max_first_span_size=self.max_first_span_size,
            min_size_to_pause=self.min_size_to_pause,
        )

    def compute_pause_spans(
        self,
        datum: HindsightCompletionDatum,
    ) -> PauseSpanResult:
        """Identify syntactically meaningful pause points in code completion data.

        This function analyzes hindsight completion data to find natural stopping points
        where a code completion model should pause generation. It uses tree-sitter parsing
        to identify syntactic boundaries (e.g., end of statements, function definitions)
        within the ground truth completion text.

        The function handles files with parsing errors using an iterative recovery strategy:
        1. First attempts to parse with the full prefix + ground truth + suffix
        2. If parsing errors occur in the middle range (ground truth + early suffix),
           progressively removes lines from the beginning of the prefix
        3. Continues until either:
           - A valid parse is achieved (returns those pause spans)
           - All prefix lines are exhausted (returns the configuration with the longest
             first pause span found during iteration)

        This approach ensures robust pause span detection even in files with syntax errors
        or incomplete code contexts.

        Args:
            datum: The hindsight completion datum containing:
                   - completion.request.prefix: Code before the completion point
                   - ground_truth: The actual code that was written
                   - completion.request.suffix: Code after the completion point
                   - completion.request.path: File path for language detection

        Returns:
            PauseSpanResult containing:
                - pause_spans: List of character ranges marking pause boundaries, indexed
                  relative to the start of the ground truth
                - has_parsing_errors_in_middle: Whether parsing errors were detected
                - unsupported_language: If the language of the completion file is supported by
                tree sitter parser
        """

        lang = guess_lang_from_fp(datum.completion.request.path)
        if lang is None:
            return self._compute_line_based_pause_spans(datum)
        prefix: str = datum.completion.request.prefix
        prefix_lines: List[str] = prefix.splitlines(keepends=True)
        has_parsing_error_in_middle_range = False
        # keep track of the pause spans with the longest first pause span if
        # parsing errors are encountered
        spans_with_longest_first_pause: List[CharRange] = []
        debug_info_for_longest_first_pause: Optional[PauseDebugInfo] = None

        for num_prefix_line_to_remove in range(max(1, len(prefix_lines))):
            prefix = "".join(prefix_lines[num_prefix_line_to_remove:])
            pfile = self._parse_full_content(
                datum.completion.request.path,
                prefix,
                datum.ground_truth,
                datum.completion.request.suffix,
                lang,
            )
            if pfile is None:
                continue
            # compute the middle range with additional suffix lines
            middle_range = self._compute_middle_range(
                prefix, datum.ground_truth, datum.completion.request.suffix
            )
            has_parsing_error_in_middle_range = self._has_parse_error_in_middle_range(
                pfile, middle_range
            )
            max_stop = len(prefix) + len(datum.ground_truth)
            pause_spans = self._compute_raw_pause_spans(pfile, middle_range)
            # shift the pause spans to the right by the number of lines removed
            char_skipped = len("".join(prefix_lines[:num_prefix_line_to_remove]))
            offset = -len(prefix)
            pause_spans = [
                CharRange(
                    span.start + offset,
                    span.stop + offset if span.stop <= max_stop else max_stop + offset,
                )
                for span in pause_spans
                if span.start < max_stop
            ]
            if not has_parsing_error_in_middle_range:
                return PauseSpanResult(
                    pause_spans=pause_spans,
                    has_parsing_errors_in_middle=has_parsing_error_in_middle_range,
                    debug_info=PauseDebugInfo(
                        required_prefix_skipping=num_prefix_line_to_remove > 0,
                        num_prefix_lines_skipped=num_prefix_line_to_remove,
                        num_prefix_chars_skipped=char_skipped,
                    ),
                )
            else:
                # keep track of the the pause spans with the longest first pause
                if (
                    not spans_with_longest_first_pause
                    or pause_spans[0].stop > spans_with_longest_first_pause[0].stop
                ):
                    spans_with_longest_first_pause = pause_spans
                    debug_info_for_longest_first_pause = PauseDebugInfo(
                        required_prefix_skipping=num_prefix_line_to_remove > 0,
                        num_prefix_lines_skipped=num_prefix_line_to_remove,
                        num_prefix_chars_skipped=char_skipped,
                    )
        # if we reach here, we have exhausted all prefix lines
        if not spans_with_longest_first_pause:
            # fall back to naive pause span computation
            return self._compute_line_based_pause_spans(datum)
        return PauseSpanResult(
            pause_spans=spans_with_longest_first_pause,
            has_parsing_errors_in_middle=has_parsing_error_in_middle_range,
            debug_info=debug_info_for_longest_first_pause,
        )

    def _compute_raw_pause_spans(
        self, pfile: TsParsedFile, middle_range: CharRange
    ) -> List[CharRange]:
        """Compute raw pause spans using tree-sitter parsing."""
        # Compute pause spans using FIM sampler
        return self._fim_sampler._compute_pause_spans(
            pfile=pfile,
            middle_range=middle_range,
            min_span_size=self.min_size_to_pause,
            max_span_size=self.max_first_span_size,
            always_recurse=True,
        )

    def _compute_line_based_pause_spans(
        self, datum: HindsightCompletionDatum
    ) -> PauseSpanResult:
        """Compute pause spans for unknown languages.
        If we can't guess the language, we can't parse the file so we can't
        compute pause spans. In this case, we return complete number of lines
        within the non-whitespace character budget.

        max_first_span_size is respected with best effort.
        if one line is longer than max_first_span_size, we will pause after that line.
        """
        pause_spans = []
        current_span_lines = []
        current_span_size = 0
        # Use keepends=True to preserve original newline structure
        lines_with_ends = datum.ground_truth.splitlines(keepends=True)
        for line in lines_with_ends:
            # Count non-whitespace characters in the line content
            line_size = len(line) - line.count(" ")
            if current_span_size + line_size > self.max_first_span_size:
                if current_span_lines:
                    span_start = pause_spans[-1].stop if pause_spans else 0
                    span_len = len("".join(current_span_lines))
                    pause_spans.append(
                        CharRange(
                            span_start,
                            span_start + span_len,
                        )
                    )
                    current_span_lines = []
                    current_span_size = 0
            current_span_lines.append(line)
            current_span_size += line_size
        if current_span_lines:
            span_start = pause_spans[-1].stop if pause_spans else 0
            span_len = len("".join(current_span_lines))
            pause_spans.append(
                CharRange(
                    span_start,
                    span_start + span_len,
                )
            )

        # post processing: 1. move newlines at the end of a span to the start of next span
        empty_span_to_remove = []
        for i in range(len(pause_spans) - 1):
            ground_truth = datum.ground_truth
            while (
                pause_spans[i].stop > pause_spans[i].start
                and ground_truth[pause_spans[i].stop - 1] == "\n"
            ):
                pause_spans[i] = CharRange(
                    pause_spans[i].start, pause_spans[i].stop - 1
                )
                pause_spans[i + 1] = CharRange(
                    pause_spans[i + 1].start - 1, pause_spans[i + 1].stop
                )
            if pause_spans[i].stop == pause_spans[i].start:
                empty_span_to_remove.append(i)
        for i in reversed(empty_span_to_remove):
            pause_spans.pop(i)
        # post processing: 2. if the last span contains only newlines, merge it with the previous span
        if (
            len(pause_spans) >= 2
            and datum.ground_truth[pause_spans[-1].start : pause_spans[-1].stop].count(
                "\n"
            )
            == pause_spans[-1].stop - pause_spans[-1].start
        ):
            pause_spans[-2] = CharRange(pause_spans[-2].start, pause_spans[-1].stop)
            pause_spans.pop()
        return PauseSpanResult(
            pause_spans=pause_spans,
            has_parsing_errors_in_middle=False,
            fail_to_use_parser=True,
            debug_info=PauseDebugInfo(
                required_prefix_skipping=False,
                num_prefix_lines_skipped=0,
                num_prefix_chars_skipped=0,
            ),
        )

    def _parse_full_content(
        self, path: str, prefix: str, ground_truth: str, suffix: str, lang: LanguageID
    ) -> Optional[TsParsedFile]:
        full_content = prefix + ground_truth + suffix
        try:
            pfile = TsParsedFile.parse(path=Path(path), lang=lang, code=full_content)
            return pfile
        except Exception:
            logging.exception(f"Failed to parse file: {path}")
            return None

    def _compute_middle_range(
        self, prefix: str, ground_truth: str, suffix: str
    ) -> CharRange:
        """Compute the middle range including additional suffix lines.

        Note: we include the first few lines of the suffix because
        there's chance the next few lines containing additional block ending
        structure like ), }, indentation that can allow the FIM sampling pauser
        to select a node corresponds to that larger scope.
        We post-process and truncate pause that happens after the end of ground
        truth.
        """
        suffix_lines = suffix.splitlines(keepends=True)
        early_suffix = "".join(suffix_lines[: self.additional_suffix_lines])

        middle_start = len(prefix)
        middle_end = middle_start + len(ground_truth) + len(early_suffix)

        return CharRange(middle_start, middle_end)

    def _has_parse_error_in_middle_range(
        self, pfile: TsParsedFile, middle_range: CharRange
    ) -> bool:
        """Check if the middle region contains tree-sitter parsing errors."""
        byte_range = pfile.bmap.crange_to_brange(middle_range)
        return self._contains_errors_in_range(pfile.ts_tree.root_node, byte_range)

    def _contains_errors_in_range(self, node: ts.Node, byte_range: ByteRange) -> bool:
        """Recursively check if a tree node contains errors in the given range."""
        if node.type == "ERROR" and self._node_overlaps_range(node, byte_range):
            return True

        for child in node.children:
            if self._contains_errors_in_range(child, byte_range):
                return True

        return False

    def _node_overlaps_range(self, node: ts.Node, byte_range: ByteRange) -> bool:
        """Check if a tree-sitter node overlaps with a character range."""
        return node.start_byte < byte_range.stop and node.end_byte > byte_range.start


def create_pauser_from_config(config: dict) -> HindsightCompletionPauser:
    """Factory function to create a pauser from configuration."""
    return HindsightCompletionPauser(
        max_first_span_size=config.get("max_first_span_size", 180),
        min_size_to_pause=config.get("min_size_to_pause", 4),
        additional_suffix_lines=config.get("additional_suffix_lines", 4),
    )
