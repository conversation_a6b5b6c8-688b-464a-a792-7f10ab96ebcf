"""Tests for slack_utils.py focusing on rate limit retry logic."""

import time
from unittest.mock import Mock, patch, call, MagicMock

import pytest
from slack_sdk.errors import SlackApiError

from slack_utils import (
    RateLimiter,
    SlackClient,
    SlackMessage,
)


class TestRateLimiter:
    """Test the RateLimiter class."""

    @patch("slack_utils.time")
    def test_rate_limiter_timing(self, mock_time):
        """Test that rate limiter enforces correct delays."""
        # Mock time progression - time.time() is called once per acquire()
        mock_time.time.side_effect = [0, 0.2, 0.7]
        mock_time.sleep = Mock()

        rate_limiter = RateLimiter(max_calls_per_second=2)  # 0.5s between calls

        # First call should have delay of 0.5s (interval - (0 - 0))
        delay1 = rate_limiter.acquire()
        assert delay1 == 0.5  # 0.5 - (0 - 0) = 0.5
        mock_time.sleep.assert_called_with(0.5)

        # Second call at t=0.2 should have 0.3s delay
        delay2 = rate_limiter.acquire()
        assert abs(delay2 - 0.3) < 0.001  # 0.5 - (0.2 - 0) = 0.3

        # Third call at t=0.7 should have no delay (0.5s already passed)
        delay3 = rate_limiter.acquire()
        assert delay3 < 0.001  # 0.5 - (0.7 - 0.2) = 0 (with floating point tolerance)

        # Verify sleep calls
        assert mock_time.sleep.call_count == 3
        sleep_calls = [call[0][0] for call in mock_time.sleep.call_args_list]
        assert abs(sleep_calls[0] - 0.5) < 0.001
        assert abs(sleep_calls[1] - 0.3) < 0.001
        assert abs(sleep_calls[2] - 0) < 0.001


class TestSlackClientRetry:
    """Test the SlackClient retry logic for rate limits."""

    @patch("time.sleep")
    @patch("slack_utils.WebClient")
    def test_rate_limit_retry_with_exponential_backoff(
        self, mock_webclient_class, mock_sleep
    ):
        """Test retry with exponential backoff on rate limit errors."""
        # Setup mock client
        mock_client_instance = Mock()
        mock_webclient_class.return_value = mock_client_instance

        # Mock rate limit errors followed by success
        rate_limit_error1 = SlackApiError(
            "rate limited", response={"error": "ratelimited"}
        )
        rate_limit_error1.response = Mock()
        rate_limit_error1.response.status_code = 429
        rate_limit_error1.response.get.return_value = "ratelimited"

        rate_limit_error2 = SlackApiError(
            "rate limited", response={"error": "ratelimited"}
        )
        rate_limit_error2.response = Mock()
        rate_limit_error2.response.status_code = 429
        rate_limit_error2.response.get.return_value = "ratelimited"

        rate_limit_error3 = SlackApiError(
            "rate limited", response={"error": "ratelimited"}
        )
        rate_limit_error3.response = Mock()
        rate_limit_error3.response.status_code = 429
        rate_limit_error3.response.get.return_value = "ratelimited"

        mock_client_instance.conversations_replies.side_effect = [
            rate_limit_error1,
            rate_limit_error2,
            rate_limit_error3,
            {
                "ok": True,
                "messages": [
                    {"text": "Parent", "user": "U123", "ts": "1234567890.123456"},
                    {
                        "text": "Reply 1",
                        "user": "U456",
                        "ts": "1234567890.123457",
                        "thread_ts": "1234567890.123456",
                    },
                    {
                        "text": "Reply 2",
                        "user": "U789",
                        "ts": "1234567890.123458",
                        "thread_ts": "1234567890.123456",
                    },
                ],
            },
        ]

        # Create client and message
        client = SlackClient("fake_token", max_calls_per_second=1)
        message = SlackMessage(
            text="Parent message",
            user="U123",
            ts="1234567890.123456",
            permalink="https://slack.com/...",
            thread_ts="1234567890.123456",
        )

        # Call the method
        client._add_thread_replies("C123456", message)

        # Verify retries happened with exponential backoff
        # Filter out rate limiter calls (which are close to 1.0 but not exactly 1.0)
        # We're looking for exact backoff values: 1.0, 2.0, 4.0
        backoff_values = [
            c[0][0]
            for c in mock_sleep.call_args_list
            if c[0][0] in [1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 60.0]
        ]
        assert backoff_values == [1.0, 2.0, 4.0]

        # Verify the API was called 4 times (3 failures + 1 success)
        assert mock_client_instance.conversations_replies.call_count == 4

        # Verify replies were added to the message
        assert len(message.replies) == 2
        assert message.replies[0].text == "Reply 1"
        assert message.replies[1].text == "Reply 2"

    @patch("slack_utils.WebClient")
    @patch("time.sleep")
    def test_rate_limit_retry_with_retry_after_header(
        self, mock_sleep, mock_webclient_class
    ):
        """Test retry using Retry-After header when present."""
        # Setup mock client
        mock_client_instance = Mock()
        mock_webclient_class.return_value = mock_client_instance

        # Create error with Retry-After header
        error_response = {"error": "ratelimited"}
        error_with_header = SlackApiError("rate limited", response=error_response)
        error_with_header.response = Mock()
        error_with_header.response.status_code = 429
        error_with_header.response.get.return_value = "ratelimited"
        error_with_header.response.headers = {"Retry-After": "5"}

        # Mock rate limit error with header followed by success
        mock_client_instance.conversations_replies.side_effect = [
            error_with_header,
            {
                "ok": True,
                "messages": [
                    {"text": "Parent", "user": "U123", "ts": "1234567890.123456"},
                ],
            },
        ]

        # Create client and message
        client = SlackClient("fake_token", max_calls_per_second=1)
        message = SlackMessage(
            text="Parent message",
            user="U123",
            ts="1234567890.123456",
            permalink="https://slack.com/...",
        )

        # Call the method
        client._add_thread_replies("C123456", message)

        # Verify it used the Retry-After value (5 seconds)
        # Look for the exact backoff value
        backoff_values = [c[0][0] for c in mock_sleep.call_args_list if c[0][0] == 5]
        assert len(backoff_values) == 1

        # Verify the API was called 2 times
        assert mock_client_instance.conversations_replies.call_count == 2

    @patch("slack_utils.WebClient")
    @patch("time.sleep")
    def test_rate_limit_max_retries_exceeded(self, mock_sleep, mock_webclient_class):
        """Test that it gives up after MAX_RETRIES attempts."""
        # Setup mock client
        mock_client_instance = Mock()
        mock_webclient_class.return_value = mock_client_instance

        # Mock continuous rate limit errors (6 times)
        rate_limit_errors = []
        for _ in range(6):
            error = SlackApiError("rate limited", response={"error": "ratelimited"})
            error.response = Mock()
            error.response.status_code = 429
            error.response.get.return_value = "ratelimited"
            rate_limit_errors.append(error)

        mock_client_instance.conversations_replies.side_effect = rate_limit_errors

        # Create client and message
        client = SlackClient("fake_token", max_calls_per_second=1)
        message = SlackMessage(
            text="Parent message",
            user="U123",
            ts="1234567890.123456",
            permalink="https://slack.com/...",
        )

        # Call the method
        client._add_thread_replies("C123456", message)

        # Verify it tried MAX_RETRIES + 1 times (6 total)
        assert mock_client_instance.conversations_replies.call_count == 6

        # Verify exponential backoff was used for all retries
        # Look for exact backoff values
        backoff_values = [
            c[0][0]
            for c in mock_sleep.call_args_list
            if c[0][0] in [1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 60.0]
        ]
        assert backoff_values == [1.0, 2.0, 4.0, 8.0, 16.0]

        # Verify no replies were added (since all attempts failed)
        assert len(message.replies) == 0

    @patch("slack_utils.WebClient")
    @patch("time.sleep")
    def test_non_rate_limit_error_no_retry(self, mock_sleep, mock_webclient_class):
        """Test that non-rate-limit errors don't trigger retries."""
        # Setup mock client
        mock_client_instance = Mock()
        mock_webclient_class.return_value = mock_client_instance

        # Mock a different error (not rate limit)
        non_rate_limit_error = SlackApiError(
            "channel not found", response={"error": "channel_not_found"}
        )
        non_rate_limit_error.response = Mock()
        non_rate_limit_error.response.status_code = 404  # Not 429
        non_rate_limit_error.response.get.return_value = "channel_not_found"

        mock_client_instance.conversations_replies.side_effect = [non_rate_limit_error]

        # Create client and message
        client = SlackClient("fake_token", max_calls_per_second=1)
        message = SlackMessage(
            text="Parent message",
            user="U123",
            ts="1234567890.123456",
            permalink="https://slack.com/...",
        )

        # Call the method
        client._add_thread_replies("C123456", message)

        # Verify it only tried once (no retries)
        assert mock_client_instance.conversations_replies.call_count == 1

        # Verify no retry backoff sleep was called (only rate limiter calls)
        backoff_values = [
            c[0][0]
            for c in mock_sleep.call_args_list
            if c[0][0] in [1.0, 2.0, 4.0, 8.0, 16.0, 32.0, 60.0]
        ]
        assert len(backoff_values) == 0

        # Verify no replies were added
        assert len(message.replies) == 0

    @patch("slack_utils.WebClient")
    @patch("time.sleep")
    def test_rate_limit_retry_after_header_capped_at_max_backoff(
        self, mock_sleep, mock_webclient_class
    ):
        """Test that Retry-After header is capped at MAX_BACKOFF."""
        # Setup mock client
        mock_client_instance = Mock()
        mock_webclient_class.return_value = mock_client_instance

        # Create error with large Retry-After header (120 seconds)
        error_response = {"error": "ratelimited"}
        error_with_header = SlackApiError("rate limited", response=error_response)
        error_with_header.response = Mock()
        error_with_header.response.status_code = 429
        error_with_header.response.get.return_value = "ratelimited"
        error_with_header.response.headers = {"Retry-After": "120"}

        # Mock rate limit error with header followed by success
        mock_client_instance.conversations_replies.side_effect = [
            error_with_header,
            {
                "ok": True,
                "messages": [
                    {"text": "Parent", "user": "U123", "ts": "1234567890.123456"},
                ],
            },
        ]

        # Create client and message
        client = SlackClient("fake_token", max_calls_per_second=1)
        message = SlackMessage(
            text="Parent message",
            user="U123",
            ts="1234567890.123456",
            permalink="https://slack.com/...",
        )

        # Call the method
        client._add_thread_replies("C123456", message)

        # Verify it capped at MAX_BACKOFF (60 seconds)
        backoff_values = [c[0][0] for c in mock_sleep.call_args_list if c[0][0] == 60.0]
        assert len(backoff_values) == 1
