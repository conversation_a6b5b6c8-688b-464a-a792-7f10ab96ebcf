"""Tests for slack_feedback_summarizer.py focusing on main flow and resilience."""

import sys
import datetime
from unittest.mock import Mock, patch, MagicMock, call

import pytest

# Import the modules we're testing
from slack_utils import SlackMessage
import slack_feedback_summarizer
from slack_feedback_summarizer import ThreadSummary, ThreadSummaryGroup


class TestSlackFeedbackSummarizer:
    """Test the main slack feedback summarizer functionality."""

    @patch("slack_feedback_summarizer.gcs.Client")
    @patch("slack_feedback_summarizer.SlackClient")
    @patch("slack_feedback_summarizer.SlackFeedbackSummarizer")
    def test_run_summarizer_happy_path(
        self, mock_summarizer_class, mock_slack_client_class, mock_gcs_client_class
    ):
        """Test successful execution of the feedback summarizer."""
        # Setup mock Slack client
        mock_slack_client = Mock()
        mock_slack_client_class.return_value = mock_slack_client
        # Mock get_channel_id to return different IDs for different channels
        mock_slack_client.get_channel_id.side_effect = ["C123456", "C789012"]

        # Mock channel messages
        test_messages = [
            SlackMessage(
                text="Great feature!",
                user="U123",
                ts="1234567890.123456",
                permalink="https://slack.com/archives/C123/p1234567890123456",
                thread_ts="1234567890.123456",
                replies=[
                    SlackMessage(
                        text="I agree!",
                        user="U456",
                        ts="1234567890.123457",
                        permalink="",
                        thread_ts="1234567890.123456",
                    )
                ],
            ),
            SlackMessage(
                text="Bug report: login issue",
                user="U789",
                ts="1234567890.123458",
                permalink="https://slack.com/archives/C123/p1234567890123458",
                thread_ts="1234567890.123458",
                replies=[],
            ),
        ]
        mock_slack_client.get_messages_from_channel.return_value = test_messages
        mock_slack_client.get_messages.return_value = test_messages

        # Mock SlackFeedbackSummarizer
        mock_summarizer = Mock()
        mock_summarizer_class.return_value = mock_summarizer

        # Mock summarize_thread to return summaries (called once per message)
        mock_summarizer.summarize_thread.side_effect = [
            ThreadSummary(
                original_message=test_messages[0],
                summary="User praises the feature",
                status="resolved",
                type="praise",
            ),
            ThreadSummary(
                original_message=test_messages[1],
                summary="User reports login issue",
                status="unresolved",
                type="bug report",
            ),
        ]

        # Mock group_threads_by_theme
        mock_summarizer.group_threads_by_theme.return_value = [
            ThreadSummaryGroup(
                name="Positive Feedback",
                category="praise",
                threads=[
                    ThreadSummary(
                        original_message=test_messages[0],
                        summary="User praises the feature",
                        status="resolved",
                        type="praise",
                    )
                ],
            ),
            ThreadSummaryGroup(
                name="Bug Reports",
                category="bugs",
                threads=[
                    ThreadSummary(
                        original_message=test_messages[1],
                        summary="User reports login issue",
                        status="unresolved",
                        type="bug report",
                    )
                ],
            ),
        ]

        # Mock GCS client
        mock_gcs = Mock()
        mock_gcs_client_class.return_value = mock_gcs
        mock_bucket = Mock()
        mock_gcs.bucket.return_value = mock_bucket
        mock_blob = Mock()
        mock_bucket.blob.return_value = mock_blob
        mock_blob.public_url = "https://storage.googleapis.com/feedback/report-123.html"

        # Mock send_message to track calls
        mock_slack_client.send_message.return_value = None

        # Run with test parameters
        slack_feedback_summarizer.run_summarizer(
            channel="test-feedback",
            slack_token="fake-token",
            anthropic_api_key="fake-key",
            enable_logging=False,
            log_dir="/tmp",
            start_date=datetime.datetime.now() - datetime.timedelta(days=7),
            end_date=datetime.datetime.now(),
            output="gs://test-bucket/feedback/report.html",
            gcp_project="test-project",
            notify_channel="test-team",
        )

        # Verify Slack client was initialized
        mock_slack_client_class.assert_called_once_with(token="fake-token")

        # Verify channel IDs were looked up (main channel and notify channel)
        assert mock_slack_client.get_channel_id.call_count == 2
        mock_slack_client.get_channel_id.assert_any_call("test-feedback")
        mock_slack_client.get_channel_id.assert_any_call("test-team")

        # Verify messages were fetched from the feedback channel
        mock_slack_client.get_messages.assert_called_once()

        # Verify summarizer was created and used
        mock_summarizer_class.assert_called_once()
        # summarize_thread should be called once per message (2 times)
        assert mock_summarizer.summarize_thread.call_count == 2
        mock_summarizer.group_threads_by_theme.assert_called_once()

        # Verify GCS upload happened
        mock_blob.upload_from_string.assert_called_once()

        # Verify notification was sent
        mock_slack_client.send_message.assert_called_once()
        send_call = mock_slack_client.send_message.call_args
        # Check that the notification was sent to the correct channel ID (C789012 = test-team)
        assert send_call[1]["channel_id"] == "C789012"

    @patch("slack_feedback_summarizer.gcs.Client")
    @patch("slack_feedback_summarizer.SlackClient")
    @patch("slack_feedback_summarizer.SlackFeedbackSummarizer")
    @patch("slack_feedback_summarizer.parse_args")
    def test_main_continues_after_channel_failure(
        self,
        mock_parse_args,
        mock_summarizer_class,
        mock_slack_client_class,
        mock_gcs_client_class,
    ):
        """Test that processing continues even if one channel fails."""
        # Mock command line args
        mock_args = Mock()
        mock_args.all_channels_report = True
        mock_args.output = "gs://test-bucket/feedback"
        mock_args.start_date = datetime.datetime.now() - datetime.timedelta(days=7)
        mock_args.end_date = datetime.datetime.now()
        mock_args.enable_logging = False
        mock_args.log_dir = "/tmp"
        mock_args.slack_token = "fake-token"
        mock_args.anthropic_api_key = "fake-key"
        mock_args.gcp_project = "test-project"
        mock_parse_args.return_value = mock_args

        # Setup mock Slack client
        mock_slack_client = Mock()
        mock_slack_client_class.return_value = mock_slack_client

        # Mock channel ID lookups (need enough for both main channels and notification channels)
        mock_slack_client.get_channel_id.side_effect = [
            "C111111",  # First channel (feedback-channel-1)
            "C222222",  # Second channel (feedback-channel-2)
            "C333333",  # Notification channel for second channel (team-2)
        ]

        # Create a mock message for channel 2
        channel2_message = SlackMessage(
            text="Feedback for channel 2",
            user="U123",
            ts="1234567890.123456",
            permalink="https://slack.com/archives/C456/p1234567890123456",
            replies=[],
        )

        # Mock first channel to fail, second to succeed
        mock_slack_client.get_messages_from_channel.side_effect = [
            Exception("Channel not found"),  # First channel fails
            [channel2_message],  # Second channel succeeds
        ]
        # Also mock get_messages to handle the case where it's called directly
        mock_slack_client.get_messages.side_effect = [
            Exception("Channel not found"),  # First channel fails
            [channel2_message],  # Second channel succeeds
        ]

        # Mock SlackFeedbackSummarizer
        mock_summarizer = Mock()
        mock_summarizer_class.return_value = mock_summarizer

        # Mock summarize_thread to return summaries for channel 2
        mock_summarizer.summarize_thread.return_value = ThreadSummary(
            original_message=channel2_message,
            summary="General feedback",
            status="unresolved",
            type="other",
        )

        # Mock group_threads_by_theme
        mock_summarizer.group_threads_by_theme.return_value = [
            ThreadSummaryGroup(
                name="General",
                category="other",
                threads=[
                    ThreadSummary(
                        original_message=channel2_message,
                        summary="General feedback",
                        status="unresolved",
                        type="other",
                    )
                ],
            )
        ]

        # Mock GCS
        mock_gcs = Mock()
        mock_gcs_client_class.return_value = mock_gcs
        mock_bucket = Mock()
        mock_gcs.bucket.return_value = mock_bucket
        mock_blob = Mock()
        mock_bucket.blob.return_value = mock_blob

        # Patch _REPORTS to have two test reports
        with patch.object(
            slack_feedback_summarizer,
            "_REPORTS",
            [
                ("feedback-channel-1", "team-1"),
                ("feedback-channel-2", "team-2"),
            ],
        ):
            # Run main - should not raise exception
            slack_feedback_summarizer.main()

        # Verify both channels were attempted (get_messages called twice)
        assert mock_slack_client.get_messages.call_count == 2

        # Verify only the successful channel called summarizer methods
        # (summarize_thread called once for the one message in channel 2)
        mock_summarizer.summarize_thread.assert_called_once()
        mock_summarizer.group_threads_by_theme.assert_called_once()

        # Verify only the successful channel sent a notification
        assert mock_slack_client.send_message.call_count == 1

    @patch("slack_feedback_summarizer.gcs.Client")
    @patch("slack_feedback_summarizer.SlackClient")
    @patch("slack_feedback_summarizer.SlackFeedbackSummarizer")
    def test_handles_empty_feedback_channel(
        self, mock_summarizer_class, mock_slack_client_class, mock_gcs_client_class
    ):
        """Test handling of channels with no feedback."""
        # Setup mock Slack client
        mock_slack_client = Mock()
        mock_slack_client_class.return_value = mock_slack_client
        mock_slack_client.get_channel_id.return_value = "C333333"

        # Mock empty channel
        mock_slack_client.get_messages_from_channel.return_value = []
        mock_slack_client.get_messages.return_value = []

        # Mock SlackFeedbackSummarizer for empty feedback
        mock_summarizer = Mock()
        mock_summarizer_class.return_value = mock_summarizer

        # Mock summarize_threads to return empty list
        mock_summarizer.summarize_threads.return_value = []

        # Mock group_threads_by_theme to return empty list
        mock_summarizer.group_threads_by_theme.return_value = []

        # Mock GCS
        mock_gcs = Mock()
        mock_gcs_client_class.return_value = mock_gcs
        mock_bucket = Mock()
        mock_gcs.bucket.return_value = mock_bucket
        mock_blob = Mock()
        mock_bucket.blob.return_value = mock_blob
        mock_blob.public_url = (
            "https://storage.googleapis.com/feedback/report-empty.html"
        )

        # Run with empty channel
        slack_feedback_summarizer.run_summarizer(
            channel="feedback-empty",
            slack_token="fake-token",
            anthropic_api_key="fake-key",
            enable_logging=False,
            log_dir="/tmp",
            start_date=datetime.datetime.now() - datetime.timedelta(days=7),
            end_date=datetime.datetime.now(),
            output="gs://test-bucket/feedback/report-empty.html",
            gcp_project="test-project",
            notify_channel="team-3",
        )

        # Verify empty list was handled gracefully
        mock_slack_client.get_messages.assert_called_once()

        # Verify summarizer was still created but no threads to summarize
        mock_summarizer_class.assert_called_once()
        # With empty messages, summarize_threads should not be called
        mock_summarizer.summarize_threads.assert_not_called()

        # Verify report was NOT uploaded (early return when no messages)
        mock_blob.upload_from_string.assert_not_called()

        # Verify notification was NOT sent (early return when no messages)
        mock_slack_client.send_message.assert_not_called()


# Test helper functions
def create_mock_slack_message(text="Test message", user="U123", has_thread=False):
    """Helper to create test SlackMessage objects."""
    return SlackMessage(
        text=text,
        user=user,
        ts="1234567890.123456",
        permalink="https://slack.com/archives/C123/p1234567890123456",
        thread_ts="1234567890.123456" if has_thread else None,
        replies=[],
    )
