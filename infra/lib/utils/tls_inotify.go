package utils

import (
	"context"
	"crypto/tls"
	"errors"
	"strings"
	"sync"
	"time"

	"github.com/fsnotify/fsnotify"

	"github.com/augmentcode/augment/infra/lib/logger"
)

var fallbackDuration = 4 * time.Hour

// TLSReader is a utility for reading a [tls.Certificate] with inotify-based dynamic reloading. This is useful for working with certs
// that are renewed at runtime (e.g., k8s cert-manager letsencrypt mounted secrets). As a fallback reloading is also attempted on a
// periodic timer (currently 4h).
type TLSReader interface {
	GetCertificate(*tls.ClientHelloInfo) (*tls.Certificate, error)
	TLSConfig() *tls.Config
}

type tlsReaderImpl struct {
	*logger.Logger

	certFile string
	keyFile  string

	cert tls.Certificate
	lk   sync.RWMutex
}

// NewTLSReader returns a new [TLSReader]. An initial [tls.Certificate] is read and an error returned on failure.
// If no watchFiles are given, certFile and keyFile are watched. Otherwise, the given watchFiles are watched.
// A background watcher go routine is started automatically and closed when the context is cancelled.
func NewTLSReader(ctx context.Context, certFile, keyFile string, watchFiles ...string) (TLSReader, error) {
	i := &tlsReaderImpl{
		certFile: certFile,
		keyFile:  keyFile,
	}
	if cert, err := i.load(); err != nil {
		return nil, err
	} else {
		i.cert = cert
		if err := i.run(ctx, fallbackDuration, watchFiles...); err != nil {
			return nil, err
		}
	}
	return i, nil
}

// load attempts to read the [tls.Certificate] from the filesystem. It does not update the internally cached cert.
func (i *tlsReaderImpl) load() (tls.Certificate, error) {
	return tls.LoadX509KeyPair(i.certFile, i.keyFile)
}

// loadAndSet is a wrapper around [load] which updates the internally cached cert with a write lock.
func (i *tlsReaderImpl) loadAndSet() error {
	if cert, err := i.load(); err != nil {
		return err
	} else {
		i.lk.Lock()
		i.cert = cert
		i.lk.Unlock()
		return nil
	}
}

// set is a simple getter for the inernally cached cert with a read lock.
func (i *tlsReaderImpl) get() *tls.Certificate {
	i.lk.RLock()
	defer i.lk.RUnlock()
	return &i.cert
}

// GetCertificate implements the [tls.Config.GetCertificate] interface. In practice it won't return an error because the internally cached
// cert is only updated when re-read successfully.
func (i *tlsReaderImpl) GetCertificate(info *tls.ClientHelloInfo) (*tls.Certificate, error) {
	return i.get(), nil
}

// TLSConfig is a convenience wrapper that returns a [tls.Config] with [GetCertificate] set.
func (i *tlsReaderImpl) TLSConfig() *tls.Config {
	return &tls.Config{
		GetCertificate: i.GetCertificate,
	}
}

func (i *tlsReaderImpl) run(ctx context.Context, fbDuration time.Duration, watchFiles ...string) error {
	// If not watchFiles are given, watch the cert and key directly. Otherwise watch the given watchFiles.
	watcher, err := fsnotify.NewWatcher()
	if err != nil {
		return err
	}
	if len(watchFiles) == 0 {
		if err := watcher.Add(i.certFile); err != nil {
			return err
		}
		if err := watcher.Add(i.keyFile); err != nil {
			return err
		}
	} else {
		for _, f := range watchFiles {
			if err := watcher.Add(f); err != nil {
				return err
			}
		}
	}

	// Background event loop. Only log errors. Only return on context cancelled.
	go func() {
		i.Info().Msgf("Starting TLS Certificate background watcher (and %v fallback) on %s.", fbDuration, strings.Join(watcher.WatchList(), ", "))
		fbTicker := time.Tick(fbDuration)

		// Track original watchSet for re-adding on removal/rename (when watching directories we receive events for the directory itself and
		// all direct members of the dir)..
		watchSet := map[string]bool{}
		for _, f := range watcher.WatchList() {
			watchSet[f] = true
		}

		for {
			select {
			case <-ctx.Done():
				if err := context.Cause(ctx); errors.Is(err, context.Canceled) {
					i.Info().Err(err).Msg("Stopping Dynmic TLS config watcher.")
				} else {
					i.Err(err).Msg("Stopping Dynmic TLS config watcher.")
				}
				if err := watcher.Close(); err != nil {
					i.Err(err).Msg("Error closing watcher.")
				}
				return

			case err := <-watcher.Errors:
				i.Warn().Err(err).Msg("Watch error.")
				continue

			case ev := <-watcher.Events:
				i.Info().Str("watch.event.file", ev.Name).Str("watch.event.op", ev.Op.String()).Msg("Watch event, reloading certs...")

				// fsnotify docs say watches are removed on these ops. Try to re-add.
				if watchSet[ev.Name] && (ev.Has(fsnotify.Remove) || ev.Has(fsnotify.Rename)) {
					if err := watcher.Add(ev.Name); err != nil {
						i.Err(err).Msg("Error re-adding watch.")
					}
				}

			case <-fbTicker:
				i.Info().Msgf("Fallback timer event (every %v), reloading certs...", fbDuration)
			}

			if err := i.loadAndSet(); err != nil {
				i.Err(err).Msg("Error loading new cert.")
			} else {
				i.Info().Msg("Certificate updated.")
			}
		}
	}()

	return nil
}
